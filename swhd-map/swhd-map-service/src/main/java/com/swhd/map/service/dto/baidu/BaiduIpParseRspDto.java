package com.swhd.map.service.dto.baidu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/4/15
 */
@Getter
@Setter
public class BaiduIpParseRspDto extends BaiduRspDto {

    private String address;

    private Content content;

    @Getter
    @Setter
    public static class Content {

        private String address;

        @JsonProperty("address_detail")
        private AddressDetail addressDetail;

        private Point point;

    }

    @Getter
    @Setter
    public static class AddressDetail {

        @JsonProperty("adcode")
        private String adCode;

        private String city;

        @JsonProperty("city_code")
        private Integer cityCode;

        private String district;

        private String province;

        private String street;

        @JsonProperty("street_number")
        private String streetNumber;

    }

    @Getter
    @Setter
    public static class Point {

        @JsonProperty("x")
        private Double longitude;

        @JsonProperty("y")
        private Double latitude;

    }

}
