package com.swhd.map.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/15
 */
@Getter
@AllArgsConstructor
public enum MapAddressLevel {

    /**
     * 省
     */
    PROVINCE(1),
    /**
     * 市
     */
    CITY(2),
    /**
     * 区
     */
    AREA(3),;

    private final int level;

    public static boolean isProvince(Integer level) {
        return Objects.equals(PROVINCE.getLevel(), level);
    }

    public static boolean isCity(Integer level) {
        return Objects.equals(CITY.getLevel(), level);
    }

    public static boolean isDistrict(Integer level) {
        return Objects.equals(AREA.getLevel(), level);
    }

}
