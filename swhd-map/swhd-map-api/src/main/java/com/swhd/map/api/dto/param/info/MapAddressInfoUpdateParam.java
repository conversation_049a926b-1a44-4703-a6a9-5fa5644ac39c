package com.swhd.map.api.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-04-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AddressInfoUpdateParam对象")
public class MapAddressInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "父菜单ID，0是顶级菜单")
    private Long parentId;

    @Schema(description = "父菜单列表json")
    private Object parentList;

    @Schema(description = "地区名称")
    private String name;

    @Schema(description = "简称")
    private String briefName;

    @Schema(description = "地区别名，多个使用半角逗号分割。如广东省别名广东，广西壮族自治区别名广西")
    private String alias;

    @Schema(description = "地区编码，以高德adCode编码为基准")
    private String code;

    @Schema(description = "地区名称拼音")
    private String spell;

    @Schema(description = "层级：1-省份，2-城市，3-区")
    private Integer level;

    @Schema(description = "序号")
    private Integer ordered;

}
