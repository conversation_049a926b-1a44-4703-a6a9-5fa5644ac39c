package com.swhd.map.api.client;

import com.swhd.map.api.constant.ApiConstant;
import com.swhd.map.api.dto.result.MapAddressApiAddressParseResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/4/15
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MapAddressApiClient.BASE_PATH)
public interface MapAddressApiClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/map/address/api";

    @GetMapping("/addressParse")
    @Operation(summary = "地址解析省市区")
    Rsp<MapAddressApiAddressParseResult> addressParse(@RequestParam("address") String address);

    @GetMapping("/locationParse")
    @Operation(summary = "经纬度解析省市区", description = "location格式：longitude,latitude(经度,纬度)，如：113.412425,23.172599")
    Rsp<MapAddressApiAddressParseResult> locationParse(@RequestParam("location") String location,
                                                     @RequestParam(value = "baidu", required = false, defaultValue = "false") Boolean isBaidu);

    @GetMapping("/ipParse")
    @Operation(summary = "ip解析省市", description = "ip地址如：**************")
    Rsp<MapAddressApiAddressParseResult> ipParse(@RequestParam("ip") String ip);

}
