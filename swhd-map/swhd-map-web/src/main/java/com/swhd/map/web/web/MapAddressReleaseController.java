package com.swhd.map.web.web;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.map.api.client.MapAddressReleaseClient;
import com.swhd.map.api.dto.result.MapAddressReleaseSimpleResult;
import com.swhd.map.web.common.constant.WebConstant;
import com.swhd.map.web.vo.result.MapAddressReleaseSimpleResultVo;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/10/30
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/mapAddressRelease")
public class MapAddressReleaseController {

    private final MapAddressReleaseClient mapAddressReleaseClient;

    @Operation(summary = "根据父节点查询列表", description = "父节点为空或100000查询顶级节点")
    @GetMapping("/listByParentCode")
    Rsp<List<MapAddressReleaseSimpleResultVo>> listByParentCode(
            @RequestParam(value = "parentCode", required = false) String parentCode) {
        Rsp<List<MapAddressReleaseSimpleResult>> rsp = mapAddressReleaseClient.listByParentCode(parentCode);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        List<MapAddressReleaseSimpleResultVo> voList = Func.copy(rsp.getData(), MapAddressReleaseSimpleResultVo.class);
        return RspHd.data(voList);
    }

}
