package com.swhd.open.service;

import com.swhd.magiccube.test.codegen.CodeGenerator;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
public class CodeGenTest {

    @Test
    public void genTest() {
        new CodeGenerator()
                .dataSourceConfig("**************************************************************************************************************************************", "test_mj_all", "test_mj_all321")
                .parentPackage("com.swhd.open")
                .columnPrefix("topen_")
                .moduleName("app")
                .authorByGitUserEmail()
                .mapperXml(false)
                .gen("topen_app_tenant_login");
    }

}
