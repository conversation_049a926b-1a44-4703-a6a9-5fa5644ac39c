package com.swhd.open.service.app.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.open.api.app.client.AppInfoClient;
import com.swhd.open.api.app.dto.param.info.AppInfoAddParam;
import com.swhd.open.api.app.dto.param.info.AppInfoPageParam;
import com.swhd.open.api.app.dto.param.info.AppInfoUpdateParam;
import com.swhd.open.api.app.dto.result.AppInfoResult;
import com.swhd.open.service.app.entity.AppInfo;
import com.swhd.open.service.app.service.AppInfoService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(AppInfoClient.BASE_PATH)
public class AppInfoController implements AppInfoClient {

    private final AppInfoService appInfoService;

    @Override
    public Rsp<PageResult<AppInfoResult>> page(AppInfoPageParam param) {
        IPage<AppInfo> iPage = appInfoService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AppInfoResult.class));
    }

    @Override
    public Rsp<AppInfoResult> getById(Long id) {
        AppInfo entity = appInfoService.getById(id);
        return RspHd.data(Func.copy(entity, AppInfoResult.class));
    }

    @Override
    public Rsp<List<AppInfoResult>> listByIds(Collection<Long> ids) {
        List<AppInfo> list = appInfoService.listByIds(ids);
        return RspHd.data(Func.copy(list, AppInfoResult.class));
    }

    @Override
    public Rsp<AppInfoResult> getBySvgOpenAppId(String svgOpenAppId) {
        AppInfo entity = appInfoService.getBySvgOpenAppId(svgOpenAppId);
        return RspHd.data(Func.copy(entity, AppInfoResult.class));
    }

    @Override
    public Rsp<Void> add(AppInfoAddParam param) {
        boolean result = appInfoService.save(Func.copy(param, AppInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AppInfoUpdateParam param) {
        boolean result = appInfoService.updateById(Func.copy(param, AppInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = appInfoService.removeByIds(ids);
        return RspHd.status(result);
    }

}
