package com.swhd.open.web.dto.param.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserTenantAddCreateParamVo对象")
public class UserTenantCreateParamVo {

    @Schema(description = "慧引流租户ID")
    private Long hylTenantId;

    @NotBlank(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private String userId;

    @NotBlank(message = "用户昵称不能为空")
    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @NotEmpty(message = "请选择角色")
    @Schema(title = "角色ID列表")
    private List<Long> roleIds;

    @NotEmpty(message = "请选择团队")
    @Schema(title = "团队主键ID列表")
    private List<Long> teamIds;

}
