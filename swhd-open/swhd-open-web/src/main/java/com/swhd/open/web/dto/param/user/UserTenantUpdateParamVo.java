package com.swhd.open.web.dto.param.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserTenantUpdateParamVo对象")
public class UserTenantUpdateParamVo {

    @Schema(description = "慧引流租户ID")
    private Long hylTenantId;

    @NotNull(message = "慧引流用户ID不能为空")
    @Schema(description = "慧引流用户ID")
    private Long hylUserId;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(title = "角色ID列表")
    private List<Long> roleIds;

    @Schema(title = "团队主键ID列表")
    private List<Long> teamIds;

}
