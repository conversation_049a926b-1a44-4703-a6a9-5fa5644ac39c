package com.swhd.open.api.app.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AppInfoResult对象")
public class AppInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "三维家开放平台应用id")
    private String svgOpenAppId;

    @Schema(description = "三维家开放平台应用密钥")
    private String svgOpenAppSecret;

    @Schema(description = "三维家开放平台签名密钥")
    private String svgOpenSignSecret;

    @Schema(description = "三维家开放平台校验密钥")
    private String svgOpenVerifySecret;

    @Schema(description = "个人注册来源场景")
    private String personRegisterSourceScene;

    @Schema(description = "企业注册来源场景")
    private String corpRegisterSourceScene;

    @Schema(description = "权限url，多个逗号隔开")
    private String permissionUrl;

    @Schema(description = "状态：0-下线，1-上线")
    private Integer state;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
