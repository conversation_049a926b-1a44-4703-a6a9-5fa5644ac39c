package com.swhd.service.market.api.rights.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 消费权益结果，包含消费流水号和过期时间
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Data
@Accessors(chain = true)
@Schema(description = "消费权益结果，包含消费流水号和过期时间")
public class ConsumeWithExpiryResult {

    @Schema(description = "消费流水号")
    private String recordTransactionId;

    @Schema(description = "过期时间")
    private LocalDate expiryTime;
} 