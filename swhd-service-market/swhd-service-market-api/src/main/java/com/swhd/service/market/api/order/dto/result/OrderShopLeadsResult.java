package com.swhd.service.market.api.order.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/4/23
 */
@Data
@Schema(description = "订单客资权益分配情况")
public class OrderShopLeadsResult {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单客资数")
    private Integer leadsCount;

    @Schema(description = "已分配客资数")
    private Integer assignedLeadsCount;

    @Schema(description = "未分配客资数")
    private Integer unassignedLeadsCount;

}
