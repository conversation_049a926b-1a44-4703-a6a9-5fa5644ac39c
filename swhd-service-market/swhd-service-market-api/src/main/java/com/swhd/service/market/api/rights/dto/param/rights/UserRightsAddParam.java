package com.swhd.service.market.api.rights.dto.param.rights;

import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserRightsAddParam对象")
public class UserRightsAddParam {

    @NotNull
    @Schema(description = "总量")
    private BigDecimal capacity;

    @NotEmpty(message = "权益类型不能为空")
    @Schema(description = "权益类型")
    private String rightsType;

    @Schema(description = "生效时间")
    private LocalDate effectiveTime;

    @Schema(description = "过期时间")
    private LocalDate expiryTime;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单类型：0-付费订单；1-赠送订单；2-试用")
    private Integer orderType;

    @Schema(description = "获得类型：RECHARGE-充值;SUBSCRIPTION-会员订阅;GIFT-赠送;TRIAL-试用")
    private String acquisitionType;

    @Schema(description = "来源：WORKSPACE-工作台;ORDER-订单;ACTIVITY-活动;")
    private String rightsSource;

    @Schema(description = "主体类型：PERSONAL-个人;TEAM-团队;SHOP-门店;TENANT-租户")
    private String subjectType;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;

}
