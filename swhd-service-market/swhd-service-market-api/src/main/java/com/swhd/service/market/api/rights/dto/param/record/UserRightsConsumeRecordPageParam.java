package com.swhd.service.market.api.rights.dto.param.record;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserRightsConsumeRecordPageParam对象")
public class UserRightsConsumeRecordPageParam extends PageReq {

    @Schema(description = "门店id")
    private Long shopId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "收支方向：in-收入，out-支出")
    private String direction;

    @Schema(description = "内容id列表")
    private List<Long> bizIds;

    @Schema(description = "业务编码")
    private String bizCode;

    @Schema(description = "业务项目")
    private String bizItem;

    @Schema(description = "消费时间")
    private List<LocalDate> consumeTimeBetween;

    @Schema(description = "权益id")
    private Long rightsId;

    @Schema(description = "权益类型")
    private String rightsType;

    @Schema(description = "支付来源：0-权益包，1-授信，2-欠费")
    private Integer paymentSource;

    @Schema(description = "消费流水号")
    private String recordTransactionId;

    @Schema(description = "消费状态：0-待确认；1-已消费；2-已回滚")
    private List<Integer> consumeStatuses;

    @Schema(description = "排除的消费状态列表：0-待确认；1-已消费；2-已回滚")
    private List<Integer> excludeConsumeStatuses;

    @Schema(description = "防重ID")
    private String nonce;

    @Schema(description = "权益主体列表")
    private List<String> subjectTypes;

    @Schema(description = "限制ID，用于查询JSON数组中包含指定值的记录")
    private Long limitId;

}
