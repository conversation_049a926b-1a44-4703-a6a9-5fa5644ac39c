package com.swhd.service.market.api.rights.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "云店客资分配记录")
@Data
public class OrderShopLeadsAssignResult {

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "已分配数量")
    private Integer assignedCount;

}
