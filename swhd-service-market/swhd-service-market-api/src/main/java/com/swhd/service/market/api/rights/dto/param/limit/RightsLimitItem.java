package com.swhd.service.market.api.rights.dto.param.limit;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 权益限制项参数
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "权益限制项参数")
public class RightsLimitItem {

    @NotNull(message = "权益类型不能为空")
    @Schema(description = "权益类型")
    private String rightsType;

    @NotNull(message = "模式不能为空")
    @Schema(description = "模式：true-限制，false-共享")
    private Boolean limitMode;

    @Schema(description = "限制数量，当limitMode=true时必填")
    private BigDecimal limitAmount;
}
