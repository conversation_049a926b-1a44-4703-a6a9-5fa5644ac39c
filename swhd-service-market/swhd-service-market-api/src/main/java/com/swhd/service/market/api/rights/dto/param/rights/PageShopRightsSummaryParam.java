package com.swhd.service.market.api.rights.dto.param.rights;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <EMAIL>
 * @since 2024/12/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分页查询门店权限参数")
public class PageShopRightsSummaryParam extends PageReq {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "权益类型")
    private String rightsType;

}
