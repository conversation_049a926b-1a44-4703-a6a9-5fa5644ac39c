package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserRightsBatchAddByOrderParam对象")
public class UserRightsBatchAddByOrderParam {

    @NotEmpty(message = "权益列表不能为空")
    @Schema(description = "权益列表")
    private List<UserRightsInfoParam> rightsInfos;

    @NotEmpty(message = "订单编码不能为空")
    @Schema(description = "订单编号")
    private String orderNo;

    @NotNull(message = "订单类型不能为空")
    @Schema(description = "订单类型：0-付费订单；1-赠送订单；2-试用;3-渠道订单")
    private Integer orderType;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "获得类型：RECHARGE-充值;SUBSCRIPTION-会员订阅;GIFT-赠送;TRIAL-试用")
    private String acquisitionType;

    @Schema(description = "来源：WORKSPACE-工作台;ORDER-订单;ACTIVITY-活动;")
    private String rightsSource;

    @Schema(description = "主体类型：PERSONAL-个人;TEAM-团队;SHOP-门店;TENANT-租户")
    private String subjectType;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;

}
