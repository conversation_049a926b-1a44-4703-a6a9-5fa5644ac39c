package com.swhd.service.market.api.product.dto.param.module;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ProductSkuAuthModuleUpdateParam对象")
public class ProductSkuAuthModuleUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "授权ID")
    private Long moduleId;

    @Schema(description = "有效期（天）")
    private Integer validDays;

}
