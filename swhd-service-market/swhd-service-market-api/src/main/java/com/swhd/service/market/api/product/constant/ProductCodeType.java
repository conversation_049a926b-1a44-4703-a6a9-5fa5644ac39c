package com.swhd.service.market.api.product.constant;

import com.google.common.collect.Maps;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.tool.enumeration.EnumCodeDesc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * 产品代码类型枚举
 * <AUTHOR>
 * @since 2025/4/27 16:09
 */
@Getter
@AllArgsConstructor
public enum ProductCodeType implements EnumCodeDesc<ProductCodeType> {

    AI_MIX("AI_MIX", "ai混剪"),
    AI_OUTFIT("AI_OUTFIT", "ai换搭"),
    AI_LIGHT_DESIGN("AI_LIGHT_DESIGN", "ai轻设计"),
    AI_STUDIO("AI_STUDIO", "ai影棚"),
    MATRIX_OPERATION("MATRIX_OPERATION", "矩阵运营"),
    CLOUD_SHOP("SHOP", "云店"),
    AI_CUSTOMER_SERVICE("AI_CUSTOMER_SERVICE", "ai客服"),
    MEMBERSHIP("MEMBERSHIP", "会员"),
    ;

    private final String code;

    private final String desc;


    private static final Map<String, ProductCodeType> CACHE;

    static {
        final Map<String, ProductCodeType> map = Maps.newHashMapWithExpectedSize(values().length);
        for (ProductCodeType value : values()) {
            map.put(StringUtils.lowerCase(value.code), value);
        }
        CACHE = Collections.unmodifiableMap(map);
    }

    public static ProductCodeType of(String code) {
        return CACHE.get(StringUtils.lowerCase(code));
    }

    public static ProductCodeType ofRequired(String code) {
        final ProductCodeType type = of(code);
        if (Objects.nonNull(type)) {
            return type;
        }
        throw new ServiceException("不支持的产品代码类型");
    }
}
