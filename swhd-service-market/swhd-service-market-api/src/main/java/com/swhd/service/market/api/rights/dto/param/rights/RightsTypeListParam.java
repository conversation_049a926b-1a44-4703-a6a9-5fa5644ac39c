package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/8
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RightsTypeListParam对象")
public class RightsTypeListParam {

    @Schema(description = "权益类型编码")
    private String rightsTypeCode;

    @Schema(description = "权益类型名称")
    private String rightsName;

    @Schema(description = "是否允许欠费：0-否，1-是")
    private Integer allowOverdraft;

    @Schema(description = "账户是否唯一：0-不唯一，1-唯一")
    private Integer isUnique;

    @Schema(description = "权益分配级别，如TEAM或INDIVIDUAL，用于查询包含指定分配级别的权益类型")
    private String distributionLevel;

}
