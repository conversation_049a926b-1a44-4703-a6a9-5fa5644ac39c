package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 检查是否有足够权益可以消费的参数对象
 *
 * @since 2024/11/28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "检查权益消费参数")
public class CanConsumeRightsParam {

    @NotEmpty(message = "权益类型不能为空")
    @Schema(description = "权益类型")
    private String rightsType;

    @NotNull(message = "消费数量不能为空")
    @Schema(description = "要消费的数量")
    private BigDecimal consumeQuantity;

    @Schema(description = "当前已有的数量,为null时候会根据剩余量去判断，不为null则直接从总量去判断")
    private BigDecimal currentCount;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;
}