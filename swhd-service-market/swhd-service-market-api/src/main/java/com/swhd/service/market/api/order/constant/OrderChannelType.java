package com.swhd.service.market.api.order.constant;

import com.google.common.collect.Maps;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.tool.enumeration.EnumCodeDesc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/12 20:46
 */
@Getter
@AllArgsConstructor
public enum OrderChannelType implements EnumCodeDesc<OrderChannelType> {

    SWHD("SWHD", "慧引流"),
    FUWU_OCEANENGINE("FuwuOceanengine", "巨量群峰市场"),
    SHOP("SHOP", "云店"),
    CRM("CRM", "CRM"),
    AI_CREATION("AI_CREATION", "AI创作"),
    OPERATION("OPERATION", "运营后台"),
    ;

    private final String code;

    private final String desc;


    private static final Map<String, OrderChannelType> CACHE;

    static {
        final Map<String, OrderChannelType> map = Maps.newHashMapWithExpectedSize(values().length);
        for (OrderChannelType value : values()) {
            map.put(StringUtils.lowerCase(value.code), value);
        }
        CACHE = Collections.unmodifiableMap(map);
    }

    public static OrderChannelType of(String code) {
        return CACHE.get(StringUtils.lowerCase(code));
    }

    public static OrderChannelType ofRequired(String code) {
        final OrderChannelType type = of(code);
        if (Objects.nonNull(type)) {
            return type;
        }
        throw new ServiceException("不支持的订单渠道类型");
    }

}
