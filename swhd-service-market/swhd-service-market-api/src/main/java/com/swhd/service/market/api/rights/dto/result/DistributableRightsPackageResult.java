package com.swhd.service.market.api.rights.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 可分配权益包结果
 * 
 * <AUTHOR>
 * @since 2025/5/10
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "可分配权益包结果")
public class DistributableRightsPackageResult {
    
    @Schema(description = "权益ID")
    private Long id;
    
    @Schema(description = "总量")
    private BigDecimal capacity;
    
    @Schema(description = "剩余量")
    private BigDecimal residueCapacity;
    
    @Schema(description = "过期时间")
    private LocalDate expiryTime;
    
    @Schema(description = "获取类型：RECHARGE-充值;SUBSCRIPTION-会员订阅;GIFT-赠送;TRIAL-试用")
    private String acquisitionType;
    
    @Schema(description = "获取类型名称")
    private String acquisitionTypeName;
    
}
