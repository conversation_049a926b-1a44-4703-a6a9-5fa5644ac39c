package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@Accessors(chain = true)
@Schema(description = "UserRightsListParam对象")
public class UserRightsListParam {

    @Schema(description = "总量")
    private BigDecimal capacity;

    @Schema(description = "剩余量")
    private BigDecimal residueCapacity;

    @Schema(description = "权益类型")
    private String rightsType;

    @Schema(description = "权益状态：0-待结算；1-已结算；2-授信的；3-欠费的；")
    private Integer rightsStatus;

    @Schema(description = "生效时间-开始")
    private LocalDate effectiveTimeBegin;

    @Schema(description = "生效时间-结束")
    private LocalDate effectiveTimeEnd;

    @Schema(description = "过期时间-开始")
    private LocalDate expiryTimeBegin;

    @Schema(description = "过期时间-结束")
    private LocalDate expiryTimeEnd;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单类型：0-付费订单；1-赠送订单；2-试用")
    private Integer orderType;

    @Schema(description = "获得类型：RECHARGE-充值;SUBSCRIPTION-会员订阅;GIFT-赠送;TRIAL-试用")
    private String acquisitionType;

    @Schema(description = "来源：WORKSPACE-工作台;ORDER-订单;ACTIVITY-活动;")
    private String rightsSource;

    @Schema(description = "主体类型：PERSONAL-个人;TEAM-团队;SHOP-门店;TENANT-租户")
    private String subjectType;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "包含的权益状态列表，优先级高于rightsStatus")
    private List<Integer> includeRightsStatusList;

    @Schema(description = "排除的权益状态列表，优先级低于includeRightsStatusList")
    private List<Integer> excludeRightsStatusList;

}
