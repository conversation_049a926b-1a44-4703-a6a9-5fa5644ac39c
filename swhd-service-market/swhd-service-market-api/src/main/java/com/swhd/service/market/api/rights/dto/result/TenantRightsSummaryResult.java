package com.swhd.service.market.api.rights.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 租户权益汇总结果
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "租户权益汇总结果")
public class TenantRightsSummaryResult {

    @Schema(description = "权益类型编码")
    private String rightsTypeCode;

    @Schema(description = "权益类型名称")
    private String rightsTypeName;

    @Schema(description = "剩余量")
    private BigDecimal remainingAmount;

    @Schema(description = "已消耗量")
    private BigDecimal consumedAmount;

    @Schema(description = "总量")
    private BigDecimal totalAmount;

    @Schema(description = "该类型的所有权益列表")
    private List<UserRightsResult> rightsList;
}
