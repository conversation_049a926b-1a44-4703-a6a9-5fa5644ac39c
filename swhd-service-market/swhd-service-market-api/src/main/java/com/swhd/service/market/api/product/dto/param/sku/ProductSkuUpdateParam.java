package com.swhd.service.market.api.product.dto.param.sku;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ProductSkuUpdateParam对象")
public class ProductSkuUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = " 0：下架，1：上架，2：待上架，3：上架审核中，4：下架审核中")
    private Integer skuStatus;

    @Schema(description = "售价(元)")
    private BigDecimal price;

    @Schema(description = "原价(元)")
    private BigDecimal originalPrice;

    /**
     * 折扣百分比
     */
    private BigDecimal discountPercentage;

    @Schema(description = "价格描述文本")
    private String priceDescription;

    @Schema(description = "sku类型")
    private String skuType;

    @Schema(description = "有效期（天）")
    private Integer validDays;

    @Schema(description = "sku介绍")
    private String skuIntro;

    @Schema(description = "sku内容")
    private String skuContent;

    @Schema(description = "授权模块Id列表")
    private List<Long> moduleIds;

    @Schema(description = "权益列表")
    private List<ProductSkuRightsSaveParam> rightsList;

    @Schema(description = "权益展示列表")
    private List<Benefit> benefitList;

    @Schema(description = "创作云账号类型")
    private String museAccountType;

    @Schema(description = "排序")
    private Integer ordered;

    @Schema(description = "会员级别：personal_professional-个人专业版;enterprise_professional-企业专业版；enterprise_flagship-企业旗舰版；")
    private String membershipCode;

    @Schema(description = "产品代码列表JSON，如：[\"AI_MIX\",\"MEMBERSHIP\",\"AI_STUDIO\"]")
    private List<String> productCodes;

    @Schema(description = "分配策略列表")
    private List<ProductSkuDistributionStrategySaveParam> distributionStrategies;

}
