package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/11/22 17:42
 */
@Data
@Accessors(chain = true)
@Schema(description = "GetPurchasePlatformParam对象")
public class GetPurchasePlatformParam {

    @NotNull
    @Schema(description = "租户ID")
    private Long tenantId;

    @NotNull
    @Schema(description = "用户ID")
    private Long userId;

}
