package com.swhd.service.market.api.rights.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/11/22 17:40
 */

@Data
@Accessors(chain = true)
@Schema(description = "PurchasePlatformResult对象")
public class PurchasePlatformResult {
    @Schema(description = "平台代码")
    private String platform;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "购买地址")
    private String purchaseUrl;

}
