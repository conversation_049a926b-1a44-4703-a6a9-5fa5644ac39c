package com.swhd.service.market.api.product.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.checkerframework.checker.units.qual.C;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ProductSkuAuthModuleResult对象")
public class ProductSkuAuthModuleResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "授权类型：1-授权模块，2-授权模块组")
    private Integer authType;

    @Schema(description = "授权模块ID")
    private Long moduleId;

    @Schema(description = "授权模块组ID")
    private Long moduleGroupId;

    @Schema(description = "有效期（天）")
    private Integer validDays;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
