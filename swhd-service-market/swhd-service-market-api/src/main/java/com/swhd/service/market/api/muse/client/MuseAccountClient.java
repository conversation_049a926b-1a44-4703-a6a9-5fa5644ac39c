package com.swhd.service.market.api.muse.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.service.market.api.common.constant.ApiConstant;
import com.swhd.service.market.api.muse.dto.param.account.MuseAccountAddParam;
import com.swhd.service.market.api.muse.dto.param.account.MuseAccountPageParam;
import com.swhd.service.market.api.muse.dto.param.account.MuseAccountUpdateParam;
import com.swhd.service.market.api.muse.dto.result.MuseAccountResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-28
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MuseAccountClient.BASE_PATH)
public interface MuseAccountClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/muse/account";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MuseAccountResult>> page(@RequestBody @Valid MuseAccountPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MuseAccountResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MuseAccountResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MuseAccountAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid MuseAccountUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
