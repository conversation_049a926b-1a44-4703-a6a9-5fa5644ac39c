package com.swhd.service.market.api.rights.dto.param.rights;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/4
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserRightsInitParam")
public class UserRightsInitParam {

    @NotNull
    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "团队ID")
    private Long teamId;
    @NotBlank
    @Schema(description = "权益类型")
    private String rightsType;

    @Schema(description = "权益主体类型")
    private String subjectType;

}
