package com.swhd.service.market.web.tenant.rights.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.swhd.ai.kefu.api.user.client.UserInfoClient;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoResult;
import com.swhd.creative.api.muse.client.MuseAccountPointClient;
import com.swhd.creative.api.muse.dto.param.point.GetMuseAccountPointParam;
import com.swhd.creative.api.muse.dto.result.MuseAccountPointResult;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.service.market.api.rights.client.UserRightsClient;
import com.swhd.service.market.api.rights.client.UserRightsConsumeRecordClient;
import com.swhd.service.market.api.rights.constant.ConsumeStatus;
import com.swhd.service.market.api.rights.dto.param.record.UserRightsConsumeRecordPageParam;
import com.swhd.service.market.api.rights.dto.param.rights.GetTotalAndRemainingRightsParam;
import com.swhd.service.market.api.rights.dto.param.rights.ListRightsSummaryParam;
import com.swhd.service.market.api.rights.dto.result.TenantRightsSummaryResult;
import com.swhd.service.market.api.rights.dto.result.UserRightsConsumeRecordResult;
import com.swhd.service.market.api.rights.dto.result.UserTotalAndRemainingRightsResult;
import com.swhd.service.market.web.tenant.rights.properties.RightsProperties;
import com.swhd.service.market.web.tenant.rights.vo.result.UserRightsConsumeRecordResultVo;
import com.swhd.user.api.tenant.wrapper.TenantUserInfoWrapper;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/10
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserRightsService {

    private final UserRightsClient userRightsClient;
    private final UserRightsConsumeRecordClient userRightsConsumeRecordClient;
    private final MuseAccountPointClient museAccountPointClient;
    private final UserInfoClient userInfoClient;

    private final RightsProperties rightsProperties;

    /**
     * 获取租户客资权益和使用情况
     */
    public Rsp<List<UserTotalAndRemainingRightsResult>> getTotalAndRemainingRights(Boolean onlyHasRights) {
        Rsp<List<UserTotalAndRemainingRightsResult>> totalAndRemainingRightsRsp = userRightsClient.getTotalAndRemainingRights(new GetTotalAndRemainingRightsParam(onlyHasRights,false,CurrentUserHolder.currentUserId(),null));
        if (CollectionUtil.isNotEmpty(totalAndRemainingRightsRsp.getData())) {
            //创点统计
            totalAndRemainingRightsRsp.getData().stream().filter(t -> Objects.equals(t.getRightsTypeCode(), "VideoCreativePoint")).findAny().ifPresent(
                    userTotalAndRemainingRightsResult -> {
                        Long userId = CurrentUserHolder.currentUserId();
                        Rsp<MuseAccountPointResult> museAccountPointRsp = museAccountPointClient.getMuseAccountPoint(new GetMuseAccountPointParam().setUserId(userId));
                        if (Rsp.isFail(museAccountPointRsp)) {
                            log.error("获取创点权益失败，Rsp:{}", JsonUtil.toJsonString(museAccountPointRsp));
                        } else if (Objects.nonNull(museAccountPointRsp.getData())) {
                            userTotalAndRemainingRightsResult.setCapacity(museAccountPointRsp.getData().getCapacity());
                            userTotalAndRemainingRightsResult.setResidueCapacity(
                                    museAccountPointRsp.getData().getCapacity().subtract(museAccountPointRsp.getData().getUsageCapacity()));
                        }
                    });
        }
        return totalAndRemainingRightsRsp;
    }

    /**
     * 分页查询消费记录
     */
    public Rsp<PageResult<UserRightsConsumeRecordResultVo>> pageRightsRecord(UserRightsConsumeRecordPageParam param) {
        param.setExcludeConsumeStatuses(List.of(ConsumeStatus.ROLLBACK.getCode()));
        Rsp<PageResult<UserRightsConsumeRecordResult>> pageRsp = userRightsConsumeRecordClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<UserRightsConsumeRecordResultVo> pageResult = PageUtil.convert(pageRsp.getData(), UserRightsConsumeRecordResultVo.class);
        //返回业务数据
        fillBizContent(pageResult.getRecords());
        //设置操作用户
        TenantUserInfoWrapper.getInstance().setList(pageResult.getRecords(),
                UserRightsConsumeRecordResultVo::getUserId,
                (vo, userInfo) -> vo.setOperator(userInfo.getNickname()));

        return RspHd.data(pageResult);
    }

    private void fillBizContent(List<UserRightsConsumeRecordResultVo> datas) {
        if (CollectionUtil.isEmpty(datas)) {
            return;
        }

        Map<Long, String> bizIdContentMap = new HashMap<>();
        Map<String, List<UserRightsConsumeRecordResultVo>> rightsTypeGroupMap = datas.stream().collect(Collectors.groupingBy(UserRightsConsumeRecordResultVo::getRightsType));
        //ai客资 权益
        if (rightsTypeGroupMap.containsKey("AiCustomerCapital")) {
            Map<Long, UserRightsConsumeRecordResultVo> bizIdMap = rightsTypeGroupMap.get("AiCustomerCapital")
                    .stream()
                    .collect(Collectors.toMap(UserRightsConsumeRecordResultVo::getBizId, Function.identity(), (o1, o2) -> o2));

            Rsp<List<UserInfoResult>> contentsRsp = TenantHolder.methodIgnoreTenant(() ->
                    userInfoClient.listByIds(bizIdMap.keySet().stream().filter(Objects::nonNull).toList()));
            if (Rsp.successAndDataNotNull(contentsRsp)) {
                bizIdContentMap = contentsRsp.getData().stream().collect(Collectors.toMap(UserInfoResult::getId, UserInfoResult::getUserNickname, (o1, o2) -> o2));
            }
        }

        for (UserRightsConsumeRecordResultVo r : datas) {
            Optional.ofNullable(bizIdContentMap.get(r.getBizId())).ifPresent(r::setBizContentName);
            //设置 业务项名称（待优化为字典）
            Optional.ofNullable(rightsProperties.getBizCodeMap().get(r.getBizCode())).ifPresent(r::setBizName);
            Optional.ofNullable(rightsProperties.getBizItemMap().get(r.getBizItem())).ifPresent(r::setBizItemName);
            if(StrUtil.isEmpty(r.getBizItemName())){
                r.setBizItemName(r.getBizItemDesc());
            }
        }
    }

    /**
     * 获取权益列表，按类型分组汇总
     *
     * @param subjectType 主体类型，可以为空，为空时返回所有类型
     * @param includeAllTypes 是否包含所有权益类型（包括租户不存在的类型）
     * @return 权益汇总列表
     */
    public Rsp<List<TenantRightsSummaryResult>> listRightsSummary(String subjectType, boolean includeAllTypes) {
        return listRightsSummary(subjectType, includeAllTypes, null);
    }

    /**
     * 获取权益列表，按类型分组汇总
     *
     * @param subjectType 主体类型，可以为空，为空时返回所有类型
     * @param includeAllTypes 是否包含所有权益类型（包括租户不存在的类型）
     * @param teamId 团队ID，当subjectType为TEAM时使用
     * @return 权益汇总列表
     */
    public Rsp<List<TenantRightsSummaryResult>> listRightsSummary(String subjectType, boolean includeAllTypes, Long teamId) {
            // 创建参数对象
            ListRightsSummaryParam param = new ListRightsSummaryParam();
            param.setSubjectType(subjectType);
            param.setIncludeAllTypes(includeAllTypes);
            param.setTeamId(teamId);

            // 调用新的API方法
            Rsp<List<TenantRightsSummaryResult>> resultRsp = userRightsClient.listRightsSummary(param);
            if (RspHd.isFail(resultRsp)) {
                log.error("获取权益列表失败:{}", resultRsp);
                return RspHd.fail("获取权益列表失败");
            }
            return resultRsp;
    }


}
