package com.swhd.service.market.web.tenant.pay.vo.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/10/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "生成支付二维码结果")
public class GetH5QrCodeResultVO {

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "二维码ID（前端自行按规则拼接成二维码图片地址，并通过此ID查询是否已扫码、是否过期等）")
    private String qrCodeId;

}
