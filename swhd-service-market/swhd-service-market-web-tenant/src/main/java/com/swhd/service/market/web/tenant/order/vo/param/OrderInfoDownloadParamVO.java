package com.swhd.service.market.web.tenant.order.vo.param;

import com.swhd.service.market.api.order.dto.param.info.OrderInfoPageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/7
 */
@Data
public class OrderInfoDownloadParamVO extends OrderInfoPageParam {

    private Long tenantId;

}
