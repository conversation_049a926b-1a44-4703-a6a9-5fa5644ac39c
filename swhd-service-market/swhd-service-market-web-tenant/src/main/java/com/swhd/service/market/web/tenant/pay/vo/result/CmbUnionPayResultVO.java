package com.swhd.service.market.web.tenant.pay.vo.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "招行聚合支付结果")
public class CmbUnionPayResultVO {

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "H5收银台地址")
    private String payUrl;

}
