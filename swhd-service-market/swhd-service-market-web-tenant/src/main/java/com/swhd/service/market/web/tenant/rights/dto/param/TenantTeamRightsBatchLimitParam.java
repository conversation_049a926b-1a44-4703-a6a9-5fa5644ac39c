package com.swhd.service.market.web.tenant.rights.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 企业团队权益批量限制参数
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "企业团队权益批量限制参数")
public class TenantTeamRightsBatchLimitParam {

    @Schema(description = "租户团队ID")
    @NotNull(message = "租户团队ID不能为空")
    private Long teamId;

    @NotEmpty(message = "权益限制列表不能为空")
    @Valid
    @Schema(description = "权益限制列表")
    private List<TenantTeamRightsLimitItem> rightsLimits;
}
