package com.swhd.service.market.web.tenant.pay.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 支付配置
 * <AUTHOR> <EMAIL>
 * @date 2024/10/12
 */
@Data
@Component
@ConfigurationProperties("pay")
public class PayProperties {

    /**
     * 支付中心支付企业id
     */
    private Long payEnterpriseId;

    /**
     * 注册在支付中心的应用编码
     */
    private String appCode = "SWHD";

    /**
     * 支付成功回调地址
     */
    private String paySuccessUrl;

}
