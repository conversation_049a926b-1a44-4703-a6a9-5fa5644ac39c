package com.swhd.service.market.web.tenant.order.dto;

import com.swhd.magiccube.easypoi.interfaces.IPoiIndex;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店客资自定义分配信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopLeadsCustomAssignItem implements IPoiIndex {

    private Integer index;

    /**
     * 门店名称
     */
    private String shopNameStr;

    /**
     * 门店ID(字符串)
     */
    private String shopIdStr;

    /**
     * 门店ID
     */
    private Long shopId;

    /**
     * 分配客资数(字符串)
     */
    private String assignCountStr;

    /**
     * 分配客资数
     */
    private Integer assignCount;

    /**
     * 错误信息
     */
    private String errorMsg;
} 