package com.swhd.service.market.service.rights.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.rights.dto.param.limit.RightsTeamUserLimitBatchParam;
import com.swhd.service.market.api.rights.dto.param.limit.RightsTeamUserLimitPageParam;
import com.swhd.service.market.api.rights.dto.param.limit.RightsTeamUserLimitQueryParam;
import com.swhd.service.market.api.rights.dto.result.RightsTeamUserLimitListResult;
import com.swhd.service.market.service.rights.entity.RightsTeamUserLimit;

import java.util.List;

/**
 * 团队用户权益消费限制表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
public interface RightsTeamUserLimitService extends IBaseHdService<RightsTeamUserLimit> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<RightsTeamUserLimit> page(RightsTeamUserLimitPageParam param);

	/**
	 * 批量设置团队用户权益限制
	 *
	 * @param param 批量设置参数
	 * @return 是否成功
	 */
	boolean batchSetUserRightsLimits(RightsTeamUserLimitBatchParam param);

	/**
	 * 查询团队用户权益限制列表
	 *
	 * @param param 查询参数
	 * @return 限制列表
	 */
	List<RightsTeamUserLimitListResult> listUserRightsLimits(RightsTeamUserLimitQueryParam param);

}
