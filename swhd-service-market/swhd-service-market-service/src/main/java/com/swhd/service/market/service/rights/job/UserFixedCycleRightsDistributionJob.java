package com.swhd.service.market.service.rights.job;

import com.swhd.magiccube.core.user.TenantHolder;
import com.xxl.job.core.ext.handler.annotation.SwjJob;
import com.swhd.service.market.service.rights.entity.UserRightsDistributionStrategy;
import com.swhd.service.market.service.rights.service.UserRightsDistributionStrategyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户固定周期权益分配任务
 * <AUTHOR>
 * @since 2025/4/14 14:33
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserFixedCycleRightsDistributionJob {

    private final UserRightsDistributionStrategyService userRightsDistributionStrategyService;

    /**
     * 固定周期策略分配到用户周期切换
     * 每天凌晨5分开始执行
     */
    @SwjJob(jobDesc = "固定周期策略分配到用户周期切换", author = "zenghaoming",
            schedule = "${magiccube.job.schedule.UserFixedCycleRightsDistributionJob.executeDistributionStrategy:0 */5 * * * ?}")
    public void executeDistributionStrategy() {
        log.info("开始执行固定周期策略分配周期切换任务");
        try {
            // 1. 查询所有固定周期分配的用户权益策略
            List<UserRightsDistributionStrategy> strategies = TenantHolder.methodIgnoreTenant(()->userRightsDistributionStrategyService.lambdaQuery()
                    .eq(UserRightsDistributionStrategy::getResourceType, "rights")
                    .eq(UserRightsDistributionStrategy::getDistributionTargetType, 2) // 用户
                    .eq(UserRightsDistributionStrategy::getTriggerType, 1) // 固定周期
                    .eq(UserRightsDistributionStrategy::getStatus, 1) // 启用状态
                    .le(UserRightsDistributionStrategy::getNextDistributionTime, LocalDateTime.now()) // 下一次分配时间已到
                    .list());
            
            log.info("查询到需要周期切换的策略数量: {}", strategies.size());
            
            // 2. 处理每一个需要周期切换的策略
            for (UserRightsDistributionStrategy strategy : strategies) {
                try {
                    TenantHolder.methodTenantVoid(strategy.getTenantId(),()-> processStrategyPeriodChange(strategy));
                } catch (Exception e) {
                    log.error("处理策略周期切换异常，strategyId: {}", strategy.getId(), e);
                }
            }
            
            log.info("固定周期策略分配周期切换任务执行完成");
        } catch (Exception e) {
            log.error("执行固定周期策略分配周期切换任务异常", e);
        }
    }
    
    /**
     * 处理单个策略的周期切换
     * @param strategy 权益分配策略
     */
    private void processStrategyPeriodChange(UserRightsDistributionStrategy strategy) {
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 增加分配次数
        strategy.setDistributedCount(strategy.getDistributedCount() + 1);
        
        // 2. 重置当前周期已分配账号数量
        strategy.setAllocatedAccountCount(0);
        
        // 3. 更新上次分配时间为当前时间
        strategy.setLastDistributionTime(now);
        
        // 4. 计算下次分配时间
        LocalDateTime nextDistributionTime = now.plusDays(strategy.getDistributionCycle());
        strategy.setNextDistributionTime(nextDistributionTime);
        
        // 5. 检查总分配次数限制（如果有）
        if (strategy.getTotalDistributionCount() != null && strategy.getDistributedCount() > strategy.getTotalDistributionCount()) {
            // 达到总分配次数上限，停用策略
            strategy.setStatus(0);
            log.info("策略达到总分配次数上限，停用策略，strategyId: {}, totalCount: {}, distributedCount: {}", 
                     strategy.getId(), strategy.getTotalDistributionCount(), strategy.getDistributedCount());
        }
        
        // 6. 更新策略
        userRightsDistributionStrategyService.updateById(strategy);
        
        log.info("策略周期切换完成，strategyId: {}, distributedCount: {}, nextDistributionTime: {}", 
                 strategy.getId(), strategy.getDistributedCount(), strategy.getNextDistributionTime());
    }
}
