package com.swhd.service.market.service.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.product.dto.param.strategy.ProductSkuDistributionStrategyPageParam;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuDistributionStrategySaveParam;
import com.swhd.service.market.api.product.dto.result.ProductSkuDistributionStrategyResult;
import com.swhd.service.market.service.product.entity.ProductSkuDistributionStrategy;

import java.util.Collection;
import java.util.List;

/**
 * 商品SKU分配策略表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public interface ProductSkuDistributionStrategyService extends IBaseHdService<ProductSkuDistributionStrategy> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<ProductSkuDistributionStrategy> page(ProductSkuDistributionStrategyPageParam param);

	/**
	 * 根据skuId查询分配策略列表
	 *
	 * @param skuId skuId
	 * @return 分配策略列表
	 */
	List<ProductSkuDistributionStrategyResult> listBySkuId(Long skuId);

	/**
	 * 根据skuId列表查询分配策略列表
	 *
	 * @param skuIds skuId列表
	 * @return 分配策略列表
	 */
	List<ProductSkuDistributionStrategyResult> listBySkuIds(Collection<Long> skuIds);

	/**
	 * 差异保存分配策略
	 *
	 * @param skuId SKU ID
	 * @param strategies 分配策略列表
	 */
	void diffSave(Long skuId, List<ProductSkuDistributionStrategySaveParam> strategies);


}
