package com.swhd.service.market.service.rights.service.impl.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员每月创点赠送策略实现类
 */
@Component
public class MemberMonthlyCreativePointsStrategyImpl extends AbstractMonthlyCreativePointsStrategy {

    @Autowired
    private  MemberMonthlyCreativePointsStrategy strategy;


    @Override
    protected MonthlyCreativePointsStrategy getStrategy() {
        return strategy;
    }

    @Override
    protected String getLockKey() {
        return "serviceMarket:rights:aiCreativePoint:memberMonthlyGiveaway:lock:";
    }
} 