package com.swhd.service.market.service.order.mq.message;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PaymentCenterOrderStatusMessage {

    @Schema(description = "订单id", name = "id")
    private Long id;

    @Schema(description =  "商户订单号，商户端生成，要求此订单号在整个商户下唯一", name = "orderId")
    private String orderId;

    @Schema(description =  "支付时间,未或者没成功支付默认1970-01-01 00:00:00", name = "payTime")
    private LocalDateTime payTime;

    @Schema(description =  "支付方式，ZF：支付宝，WX：微信，YL：银联", name = "payType")
    private String payType;

    @Schema(description =  "付款银行类型 ALIPAYACCOUNT 支付宝账户 PCREDIT 蚂蚁花呗 DEBIT_CARD 借记卡 CREDIT_CARD 信用卡 MIXED_CARD " +
            "借贷合一卡 等 CMB_CREDIT 微信支付", name = "payBank")
    private String payBank;

    @Schema(description =  "第三方支付订单号", name = "thirdOrderId")
    private String thirdOrderId;

    @Schema(description =  "招行生成的订单号", name = "cmbOrderId")
    private String cmbOrderId;

    @Schema(description =  "订单状态 0:待支付 1:支付成功，2：订单已经关闭，3：订单已经撤销，4：交易失败，5：转入退款，表示已支付成功并发起了退款申请,6:二维码已经过期", name =
            "status")
    private Integer status;
}
