package com.swhd.service.market.service.product.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuDistributionStrategySaveParam;
import com.swhd.service.market.api.product.dto.param.strategy.ProductSkuDistributionStrategyPageParam;
import com.swhd.service.market.api.product.dto.result.ProductSkuDistributionStrategyResult;
import com.swhd.service.market.service.product.entity.ProductSkuDistributionStrategy;
import com.swhd.service.market.service.product.mapper.ProductSkuDistributionStrategyMapper;
import com.swhd.service.market.service.product.service.ProductSkuDistributionStrategyService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品SKU分配策略表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Service
@AllArgsConstructor
public class ProductSkuDistributionStrategyServiceImpl extends BaseHdServiceImpl<ProductSkuDistributionStrategyMapper, ProductSkuDistributionStrategy> implements ProductSkuDistributionStrategyService {

    @Override
    public IPage<ProductSkuDistributionStrategy> page(ProductSkuDistributionStrategyPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getSkuId()), ProductSkuDistributionStrategy::getSkuId, param.getSkuId())
                .eq(Func.isNotEmpty(param.getResourceType()), ProductSkuDistributionStrategy::getResourceType, param.getResourceType())
                .eq(Func.isNotEmpty(param.getResourceCode()), ProductSkuDistributionStrategy::getResourceCode, param.getResourceCode())
                .eq(Func.isNotEmpty(param.getDistributionTargetType()), ProductSkuDistributionStrategy::getDistributionTargetType, param.getDistributionTargetType())
                .eq(Func.isNotEmpty(param.getTriggerType()), ProductSkuDistributionStrategy::getTriggerType, param.getTriggerType())
                .eq(Func.isNotEmpty(param.getCronExpression()), ProductSkuDistributionStrategy::getCronExpression, param.getCronExpression())
                .eq(Func.isNotEmpty(param.getDistributionCycle()), ProductSkuDistributionStrategy::getDistributionCycle, param.getDistributionCycle())
                .eq(Func.isNotEmpty(param.getDistributionQuantity()), ProductSkuDistributionStrategy::getDistributionQuantity, param.getDistributionQuantity())
                .eq(Func.isNotEmpty(param.getValidDays()), ProductSkuDistributionStrategy::getValidDays, param.getValidDays())
                .eq(Func.isNotEmpty(param.getTotalDistributionCount()), ProductSkuDistributionStrategy::getTotalDistributionCount, param.getTotalDistributionCount())
                .eq(Func.isNotEmpty(param.getStatus()), ProductSkuDistributionStrategy::getStatus, param.getStatus())
                .eq(Func.isNotEmpty(param.getDescription()), ProductSkuDistributionStrategy::getDescription, param.getDescription())
                .orderByDesc(ProductSkuDistributionStrategy::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<ProductSkuDistributionStrategyResult> listBySkuId(Long skuId) {
        if (Func.isEmpty(skuId)) {
            return Collections.emptyList();
        }
        return Func.copy(this.lambdaQuery().eq(ProductSkuDistributionStrategy::getSkuId, skuId).list(), ProductSkuDistributionStrategyResult.class);
    }

    @Override
    public List<ProductSkuDistributionStrategyResult> listBySkuIds(Collection<Long> skuIds) {
        if (Func.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        return Func.copy(this.lambdaQuery().in(ProductSkuDistributionStrategy::getSkuId, skuIds).list(), ProductSkuDistributionStrategyResult.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void diffSave(Long skuId, List<ProductSkuDistributionStrategySaveParam> strategies) {
        //null时不处理，删除需为空列表
        if (strategies == null) {
            return;
        }

        // 1. 获取现有策略
        List<ProductSkuDistributionStrategy> existingStrategies = this.lambdaQuery()
                .eq(ProductSkuDistributionStrategy::getSkuId, skuId)
                .list();

        // 2. 删除不在新列表中的策略
        if (Func.isNotEmpty(existingStrategies)) {
            List<Long> existingIds = existingStrategies.stream()
                    .map(ProductSkuDistributionStrategy::getId)
                    .toList();
            List<Long> newIds = strategies.stream()
                    .map(ProductSkuDistributionStrategySaveParam::getId)
                    .filter(Objects::nonNull)
                    .toList();
            List<Long> toDeleteIds = existingIds.stream()
                    .filter(id -> !newIds.contains(id))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(toDeleteIds)) {
                this.removeByIds(toDeleteIds);
            }
        }

        // 3. 保存新策略
        if (Func.isNotEmpty(strategies)) {
            List<ProductSkuDistributionStrategy> toSave = strategies.stream()
                    .map(strategy -> {
                        ProductSkuDistributionStrategy entity = Func.copy(strategy, ProductSkuDistributionStrategy.class);
                        entity.setSkuId(skuId);
                        return entity;
                    })
                    .collect(Collectors.toList());
            this.saveOrUpdateBatch(toSave);
        }
    }

}
