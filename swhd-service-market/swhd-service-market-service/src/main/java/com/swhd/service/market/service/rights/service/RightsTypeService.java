package com.swhd.service.market.service.rights.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.rights.dto.param.rights.RightsTypeListParam;
import com.swhd.service.market.api.rights.dto.param.type.RightsTypePageParam;
import com.swhd.service.market.service.rights.entity.RightsType;

import java.util.List;

/**
 * 权益类型表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface RightsTypeService extends IBaseHdService<RightsType> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<RightsType> page(RightsTypePageParam param);

	RightsType getByCode(String rightsTypeCode);

	/**
	 * 获取要求账户唯一的类型编码
	 * @return
	 */
	List<String> listUniqueCodes();

    List<RightsType> list(RightsTypeListParam param);
}
