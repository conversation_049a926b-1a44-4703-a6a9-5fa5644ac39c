package com.swhd.service.market.service.oceanengine.api.dto.resp.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/9 16:42
 */

@Data
public class OceanengineOrderInfo implements Serializable {

    /**
     * 订单id
     */
    @SerializedName("order_id")
    private Long orderId;

    /**
     * 订单状态，枚举值如下：
     * <p>
     * UNPAID // 待支付
     * <p>
     * CANCELED //取消
     * <p>
     * OVERTIME_CANCELED //超时取消
     * <p>
     * SERVING //服务中，应用类订单目前无该状态
     * <p>
     * REFUND_APPLY //申请退款
     * <p>
     * REFUND_REJECT //拒绝退款
     * <p>
     * REFUND_APPROVED //退款已审批
     * <p>
     * REFUND_SUCCESS //退款成功
     * <p>
     * DELIVERED //已发货
     * <p>
     * FINISHED //已完成
     * <p>
     * OVERTIME_FINISHED //超时完成
     * <p>
     * DELIVERY_REJECT //发货拒绝
     */
    @SerializedName("order_status")
    private String orderStatus;

    /**
     * 下单用户uid
     */
    @SerializedName("paid_user_id")
    private Long paidUserId;

    /**
     * 购买的商品规格ID
     */
    @SerializedName("sku_id")
    private String skuId;

    /**
     * 购买的商品规格描述
     */
    @SerializedName("sku_description")
    private String skuDescription;

    /**
     * 购买的商品规格类型
     * <p>
     * FREE //免费
     * <p>
     * TRY //试用
     * <p>
     * PAY //付费
     */
    @SerializedName("sku_type")
    private String skuType;

    /**
     * 是否是付费功能点类型的sku（千川应用暂不支持该类型）
     */
    @SerializedName("is_func")
    private Boolean isFunc;

    /**
     * 应付价格，单位分
     */
    @SerializedName("fee")
    private Integer fee;

    /**
     * 规格原价，单位分
     */
    @SerializedName("origin_price")
    private Long originPrice;

    /**
     * 购买的商品规格的授权账户数量
     */
    @SerializedName("app_limit_user_count")
    private Integer appLimitUserCount;

    /**
     * 可使用用户uid list，即下单人填写的应用可使用的帐号列表
     */
    @SerializedName("app_available_user_ids")
    private List<Long> appAvailableUserIds;

    /**
     * 购买的商品规格的服务周期，单位天
     */
    @SerializedName("app_active_days")
    private Long appActiveDays;

    /**
     * 下单时间（UNIX时间戳，单位ms）
     */
    @SerializedName("create_time")
    private Long createTime;

    /**
     * 付款时间（UNIX时间戳，单位ms）
     */
    @SerializedName("pay_time")
    private Long payTime;

    /**
     * 购买生效期的开始时间（UNIX时间戳，单位ms）
     */
    @SerializedName("begin_time")
    private Long beginTime;

    /**
     * 购买生效期的结束时间（UNIX时间戳，单位ms）
     */
    @SerializedName("end_time")
    private Long endTime;

}
