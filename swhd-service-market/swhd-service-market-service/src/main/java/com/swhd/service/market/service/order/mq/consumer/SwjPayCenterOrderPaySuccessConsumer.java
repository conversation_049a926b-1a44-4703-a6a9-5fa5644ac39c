package com.swhd.service.market.service.order.mq.consumer;

import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.service.market.api.order.constant.OrderStatus;
import com.swhd.service.market.api.order.constant.PayChannelEnum;
import com.swhd.service.market.service.common.properties.PayProperties;
import com.swhd.service.market.service.dingtalk.DingTalkService;
import com.swhd.service.market.service.order.entity.OrderInfo;
import com.swhd.service.market.service.order.mq.message.OrderPaySuccessMessage;
import com.swhd.service.market.service.order.service.OrderInfoService;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.enumeration.BinaryEnum;
import com.swj.magiccube.exception.MagiccubeException;
import com.swj.pay.center.api.order.constant.PayOrderPayStateEnum;
import com.swj.pay.center.api.order.dto.result.query.OrderPayTransactionResult;
import com.swj.pay.center.api.order.feign.IOrderPayQueryClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * 订单支付成功消费者
 *
 * <AUTHOR> <EMAIL>
 * @date 2024/10/17
 */
@Slf4j
@AllArgsConstructor
@Component
public class SwjPayCenterOrderPaySuccessConsumer {

    private final PayProperties payProperties;
    private final OrderInfoService orderInfoService;
    private final DingTalkService dingTalkService;

    private final IOrderPayQueryClient orderPayQueryClient;

    @Bean
    public Consumer<OrderPaySuccessMessage> swjPayCenterOrderPaySuccess() {
        return message -> {

            log.info("start consume swj-pay-center order-pay-success message: {}", message);

            if (message == null) {
                log.warn("订单支付结果消息为空");
                return;
            }
            if (!Objects.equals(message.getAppCode(), payProperties.getAppCode())) {
                log.info("非慧引流支付订单，忽略");
                return;
            }
            if (!Objects.equals(message.getPayState(), PayOrderPayStateEnum.SUCCESS.getCode())) {
                log.warn("支付状态不是成功，忽略");
                return;
            }


            String orderNo = message.getOrderNo();
            if (StringUtils.isBlank(orderNo)) {
                log.warn("订单编号为空. message:{}", message);
                return;
            }
            OrderInfo order = TenantHolder.methodIgnoreTenant(() -> orderInfoService.getByOrderNo(orderNo));
            if (order == null) {
                log.warn("订单不存在. orderNo:{}", orderNo);
                return;
            }

            // 处理订单成功
            TenantHolder.methodIgnoreTenantVoid(() -> this.paySuccess(order));

            log.info("finish consume swj-pay-center order-pay-success message: {}", JsonUtil.toJsonString(message));
        };
    }

    private void paySuccess(OrderInfo order) {

        if (!Objects.equals(order.getOrderStatus(), OrderStatus.UN_PAY.getCode())) {
            throw new MagiccubeException("订单状态orderState非待支付，不再处理");
        }

        OrderPayTransactionResult transaction = this.queryPayResult(order.getOrderNo());

        boolean update = orderInfoService.lambdaUpdate()
                .eq(OrderInfo::getOrderNo, order.getOrderNo())
                .set(OrderInfo::getPayChannel, PayChannelEnum.ALLIN_PAY_VSP.getCode())
                .set(OrderInfo::getPayState, BinaryEnum.YES.getCode())
                .set(OrderInfo::getOrderStatus, OrderStatus.UN_DELIVER.getCode())
                .set(OrderInfo::getActualPayPrice, transaction.getPayTotalPrice())
                .set(OrderInfo::getPayTime, transaction.getPaySuccessTime())
                .set(OrderInfo::getPayTransactionNo, transaction.getPayTransactionNo())
                // 支付订单直接审核通过
                .set(OrderInfo::getAuditState, BinaryEnum.YES.getCode())
                .update();
        log.info("订单支付成功，更新状态成功. orderNo:{}", order.getOrderNo());

        if (update) {
            // 发送钉钉通知
            dingTalkService.orderPaySuccessNotify(order, transaction.getPayTotalPrice());
            // 分配权益
            orderInfoService.assignUnDeliverOrderById(order.getId());
        }
    }

    private OrderPayTransactionResult queryPayResult(String orderNo) {
        Rsp<OrderPayTransactionResult> payResultRsp = orderPayQueryClient.getSuccessByOrderNo(payProperties.getAppCode(), orderNo);
        if (Rsp.failOrDataIsNull(payResultRsp)) {
            throw new MagiccubeException(String.format("订单%s查询不到成功的支付记录", orderNo));
        }
        return payResultRsp.getData();
    }

}
