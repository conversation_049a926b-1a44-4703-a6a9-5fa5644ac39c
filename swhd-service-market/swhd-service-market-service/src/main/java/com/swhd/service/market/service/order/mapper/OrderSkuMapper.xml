<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.service.market.service.order.mapper.OrderSkuMapper">

    <select id="countByShopId" resultType="java.lang.Integer">
        select count(distinct o.id)
        from tservicemarket_order_info o
        join tservicemarket_order_sku s on s.order_no = o.order_no
        where JSON_CONTAINS(shop_ids -> '$', JSON_QUOTE(CAST(#{shopId} AS CHAR)))
          and o.order_status in (1, 2, 3)
    </select>

    <select id="countByShopIdAndSkuCode" resultType="java.lang.Integer">
        select count(distinct o.id)
        from tservicemarket_order_info o
        join tservicemarket_order_sku s on s.order_no = o.order_no
        where JSON_CONTAINS(shop_ids -> '$', JSON_QUOTE(CAST(#{shopId} AS CHAR)))
          and s.sku_code = #{skuCode} and o.order_status in (1, 2, 3)
    </select>

    <select id="countUnPayByShopIdAndSkuCode" resultType="java.lang.Integer">
        select count(distinct o.id)
        from tservicemarket_order_info o
        join tservicemarket_order_sku s on s.order_no = o.order_no
        where JSON_CONTAINS(shop_ids -> '$', JSON_QUOTE(CAST(#{shopId} AS CHAR)))
          and s.sku_code = #{skuCode} and o.order_status = 0
    </select>



</mapper>