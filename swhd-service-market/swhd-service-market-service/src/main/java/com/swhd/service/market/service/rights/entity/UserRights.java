package com.swhd.service.market.service.rights.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 用户权益表实体类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tservicemarket_user_rights")
public class UserRights extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "总量")
    private BigDecimal capacity;

    @Schema(description = "剩余量")
    private BigDecimal residueCapacity;

    @Schema(description = "权益类型")
    private String rightsType;

    @Schema(description = "权益状态：0-初始化；1-正常；")
    private Integer rightsStatus;

    @Schema(description = "生效时间")
    private LocalDate effectiveTime;

    @Schema(description = "过期时间")
    private LocalDate expiryTime;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单类型：0-付费订单；1-赠送订单；2-试用")
    private Integer orderType;

    @Schema(description = "获得类型：RECHARGE-充值;SUBSCRIPTION-会员订阅;GIFT-赠送;TRIAL-试用")
    private String acquisitionType;

    @Schema(description = "来源：WORKSPACE-工作台;ORDER-订单;ACTIVITY-活动;")
    private String rightsSource;

    @Schema(description = "主体类型：PERSONAL-个人;TEAM-团队;SHOP-门店;TENANT-租户")
    private String subjectType;

    @Schema(description = "团队ID")
    private Long teamId;

}
