package com.swhd.service.market.service.rights.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.rights.dto.param.record.OrderShopLeadsAssignPageParam;
import com.swhd.service.market.api.rights.dto.param.record.UserRightsConsumeRecordListParam;
import com.swhd.service.market.api.rights.dto.param.record.UserRightsConsumeRecordPageParam;
import com.swhd.service.market.api.rights.dto.result.OrderShopLeadsAssignResult;
import com.swhd.service.market.service.rights.entity.UserRightsConsumeRecord;

import java.util.List;

/**
 * 用户权益消费记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface UserRightsConsumeRecordService extends IBaseHdService<UserRightsConsumeRecord> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<UserRightsConsumeRecord> page(UserRightsConsumeRecordPageParam param);

	List<UserRightsConsumeRecord> list(UserRightsConsumeRecordListParam param);

    IPage<OrderShopLeadsAssignResult> pageShopLeadsByOrder(OrderShopLeadsAssignPageParam param);

}
