package com.swhd.service.market.service.rights.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.rights.dto.param.rights.*;
import com.swhd.service.market.api.rights.dto.result.*;
import com.swhd.service.market.service.rights.entity.UserRights;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;

import java.util.List;

/**
 * 用户权益表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface UserRightsService extends IBaseHdService<UserRights> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<UserRights> page(UserRightsPageParam param);


    List<UserRights> list(UserRightsListParam param);

    void batchAddByOrder(UserRightsBatchAddByOrderParam param);

    /**
     * 根据权益类型编码和业务项目获取权益总量和剩余量
     * @param rightsTypeCodes 权益类型编码列表
     * @param acquisitionTypes 获取类型列表
     * @param onlyHasRights 是否只返回有权益的数据
     * @param useAuthorizationLimit 不存在权益时是否使用授信额度作为容量和剩余容量
     * @return 权益总量和剩余量列表
     */
    //todo delete
    List<UserTotalAndRemainingRightsResult> getTotalAndRemainingByTypeCodes(List<String> rightsTypeCodes, List<String> acquisitionTypes, Boolean onlyHasRights, Boolean useAuthorizationLimit,Long userId,Long teamId);

    //todo delete
    List<UserTotalAndRemainingRightsResult> getTotalAndRemainingRights(GetTotalAndRemainingRightsParam param);

    /**
     * 获取指定权益类型的总量和剩余量
     *
     * @param param 查询参数
     * @return 权益总量和剩余量结果
     */
    UserTotalAndRemainingRightsResult getTotalAndRemainingByType(GetTotalAndRemainingByTypeParam param);

    /**
     * 消费权益
     *
     * @param param 消费权益入参
     * @return 消费流水号
     */
    Rsp<String> consume(ConsumeRightsParam param);

    /**
     * 消费权益并返回过期时间
     *
     * @param param 消费权益入参
     * @return 包含消费流水号和过期时间的结果对象
     */
    Rsp<ConsumeWithExpiryResult> consumeWithExpiry(ConsumeRightsParam param);


    Rsp<String> preConsume(ConsumeRightsParam param);


    void rollbackConsume(RollbackConsumeParam param);

    /**
     * 确认消费
     */
    void confirmConsume(ConfirmConsumeParam param);

    /**
     * 批量预消费
     * @param param 批量预消费参数
     * @return 批量预消费结果
     */
    Rsp<BatchPreConsumeResult> batchPreConsume(BatchPreConsumeParam param);

    /**
     * 分页查询门店权益汇总
     * @param param
     * @return
     */
    PageResult<ShopRightsSummaryResult> pageShopRightsSummary(PageShopRightsSummaryParam param);

    /**
     * 唯一权益账户初始化
     * @param param
     * @return
     */
    UserRightsResult uniqueInit(UserRightsInitParam param);

    /**
     * 唯一权益充值
     * @param param
     */
    void uniqueAdd(UniqueRightChangeParam param);

    /**
     * 唯一权益消费
     * @param param
     */
    void uniqueConsume(UniqueRightChangeParam param);

    /**
     * 清理过期权益
     */
    void clearExpiredRights();

    /**
     * 直接新增权益
     * @param param 新增权益参数
     */
    void batchAdd(UserRightsBatchAddParam param);

    /**
     * 判断是否有足够的权益可以消费
     *
     * @param param 权益消费参数
     * @return 是否有足够的权益可以消费
     */
    boolean canConsumeRights(CanConsumeRightsParam param);

    /**
     * 判断是否可以购买创点
     *
     * @param tenantId 租户id
     * @param userId
     * @return 是否可以购买创点
     */
    boolean canPurchaseCP(Long tenantId, Long userId);

    /**
     * 获取权益列表，按类型分组汇总
     *
     * @param param 查询参数
     * @return 权益汇总列表
     */
    List<TenantRightsSummaryResult> listRightsSummary(ListRightsSummaryParam param);

}
