package com.swhd.service.market.service.oceanengine.api.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/9
 */
@Getter
@Setter
@Component
@ConfigurationProperties(OceanengineApiProperties.PREFIX)
public class OceanengineApiProperties {

    public static final String PREFIX = "service.market.oceanengine.api";

    /**
     * AI客服应用id
     */
    private Long aiKefuAppId;

    /**
     * 应用密钥
     */
    private Map<Long, String> appSecret = Collections.emptyMap();

}
