package com.swhd.service.market.service.product.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.service.market.api.product.dto.param.module.ProductSkuAuthModulePageParam;
import com.swhd.service.market.api.product.dto.result.ProductSkuAuthModuleResult;
import com.swhd.service.market.service.product.entity.ProductSkuAuthModule;
import com.swhd.service.market.service.product.mapper.ProductSkuAuthModuleMapper;
import com.swhd.service.market.service.product.service.ProductSkuAuthModuleService;
import com.swj.magiccube.tool.bean.BeanUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品SKU授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
@AllArgsConstructor
public class ProductSkuAuthModuleServiceImpl extends BaseHdServiceImpl<ProductSkuAuthModuleMapper, ProductSkuAuthModule> implements ProductSkuAuthModuleService {

    @Override
    public IPage<ProductSkuAuthModule> page(ProductSkuAuthModulePageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getSkuId()), ProductSkuAuthModule::getSkuId, param.getSkuId())
                .eq(Func.isNotEmpty(param.getModuleId()), ProductSkuAuthModule::getModuleId, param.getModuleId())
                .eq(Func.isNotEmpty(param.getValidDays()), ProductSkuAuthModule::getValidDays, param.getValidDays())
                .orderByDesc(ProductSkuAuthModule::getCreateTime)
                .orderByDesc(ProductSkuAuthModule::getId)
                .page(convertToPage(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void diffSave(Long skuId, Integer validDays, List<Long> moduleIds) {

        //为null时不处理，需要删除时传空列表
        if (moduleIds == null) {
            return;
        }

        List<ProductSkuAuthModule> oldList = listBySkuId(skuId);
        Map<Long, ProductSkuAuthModule> oldModuleIdMap = oldList.stream().collect(Collectors.toMap(ProductSkuAuthModule::getModuleId, Function.identity(), (o1, o2) -> o2));


        //新增数据
        List<ProductSkuAuthModule> saveList = moduleIds.stream().filter(moduleId -> !oldModuleIdMap.containsKey(moduleId))
                .map(moduleId -> {
                    ProductSkuAuthModule skuAuthModule = new ProductSkuAuthModule();
                    skuAuthModule.setModuleId(moduleId)
                            .setSkuId(skuId)
                            .setValidDays(validDays);
                    return skuAuthModule;
                }).toList();

        this.saveBatch(saveList);

        //修改的数据
        List<ProductSkuAuthModule> updateList = moduleIds.stream()
                .filter(moduleId -> oldModuleIdMap.containsKey(moduleId))
                .map(moduleId -> {
                            ProductSkuAuthModule skuAuthModule = new ProductSkuAuthModule();
                            skuAuthModule.setValidDays(validDays)
                                    .setId(oldModuleIdMap.get(moduleId).getId());
                            return skuAuthModule;
                        }
                ).toList();
        this.updateBatchById(updateList);

        //删除数据
        List<Long> updateIds = updateList.stream().map(ProductSkuAuthModule::getId).toList();
        List<Long> oldIds = oldList.stream().map(ProductSkuAuthModule::getId).toList();
        List<Long> removeIds = CollectionUtil.subtractToList(oldIds, updateIds);
        this.removeByIds(removeIds);

    }


    @Override
    public List<ProductSkuAuthModule> listBySkuId(Long skuId) {
        return lambdaQuery().eq(ProductSkuAuthModule::getSkuId, skuId).list();
    }

    @Override
    public List<ProductSkuAuthModuleResult> listBySkuIds(Collection<Long> skuIds) {
        List<ProductSkuAuthModule> list = lambdaQuery().in(ProductSkuAuthModule::getSkuId, skuIds).list();
        return BeanUtil.copy(list, ProductSkuAuthModuleResult.class);
    }

}
