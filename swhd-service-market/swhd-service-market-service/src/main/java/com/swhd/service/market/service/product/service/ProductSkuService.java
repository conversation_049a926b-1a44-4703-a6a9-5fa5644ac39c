package com.swhd.service.market.service.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuAddParam;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuListParam;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuPageParam;
import com.swhd.service.market.api.product.dto.param.sku.ProductSkuUpdateParam;
import com.swhd.service.market.api.product.dto.result.ProductSkuDetailResult;
import com.swhd.service.market.service.product.entity.ProductSku;

import java.util.Collection;
import java.util.List;

/**
 * 商品SKU表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
public interface ProductSkuService extends IBaseHdService<ProductSku> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<ProductSku> page(ProductSkuPageParam param);

	/**
	 * 新增（包括权益、权限）
	 * @param param 新增入参
	 */
	boolean add(ProductSkuAddParam param);

	/**
	 * 更新（包括权益、权限）
	 * @param param 更新入参
	 */
	boolean update(ProductSkuUpdateParam param);

	ProductSkuDetailResult getDetailById(Long id);

	ProductSkuDetailResult getDetailByCode(String skuCode);

	List<ProductSkuDetailResult> listDetailByIds(Collection<Long> skuIds);

	List<ProductSkuDetailResult> listDetailByCodes(Collection<String> skuCodes);

	/**
	 * 根据场景获取套餐详情
	 * @param sourceScene
	 * @return
	 */
	List<ProductSkuDetailResult> listDetailByScene(String sourceScene);

    List<ProductSku> listByCodes(Collection<String> codes);

	ProductSku getByCode(String skuCode);

	/**
	 * 根据条件查询套餐列表
	 *
	 * @param param 查询参数
	 * @return 套餐列表
	 */
	List<ProductSku> listSkus(ProductSkuListParam param);

}
