package com.swhd.service.market.service.rights.job;

import cn.hutool.core.collection.CollectionUtil;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.redis.utils.RedisLockUtil;
import com.swhd.service.market.api.rights.constant.RightsAcquisitionType;
import com.swhd.service.market.api.rights.constant.RightsSourceType;
import com.swhd.service.market.api.rights.dto.param.rights.UserRightsBatchAddParam;
import com.swhd.service.market.api.rights.dto.param.rights.UserRightsInfoParam;
import com.swhd.service.market.service.rights.entity.UserRightsDistributionStrategy;
import com.swhd.service.market.service.rights.service.UserRightsDistributionStrategyService;
import com.swhd.service.market.service.rights.service.UserRightsService;
import com.swj.magiccube.mp.tx.MagiccubeTransactionManager;
import com.xxl.job.core.ext.handler.annotation.SwjJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户权益分配策略定时任务
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@Slf4j
@Component
@AllArgsConstructor
public class UserRightsDistributionStrategyJob {

    private final UserRightsDistributionStrategyService userRightsDistributionStrategyService;
    private final UserRightsService userRightsService;

    /**
     * 每5分钟执行一次权益分配策略
     */
    @SwjJob(jobDesc = "固定周期权益分配任务（rights）", author = "zenghaoming",
            schedule = "${magiccube.job.schedule.UserRightsDistributionStrategyJob.executeDistributionStrategy:0 */1 * * * ?}")
    public void executeDistributionStrategy() {
        log.info("开始执行权益分配策略定时任务");
        try {
            // 1. 查询需要执行的策略ID列表
            List<UserRightsDistributionStrategy> strategys = TenantHolder.methodIgnoreTenant(() -> userRightsDistributionStrategyService.lambdaQuery()
                    .eq(UserRightsDistributionStrategy::getResourceType, "rights")
                    .eq(UserRightsDistributionStrategy::getTriggerType, 1)
                    .eq(UserRightsDistributionStrategy::getStatus, 1)
                    .eq(UserRightsDistributionStrategy::getDistributionTargetType,1)
                    .le(UserRightsDistributionStrategy::getNextDistributionTime, LocalDateTime.now())
                    .select(List.of(UserRightsDistributionStrategy::getId, UserRightsDistributionStrategy::getTenantId))
                    .list());

            if (CollectionUtil.isEmpty(strategys)) {
                log.info("没有需要执行的权益分配策略");
                return;
            }

            log.info("找到{}个需要执行的权益分配策略", strategys.size());

            // 2. 遍历处理每个策略
            for (UserRightsDistributionStrategy strategy : strategys) {
                TenantHolder.methodTenantVoid(strategy.getTenantId(), () -> executeStrategy(strategy.getId()));
            }

            log.info("权益分配策略定时任务执行完成");
        } catch (Exception e) {
            log.error("权益分配策略定时任务执行异常", e);
        }
    }


    private void executeStrategy(Long strategyId) {
        String key = "serviceMarket:distributionStrategy:" + strategyId;
        RedisLockUtil.lockVoid(() -> {
            TransactionStatus tcStatus = null;
            try {
                UserRightsDistributionStrategy strategy = userRightsDistributionStrategyService.getById(strategyId);

                // 获取锁后再次校验策略是否满足执行条件
                if (strategy == null ||
                        !"rights".equals(strategy.getResourceType()) ||
                        strategy.getTriggerType() != 1 ||
                        strategy.getStatus() != 1 ||
                        strategy.getNextDistributionTime() == null ||
                        strategy.getNextDistributionTime().isAfter(LocalDateTime.now())) {
                    log.warn("策略不满足执行条件, strategyId:{}", strategyId);
                    return;
                }

                // 1. 检查是否达到总分配次数限制
                // totalDistributionCount为0表示不限制，其他情况按照实际次数限制
                if (strategy.getTotalDistributionCount() > 0 &&
                        strategy.getDistributedCount() >= strategy.getTotalDistributionCount()) {
                    log.info("策略已达到总分配次数限制, strategyId:{}, totalCount:{}, distributedCount:{}",
                            strategy.getId(), strategy.getTotalDistributionCount(), strategy.getDistributedCount());
                    // 更新策略状态为禁用，并将下一次执行时间设置为null
                    boolean success = userRightsDistributionStrategyService.lambdaUpdate()
                            .eq(UserRightsDistributionStrategy::getId, strategy.getId())
                            .set(UserRightsDistributionStrategy::getStatus, 0)
                            .set(UserRightsDistributionStrategy::getNextDistributionTime, null)
                            .update();
                    if (success) {
                        log.info("策略已禁用, strategyId:{}", strategy.getId());
                    } else {
                        log.error("更新策略状态失败, strategyId:{}", strategy.getId());
                    }
                    return;
                }

                // 2. 分配权益
                UserRightsInfoParam rightsParam = new UserRightsInfoParam();
                rightsParam.setRightsType(strategy.getResourceCode())
                        .setCapacity(strategy.getDistributionQuantity())
                        .setDescription(strategy.getDescription())
                        .setEffectiveTime(LocalDateTime.now().toLocalDate())
                        .setExpiryTime(rightsParam.getEffectiveTime().plusDays(strategy.getValidDays() - 1));

                UserRightsBatchAddParam batchAddParam = new UserRightsBatchAddParam();
                List<UserRightsInfoParam> rightsInfos = new ArrayList<>();
                rightsInfos.add(rightsParam);
                batchAddParam.setRightsInfos(rightsInfos)
                        .setRightsSourceId(strategyId.toString())
                        .setTenantId(strategy.getTenantId())
                        .setAcquisitionType(RightsAcquisitionType.SUBSCRIPTION.getCode())
                        .setRightsSource(RightsSourceType.ORDER.getCode());

                //开启事务
                tcStatus = MagiccubeTransactionManager.open();
                userRightsService.batchAdd(batchAddParam);

                // 3. 更新策略信息（增加分配次数、更新分配时间）
                LocalDateTime now = LocalDateTime.now();
                // 计算下一次分配时间，设置为当天的00:00:00
                LocalDateTime nextTime = now.plusDays(strategy.getDistributionCycle()).with(LocalTime.MIN);
                boolean success = userRightsDistributionStrategyService.lambdaUpdate()
                        .eq(UserRightsDistributionStrategy::getId, strategy.getId())
                        .setSql("distributed_count = distributed_count + 1")
                        .set(UserRightsDistributionStrategy::getLastDistributionTime, now)
                        .set(UserRightsDistributionStrategy::getNextDistributionTime, nextTime)
                        .update();

                if (success) {
                    log.info("权益分配策略执行成功, strategyId:{}, rightsType:{}, quantity:{}, nextTime:{}",
                            strategy.getId(), strategy.getResourceCode(), strategy.getDistributionQuantity(), nextTime);
                } else {
                    log.error("更新策略信息失败, strategyId:{}", strategy.getId());
                }
                MagiccubeTransactionManager.commit(tcStatus);

            } catch (Exception e) {
                if (Objects.nonNull(tcStatus)) {
                    MagiccubeTransactionManager.rollback(tcStatus);
                }
                log.error("执行权益分配策略失败, strategyId:{}", strategyId, e);
            }
        }, key);
    }
}
