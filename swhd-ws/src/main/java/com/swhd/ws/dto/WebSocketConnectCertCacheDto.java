package com.swhd.ws.dto;

import com.swhd.magiccube.tool.jwt.JwtBasePayload;
import com.swhd.ws.constant.BusinessCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/9/25
 */
@Getter
@Setter
@NoArgsConstructor
public class WebSocketConnectCertCacheDto extends JwtBasePayload {

    private BusinessCode businessCode;

    private Long tenantId;

    private String userId;

    public WebSocketConnectCertCacheDto(LocalDateTime exp) {
        super(exp);
    }

}
