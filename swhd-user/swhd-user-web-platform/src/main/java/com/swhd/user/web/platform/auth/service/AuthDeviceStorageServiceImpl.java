package com.swhd.user.web.platform.auth.service;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.common.dto.result.UserLoginDeviceResult;
import com.swhd.user.api.platform.client.PlatformUserLoginInfoClient;
import com.swhd.user.api.platform.client.PlatformUserLoginRecordClient;
import com.swhd.user.api.platform.dto.param.login.info.PlatformUserLoginInfoSaveParam;
import com.swhd.user.api.platform.dto.param.login.record.PlatformUserLoginRecordAddParam;
import com.swhd.user.api.platform.dto.result.PlatformUserLoginInfoResult;
import com.swhd.user.auth.core.dto.LoginUserDto;
import com.swhd.user.auth.core.service.AuthDeviceStorageService;
import com.swhd.user.auth.login.core.utils.DeviceUtil;
import com.swhd.user.auth.login.core.utils.IpAddressUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.WebUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/28
 */
@Service
public class AuthDeviceStorageServiceImpl implements AuthDeviceStorageService {

    @Lazy
    @Autowired
    private PlatformUserLoginInfoClient platformUserLoginInfoClient;

    @Lazy
    @Autowired
    private PlatformUserLoginRecordClient platformUserLoginRecordClient;

    @Autowired
    @Qualifier(TaskExecutionAutoConfiguration.APPLICATION_TASK_EXECUTOR_BEAN_NAME)
    private AsyncTaskExecutor asyncTaskExecutor;

    @Override
    public UserLoginDeviceResult getByDeviceId(Long userId, String deviceId) {
        Rsp<PlatformUserLoginInfoResult> rsp = platformUserLoginInfoClient.getByUserId(userId);
        RspHd.failThrowException(rsp);
        if (rsp.getData() == null || Func.isEmpty(rsp.getData().getDeviceList())) {
            return null;
        }
        return rsp.getData().getDeviceList().stream()
                .filter(t -> t.getId().equals(deviceId))
                .findFirst()
                .orElse(null);
    }

    @Override
    public void addLoginRecord(LoginUserDto loginUser, String deviceId) {
        Long userId = loginUser.getUserId();
        String tokenId = loginUser.getTokenId();
        String ip = WebUtil.getIp();
        String deviceName = DeviceUtil.getDeviceName();
        LocalDateTime createTime = LocalDateTime.now();
        asyncTaskExecutor.execute(() -> {
            String ipBelong = IpAddressUtil.getIpBelong(ip);
            UserLoginDeviceResult deviceInfo = new UserLoginDeviceResult();
            deviceInfo.setId(deviceId);
            deviceInfo.setName(deviceName);
            deviceInfo.setTokenId(tokenId);
            deviceInfo.setIp(ip);
            deviceInfo.setIpBelong(ipBelong);
            deviceInfo.setCreateTime(createTime);
            PlatformUserLoginRecordAddParam loginRecordAddParam = new PlatformUserLoginRecordAddParam()
                    .setUserId(userId)
                    .setIp(ip)
                    .setIpBelong(ipBelong)
                    .setDeviceInfo(deviceInfo);
            platformUserLoginRecordClient.add(loginRecordAddParam);
        });
    }

    @Override
    public void updateLoginDevice(LoginUserDto loginUser, String deviceId) {
        Long userId = loginUser.getUserId();
        String tokenId = loginUser.getTokenId();
        String ip = WebUtil.getIp();
        String deviceName = DeviceUtil.getDeviceName();
        LocalDateTime createTime = LocalDateTime.now();
        asyncTaskExecutor.execute(() -> {
            String ipBelong = IpAddressUtil.getIpBelong(ip);
            UserLoginDeviceResult deviceInfo = new UserLoginDeviceResult();
            deviceInfo.setId(deviceId);
            deviceInfo.setName(deviceName);
            deviceInfo.setTokenId(tokenId);
            deviceInfo.setIp(ip);
            deviceInfo.setIpBelong(ipBelong);
            deviceInfo.setCreateTime(createTime);
            PlatformUserLoginInfoSaveParam loginInfoSaveParam = new PlatformUserLoginInfoSaveParam()
                    .setUserId(userId)
                    .setAddDeviceInfo(deviceInfo);
            platformUserLoginInfoClient.save(loginInfoSaveParam);
        });
    }

    @Override
    public void deleteByDeviceId(Long userId, String deviceId) {
        PlatformUserLoginInfoSaveParam loginInfoSaveParam = new PlatformUserLoginInfoSaveParam();
        loginInfoSaveParam.setUserId(userId);
        loginInfoSaveParam.setDeleteDeviceId(deviceId);
        platformUserLoginInfoClient.save(loginInfoSaveParam);
    }

}
