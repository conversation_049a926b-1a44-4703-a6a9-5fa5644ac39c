package com.swhd.user.web.platform.tenant.service;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.client.TenantTeamInfoClient;
import com.swhd.user.api.tenant.client.TenantTeamMemberClient;
import com.swhd.user.api.tenant.dto.result.TenantTeamInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantTeamMemberResult;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
@Service
@AllArgsConstructor
public class TenantTeamService {

    private final TenantTeamInfoClient tenantTeamInfoClient;

    private final TenantTeamMemberClient tenantTeamMemberClient;

    /**
     * 根据userTenantId获取团队
     *
     * @param userTenantId 用户租户ID
     * @return List
     */
    public List<TenantTeamInfoResult> listByUserTenantId(Long userTenantId) {
        if (userTenantId == null) {
            return Collections.emptyList();
        }
        Rsp<List<TenantTeamMemberResult>> userTeamRsp = tenantTeamMemberClient.listByUserTenantId(userTenantId);
        RspHd.failThrowException(userTeamRsp);
        if (Func.isEmpty(userTeamRsp.getData())) {
            return Collections.emptyList();
        }
        List<Long> teamIds = userTeamRsp.getData().stream().map(TenantTeamMemberResult::getTeamId).distinct().toList();
        Rsp<List<TenantTeamInfoResult>> teamListRsp = tenantTeamInfoClient.listByIds(teamIds);
        RspHd.failThrowException(teamListRsp);
        return teamListRsp.getData();
    }

    /**
     * 根据userTenantId列表获取团队
     *
     * @param userTenantIds 用户租户ID列表
     * @return Map<userTenantId, List < TenantTeamInfoResult>>
     */
    public Map<Long, List<TenantTeamInfoResult>> mapByUserTenantIds(List<Long> userTenantIds) {
        if (Func.isEmpty(userTenantIds)) {
            return Collections.emptyMap();
        }
        Rsp<List<TenantTeamMemberResult>> userTeamRsp = tenantTeamMemberClient.listByUserTenantIds(userTenantIds);
        RspHd.failThrowException(userTeamRsp);
        if (Func.isEmpty(userTeamRsp.getData())) {
            return Collections.emptyMap();
        }

        List<Long> teamIds = userTeamRsp.getData().stream().map(TenantTeamMemberResult::getTeamId).distinct().toList();
        Rsp<List<TenantTeamInfoResult>> roleListRsp = tenantTeamInfoClient.listByIds(teamIds);
        RspHd.failThrowException(roleListRsp);
        if (Func.isEmpty(roleListRsp.getData())) {
            return Collections.emptyMap();
        }
        // Map<teamId, TenantRoleInfoResult>
        Map<Long, TenantTeamInfoResult> teamMap = roleListRsp.getData().stream()
                .collect(Collectors.toMap(TenantTeamInfoResult::getId, r -> r));

        // userTeamList分组，同时把TenantTeamMemberResult转为TenantTeamInfoResult
        return userTeamRsp.getData().stream()
                .filter(ut -> teamMap.containsKey(ut.getTeamId()))
                .collect(Collectors.groupingBy(TenantTeamMemberResult::getUserTenantId,
                        Collectors.mapping(ut -> teamMap.get(ut.getTeamId()), Collectors.toList())));
    }

}
