package com.swhd.user.web.platform;

import com.swj.magiccube.MagiccubeApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @since 2023/11/14
 */
@SpringBootApplication
@EnableFeignClients("com.swhd")
public class UserWebPlatformApplication {

    public static void main(String[] args) {
        MagiccubeApplication.run(UserWebPlatformApplication.class, args);
    }

}
