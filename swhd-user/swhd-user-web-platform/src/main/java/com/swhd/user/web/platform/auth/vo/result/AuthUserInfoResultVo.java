package com.swhd.user.web.platform.auth.vo.result;

import com.swhd.magiccube.core.annotation.JsonMask;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Getter
@Setter
@Accessors(chain = true)
public class AuthUserInfoResultVo {

    @Schema(description = "用户id")
    private Long userId;

    @JsonMask(type = JsonMaskType.EMAIL)
    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

}
