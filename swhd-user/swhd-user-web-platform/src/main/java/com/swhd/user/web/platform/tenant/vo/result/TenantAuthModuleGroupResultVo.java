package com.swhd.user.web.platform.tenant.vo.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantModuleGroupResult对象")
public class TenantAuthModuleGroupResultVo {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "模块组名称")
    private String groupName;

    @Schema(description = "模块ID列表")
    private List<Long> moduleIds;

    @Schema(description = "模块名称列表")
    private List<String> moduleNames;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
