package com.swhd.user.service.tenant.web;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.client.TenantMembershipInfoClient;
import com.swhd.user.api.tenant.dto.param.membership.TenantMembershipInfoAddParam;
import com.swhd.user.api.tenant.dto.param.membership.TenantMembershipInfoPageParam;
import com.swhd.user.api.tenant.dto.param.membership.TenantMembershipInfoUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantMembershipInfoResult;
import com.swhd.user.service.tenant.entity.TenantMembershipInfo;
import com.swhd.user.service.tenant.service.TenantMembershipInfoService;
import com.swhd.user.service.tenant.util.TenantMembershipLevelTypeUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2025-03-29
 */
@RestController
@AllArgsConstructor
@RequestMapping(TenantMembershipInfoClient.BASE_PATH)
public class TenantMembershipInfoController implements TenantMembershipInfoClient {

    private final TenantMembershipInfoService tenantMembershipInfoService;
    private final TenantMembershipLevelTypeUtil membershipLevelTypeUtil;

    @Override
    public Rsp<PageResult<TenantMembershipInfoResult>> page(TenantMembershipInfoPageParam param) {
        IPage<TenantMembershipInfo> iPage = tenantMembershipInfoService.page(param);
        PageResult<TenantMembershipInfoResult> result = PageUtil.convertFromMyBatis(iPage, TenantMembershipInfoResult.class);
        result.getRecords().forEach(item -> {
            if (StrUtil.isNotEmpty(item.getMembershipCode())) {
                var levelType = membershipLevelTypeUtil.of(item.getMembershipCode());
                if (levelType != null) {
                    item.setName(levelType.getName());
                    item.setLevel(levelType.getLevel());
                }
            }
        });
        return RspHd.data(result);
    }

    @Override
    public Rsp<TenantMembershipInfoResult> getById(Long id) {
        TenantMembershipInfo entity = tenantMembershipInfoService.getById(id);
        return RspHd.data(convertToResult(entity));
    }

    @Override
    public Rsp<Void> add(TenantMembershipInfoAddParam param) {
        boolean result = tenantMembershipInfoService.save(Func.copy(param, TenantMembershipInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(TenantMembershipInfoUpdateParam param) {
        boolean result = tenantMembershipInfoService.updateById(Func.copy(param, TenantMembershipInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = tenantMembershipInfoService.removeByIds(ids);
        return RspHd.status(result);
    }

    @Override
    public Rsp<TenantMembershipInfoResult> getCurrentMembership() {
        TenantMembershipInfo currentMembership = tenantMembershipInfoService.getCurrentMembership();

        return RspHd.data(convertToResult(currentMembership));
    }

    /**
     * 转换为结果对象
     *
     * @param entity 实体对象
     * @return 结果对象
     */
    private TenantMembershipInfoResult convertToResult(TenantMembershipInfo entity) {
        if (entity == null) {
            return null;
        }
        TenantMembershipInfoResult result = Func.copy(entity, TenantMembershipInfoResult.class);
        // 设置会员等级信息
        if (StrUtil.isNotEmpty(entity.getMembershipCode())) {
            var levelType = membershipLevelTypeUtil.of(entity.getMembershipCode());
            if (levelType != null) {
                result.setName(levelType.getName());
                result.setLevel(levelType.getLevel());
            }
        }
        return result;
    }
}
