package com.swhd.user.service.tenant.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.dto.param.role.TenantRoleInfoPageParam;
import com.swhd.user.service.tenant.entity.TenantRoleInfo;
import com.swhd.user.service.tenant.mapper.TenantRoleInfoMapper;
import com.swhd.user.service.tenant.service.TenantRoleInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户后台角色信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Service
@AllArgsConstructor
public class TenantRoleInfoServiceImpl extends BaseHdServiceImpl<TenantRoleInfoMapper, TenantRoleInfo>
        implements TenantRoleInfoService {

    @Override
    public IPage<TenantRoleInfo> page(TenantRoleInfoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getRoleType()), TenantRoleInfo::getRoleType, param.getRoleType())
                .like(Func.isNotEmpty(param.getTitle()), TenantRoleInfo::getTitle, param.getTitle())
                .eq(Func.isNotEmpty(param.getState()), TenantRoleInfo::getState, param.getState())
                .eq(Func.isNotEmpty(param.getAdmin()), TenantRoleInfo::getAdmin, param.getAdmin())
                .orderByDesc(TenantRoleInfo::getCreateTime)
                .orderByDesc(TenantRoleInfo::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<TenantRoleInfo> list(TenantRoleInfoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getRoleType()), TenantRoleInfo::getRoleType, param.getRoleType())
                .like(Func.isNotEmpty(param.getTitle()), TenantRoleInfo::getTitle, param.getTitle())
                .eq(Func.isNotEmpty(param.getState()), TenantRoleInfo::getState, param.getState())
                .eq(Func.isNotEmpty(param.getAdmin()), TenantRoleInfo::getAdmin, param.getAdmin())
                .orderByDesc(TenantRoleInfo::getCreateTime)
                .orderByDesc(TenantRoleInfo::getId)
                .list();
    }

}
