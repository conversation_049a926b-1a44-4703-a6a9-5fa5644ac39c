package com.swhd.user.service.platform.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.typehandler.OssTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 平台后台用户信息表实体类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tuser_platform_user_info")
public class PlatformUserInfo extends BaseHdEntity {

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "昵称")
    private String nickname;

    @TableField(typeHandler = OssTypeHandler.class)
    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

}
