package com.swhd.user.service.tenant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.api.tenant.dto.param.team.info.TenantTeamInfoAddParam;
import com.swhd.user.api.tenant.dto.param.team.info.TenantTeamInfoPageParam;
import com.swhd.user.api.tenant.dto.param.team.info.TenantTeamInfoUpdateParam;
import com.swhd.user.service.tenant.entity.TenantTeamInfo;
import com.swj.magiccube.api.Rsp;

import java.util.List;

/**
 * 租户团队信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface TenantTeamInfoService extends IBaseHdService<TenantTeamInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<TenantTeamInfo> page(TenantTeamInfoPageParam param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return List
     */
    List<TenantTeamInfo> list(TenantTeamInfoPageParam param);

    /**
     * 根据tenantTeamId查询
     *
     * @param tenantTeamId 租户团队id
     * @return TenantTeamInfo
     */
    TenantTeamInfo getByTenantTeamId(Long tenantTeamId);

    /**
     * 新增
     *
     * @param param 参数
     * @return TenantTeamInfo
     */
    TenantTeamInfo add(TenantTeamInfoAddParam param);

    /**
     * 新增
     *
     * @param param        参数
     * @param tenantTeamId 租户团队id（null自动生成）
     * @return TenantTeamInfo
     */
    TenantTeamInfo add(TenantTeamInfoAddParam param, Long tenantTeamId);

    /**
     * 修改
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> update(TenantTeamInfoUpdateParam param);

}
