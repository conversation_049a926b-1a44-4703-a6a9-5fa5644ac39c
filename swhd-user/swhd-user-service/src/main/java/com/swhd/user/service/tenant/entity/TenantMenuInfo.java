package com.swhd.user.service.tenant.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.magiccube.mybatis.base.tree.BaseHdTreeEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import com.swhd.user.api.common.constant.MenuTenantType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 租户菜单信息表实体类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tuser_tenant_menu_info", autoResultMap = true)
public class TenantMenuInfo extends BaseHdTreeEntity {

    @TableField(typeHandler = JsonTypeHandler.class)
    @Schema(description = "租户类型")
    private List<MenuTenantType> tenantTypes;

    @Schema(description = "菜单标题")
    private String title;

    @Schema(description = "菜单类型：0-菜单，1-权限，2-隐藏")
    private Integer menuType;

    @Schema(description = "菜单code")
    private String menuCode;

    @Schema(description = "点击类型：0-当前窗口打开，1-新窗口打开")
    private Integer clickType;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "路由路径")
    private String routePath;

    @Schema(description = "按钮/权限code")
    private String buttonCode;

    @Schema(description = "权限url，多个逗号隔开")
    private String permissionUrl;

    @TableField(typeHandler = JsonTypeHandler.class)
    @Schema(description = "拓展属性")
    private JsonNode expandAttrs;

    @Override
    @JsonIgnore
    public String getName() {
        return this.title;
    }

}
