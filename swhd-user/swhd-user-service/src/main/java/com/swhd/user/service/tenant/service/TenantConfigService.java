package com.swhd.user.service.tenant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.api.tenant.dto.param.config.TenantConfigPageParam;
import com.swhd.user.service.tenant.entity.TenantConfig;

/**
 * 租户配置表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-05
 */
public interface TenantConfigService extends IBaseHdService<TenantConfig> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<TenantConfig> page(TenantConfigPageParam param);

	/**
	 * 获取租户配置
	 *
	 * @return 租户配置
	 */
	TenantConfig get();
	

	/**
	 * 保存或更新
	 *
	 * @param tenantConfig 租户配置
	 * @return 是否成功
	 */
	boolean saveOrUpdate(TenantConfig tenantConfig);

}
