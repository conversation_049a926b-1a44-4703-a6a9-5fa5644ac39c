package com.swhd.user.service.tenant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.api.tenant.dto.param.tenant.TenantInfoAddParam;
import com.swhd.user.api.tenant.dto.param.tenant.TenantInfoPageParam;
import com.swhd.user.api.tenant.dto.param.tenant.TenantInfoUpdateParam;
import com.swhd.user.service.tenant.entity.TenantInfo;
import com.swj.magiccube.api.Rsp;

import java.util.List;
import java.util.Optional;

/**
 * 租户信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public interface TenantInfoService extends IBaseHdService<TenantInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<TenantInfo> page(TenantInfoPageParam param);

    /**
     * 根据parentId获取
     *
     * @param parentId 父租户id
     * @return List
     */
    List<TenantInfo> listByParentId(Long parentId);

    /**
     * 获取数据里最大的租户id
     *
     * @return Optional<租户id>
     */
    Optional<Long> maxTenantId();

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Long add(TenantInfoAddParam param);

    /**
     * 修改
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> update(TenantInfoUpdateParam param);

}
