<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.user.service.platform.mapper.PlatformUserInfoMapper">

    <select id="page" resultType="com.swhd.user.service.platform.entity.PlatformUserInfo">
        select ui.id,ui.email,ui.password,ui.nickname,ui.avatar,ui.state,
        ui.creator_id,ui.create_time,ui.modifier_id,ui.modify_time,ui.is_delete
        from tuser_platform_user_info ui
        <if test="param.roleId != null">
            join tuser_platform_user_role ur on ur.user_id = ui.id and ur.is_delete = 0 and ur.role_id = #{param.roleId}
        </if>
        where ui.is_delete = 0
        <if test="param.email != null and param.email != ''">
            and ui.email like concat('%', #{param.email}, '%')
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and ui.nickname like concat('%', #{param.nickname}, '%')
        </if>
        <if test="param.state != null">
            and ui.state = #{param.state}
        </if>
        order by ui.create_time desc, ui.id desc
    </select>

</mapper>
