<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.user.service.tenant.mapper.TenantUserTenantMapper">

    <select id="page" resultType="com.swhd.user.api.tenant.dto.result.TenantUserTenantResult">
        select ut.*,
               case when ut.state = 0 or ut.state = 1 then ub.email else null end as email,
               case when ut.state = 0 or ut.state = 1 then ub.nickname else null end as nickname,
               case when ut.state = 0 or ut.state = 1 then ub.avatar else null end as avatar,
               case when ut.state = 2 or ut.state = 3 then ui.mobile else ub.mobile end as mobile
        from tuser_tenant_user_tenant ut
            left join tuser_tenant_user_base ub on ub.id = ut.user_id and ub.is_delete = 0
            left join tuser_tenant_user_invitation ui on ui.id = ut.invitation_id and ui.is_delete = 0
        <if test="param.joinRole()">
            join tuser_tenant_user_role ur on ur.user_tenant_id = ut.id and ur.is_delete = 0 and ur.role_id = #{param.roleId}
        </if>
        <if test="param.joinTeam()">
            join tuser_tenant_team_member tm on tm.user_tenant_id = ut.id and tm.is_delete = 0 and tm.team_id = #{param.teamId}
        </if>
        <if test="param.joinSwjUser()">
            join tuser_tenant_user_oauth uo on uo.user_id = ut.user_id and uo.is_delete = 0 and uo.oauth_type = 2
            <if test="param.swjUserId != null and param.swjUserId != ''">
                and uo.oauth_account = #{param.swjUserId}
            </if>
            <if test="param.swjUserIds != null and param.swjUserIds.size() > 0">
                and uo.oauth_account in
                <foreach collection="param.swjUserIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="param.parentTenantId != null">
            join tuser_tenant_info ti on ti.id = ut.tenant_id and ti.is_delete = 0 and ti.parent_id = #{param.parentTenantId}
        </if>
        where ut.is_delete = 0
        <if test="param.ids != null and param.ids.size() > 0">
            and ut.id in
            <foreach collection="param.ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.email != null and param.email != ''">
            and ub.email = #{param.email}
        </if>
        <if test="param.nickname != null and param.nickname != ''">
            and ub.nickname like concat('%', #{param.nickname}, '%')
        </if>
        <if test="param.state != null">
            and ut.state = #{param.state}
        </if>
        <if test="param.stateList != null and param.stateList.size() > 0">
            and ut.state in
            <foreach collection="param.stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.name != null and param.name != ''">
            and ut.name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.remark != null and param.remark != ''">
            and ut.remark like concat('%', #{param.remark}, '%')
        </if>
        <if test="param.sourceScene != null and param.sourceScene != ''">
            and ut.source_scene = #{param.sourceScene}
        </if>
        order by ut.create_time desc, ut.id desc
    </select>

    <select id="listByEmail" resultType="com.swhd.user.api.tenant.dto.result.TenantUserTenantResult">
        select ut.*, ub.email, ub.mobile, ub.nickname, ub.avatar
        from tuser_tenant_user_tenant ut join tuser_tenant_user_base ub on ub.id = ut.user_id and ub.is_delete = 0
        where ut.is_delete = 0 and ub.email = #{email}
    </select>

    <select id="listByMobile" resultType="com.swhd.user.api.tenant.dto.result.TenantUserTenantResult">
        select ut.*, ub.email, ub.mobile, ub.nickname, ub.avatar
        from tuser_tenant_user_tenant ut join tuser_tenant_user_base ub on ub.id = ut.user_id and ub.is_delete = 0
        where ut.is_delete = 0 and ub.mobile = #{mobile}
    </select>

</mapper>
