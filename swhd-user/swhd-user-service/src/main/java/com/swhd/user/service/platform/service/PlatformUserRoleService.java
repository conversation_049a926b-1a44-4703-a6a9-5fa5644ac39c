package com.swhd.user.service.platform.service;

import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.service.platform.entity.PlatformUserRole;

import java.util.List;

/**
 * 平台后台用户角色关联表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
public interface PlatformUserRoleService extends IBaseHdService<PlatformUserRole> {

	/**
	 * 根据用户id获取
	 *
	 * @param userId 用户id
	 * @return List<PlatformUserRole>
	 */
	List<PlatformUserRole> listByUserId(Long userId);

	/**
	 * 根据用户id列表获取
	 *
	 * @param userIds 用户id列表
	 * @return List<PlatformUserRole>
	 */
	List<PlatformUserRole> listByUserIds(List<Long> userIds);

	/**
	 * 根据角色id获取
	 *
	 * @param roleId 角色id
	 * @return List<PlatformUserRole>
	 */
	List<PlatformUserRole> listByRoleId(Long roleId);

	/**
	 * 批量保存，数据库与menuIds数据进行差异处理，删除menuIds里不存在的数据
	 *
	 * @param userId  用户ID
	 * @param roleIds 角色ID列表
	 */
	void saveBatch(Long userId, List<Long> roleIds);

	/**
	 * 删除角色id关联的数据
	 *
	 * @param roleId 角色id
	 */
	void removeByRoleId(Long roleId);

}
