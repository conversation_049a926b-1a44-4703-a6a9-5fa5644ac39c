package com.swhd.user.service.tenant.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.dto.param.invitation.notice.tenant.TenantUserInvitationNoticeTenantPageParam;
import com.swhd.user.service.tenant.entity.TenantUserInvitationNoticeTenant;
import com.swhd.user.service.tenant.mapper.TenantUserInvitationNoticeTenantMapper;
import com.swhd.user.service.tenant.service.TenantUserInvitationNoticeTenantService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 巨量引擎的客户表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
@Service
@AllArgsConstructor
public class TenantUserInvitationNoticeTenantServiceImpl
        extends BaseHdServiceImpl<TenantUserInvitationNoticeTenantMapper, TenantUserInvitationNoticeTenant>
        implements TenantUserInvitationNoticeTenantService {

    @Override
    public IPage<TenantUserInvitationNoticeTenant> page(TenantUserInvitationNoticeTenantPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getValidDate()), TenantUserInvitationNoticeTenant::getValidDate, param.getValidDate())
                .betweenList(param.getValidDateBetween(), TenantUserInvitationNoticeTenant::getValidDate)
                .orderByDesc(TenantUserInvitationNoticeTenant::getCreateTime)
                .orderByDesc(TenantUserInvitationNoticeTenant::getId)
                .page(convertToPage(param));
    }

    @Override
    public boolean subtractUsedNum(int num) {
        TenantUserInvitationNoticeTenant noticeTenant = lambdaQuery().limitOne();
        if (noticeTenant == null) {
            return false;
        }
        return baseMapper.subtractUsedNum(noticeTenant.getId(), num) > 0;
    }

    @Override
    public boolean addUsedNum(int num) {
        TenantUserInvitationNoticeTenant noticeTenant = lambdaQuery().limitOne();
        if (noticeTenant == null) {
            return false;
        }
        return baseMapper.addUsedNum(noticeTenant.getId(), num) > 0;
    }

}
