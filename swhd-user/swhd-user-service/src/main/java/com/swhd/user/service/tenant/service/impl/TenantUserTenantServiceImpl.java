package com.swhd.user.service.tenant.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.ValidatorUtil;
import com.swhd.service.market.api.rights.client.UserRightsClient;
import com.swhd.service.market.api.rights.dto.param.rights.CanConsumeRightsParam;
import com.swhd.user.api.common.constant.UserStatusCode;
import com.swhd.user.api.tenant.constant.UserInvitationState;
import com.swhd.user.api.tenant.constant.UserTenantState;
import com.swhd.user.api.tenant.dto.message.TenantUserTenantAddMessage;
import com.swhd.user.api.tenant.dto.param.tenant.TenantInfoAddParam;
import com.swhd.user.api.tenant.dto.param.user.tenant.*;
import com.swhd.user.api.tenant.dto.result.TenantUserBaseGetResult;
import com.swhd.user.api.tenant.dto.result.TenantUserBasePasswordResult;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantPasswordResult;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import com.swhd.user.api.tenant.properties.TenantRegProperties;
import com.swhd.user.service.tenant.entity.*;
import com.swhd.user.service.tenant.mapper.TenantUserTenantMapper;
import com.swhd.user.service.tenant.mq.producer.TenantProducer;
import com.swhd.user.service.tenant.service.*;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 租户后台用户和租户绑定表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
@AllArgsConstructor
public class TenantUserTenantServiceImpl extends BaseHdServiceImpl<TenantUserTenantMapper, TenantUserTenant>
        implements TenantUserTenantService {

    private final TenantUserBaseService tenantUserBaseService;
    private final ObjectProvider<TenantUserRoleService> tenantUserRoleServiceProvider;

    private final TenantInfoService tenantInfoService;

    private final TenantRoleInfoService tenantRoleInfoService;

    private final TenantUserInvitationService tenantUserInvitationService;
    @Lazy
    private final TenantTeamMemberService tenantTeamMemberService;

    private final TenantTeamInfoService tenantTeamInfoService;

    private final TenantRegProperties tenantRegProperties;

    private final UserRightsClient userRightsClient;

    @Override
    public IPage<TenantUserTenantResult> page(TenantUserTenantPageParam param) {
        Page<TenantUserTenantResult> page = convertToPageDto(param);
        if (Func.isNotEmpty(param.getMobile())) {
            // 传入手机号查询，需要查询用户表和邀请表
            if (!ValidatorUtil.isMobile(param.getMobile())) {
                page.setRecords(Collections.emptyList());
                return page;
            }
            List<Long> ids = new ArrayList<>();
            // 查询用户表关联的数据
            TenantUserBase userBase = tenantUserBaseService.getByMobile(param.getMobile());
            if (userBase != null) {
                lambdaQuery()
                        .select(List.of(TenantUserTenant::getId))
                        .eq(TenantUserTenant::getUserId, userBase.getId())
                        .list()
                        .forEach(userTenant -> ids.add(userTenant.getId()));
            }
            // 查询邀请表关联的数据
            List<TenantUserInvitation> userInvitationList = tenantUserInvitationService.unconfirmedList(param.getMobile());
            if (Func.isNotEmpty(userInvitationList)) {
                lambdaQuery()
                        .select(List.of(TenantUserTenant::getId))
                        .in(TenantUserTenant::getInvitationId, userInvitationList.stream().map(TenantUserInvitation::getId).toList())
                        .list()
                        .forEach(userTenant -> ids.add(userTenant.getId()));
            }
            List<Long> queryIds = ids;
            if (Func.isNotEmpty(param.getIds())) {
                queryIds = queryIds.stream()
                        .filter(id -> param.getIds().contains(id))
                        .toList();
            }
            if (Func.isEmpty(queryIds)) {
                page.setRecords(Collections.emptyList());
                return page;
            }
            param.setIds(queryIds);
        }
        return getBaseMapper().page(param, page);
    }

    @Override
    public TenantUserTenant getByUserId(Long userId) {
        return lambdaQuery().eq(TenantUserTenant::getUserId, userId).limitOne();
    }

    @Override
    public boolean existsByUserId(Long userId) {
        return lambdaQuery().eq(TenantUserTenant::getUserId, userId).exists();
    }

    @Override
    public List<TenantUserTenant> listByUserId(Long userId) {
        return lambdaQuery().eq(TenantUserTenant::getUserId, userId).list();
    }

    @Override
    public List<TenantUserTenant> listByUserIds(Collection<Long> userIds) {
        if (Func.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(TenantUserTenant::getUserId, userIds).list();
    }

    @Override
    public List<TenantUserTenantResult> listByEmail(String email) {
        if (Func.isBlank(email)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listByEmail(email);
    }

    @Override
    public List<TenantUserTenantResult> listByMobile(String mobile) {
        if (Func.isBlank(mobile)) {
            return Collections.emptyList();
        }
        return getBaseMapper().listByMobile(mobile);
    }

    @Override
    public boolean inviteUserExists(String mobile) {
        TenantUserBase userBase = tenantUserBaseService.getByMobile(mobile);
        if (userBase != null) {
            if (lambdaQuery().eq(TenantUserTenant::getUserId, userBase.getId()).exists()) {
                return true;
            }
        }
        return Func.isNotEmpty(tenantUserInvitationService.unconfirmedList(mobile));
    }

    @Override
    @Lockable(prefixKey = "user:tenant:user:tenant:add", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Void> invite(TenantUserTenantInviteParam param) {
        return invite(param, true);
    }

    private Rsp<Void> invite(TenantUserTenantInviteParam param, boolean checkUserCount) {
        if (checkUserCount) {
            // 判断是否已超过用户数
            Rsp<Void> checkUserCountRsp = checkUserCount(1);
            if (RspHd.isFail(checkUserCountRsp)) {
                return checkUserCountRsp;
            }
        }
        boolean exists = inviteUserExists(param.getMobile());
        if (exists) {
            return RspHd.fail("用户已存在");
        }
        // 保存邀请
        TenantUserInvitation userInvitationAdd = new TenantUserInvitation();
        userInvitationAdd.setMobile(param.getMobile());
        userInvitationAdd.setState(UserInvitationState.WAIT_CONFIRM.getState());
        tenantUserInvitationService.save(userInvitationAdd);
        // 保存租户用户
        TenantUserTenant userTenantAdd = new TenantUserTenant();
        userTenantAdd.setSourceScene(param.getSourceScene());
        userTenantAdd.setName(param.getName());
        userTenantAdd.setRemark(param.getRemark());
        userTenantAdd.setState(UserTenantState.INVITATION.getState());
        userTenantAdd.setInvitationId(userInvitationAdd.getId());
        save(userTenantAdd);
        // 保存关联角色
        tenantUserRoleServiceProvider.getObject().saveBatch(userTenantAdd.getId(), param.getRoleIds());
        // 保存关联团队
        tenantTeamMemberService.saveBatch(param.getTeamIds(), List.of(userTenantAdd.getId()));
        // 发送租户用户新增
        TenantProducer.userTenantAdd(new TenantUserTenantAddMessage(userTenantAdd.getId()));
        return RspHd.success();
    }

    @Override
    @Lockable(prefixKey = "user:tenant:user:tenant:add", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Void> batchInvite(TenantUserTenantBatchInviteParam param) {
        // 判断是否已超过用户数
        Rsp<Void> checkUserCountRsp = checkUserCount(param.getUserList().size());
        if (RspHd.isFail(checkUserCountRsp)) {
            return checkUserCountRsp;
        }

        param.getUserList().forEach(user -> {
            TenantUserTenantInviteParam inviteParam = new TenantUserTenantInviteParam();
            inviteParam.setMobile(user.getMobile());
            inviteParam.setName(user.getName());
            inviteParam.setSourceScene(param.getSourceScene());
            inviteParam.setRoleIds(param.getRoleIds());
            inviteParam.setTeamIds(param.getTeamIds());
            invite(inviteParam, false);
        });
        return RspHd.success();
    }

    @Override
    @Lockable(prefixKey = "user:tenant:user:tenant:add", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Long> add(TenantUserTenantAddParam param) {
        // 判断是否已超过用户数
        Rsp<Void> checkUserCountRsp = checkUserCount(1);
        if (RspHd.isFail(checkUserCountRsp)) {
            return RspHd.fail(checkUserCountRsp);
        }

        if (lambdaQuery().eq(TenantUserTenant::getUserId, param.getUserId()).exists()) {
            return RspHd.fail("用户已存在");
        }

        TenantUserTenant add = new TenantUserTenant();
        add.setUserId(param.getUserId());
        add.setState(param.getState());
        add.setSourceScene(param.getSourceScene());
        add.setName(param.getName());
        add.setRemark(param.getRemark());
        add.setState(Func.emptyOrDefault(param.getState(), UserTenantState.ENABLED.getState()));
        save(add);
        // 保存关联角色
        tenantUserRoleServiceProvider.getObject().saveBatch(add.getId(), param.getRoleIds());
        // 保存关联团队
        tenantTeamMemberService.saveBatch(param.getTeamIds(), List.of(add.getId()));
        // 发送租户用户新增
        TenantProducer.userTenantAdd(new TenantUserTenantAddMessage(add.getId()));
        return RspHd.data(add.getId());
    }

    @Override
    @Lockable(prefixKey = "user:tenant:user:tenant:add", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public Rsp<TenantUserBasePasswordResult> addUser(TenantUserTenantAddUserParam param) {
        Rsp<TenantUserBaseGetResult> getUserBaseRsp = tenantUserBaseService.getOrSave(
                param.getMobile(), param.getEmail(), param.getNickname(), param.getAvatar());
        if (Rsp.isFail(getUserBaseRsp)) {
            return RspHd.fail(getUserBaseRsp);
        }
        TenantUserBaseGetResult userBaseGetResult = getUserBaseRsp.getData();
        if (lambdaQuery().eq(TenantUserTenant::getUserId, userBaseGetResult.getUserId()).exists()) {
            return RspHd.fail(UserStatusCode.USER_EXIST, "用户已存在");
        }

        TenantUserTenant add = new TenantUserTenant();
        add.setUserId(userBaseGetResult.getUserId());
        add.setState(param.getState());
        add.setSourceScene(param.getSourceScene());
        add.setName(param.getName());
        add.setRemark(param.getRemark());
        add.setState(Func.emptyOrDefault(param.getState(), UserTenantState.ENABLED.getState()));
        save(add);
        // 保存关联角色
        tenantUserRoleServiceProvider.getObject().saveBatch(add.getId(), param.getRoleIds());
        // 保存关联团队
        tenantTeamMemberService.saveBatch(param.getTeamIds(), List.of(add.getId()));
        TenantUserBasePasswordResult passwordResult = new TenantUserBasePasswordResult()
                .setUserId(add.getUserId())
                .setPassword(userBaseGetResult.getPassword());
        // 发送租户用户新增
        TenantProducer.userTenantAdd(new TenantUserTenantAddMessage(add.getId()));
        return RspHd.data(passwordResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp<TenantUserTenantPasswordResult> addUserAndTenant(TenantUserTenantAddUserAndTenantParam param) {
        // 没有父租户或者父租户为0时，兜底父租户为1000（慧引流）
        long parentTenantId = Objects.nonNull(param.getParentTenantId()) && !Objects.equals(param.getParentTenantId(), 0L)
                ? param.getParentTenantId()
                : tenantRegProperties.getParentTenantId();
        TenantInfo parentTenantInfo = tenantInfoService.getById(parentTenantId);
        if (parentTenantInfo == null || !Objects.equals(parentTenantInfo.getParentId(), 0L)) {
            return RspHd.fail(UserStatusCode.TENANT_NOT_ALLOWED_REG, "不允许注册");
        }

        // 创建租户
        TenantInfoAddParam tenantParam = new TenantInfoAddParam();
        tenantParam.setParentId(parentTenantId);
        tenantParam.setName(param.getTenantName());
        tenantParam.setState(UserTenantState.ENABLED.getState());
        tenantParam.setTenantType(param.getTenantType());
        tenantParam.setAuthInfos(param.getAuthInfos());
        Long tenantId = tenantInfoService.add(tenantParam);

        // 创建用户
        return TenantHolder.methodTenant(tenantId, () -> {
            //查询租户角色
            List<Long> roleIds = tenantRoleInfoService.lambdaQuery().list().stream().map(TenantRoleInfo::getId).toList();
            //创建用户
            TenantUserTenantAddUserParam addParam = new TenantUserTenantAddUserParam();
            addParam.setState(UserTenantState.ENABLED.getState());
            addParam.setMobile(param.getMobile());
            addParam.setNickname(param.getNickname());
            addParam.setAvatar(param.getAvatar());
            addParam.setRoleIds(roleIds);
            addParam.setSourceScene(param.getSourceScene());
            List<TenantTeamInfo> allTeams = tenantTeamInfoService.list();
            addParam.setTeamIds(allTeams.stream().map(TenantTeamInfo::getId).toList());
            Rsp<TenantUserBasePasswordResult> addRsp = this.addUser(addParam);
            if (RspHd.isFail(addRsp)) {
                return RspHd.fail(addRsp);
            }
            TenantUserTenantPasswordResult tenantPasswordResult = new TenantUserTenantPasswordResult();
            tenantPasswordResult.setUserId(addRsp.getData().getUserId());
            tenantPasswordResult.setTenantId(tenantId);
            tenantPasswordResult.setPassword(addRsp.getData().getPassword());
            tenantPasswordResult.setTeamIds(allTeams.stream().map(TenantTeamInfo::getId).toList());
            return RspHd.data(tenantPasswordResult);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "user:tenant:user:tenant:update", key = "#param.id", waitTime = 6000)
    public Rsp<Void> update(TenantUserTenantUpdateParam param) {
        boolean result = updateById(Func.copy(param, TenantUserTenant.class));
        if (result) {
            // 保存关联角色
            tenantUserRoleServiceProvider.getObject().saveBatch(param.getId(), param.getRoleIds());
            // 保存关联团队
            tenantTeamMemberService.saveBatch(param.getTeamIds(), List.of(param.getId()));
        }
        return RspHd.status(result);
    }

    @Override
    public void fillUserInfo(TenantUserTenantResult result) {
        if (result == null) {
            return;
        }
        TenantUserBase userBase = tenantUserBaseService.getById(result.getUserId());
        if (userBase != null) {
            result.setMobile(userBase.getMobile());
            result.setEmail(userBase.getEmail());
            result.setNickname(userBase.getNickname());
            result.setAvatar(userBase.getAvatar());
        }
    }

    @Override
    public void fillUserInfo(List<TenantUserTenantResult> resultList) {
        if (Func.isEmpty(resultList)) {
            return;
        }
        List<Long> userIds = resultList.stream().map(TenantUserTenantResult::getUserId).toList();
        List<TenantUserBase> userBaseList = tenantUserBaseService.listByIds(userIds);
        if (Func.isEmpty(userBaseList)) {
            return;
        }
        Map<Long, TenantUserBase> userBaseMap = userBaseList.stream()
                .collect(Collectors.toMap(TenantUserBase::getId, Function.identity()));
        resultList.stream()
                .filter(result -> userBaseMap.containsKey(result.getUserId()))
                .forEach(result -> {
                    TenantUserBase userBase = userBaseMap.get(result.getUserId());
                    result.setMobile(userBase.getMobile())
                            .setEmail(userBase.getEmail())
                            .setNickname(userBase.getNickname())
                            .setAvatar(userBase.getAvatar());
                });
    }

    @Override
    public Long countUsers(TenantUserTenantCountParam param) {
        if (Func.isEmpty(param) || Func.isEmpty(param.getStates())) {
            // 如果没有指定状态，则统计所有用户
            return lambdaQuery().count();
        }
        return lambdaQuery()
                .in(TenantUserTenant::getState, param.getStates())
                .count();
    }

    private Rsp<Void> checkUserCount(int consumeCount) {
        Long existingCount = this.lambdaQuery()
                .in(TenantUserTenant::getState, UserTenantState.ENABLED.getState(),
                        UserTenantState.DISABLED.getState(),
                        UserTenantState.INVITATION.getState())
                .count();

        // 创建参数对象
        CanConsumeRightsParam param = new CanConsumeRightsParam()
                // 权益类型
                .setRightsType("UserCount")
                // 消费数量
                .setConsumeQuantity(new BigDecimal(consumeCount))
                // 当前用户数量
                .setCurrentCount(new BigDecimal(existingCount));

        // 调用权益服务
        Rsp<Boolean> canConsumeRsp = userRightsClient.canConsumeRights(param);
        if (RspHd.isFail(canConsumeRsp)) {
            return RspHd.fail(canConsumeRsp);
        }
        if (BooleanUtil.isFalse(canConsumeRsp.getData())) {
            return RspHd.fail("权益不足");
        }
        return RspHd.success();
    }

}
