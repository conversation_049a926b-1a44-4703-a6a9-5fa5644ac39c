package com.swhd.user.service.tenant.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.user.api.common.constant.SourceSceneEnum;
import com.swhd.user.service.tenant.entity.TenantInfo;
import com.swhd.user.service.tenant.entity.TenantUserTenant;
import com.swhd.user.service.tenant.properties.TenantProperties;
import com.swhd.user.service.tenant.service.TenantInfoService;
import com.swhd.user.service.tenant.service.TenantUserTenantService;
import com.swj.magiccube.tool.dingtalk.DingTalkUtil;
import com.swj.magiccube.tool.dingtalk.vo.DingTalkMarkdown;
import com.swj.magiccube.tool.dingtalk.vo.DingTalkMarkdownMessage;
import com.swj.magiccube.tool.dingtalk.vo.DingTalkMessageAt;
import com.xxl.job.core.ext.handler.annotation.SwjJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户统计相关定时任务
 *
 * <AUTHOR>
 * @since 2025/1/17 15:51
 */
@Slf4j
@Component
@AllArgsConstructor
public class UserStatisticsJob {

    private final TenantInfoService tenantInfoService;
    private final TenantUserTenantService tenantUserTenantService;
    private final TenantProperties tenantProperties;

    @SwjJob(jobDesc = "每日新增租户和用户数据统计", author = "zenghaoming",
            schedule = "${magiccube.job.schedule.UserStatisticsJob.sendDailyStatistics:0 0 8-21/1 * * ?}")
    public void sendDailyStatistics() {
        // 获取今天的开始和结束时间
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = LocalDateTime.now();

        // 1. 统计新增租户数
        LambdaQueryWrapper<TenantInfo> tenantWrapper = new LambdaQueryWrapper<TenantInfo>()
                .between(TenantInfo::getCreateTime, startTime, endTime);
        long newTenantCount = tenantInfoService.count(tenantWrapper);

        // 2. 统计新增账号数
        LambdaQueryWrapper<TenantUserTenant> userWrapper = new LambdaQueryWrapper<TenantUserTenant>()
                .between(TenantUserTenant::getCreateTime, startTime, endTime);
        long newUserCount = TenantHolder.methodIgnoreTenant(() -> tenantUserTenantService.count(userWrapper));

        // 3. 统计账号来源分布
        List<TenantUserTenant> newUsers = TenantHolder.methodIgnoreTenant(() -> tenantUserTenantService.list(userWrapper));
        Map<String, Long> sourceDistribution = newUsers.stream()
                .collect(Collectors.groupingBy(
                        user -> {
                            String sourceScene = user.getSourceScene();
                            // 获取来源场景的描述
                            for (SourceSceneEnum scene : SourceSceneEnum.values()) {
                                if (scene.getScene().equals(sourceScene)) {
                                    return scene.getDesc();
                                }
                            }
                            return StrUtil.isNotEmpty(sourceScene) ? sourceScene : "其他";
                        },
                        Collectors.counting()
                ));

        // 4. 构建钉钉消息
        StringBuilder markdownText = new StringBuilder();
        markdownText.append("#### **📈 新增数据**\n\n");
        markdownText.append("- 租户数：**").append(newTenantCount).append("**\n\n");
        markdownText.append("- 账号数：**").append(newUserCount).append("**\n\n");
        markdownText.append("---\n\n");
        markdownText.append("#### **📊 新增账号来源分布**\n");

        sourceDistribution.entrySet().stream()
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                .forEach(entry ->
                        markdownText.append("- ").append(entry.getKey()).append("：**").append(entry.getValue()).append("**\n")
                );

        String startTimeStr = LocalDateTimeUtil.format(startTime, "MM月dd日 HH:mm:ss");
        String endTimeStr = LocalDateTimeUtil.format(endTime, "MM月dd日 HH:mm:ss");
        markdownText.append("\n\n---\n\n");
        markdownText.append("###### 统计时间：").append(startTimeStr).append(" - ").append(endTimeStr);

        // 5. 发送钉钉消息
        DingTalkMarkdownMessage message = new DingTalkMarkdownMessage();
        message.setMarkdown(new DingTalkMarkdown()
                .setTitle("📊 新增账号分析")
                .setText(markdownText.toString()));
        message.setAt(new DingTalkMessageAt().setIsAtAll(false));

        try {
            DingTalkUtil.send(tenantProperties.getUserStatisticsDingTalkAccessToken(), message);
            log.info("发送新增账号统计数据成功");
        } catch (Exception e) {
            log.error("发送新增账号统计数据失败", e);
        }
    }
}
