package com.swhd.user.service.tenant.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.user.api.tenant.dto.param.auth.module.group.TenantAuthModuleGroupPageParam;
import com.swhd.user.service.tenant.entity.TenantAuthModuleGroup;
import com.swhd.user.service.tenant.mapper.TenantModuleGroupMapper;
import com.swhd.user.service.tenant.service.TenantAuthModuleGroupService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 租户授权模块组 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
@AllArgsConstructor
public class TenantAuthModuleGroupServiceImpl extends BaseHdServiceImpl<TenantModuleGroupMapper, TenantAuthModuleGroup>
        implements TenantAuthModuleGroupService {

    @Override
    public IPage<TenantAuthModuleGroup> page(TenantAuthModuleGroupPageParam param) {
        return lambdaQuery()
                .like(Func.isNotEmpty(param.getGroupName()), TenantAuthModuleGroup::getGroupName, param.getGroupName())
                .jsonArrayContains(Func.isNotEmpty(param.getModuleId()), TenantAuthModuleGroup::getModuleIds, param.getModuleId())
                .orderByDesc(TenantAuthModuleGroup::getCreateTime)
                .orderByDesc(TenantAuthModuleGroup::getId)
                .page(convertToPage(param));
    }

}
