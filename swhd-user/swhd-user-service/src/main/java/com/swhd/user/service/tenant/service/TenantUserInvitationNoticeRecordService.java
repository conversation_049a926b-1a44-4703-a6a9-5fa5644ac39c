package com.swhd.user.service.tenant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.api.tenant.dto.param.invitation.notice.record.TenantUserInvitationNoticeRecordPageParam;
import com.swhd.user.service.tenant.entity.TenantUserInvitationNoticeRecord;
import com.swj.magiccube.api.Rsp;

/**
 * 租户后台用户邀请通知记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-18
 */
public interface TenantUserInvitationNoticeRecordService extends IBaseHdService<TenantUserInvitationNoticeRecord> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<TenantUserInvitationNoticeRecord> page(TenantUserInvitationNoticeRecordPageParam param);

	/**
	 * 新增
	 */
	Rsp<Long> add(String mobile);

	/**
	 * 成功
	 */
	void success(Long id, String noticeResponseMsg);

	/**
	 * 失败
	 */
	void fail(Long id, String noticeResponseMsg);

}
