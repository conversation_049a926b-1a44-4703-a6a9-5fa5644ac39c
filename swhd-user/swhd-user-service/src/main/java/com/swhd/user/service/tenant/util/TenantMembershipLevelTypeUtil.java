package com.swhd.user.service.tenant.util;

import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.user.api.tenant.dto.param.membership.TenantMembershipType;
import com.swhd.user.service.tenant.properties.TenantProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 租户会员等级类型工具类
 *
 * <AUTHOR>
 * @since 2025-03-29
 */
@Component
@RequiredArgsConstructor
public class TenantMembershipLevelTypeUtil {

    private final TenantProperties tenantProperties;

    /**
     * 根据编码获取会员等级类型
     *
     * @param code 等级编码
     * @return 会员等级类型
     */
    public TenantMembershipType of(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return tenantProperties.getMembershipLevelTypeMap().get(code.toLowerCase());
    }

    /**
     * 根据编码获取会员等级类型(必填)
     *
     * @param code 等级编码
     * @return 会员等级类型
     */
    public TenantMembershipType ofRequired(String code) {
        TenantMembershipType type = of(code);
        if (Objects.nonNull(type)) {
            return type;
        }
        throw new ServiceException("不支持的会员类型");
    }

    /**
     * 获取所有会员等级类型
     *
     * @return 会员等级类型Map
     */
    public Map<String, TenantMembershipType> getAllTypes() {
        return tenantProperties.getMembershipLevelTypeMap();
    }
} 