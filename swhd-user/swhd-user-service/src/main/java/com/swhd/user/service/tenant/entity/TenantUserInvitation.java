package com.swhd.user.service.tenant.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 租户后台用户邀请表实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tuser_tenant_user_invitation")
public class TenantUserInvitation extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "状态：0-待确认，1-已确认，2-已取消")
    private Integer state;

    @Schema(description = "确认时间")
    private LocalDateTime confirmTime;

}
