package com.swhd.user.service.tenant.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 租户前端乾坤子应用表实体类
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tuser_tenant_frontend_qiankun")
public class TenantFrontendQiankun extends BaseHdEntity {

    @Schema(description = "环境")
    private String env;

    @Schema(description = "子应用code")
    private String code;

    @Schema(description = "子应用标题")
    private String title;

    @Schema(description = "子应用url")
    private String url;

}
