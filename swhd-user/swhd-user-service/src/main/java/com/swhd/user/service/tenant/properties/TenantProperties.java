package com.swhd.user.service.tenant.properties;

import com.swhd.user.api.tenant.dto.param.membership.TenantMembershipType;
import com.swhd.user.service.common.utils.PasswordUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@Getter
@Setter
@Component
@ConfigurationProperties(TenantProperties.PREFIX)
public class TenantProperties {

    public static final String PREFIX = "user.tenant";

    /**
     * 租户开始的id
     */
    private long tenantIdStart = 1000;

    /**
     * 租户id递增量
     */
    private int tenantIdIncrement = 10;

    /**
     * 权限缓存时间
     */
    private Duration permissionCacheTime = Duration.ofMinutes(30);

    /**
     * 初始密码，为空使用随机生成
     */
    private String initPassword;

    /**
     * 自动生成密码长度
     */
    private int autoPasswordLength = 16;

    /**
     * 密码校验
     */
    @NestedConfigurationProperty
    private PasswordUtil.PasswordVerify passwordVerify = new PasswordUtil.PasswordVerify();

    /**
     * 最大登陆设备数
     */
    private int maxLoginDeviceNum = 20;

    /**
     * 默认三维家站点配置
     */
    @NestedConfigurationProperty
    private TenantSiteConfigDefault defaultSwjSiteConfig = new TenantSiteConfigDefault();

    /**
     * 三维家域名
     */
    private List<String> swjDomainList = List.of("3vjia.com", "3weijia.com", "localhost");

    /**
     * 默认海外站点配置
     */
    @NestedConfigurationProperty
    private TenantSiteConfigDefault defaultAihouseSiteConfig = new TenantSiteConfigDefault();

    /**
     * 海外域名
     */
    private List<String> aihouseDomainList = List.of("aihouse.com");

    /**
     * 用户统计钉钉token
     */
    private String userStatisticsDingTalkAccessToken;

    /**
     * 租户额外按钮权限：Map<租户ID, 按钮code列表>
     */
    private Map<Long, List<String>> tenantExtraButtonCodes = new HashMap<>();

    /**
     * 租户额外按钮权限：Map<按钮code, 权限url列表>
     */
    private Map<String, List<String>> tenantExtraButtonCodePermissionUrls = new HashMap<>();

    /**
     * 会员等级类型配置：Map<等级编码, 等级信息>
     */
    private Map<String, TenantMembershipType> membershipLevelTypeMap = new HashMap<>();

    /**
     * 企业空间默认名称
     */
    private String defaultCorpTeamName = "企业空间";

    /**
     * 个人空间默认名称
     */
    private String defaultPersonTeamName = "个人版";

}
