package com.swhd.user.service.tenant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.user.api.tenant.dto.param.auth.module.group.TenantAuthModuleGroupPageParam;
import com.swhd.user.service.tenant.entity.TenantAuthModuleGroup;

/**
 * 租户授权模块组 服务类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface TenantAuthModuleGroupService extends IBaseHdService<TenantAuthModuleGroup> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<TenantAuthModuleGroup> page(TenantAuthModuleGroupPageParam param);

}
