package com.swhd.user.service.platform.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 平台后台角色信息表实体类
 *
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tuser_platform_role_info")
public class PlatformRoleInfo extends BaseHdEntity {

    @Schema(description = "角色标题")
    private String title;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "超管：0-否，1-是")
    private Integer admin;

}
