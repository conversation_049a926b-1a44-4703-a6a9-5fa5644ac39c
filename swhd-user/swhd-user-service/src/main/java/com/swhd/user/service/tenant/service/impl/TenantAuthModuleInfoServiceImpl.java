package com.swhd.user.service.tenant.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.dto.param.auth.module.info.TenantAuthModuleInfoPageParam;
import com.swhd.user.service.tenant.entity.TenantAuthModuleInfo;
import com.swhd.user.service.tenant.mapper.TenantAuthModuleInfoMapper;
import com.swhd.user.service.tenant.service.TenantAuthModuleInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户授权模块信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
@AllArgsConstructor
public class TenantAuthModuleInfoServiceImpl extends BaseHdServiceImpl<TenantAuthModuleInfoMapper, TenantAuthModuleInfo>
        implements TenantAuthModuleInfoService {

    @Override
    public IPage<TenantAuthModuleInfo> page(TenantAuthModuleInfoPageParam param) {
        return lambdaQuery()
                .like(Func.isNotEmpty(param.getModuleName()), TenantAuthModuleInfo::getModuleName, param.getModuleName())
                .orderByDesc(TenantAuthModuleInfo::getCreateTime)
                .orderByDesc(TenantAuthModuleInfo::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<TenantAuthModuleInfo> listBaseModules() {
        return lambdaQuery()
                .eq(TenantAuthModuleInfo::getAuthType, 1)
                .list();
    }

}
