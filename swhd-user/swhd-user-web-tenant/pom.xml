<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.swhd</groupId>
        <artifactId>swhd-user</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>swhd-user-web-tenant</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-user-auth-login-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-user-auth-permission-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swj</groupId>
            <artifactId>magiccube-starter-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream-binder-rabbit</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-service-market-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-message-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-open-api</artifactId>
        </dependency>
        <!-- 三维家用户 -->
        <dependency>
            <groupId>com.swj</groupId>
            <artifactId>auth-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swj</groupId>
            <artifactId>user-center-plus-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swj</groupId>
            <artifactId>magiccube-starter-freeway-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swj</groupId>
            <artifactId>i18n-center-magiccubeV2-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.graalvm.buildtools</groupId>
                <artifactId>native-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
