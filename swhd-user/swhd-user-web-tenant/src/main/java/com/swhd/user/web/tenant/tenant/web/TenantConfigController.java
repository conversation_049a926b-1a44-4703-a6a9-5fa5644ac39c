package com.swhd.user.web.tenant.tenant.web;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.swhd.user.api.tenant.client.TenantConfigClient;
import com.swhd.user.api.tenant.dto.param.config.TenantConfigSaveParam;
import com.swhd.user.api.tenant.dto.result.TenantConfigResult;
import com.swhd.user.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;

@RestController
@RequestMapping(WebConstant.BASE_PATH + "/tenantConfig")
@AllArgsConstructor
public class TenantConfigController {

    private final TenantConfigClient tenantConfigClient;

    @Operation(summary = "获取租户配置")
    @GetMapping("/get")
    public Rsp<TenantConfigResult> get() {
        return tenantConfigClient.get();
    }

    @Operation(summary = "保存租户配置")
    @PostMapping("/save")
    public Rsp<Void> save(@RequestBody @Valid TenantConfigSaveParam param) {
        return tenantConfigClient.saveOrUpdate(param);
    }

}
