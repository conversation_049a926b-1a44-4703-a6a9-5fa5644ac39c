package com.swhd.user.web.tenant.auth.strategy;

import cn.hutool.core.util.RandomUtil;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.message.api.sms.client.SmsCaptchaClient;
import com.swhd.message.api.sms.dto.result.SmsCaptchaTokenResult;
import com.swhd.user.api.common.client.SvgWechatClient;
import com.swhd.user.api.common.constant.OauthTokenType;
import com.swhd.user.api.common.constant.SourceSceneEnum;
import com.swhd.user.api.common.constant.UserStatusCode;
import com.swhd.user.api.common.dto.result.SvgWechatGetQrcodeResult;
import com.swhd.user.api.tenant.dto.param.auth.info.TenantAuthInfoAddParam;
import com.swhd.user.api.tenant.dto.param.oauth.TenantUserOauthThirdAccountParam;
import com.swhd.user.api.tenant.dto.param.oauth.TenantUserOauthThirdAccountTenantParam;
import com.swhd.user.api.tenant.dto.result.TenantTeamMemberTenantTeamResult;
import com.swhd.user.api.tenant.dto.result.TenantUserBaseResult;
import com.swhd.user.api.tenant.dto.result.TenantUserOauthResult;
import com.swhd.user.api.tenant.dto.result.TenantUserOauthThirdAccountTenantResult;
import com.swhd.user.auth.core.dto.LoginUserDto;
import com.swhd.user.auth.core.dto.param.OauthTokenParam;
import com.swhd.user.web.tenant.auth.mq.producer.AssignSceneServicePkgProducer;
import com.swhd.user.web.tenant.auth.service.AuthUserInfoService;
import com.swhd.user.web.tenant.auth.strategy.dto.OauthToken;
import com.swhd.user.web.tenant.auth.strategy.dto.OauthUserInfo;
import com.swj.magiccube.api.Rsp;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 授权策略：微信公众号
 *
 * <AUTHOR> <EMAIL>
 * @date 2024/12/3
 */
@Slf4j
@Component
@AllArgsConstructor
public class WechatMpStrategy extends AbstractOauthStrategy implements OauthStrategy {

    private final SvgWechatClient svgWechatClient;
    private final SmsCaptchaClient smsCaptchaClient;

    @PostConstruct
    public void init() {
        OauthStrategyContext.addStrategy(OauthTokenType.WECHAT_MP.getCode(), this);
    }

    @Override
    public boolean isUserLoginErrorLimit(OauthTokenParam param) {
        // 取消用户登陆错误限制
        return false;
    }

    @Override
    public Rsp<LoginUserDto> login(OauthTokenParam param) {
        String logkey = param.getPassword();
        Rsp<SvgWechatGetQrcodeResult> qrcodeResultRsp = svgWechatClient.getQrcodeResult(logkey);
        if (RspHd.isFail(qrcodeResultRsp)) {
            return RspHd.fail(qrcodeResultRsp);
        }
        if (qrcodeResultRsp.getData() == null || Func.isBlank(qrcodeResultRsp.getData().getUnionId())) {
            return RspHd.fail("二维码已失效，请刷新");
        }
        String unionId = qrcodeResultRsp.getData().getUnionId();

        Long userId;
        // 查询授权信息
        TenantUserOauthResult oauthAccount = getOauthByAccount(OauthTokenType.WECHAT_MP, unionId);
        if (oauthAccount == null) {
            // 用户不存在，自动注册
            Rsp<Long> registerRsp = register(null, unionId, param);
            if (RspHd.isFail(registerRsp)) {
                return RspHd.fail(registerRsp);
            }
            userId = registerRsp.getData();
        } else {
            userId = oauthAccount.getUserId();
        }

        // 获取租户团队成员
        Rsp<TenantTeamMemberTenantTeamResult> tenantTeamMemberRsp = getTenantTeamMemberOfRegister(param.getDomainTenantId(),
                userId, () -> register(userId, unionId, param));
        if (RspHd.isFail(tenantTeamMemberRsp)) {
            return RspHd.fail(tenantTeamMemberRsp);
        }

        // 删除登陆key
        svgWechatClient.removeLogkey(logkey);
        if (Func.isNotBlank(param.getBindMobileCaptchaToken())) {
            // 删除短信token
            smsCaptchaClient.tokenGetAndDelete(param.getBindMobileCaptchaToken());
        }

        return RspHd.data(toLoginUserDto(userId, tenantTeamMemberRsp.getData()));
    }

    @Override
    public Rsp<LoginUserDto> registerTenant(OauthTokenParam param, Map<String, Object> extendParams) {
        return Rsp.fail(UserStatusCode.REGISTER_UN_SUPPORT, UserStatusCode.REGISTER_UN_SUPPORT_MSG);
    }

    @Override
    public OauthUserInfo oauthUserInfo(OauthToken req) {
        return null;
    }

    private Rsp<Long> register(Long userId, String unionId, OauthTokenParam param) {
        String bindMobile = null;
        if (userId != null) {
            Rsp<TenantUserBaseResult> userRsp = tenantUserBaseClient.getById(userId);
            if (RspHd.isFail(userRsp)) {
                return RspHd.fail(userRsp);
            }
            bindMobile = Optional.ofNullable(userRsp.getData()).map(TenantUserBaseResult::getMobile).orElse(null);
        }

        String mobile;
        if (Func.isBlank(bindMobile)) {
            if (Func.isNotBlank(param.getBingMobile()) && Func.isNotBlank(param.getBindMobileCaptchaToken())) {
                // 验证手机验证码
                Rsp<SmsCaptchaTokenResult> verify = smsCaptchaClient.tokenGet(param.getBindMobileCaptchaToken());
                if (RspHd.isFail(verify)) {
                    return RspHd.fail(verify);
                }
                if (!Objects.equals(verify.getData().getMobile(), param.getBingMobile())
                        || !Objects.equals(verify.getData().getBusinessCode(), AuthUserInfoService.BIND_MOBILE_CAPTCHA_BUSINESS_CODE)) {
                    return RspHd.fail("验证码token无效");
                }
                mobile = param.getBingMobile();
            } else {
                return RspHd.fail(UserStatusCode.BING_MOBILE_CODE, UserStatusCode.BING_MOBILE_MSG);
            }
        } else {
            mobile = bindMobile;
        }

        // 检查未确认列表
        Rsp<?> checkUnconfirmedListRsp = checkUnconfirmedList(param.getConfirmedMobileInvitation(), mobile, () -> {
            if (userId != null) {
                return RspHd.data(userId);
            }
            TenantUserOauthThirdAccountParam oauthThirdAccountParam = new TenantUserOauthThirdAccountParam()
                    .setOauthType(OauthTokenType.WECHAT_MP.getOauthType())
                    .setOauthAccount(unionId)
                    .setMobile(mobile);
            Rsp<Long> oauthRsp = tenantUserOauthClient.oauthThirdAccount(oauthThirdAccountParam);
            if (Objects.equals(oauthRsp.getCode(), UserStatusCode.MOBILE_BIND_EXIST)) {
                if (Func.isBlank(param.getBingMobile()) || Func.isBlank(param.getBindMobileCaptchaToken())) {
                    // 手机号码已被其他账号绑定，重新绑定手机号码
                    return RspHd.fail(UserStatusCode.BING_MOBILE_CODE, UserStatusCode.BING_MOBILE_MSG);
                }
            }
            return oauthRsp;
        });
        if (RspHd.isFail(checkUnconfirmedListRsp)) {
            return RspHd.fail(checkUnconfirmedListRsp);
        }

        // 校验用户是否存在
        TenantUserOauthResult oauthAccount = getOauthByAccount(OauthTokenType.WECHAT_MP, unionId);
        if (oauthAccount != null) {
            Rsp<List<TenantTeamMemberTenantTeamResult>> listTenantTeamMemberRsp = TenantHolder.methodIgnoreTenant(() ->
                    listTenantTeamMember(param.getDomainTenantId(), oauthAccount.getUserId()));
            if (Func.isNotEmpty(listTenantTeamMemberRsp.getData())) {
                return RspHd.fail(UserStatusCode.USER_EXIST, UserStatusCode.USER_EXIST_MSG);
            }
        }

        // 注册授权用户
        TenantUserOauthThirdAccountTenantParam oauthThirdAccountTenantParam = new TenantUserOauthThirdAccountTenantParam();
        oauthThirdAccountTenantParam.setOauthType(OauthTokenType.WECHAT_MP.getOauthType());
        oauthThirdAccountTenantParam.setOauthAccount(unionId);
        oauthThirdAccountTenantParam.setMobile(mobile);
        oauthThirdAccountTenantParam.setNickname(RandomUtil.randomString(8));
        oauthThirdAccountTenantParam.setTenantName("个人：" + subMobile(mobile));
        oauthThirdAccountTenantParam.setParentTenantId(getRegParentTenantId(param.getDomainTenantId()));
        if (StringUtils.isBlank(oauthThirdAccountTenantParam.getSourceScene())) {
            oauthThirdAccountTenantParam.setSourceScene(SourceSceneEnum.WECHAT.getScene());
        }
        // 根据场景获取授权信息
        List<TenantAuthInfoAddParam> authInfoList = super.getAuthByScene(
                param.getDomainTenantId(), oauthThirdAccountTenantParam.getSourceScene());
        oauthThirdAccountTenantParam.setAuthInfos(authInfoList);
        Rsp<TenantUserOauthThirdAccountTenantResult> oauthRsp = tenantUserOauthClient
                .oauthThirdAccountTenant(oauthThirdAccountTenantParam);
        if (RspHd.failOrDataIsNull(oauthRsp)) {
            if (Objects.equals(oauthRsp.getCode(), UserStatusCode.MOBILE_BIND_EXIST)) {
                if (Func.isBlank(param.getBingMobile()) || Func.isBlank(param.getBindMobileCaptchaToken())) {
                    // 手机号码已被其他账号绑定，重新绑定手机号码
                    return RspHd.fail(UserStatusCode.BING_MOBILE_CODE, UserStatusCode.BING_MOBILE_MSG);
                }
            }
            return RspHd.fail(UserStatusCode.REGISTER_FAILED, "注册失败：" + oauthRsp.getMsg());
        }
        TenantUserOauthThirdAccountTenantResult result = oauthRsp.getData();

        if (result.getAddTenantId() != null) {
            // 发放其它场景服务套餐
            AssignSceneServicePkgProducer.sendAssignMessage(
                    result.getAddTenantId(), getRegParentTenantId(param.getDomainTenantId()),
                    result.getUserId(), oauthThirdAccountTenantParam.getSourceScene());
        }

        return RspHd.data(result.getUserId());
    }

}
