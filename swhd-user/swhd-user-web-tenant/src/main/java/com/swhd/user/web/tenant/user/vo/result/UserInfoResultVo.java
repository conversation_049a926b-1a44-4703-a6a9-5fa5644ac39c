package com.swhd.user.web.tenant.user.vo.result;

import com.swhd.magiccube.core.annotation.JsonMask;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.user.api.tenant.dto.result.TenantInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantRoleInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantTeamInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserInfoResultVo对象")
public class UserInfoResultVo extends TenantUserTenantResult {

    private List<TenantRoleInfoResult> roleList;

    private List<TenantTeamInfoResult> teamList;

    private TenantInfoResult tenantInfo;

    @JsonMask(type = JsonMaskType.EMAIL)
    @Schema(description = "邮箱")
    private String email;

    @JsonMask(type = JsonMaskType.MOBILE)
    @Schema(description = "手机号")
    private String mobile;

}
