package com.swhd.user.web.tenant.auth.vo.param;

import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.magiccube.core.constant.PatternConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Getter
@Setter
public class AuthUserBindMobileParamVo {

    @Schema(description = "旧手机验证token")
    @LogMask(type = JsonMaskType.PASSWORD)
    private String oldMobileCaptchaToken;

    @NotEmpty(message = "验证码不能为空")
    @Schema(description = "新手机验证码")
    @LogMask(type = JsonMaskType.PASSWORD)
    private String newMobileCaptchaCode;

    @NotEmpty(message = "新手机号不能为空")
    @Schema(description = "新手机号")
    @LogMask(type = JsonMaskType.MOBILE)
    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "手机号码格式不正确")
    private String newMobile;

}
