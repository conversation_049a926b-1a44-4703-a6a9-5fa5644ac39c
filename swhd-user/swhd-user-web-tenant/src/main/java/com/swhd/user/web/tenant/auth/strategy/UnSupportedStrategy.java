package com.swhd.user.web.tenant.auth.strategy;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.user.api.common.constant.OauthTokenType;
import com.swhd.user.api.common.constant.UserStatusCode;
import com.swhd.user.auth.core.dto.LoginUserDto;
import com.swhd.user.auth.core.dto.param.OauthTokenParam;
import com.swhd.user.web.tenant.auth.strategy.dto.OauthToken;
import com.swhd.user.web.tenant.auth.strategy.dto.OauthUserInfo;
import com.swj.magiccube.api.Rsp;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 兜底策略
 *
 * <AUTHOR> <EMAIL>
 * @date 2024/12/3
 */
@Slf4j
@Component
public class UnSupportedStrategy implements OauthStrategy {

    @PostConstruct
    public void init() {
        OauthStrategyContext.addStrategy(OauthTokenType.UNSUPPORTED.getCode(), this);
    }

    @Override
    public Rsp<LoginUserDto> login(OauthTokenParam param) {
        log.warn("不支持的授权类型:{}", param.getOauthType());
        return RspHd.fail("不支持的授权类型");
    }

    @Override
    public Rsp<LoginUserDto> registerTenant(OauthTokenParam param, Map<String, Object> extendParams) {
        return Rsp.fail(UserStatusCode.REGISTER_UN_SUPPORT, UserStatusCode.REGISTER_UN_SUPPORT_MSG);
    }

    @Override
    public OauthUserInfo oauthUserInfo(OauthToken req) {
        // unsupported
        return null;
    }

}
