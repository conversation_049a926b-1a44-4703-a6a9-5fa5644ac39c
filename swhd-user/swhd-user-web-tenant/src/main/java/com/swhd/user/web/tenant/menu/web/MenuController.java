package com.swhd.user.web.tenant.menu.web;

import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.user.api.common.constant.MenuTenantType;
import com.swhd.user.api.tenant.client.TenantAuthMenuClient;
import com.swhd.user.api.tenant.client.TenantFrontendQiankunClient;
import com.swhd.user.api.tenant.client.TenantInfoClient;
import com.swhd.user.api.tenant.dto.result.TenantFrontendQiankunRouteResult;
import com.swhd.user.api.tenant.dto.result.TenantInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantMenuInfoTreeResult;
import com.swhd.user.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/menu")
public class MenuController {

    private final TenantAuthMenuClient tenantAuthMenuClient;

    private final TenantInfoClient tenantInfoClient;

    private final TenantFrontendQiankunClient tenantFrontendQiankunClient;

    @Operation(summary = "主租户菜单树")
    @GetMapping("/mainMenuTree")
    public Rsp<List<TenantMenuInfoTreeResult>> mainMenuTree() {
        return tenantAuthMenuClient.menuTree(MenuTenantType.MAIN);
    }

    @Operation(summary = "子租户菜单树")
    @GetMapping("/subMenuTree")
    public Rsp<List<TenantMenuInfoTreeResult>> subMenuTree() {
        return tenantAuthMenuClient.menuTree(MenuTenantType.SUB);
    }

    @Operation(summary = "租户菜单树", description = "根据主/子租户获取对应菜单")
    @GetMapping("/tenantMenuTree")
    public Rsp<List<TenantMenuInfoTreeResult>> tenantMenuTree() {
        Rsp<TenantInfoResult> tenantRsp = tenantInfoClient.getById(TenantHolder.getRequiredTenantId());
        if (RspHd.isFail(tenantRsp)) {
            return RspHd.fail(tenantRsp);
        }
        TenantInfoResult tenant = tenantRsp.getData();
        if (tenant == null) {
            return RspHd.fail("租户信息不存在");
        }
        MenuTenantType type = Objects.equals(tenant.getParentId(), Constant.LongNum.ZERO) ? MenuTenantType.MAIN : MenuTenantType.SUB;
        return tenantAuthMenuClient.menuTree(type);
    }

    @Operation(summary = "微前端子应用路由")
    @GetMapping("/frontendQiankunList")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<List<TenantFrontendQiankunRouteResult>> frontendQiankunList() {
        return tenantFrontendQiankunClient.routes();
    }

}
