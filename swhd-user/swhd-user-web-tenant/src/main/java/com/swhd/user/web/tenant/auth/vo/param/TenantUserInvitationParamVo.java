package com.swhd.user.web.tenant.auth.vo.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/3/21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserInvitationParamVo对象")
public class TenantUserInvitationParamVo {

    @NotNull(message = "租户id不能为空")
    @Schema(description = "租户id")
    private Long tenantId;

}
