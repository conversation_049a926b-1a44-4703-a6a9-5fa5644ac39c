package com.swhd.user.web.tenant.team.vo.result;

import com.swhd.user.api.tenant.constant.TeamMemberButton;
import com.swhd.user.api.tenant.dto.result.TenantTeamInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantTeamInfoMeResultVo对象")
public class TenantTeamInfoMeResultVo extends TenantTeamInfoResult {

    private List<TeamMemberButton> buttonCodes;

}
