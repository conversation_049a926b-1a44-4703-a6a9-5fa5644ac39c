package com.swhd.user.web.tenant.tenant.vo.param;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantInfoPageParam对象")
public class TenantInfoPageParamVo extends PageReq {

    @Schema(description = "租户id")
    private String id;

    @Schema(description = "父租户id，0是主租户")
    private Long parentId;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司简称")
    private String companyShortName;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "租户类型：1-个人，2-企业")
    private Integer tenantType;

}
