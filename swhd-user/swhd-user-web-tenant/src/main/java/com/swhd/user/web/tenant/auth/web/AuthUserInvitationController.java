package com.swhd.user.web.tenant.auth.web;

import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.web.log.utils.WebSecureUtil;
import com.swhd.user.api.tenant.client.TenantUserInvitationClient;
import com.swhd.user.api.tenant.dto.param.invitation.TenantUserInvitationAcceptAllParam;
import com.swhd.user.api.tenant.dto.param.invitation.TenantUserInvitationAcceptParam;
import com.swhd.user.api.tenant.dto.param.invitation.TenantUserInvitationRefuseParam;
import com.swhd.user.api.tenant.dto.result.TenantUserInvitationResult;
import com.swhd.user.api.tenant.wrapper.TenantInfoWrapper;
import com.swhd.user.web.tenant.auth.vo.param.TenantUserInvitationParamVo;
import com.swhd.user.web.tenant.auth.vo.result.OauthUnconfirmedTokenResultVo;
import com.swhd.user.web.tenant.auth.vo.result.TenantUserInvitationResultVo;
import com.swhd.user.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Slf4j
@RestController
@RequestMapping(WebConstant.BASE_PATH + "/authUserInvitation")
@AllArgsConstructor
@Auth(way = Auth.Way.ALL_USER)
public class AuthUserInvitationController {

    private final TenantUserInvitationClient tenantUserInvitationClient;

    @Operation(summary = "邀请未确认列表")
    @GetMapping("/unconfirmedList")
    public Rsp<List<TenantUserInvitationResultVo>> unconfirmedList() {
        Rsp<List<TenantUserInvitationResult>> listRsp = tenantUserInvitationClient.unconfirmedListByUserId(
                CurrentUserHolder.currentUserId());
        if (RspHd.isFail(listRsp)) {
            return RspHd.fail(listRsp);
        }
        if (Func.isEmpty(listRsp.getData())) {
            return RspHd.data(Collections.emptyList());
        }
        List<TenantUserInvitationResultVo> vos = Func.copy(listRsp.getData(), TenantUserInvitationResultVo.class);
        TenantInfoWrapper.getInstance().setList(vos, TenantUserInvitationResultVo::getTenantId,
                (vo, tenantInfo) -> {
                    vo.setTenantName(tenantInfo.getName());
                    vo.setCompanyName(tenantInfo.getCompanyName());
                    vo.setCompanyShortName(tenantInfo.getCompanyShortName());
                });
        return RspHd.data(vos);
    }

    @Operation(summary = "接受邀请")
    @PostMapping("/accept")
    public Rsp<Void> accept(@RequestBody @Valid TenantUserInvitationParamVo paramVo) {
        TenantUserInvitationAcceptParam param = new TenantUserInvitationAcceptParam()
                .setTenantId(paramVo.getTenantId())
                .setUserId(CurrentUserHolder.currentUserId());
        return tenantUserInvitationClient.accept(param);
    }

    @Operation(summary = "接受邀请(token)")
    @PostMapping("/acceptByToken")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<Void> acceptByToken(@RequestParam("token") String token,
                                       @RequestBody @Valid TenantUserInvitationParamVo paramVo) {
        OauthUnconfirmedTokenResultVo tokenVo = WebSecureUtil.jwtDecryptParam(token, OauthUnconfirmedTokenResultVo.class);
        TenantUserInvitationAcceptParam param = new TenantUserInvitationAcceptParam()
                .setTenantId(paramVo.getTenantId())
                .setUserId(tokenVo.getUserId());
        return tenantUserInvitationClient.accept(param);
    }

    @Operation(summary = "接受所有邀请")
    @PostMapping("/acceptAll")
    public Rsp<Void> acceptAll() {
        TenantUserInvitationAcceptAllParam param = new TenantUserInvitationAcceptAllParam()
                .setUserId(CurrentUserHolder.currentUserId());
        return tenantUserInvitationClient.acceptAll(param);
    }

    @Operation(summary = "接受所有邀请(token)")
    @PostMapping("/acceptAllByToken")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<Void> acceptAllByToken(@RequestParam("token") String token) {
        OauthUnconfirmedTokenResultVo tokenVo = WebSecureUtil.jwtDecryptParam(token, OauthUnconfirmedTokenResultVo.class);
        TenantUserInvitationAcceptAllParam param = new TenantUserInvitationAcceptAllParam()
                .setUserId(tokenVo.getUserId());
        return tenantUserInvitationClient.acceptAll(param);
    }

    @Operation(summary = "拒绝邀请")
    @PostMapping("/refuse")
    public Rsp<Void> refuse(@RequestBody @Valid TenantUserInvitationParamVo paramVo) {
        TenantUserInvitationRefuseParam param = new TenantUserInvitationRefuseParam()
                .setTenantId(paramVo.getTenantId())
                .setUserId(CurrentUserHolder.currentUserId());
        return tenantUserInvitationClient.refuse(param);
    }

    @Operation(summary = "拒绝邀请(token)")
    @PostMapping("/refuseByToken")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<Void> refuseByToken(@RequestParam("token") String token,
                            @RequestBody @Valid TenantUserInvitationParamVo paramVo) {
        OauthUnconfirmedTokenResultVo tokenVo = WebSecureUtil.jwtDecryptParam(token, OauthUnconfirmedTokenResultVo.class);
        TenantUserInvitationRefuseParam param = new TenantUserInvitationRefuseParam()
                .setTenantId(paramVo.getTenantId())
                .setUserId(tokenVo.getUserId());
        return tenantUserInvitationClient.refuse(param);
    }

}
