server:
  port: 9312

spring:
  application:
    name: swhd-user-web-tenant
  cloud:
    nacos:
      # 默认test环境
      server-addr: 10.101.23.45:8848,10.101.23.243:8848,10.101.23.12:8848
    stream:
      swhd-binder: rabbitSwj
      bindings:
        # 生产者：发放场景服务套餐
        serviceMarketAssignSceneServicePkg-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-service-market-assign-scene-service-pkg

magiccube:
  # 以下两个核心配置请务必根据 https://wiki.3weijia.com/pages/viewpage.action?pageId=33001928 进行调整
  application:
    # 应用主要维护人信息（必须修改）
    owner:
      - 美家平台事业部_钟廷员_13763340378
    # 应用对应业务码（必须修改）
    status-code: 11312


# 启用国际化
i18ncenter:
  i18n:
    enabled: true
    types: redis
    dynamic-translation-enabled: true
    exception:
      # 开始全局异常配置
      enable-global-handler: true
      # 异常动态翻译
      dynamic-translation-enabled: true