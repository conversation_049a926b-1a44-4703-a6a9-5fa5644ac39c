package com.swhd.user.auth.core.service;

import com.swhd.magiccube.core.auth.storage.AccessTokenStorage;
import com.swhd.magiccube.core.auth.storage.ITokenStorage;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.web.log.utils.WebSecureUtil;
import com.swhd.user.auth.core.dto.LoginUserDto;
import com.swhd.user.auth.core.dto.RefreshTokenJwtStorage;
import com.swhd.user.auth.core.dto.result.OauthTokenResult;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.RandomStringUtils;

import java.text.MessageFormat;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Supplier;

import static com.swhd.magiccube.core.auth.storage.ITokenStorage.STORAGE_KEY_SEPARATOR;
import static com.swj.magiccube.api.FrameworkStatusCode.SC_INTERNAL_SERVER_ERROR;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
public class AuthStorageService {

    private static final String SERVER_ERROR_MSG = "系统异常，请稍后重试";

    /**
     * 创建token
     *
     * @param loginUser        登录结果
     * @param deviceId         设备ID
     * @param tokenTime        token存活时间
     * @param refreshTokenTime refreshToken存活时间
     * @return OauthTokenResult
     */
    public OauthTokenResult token(@NotNull LoginUserDto loginUser,
                                  @NotNull String deviceId,
                                  @NotNull Duration tokenTime,
                                  @Nullable Duration refreshTokenTime) {
        if (Func.isBlank(loginUser.getTokenId())) {
            throw new ServiceException("tokenId不能为空");
        }
        boolean refreshToken = refreshTokenTime != null;
        // token
        AccessTokenStorage accessTokenStorage = genAccessToken(loginUser, deviceId, refreshToken);
        RedisUtil.set(getAccessTokenStorageKey(accessTokenStorage.getAccessTokenId()),
                accessTokenStorage, tokenTime);
        RefreshTokenJwtStorage refreshTokenStorage = null;
        if (refreshToken) {
            // 刷新token
            refreshTokenStorage = genRefreshToken(accessTokenStorage, refreshTokenTime);
        }
        return genOauthTokenResult(accessTokenStorage, refreshTokenStorage, tokenTime, refreshTokenTime);
    }

    /**
     * accessToken获取存储的登录数据
     *
     * @param accessToken accessToken
     * @return StorageTokenResult
     */
    public AccessTokenStorage getStorageByAccessToken(String accessToken) {
        String accessTokenId = getTokenId(accessToken);
        if (Func.isBlank(accessTokenId)) {
            return null;
        }
        return legalCheck(getStorageByAccessTokenId(accessTokenId), accessToken);
    }

    /**
     * accessToken获取存储的登录数据
     *
     * @param accessTokenId accessTokenId
     * @return StorageTokenResult
     */
    public AccessTokenStorage getStorageByAccessTokenId(String accessTokenId) {
        return RedisUtil.get(getAccessTokenStorageKey(accessTokenId), AccessTokenStorage.class);
    }

    /**
     * refreshToken获取存储的登录数据
     *
     * @param refreshToken refreshToken
     * @return StorageTokenResult
     */
    public RefreshTokenJwtStorage getStorageByRefreshToken(String refreshToken) {
        if (Func.isBlank(refreshToken)) {
            return null;
        }
        try {
            return WebSecureUtil.jwtDecryptParam(refreshToken, RefreshTokenJwtStorage.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 删除accessToken
     *
     * @param accessTokenId accessTokenId
     */
    public void removeAccessToken(String accessTokenId, Duration tokenRemoveBufferTime) {
        if (Func.isBlank(accessTokenId)) {
            return;
        }
        if (tokenRemoveBufferTime == null || tokenRemoveBufferTime.toMillis() <= 0) {
            RedisUtil.delete(getAccessTokenStorageKey(accessTokenId));
        } else {
            RedisUtil.expire(getAccessTokenStorageKey(accessTokenId), tokenRemoveBufferTime);
        }
    }

    private AccessTokenStorage genAccessToken(LoginUserDto loginUser, String deviceId, boolean refreshToken) {
        AccessTokenStorage accessTokenStorage = new AccessTokenStorage()
                .setDeviceId(deviceId)
                .setAccessTokenId(loginUser.getTokenId())
                .setAccessTokenRandom(RandomStringUtils.randomAlphanumeric(32))
                .setUserId(loginUser.getUserId())
                .setTeamId(loginUser.getTeamId())
                .setTenantId(loginUser.getTenantId())
                .setGroup(loginUser.getGroup())
                .setRefererUserId(loginUser.getRefererUserId());
        if (refreshToken) {
            accessTokenStorage.setRefreshTokenId(loginUser.getTokenId());
        }
        return accessTokenStorage;
    }

    private RefreshTokenJwtStorage genRefreshToken(AccessTokenStorage accessTokenStorage, Duration refreshTokenTime) {
        return new RefreshTokenJwtStorage()
                .setDeviceId(accessTokenStorage.getDeviceId())
                .setAccessTokenId(accessTokenStorage.getAccessTokenId())
                .setRefreshTokenId(accessTokenStorage.getRefreshTokenId())
                .setUserId(accessTokenStorage.getUserId())
                .setTeamId(accessTokenStorage.getTeamId())
                .setTenantId(accessTokenStorage.getTenantId())
                .setGroup(accessTokenStorage.getGroup())
                .setRefererUserId(accessTokenStorage.getRefererUserId())
                .setIatNowTime()
                .setExpDuration(refreshTokenTime);
    }

    private OauthTokenResult genOauthTokenResult(@NotNull AccessTokenStorage accessTokenStorage,
                                                 @Nullable RefreshTokenJwtStorage refreshTokenStorage,
                                                 @NotNull Duration tokenTime,
                                                 @Nullable Duration refreshTokenTime) {
        String accessToken = accessTokenStorage.getAccessTokenId() + STORAGE_KEY_SEPARATOR + accessTokenStorage.getAccessTokenRandom();

        OauthTokenResult oauthTokenResult = new OauthTokenResult()
                .setAccessToken(accessToken)
                .setExpiresIn((int) tokenTime.getSeconds())
                .setUserId(accessTokenStorage.getUserId())
                .setTeamId(accessTokenStorage.getTeamId())
                .setTenantId(accessTokenStorage.getTenantId())
                .setRefererUserId(accessTokenStorage.getRefererUserId());
        if (refreshTokenStorage != null) {
            String refreshToken = WebSecureUtil.jwtEncryptParam(refreshTokenStorage);
            oauthTokenResult.setRefreshToken(refreshToken);
        }
        if (refreshTokenTime != null) {
            oauthTokenResult.setRefreshExpiresIn((int) refreshTokenTime.getSeconds());
        }
        return oauthTokenResult;
    }

    public String getTokenId(String token) {
        if (Func.isBlank(token) || !token.contains(STORAGE_KEY_SEPARATOR)) {
            return null;
        }
        return token.substring(0, token.indexOf(STORAGE_KEY_SEPARATOR));
    }

    /**
     * token合法性校验
     */
    public <T extends ITokenStorage> T legalCheck(T t, String token) {
        if (t == null) {
            return null;
        }
        return t.legalCheck(token) ? t : null;
    }

    public String getAccessTokenStorageKey(String accessTokenId) {
        return String.format("swhd-user-auth:auth:accessToken:%s", accessTokenId);
    }

    public <T> T authUserLockCall(String sysTypeName, Long userId, Supplier<T> supplier) {
        return authLockCall(sysTypeName, "userId:" + userId, supplier);
    }

    public <T> T authLockCall(String sysTypeName, String lockKey, Supplier<T> supplier) {
        String key = String.format("swhd-user-auth:%s:auth:lock:%s", sysTypeName, lockKey);
        Lock lock = RedisUtil.getLock(key);
        try {
            if (!lock.tryLock(3, TimeUnit.SECONDS)) {
                throw new ServiceException("操作频繁，请稍后重试");
            }
            return supplier.get();
        } catch (InterruptedException e) {
            throw new ServiceException(SC_INTERNAL_SERVER_ERROR, SERVER_ERROR_MSG, e);
        } finally {
            try {
                lock.unlock();
            } catch (Throwable e) {
                // none
            }
        }
    }


}
