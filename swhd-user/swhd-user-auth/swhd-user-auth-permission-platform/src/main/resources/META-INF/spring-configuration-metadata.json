{"groups": [{"sourceType": "com.swhd.user.auth.permission.platform.properties.AuthPermissionProperties", "name": "auth.permission", "type": "com.swhd.user.auth.permission.platform.properties.AuthPermissionProperties"}], "properties": [{"sourceType": "com.swhd.user.auth.permission.platform.properties.AuthPermissionProperties", "defaultValue": true, "name": "auth.permission.use-gateway", "description": "使用gateway", "type": "java.lang.Bo<PERSON>an"}, {"sourceType": "com.swhd.user.auth.permission.platform.properties.AuthPermissionProperties", "defaultValue": "/**", "name": "auth.permission.interceptor-path-patterns", "description": "权限拦截器pathPatterns", "type": "java.lang.String"}]}