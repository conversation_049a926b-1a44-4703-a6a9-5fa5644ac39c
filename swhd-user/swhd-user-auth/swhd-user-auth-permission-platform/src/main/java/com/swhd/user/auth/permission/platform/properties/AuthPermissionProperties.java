package com.swhd.user.auth.permission.platform.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Getter
@Setter
@ConfigurationProperties(AuthPermissionProperties.PREFIX)
public class AuthPermissionProperties {

    public static final String PREFIX = "auth.permission";

    /**
     * 使用gateway
     */
    private Boolean useGateway = true;

    /**
     * 权限拦截器pathPatterns
     */
    private String interceptorPathPatterns = "/**";

}
