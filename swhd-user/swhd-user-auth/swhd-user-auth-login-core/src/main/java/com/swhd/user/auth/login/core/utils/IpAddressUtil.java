package com.swhd.user.auth.login.core.utils;

import cn.hutool.core.net.Ipv4Util;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.map.api.client.MapAddressApiClient;
import com.swhd.map.api.dto.result.MapAddressApiAddressParseResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.experimental.UtilityClass;

import static com.swj.magiccube.api.Constant.Str.EMPTY;

/**
 * <AUTHOR>
 * @since 2025/4/28
 */
@UtilityClass
public class IpAddressUtil {

    private static MapAddressApiClient mapAddressApiClient;

    private MapAddressApiClient getMapAddressApiClient() {
        if (mapAddressApiClient == null) {
            mapAddressApiClient = SpringUtil.getBean(MapAddressApiClient.class);
        }
        return mapAddressApiClient;
    }

    public String getIpBelong(String ip) {
        String ipBelong;
        if (Func.isBlank(ip)) {
            ipBelong = "未知";
        } else if (isInnerIp(ip)) {
            ipBelong = "内网";
        } else {
            Rsp<MapAddressApiAddressParseResult> ipRsp = getMapAddressApiClient().ipParse(ip);
            if (RspHd.isSuccess(ipRsp) && ipRsp.getData() != null) {
                MapAddressApiAddressParseResult address = ipRsp.getData();
                String province = address.getProvince() == null ? EMPTY : address.getProvince().getName();
                String city = address.getCity() == null ? EMPTY : address.getCity().getName();
                ipBelong = province + city;
            } else {
                ipBelong = "未知";
            }
        }
        return ipBelong;
    }

    private boolean isInnerIp(String ip) {
        try {
            return Ipv4Util.isInnerIP(ip);
        } catch (Exception e) {
            // 无法解析的IPv4都算内网
            return true;
        }
    }

}
