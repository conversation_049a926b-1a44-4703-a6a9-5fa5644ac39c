package com.swhd.user.auth.permission.tenant.interceptor;

import com.swhd.user.auth.permission.tenant.properties.AuthPermissionProperties;
import lombok.AllArgsConstructor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@AllArgsConstructor
public class AuthPermissionWebMvcConfigurer implements WebMvcConfigurer {

    private static final String[] EXCLUDE_PATH_PATTERNS = new String[]{
            "/",
            "/actuator/health/**",
            "/swagger-resources*",
            "/swagger-resources/**",
            "/v3/api-docs/**",
            "/v3/api-docs-ext/**",
            "/doc.html"
    };

    private final AuthPermissionInterceptor authPermissionInterceptor;

    private final AuthSubTenantInterceptor authSubTenantInterceptor;

    private final AuthPermissionProperties properties;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry
                .addInterceptor(authPermissionInterceptor)
                .addPathPatterns(properties.getInterceptorPathPatterns())
                .excludePathPatterns(EXCLUDE_PATH_PATTERNS)
                .order(100);
        registry
                .addInterceptor(authSubTenantInterceptor)
                .addPathPatterns(properties.getInterceptorPathPatterns())
                .excludePathPatterns(EXCLUDE_PATH_PATTERNS)
                .order(200);
    }

}
