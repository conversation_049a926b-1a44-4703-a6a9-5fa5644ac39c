<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.swhd</groupId>
        <artifactId>swhd-user-auth</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>swhd-user-auth-permission-tenant</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-user-auth-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-redis</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 源码插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
