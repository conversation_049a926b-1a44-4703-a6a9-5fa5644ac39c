package com.swhd.user.api.tenant.dto.param.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-27
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantSiteConfigSaveParam对象")
public class TenantSiteConfigSaveParam {

    @Schema(description = "自定义域名")
    private String domainName;

    @Schema(description = "网页标签logo")
    private String tagLogo;

    @Schema(description = "网页标签名")
    private String tagTitle;

    @Schema(description = "平台logo")
    private String platformLogo;

    @Schema(description = "平台(AI)logo")
    private String platformAiLogo;

}
