package com.swhd.user.api.common.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Getter
@Setter
@Schema(description = "SvgWechatCreateQrcodeResult对象")
public class SvgWechatCreateQrcodeResult {

    @Schema(description = "登录key")
    private String logkey;

    @Schema(description = "过期时间")
    protected Integer expireSeconds;

    @Schema(description = "二维码信息")
    protected String url;

}
