package com.swhd.user.api.tenant.dto.param.oauth;

import com.swhd.magiccube.core.constant.PatternConstant;
import com.swhd.user.api.tenant.dto.param.auth.info.TenantAuthInfoAddParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/5 9:00
 */

@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserOauthThirdAccountTenantParam对象")
public class TenantUserOauthThirdAccountTenantParam {

    @Schema(description = "授权类型：1-巨量引擎群峰市场，2-三维家")
    @NotNull(message = "授权类型不能为空")
    private Integer oauthType;

    @Schema(description = "第三方授权应用ID")
    private String oauthAppId;

    @Schema(description = "第三方授权账号")
    @NotEmpty(message = "第三方账号不能为空")
    private String oauthAccount;

    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "手机号格式不正确")
    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "租户功能授权")
    private List<TenantAuthInfoAddParam> authInfos;

    @Schema(description = "租户名称")
    @NotEmpty(message = "租户名称不为空")
    private String tenantName;

    @Schema(description = "租户类型：1-个人，2-企业")
    private Integer tenantType;

    @Schema(description = "父级租户ID")
    private Long parentTenantId;

    @Schema(description = "来源场景")
    private String sourceScene;

}
