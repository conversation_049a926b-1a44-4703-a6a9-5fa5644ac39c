package com.swhd.user.api.platform.dto.result;

import com.swhd.magiccube.core.annotation.JsonOss;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PlatformUserInfoResult对象")
public class PlatformUserInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "昵称")
    private String nickname;

    @JsonOss
    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
