package com.swhd.user.api.tenant.dto.result;

import com.swhd.magiccube.core.annotation.JsonOss;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserTenantResult对象")
public class TenantUserTenantSimpleResult {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @JsonOss
    @Schema(description = "头像")
    private String avatar;

}
