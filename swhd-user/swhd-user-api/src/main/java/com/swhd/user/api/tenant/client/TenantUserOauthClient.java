package com.swhd.user.api.tenant.client;

import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.oauth.*;
import com.swhd.user.api.tenant.dto.result.TenantUserOauthResult;
import com.swhd.user.api.tenant.dto.result.TenantUserOauthThirdAccountTenantResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-10
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantUserOauthClient.BASE_PATH)
public interface TenantUserOauthClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/user/oauth";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<TenantUserOauthResult>> page(@RequestBody @Valid TenantUserOauthPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<TenantUserOauthResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<TenantUserOauthResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid TenantUserOauthAddParam param);

    @Operation(summary = "新增(同时新增用户)")
    @PostMapping("/addAndUser")
    Rsp<Long> addAndUser(@RequestBody @Valid TenantUserOauthAddAndUserParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据第三方信息获取授权信息")
    @PostMapping("/getByOauthAccount")
    Rsp<TenantUserOauthResult> getByOauthAccount(@RequestBody @Valid GetByOauthAccountParam param);

    @Operation(summary = "第三方授权账号", description = "账号不存在创建账号")
    @PostMapping("/oauthThirdAccount")
    Rsp<Long> oauthThirdAccount(@RequestBody @Valid TenantUserOauthThirdAccountParam param);

    @Operation(summary = "第三方授权账号租户", description = "账号不存在创建账号，不存在租户创建新租户")
    @PostMapping("/oauthThirdAccountTenant")
    Rsp<TenantUserOauthThirdAccountTenantResult> oauthThirdAccountTenant(@RequestBody @Valid TenantUserOauthThirdAccountTenantParam param);

    @Operation(summary = "根据第三方信息获取授权信息列表")
    @PostMapping("/listByOauthAccount")
    Rsp<List<TenantUserOauthResult>> listByOauthAccount(@RequestBody @Valid ListByOauthAccountParam param);

    @Operation(summary = "检查是否存在某种类型的授权")
    @GetMapping("/checkOauthByType")
    Rsp<Boolean> checkOauthByType(@RequestParam("userId") Long userId, @RequestParam("oauthType") Integer oauthType);

    @Operation(summary = "取消某种类型的授权")
    @GetMapping("/cancelOauthByType")
    Rsp<Void> cancelOauthByType(@RequestParam("userId") Long userId, @RequestParam("oauthType") Integer oauthType);

}
