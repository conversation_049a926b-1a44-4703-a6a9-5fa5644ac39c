package com.swhd.user.api.tenant.client;

import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.role.TenantRoleInfoAddParam;
import com.swhd.user.api.tenant.dto.param.role.TenantRoleInfoPageParam;
import com.swhd.user.api.tenant.dto.param.role.TenantRoleInfoUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantRoleInfoResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantRoleInfoClient.BASE_PATH)
public interface TenantRoleInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/role/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<TenantRoleInfoResult>> page(@RequestBody @Valid TenantRoleInfoPageParam param);

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    Rsp<List<TenantRoleInfoResult>> list(@RequestBody @Valid TenantRoleInfoPageParam param);

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    Rsp<List<AntdSelectResult>> selectAll(@RequestParam(value = "roleType", required = false) Integer roleType);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<TenantRoleInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<TenantRoleInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid TenantRoleInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid TenantRoleInfoUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
