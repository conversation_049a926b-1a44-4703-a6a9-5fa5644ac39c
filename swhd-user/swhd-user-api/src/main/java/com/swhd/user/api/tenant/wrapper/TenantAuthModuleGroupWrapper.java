package com.swhd.user.api.tenant.wrapper;

import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swhd.user.api.tenant.client.TenantAuthModuleGroupClient;
import com.swhd.user.api.tenant.dto.result.TenantAuthModuleGroupResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
public class TenantAuthModuleGroupWrapper extends BaseLongApiWrapper<TenantAuthModuleGroupResult, TenantAuthModuleGroupResult> {

    @Getter
    private static final TenantAuthModuleGroupWrapper instance = new TenantAuthModuleGroupWrapper();

    private final TenantAuthModuleGroupClient tenantAuthModuleGroupClient;

    private TenantAuthModuleGroupWrapper() {
        this.tenantAuthModuleGroupClient = SpringUtil.getBean(TenantAuthModuleGroupClient.class);
    }

    @Override
    protected Rsp<TenantAuthModuleGroupResult> getRspById(Long id) {
        return tenantAuthModuleGroupClient.getById(id);
    }

    @Override
    protected Rsp<List<TenantAuthModuleGroupResult>> getRspByIds(Collection<Long> ids) {
        return tenantAuthModuleGroupClient.listByIds(ids);
    }

    @Override
    protected Long getListId(TenantAuthModuleGroupResult tenantAuthModuleGroupResult) {
        return tenantAuthModuleGroupResult.getId();
    }

}
