package com.swhd.user.api.platform.client;

import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.platform.dto.param.user.PlatformUserRoleSaveParam;
import com.swhd.user.api.platform.dto.result.PlatformUserRoleResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = PlatformUserRoleClient.BASE_PATH)
public interface PlatformUserRoleClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/platform/user/role";

    @Operation(summary = "根据用户ID获取角色")
    @GetMapping("/listByUserId")
    Rsp<List<PlatformUserRoleResult>> listByUserId(@RequestParam("userId") Long userId);

    @Operation(summary = "根据用户ID列表获取")
    @PostMapping("/listByUserIds")
    Rsp<List<PlatformUserRoleResult>> listByUserIds(@RequestBody @Valid @NotEmpty List<Long> userIds);

    @Operation(summary = "批量保存", description = "数据库与roleIds数据进行差异处理，删除roleIds里不存在的数据")
    @PostMapping("/saveBatch")
    Rsp<Void> saveBatch(@RequestBody @Valid PlatformUserRoleSaveParam param);

}
