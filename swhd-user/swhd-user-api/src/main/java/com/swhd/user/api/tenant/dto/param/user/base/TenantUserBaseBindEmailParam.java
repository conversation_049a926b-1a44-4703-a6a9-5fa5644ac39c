package com.swhd.user.api.tenant.dto.param.user.base;

import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.magiccube.core.constant.PatternConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserBaseBindEmailParam对象")
public class TenantUserBaseBindEmailParam {

    @Schema(description = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @LogMask(type = JsonMaskType.EMAIL)
    @Schema(description = "新邮箱")
    @Pattern(regexp = PatternConstant.EMAIL, message = "邮箱格式不正确")
    private String newEmail;

}
