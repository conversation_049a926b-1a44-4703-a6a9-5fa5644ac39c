package com.swhd.user.api.tenant.client;

import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.auth.module.group.TenantAuthModuleGroupAddParam;
import com.swhd.user.api.tenant.dto.param.auth.module.group.TenantAuthModuleGroupPageParam;
import com.swhd.user.api.tenant.dto.param.auth.module.group.TenantAuthModuleGroupUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantAuthModuleGroupResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantAuthModuleGroupClient.BASE_PATH)
public interface TenantAuthModuleGroupClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/auth/module/group";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<TenantAuthModuleGroupResult>> page(@RequestBody @Valid TenantAuthModuleGroupPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<TenantAuthModuleGroupResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<TenantAuthModuleGroupResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    Rsp<List<AntdSelectResult>> selectAll();

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid TenantAuthModuleGroupAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid TenantAuthModuleGroupUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
