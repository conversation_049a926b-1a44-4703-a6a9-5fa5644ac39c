package com.swhd.user.api.tenant.dto.param.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantInfoUpdateParam对象")
public class TenantInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司简称")
    private String companyShortName;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "租户类型：1-个人，2-企业")
    private Integer tenantType;

    @Schema(description = "社会信用编码")
    private String creditCode;

}
