package com.swhd.user.api.tenant.dto.param.tenant;

import com.swhd.magiccube.core.constant.PatternConstant;
import com.swhd.user.api.tenant.dto.param.auth.info.TenantAuthInfoAddParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantInfoAddParam对象")
public class TenantInfoAddParam {

    @Schema(description = "父租户id，0是主租户")
    private Long parentId;

    @NotEmpty(message = "租户名称不能为空")
    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司简称")
    private String companyShortName;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "租户类型：1-个人，2-企业")
    private Integer tenantType;

    @Schema(description = "社会信用编码")
    private String creditCode;

    @Schema(description = "租户功能授权")
    private List<TenantAuthInfoAddParam> authInfos;

    @Schema(description = "管理员手机号")
    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "管理员手机号格式不正确")
    private String adminMobile;

    @Schema(description = "管理员姓名")
    private String adminName;

    @Schema(description = "管理员来源场景")
    private String adminSourceScene;

}
