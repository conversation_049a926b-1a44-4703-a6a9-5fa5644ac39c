package com.swhd.user.api.tenant.dto.param.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-04-17
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantAgentTenantUpdateParam对象")
public class TenantAgentTenantUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "用户id")
    private Long agentId;

    @Schema(description = "状态：0-禁用，1-启用，2-邀请中，3-拒绝")
    private Integer state;

    @Schema(description = "邀请id")
    private Long invitationId;

    @Schema(description = "备注")
    private String remark;

}
