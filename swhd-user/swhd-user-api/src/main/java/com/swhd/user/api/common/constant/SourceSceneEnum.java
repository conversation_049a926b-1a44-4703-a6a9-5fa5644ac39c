package com.swhd.user.api.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 来源场景枚举类
 * <AUTHOR> <EMAIL>
 * @since 2025/1/14
 */
@Getter
@AllArgsConstructor
public enum SourceSceneEnum {

    PLATFORM("platform", "运营后台"),
    MAIN_TENANT("main_tenant", "一级租户创建"),
    CUR_TENANT("cur_tenant", "当前租户创建"),
    FUWU_OCEANENGINE("fuwuOceanengine", "群峰服务市场"),
    SHOP("shop", "云店注册"),
    SVG("svg", "三维家注册"),
    MOBILE("mobile", "手机号注册"),
    WECHAT("wechat", "微信注册"),
    CRM("crm", "三维家CRM"),
    SELF_CREATE("self_create", "自建"),
    OPEN_PLATFORM("open_platform", "开放平台"),
    AI_HOUSE("ai_house", "AiHouse注册"),
    ;

    private String scene;
    private String desc;

}
