package com.swhd.user.api.tenant.wrapper;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swhd.user.api.tenant.client.TenantUserTenantClient;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
public class TenantUserInfoWrapper extends BaseLongApiWrapper<TenantUserTenantResult, TenantUserTenantResult> {

    @Getter
    private static final TenantUserInfoWrapper instance = new TenantUserInfoWrapper();

    private final TenantUserTenantClient tenantUserTenantClient;

    private TenantUserInfoWrapper() {
        this.tenantUserTenantClient = SpringUtil.getBean(TenantUserTenantClient.class);
    }

    @Override
    protected Rsp<TenantUserTenantResult> getRspById(Long userId) {
        return tenantUserTenantClient.getByUserId(userId);
    }

    @Override
    protected Rsp<List<TenantUserTenantResult>> getRspByIds(Collection<Long> userIds) {
        return tenantUserTenantClient.listByUserIds(userIds);
    }

    @Override
    protected Long getListId(TenantUserTenantResult result) {
        return result.getUserId();
    }

    public String getNicknameByUserId(Long userId) {
        Rsp<TenantUserTenantResult> userRsp = tenantUserTenantClient.getByUserId(userId);
        RspHd.failThrowException(userRsp);
        return Optional.ofNullable(userRsp.getData()).map(TenantUserTenantResult::getNickname).orElse(null);
    }

}
