package com.swhd.user.api.tenant.client;

import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.auth.module.info.TenantAuthModuleInfoAddParam;
import com.swhd.user.api.tenant.dto.param.auth.module.info.TenantAuthModuleInfoPageParam;
import com.swhd.user.api.tenant.dto.param.auth.module.info.TenantAuthModuleInfoUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantAuthModuleInfoResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-04
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantAuthModuleInfoClient.BASE_PATH)
public interface TenantAuthModuleInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/auth/module/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<TenantAuthModuleInfoResult>> page(@RequestBody @Valid TenantAuthModuleInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<TenantAuthModuleInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<TenantAuthModuleInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    Rsp<List<AntdSelectResult>> selectAll();

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid TenantAuthModuleInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid TenantAuthModuleInfoUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
