package com.swhd.user.api.platform.dto.param.user;

import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PlatformUserInfoUpdatePasswordParam对象")
public class PlatformUserInfoUpdatePasswordParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @LogMask(type = JsonMaskType.PASSWORD)
    @Schema(description = "旧密码")
    private String oldPassword;

    @LogMask(type = JsonMaskType.PASSWORD)
    @Schema(description = "新密码")
    private String newPassword;

}
