package com.swhd.user.api.tenant.dto.param.login.sub;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-09-27
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantSubLoginRecordAddParam对象")
public class TenantSubLoginRecordAddParam {

    @NotNull(message = "用户id不能为空")
    @Schema(description = "用户id")
    private Long userId;

    @NotNull(message = "子租户id不能为空")
    @Schema(description = "子租户id")
    private Long subTenantId;

    @NotNull(message = "子用户id不能为空")
    @Schema(description = "子用户id")
    private Long subUserId;

    @NotEmpty(message = "登陆tokenId不能为空")
    @Schema(description = "登陆token id")
    private String loginTokenId;

    @Schema(description = "IP地址")
    private String ip;

}
