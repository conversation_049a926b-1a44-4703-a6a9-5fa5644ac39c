package com.swhd.user.api.tenant.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-01-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantTeamMemberSumResult对象")
public class TenantTeamMemberCountResult {

    @Schema(description = "团队ID")
    private Long teamId;

    @Schema(description = "用户数")
    private Integer userNum;

}
