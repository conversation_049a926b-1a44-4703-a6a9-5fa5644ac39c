package com.swhd.user.api.tenant.dto.param.user.tenant;

import com.swhd.magiccube.core.constant.PatternConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserTenantAddUserParam对象")
public class TenantUserTenantAddUserParam {

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱")
    private String email;

    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "手机号格式不正确")
    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "来源场景")
    private String sourceScene;

    @Schema(title = "角色ID列表")
    private List<Long> roleIds;

    @NotEmpty(message = "请选择团队")
    @Schema(title = "团队主键ID列表")
    private List<Long> teamIds;

}
