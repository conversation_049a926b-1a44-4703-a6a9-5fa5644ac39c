package com.swhd.user.api.common.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@Getter
@Setter
@Schema(description = "UserLoginDeviceResult对象")
public class UserLoginDeviceResult {

    @Schema(description = "设备ID")
    private String id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "token id")
    private String tokenId;

    @Schema(description = "登陆IP")
    private String ip;

    @Schema(description = "登陆IP归属地")
    private String ipBelong;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
