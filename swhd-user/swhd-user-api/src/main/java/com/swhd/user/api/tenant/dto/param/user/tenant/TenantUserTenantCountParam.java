package com.swhd.user.api.tenant.dto.param.user.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 租户用户统计请求参数
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Data
@Accessors(chain = true)
@Schema(description = "租户用户统计请求参数")
public class TenantUserTenantCountParam implements Serializable {

    @Schema(description = "用户状态列表")
    private List<Integer> states;

} 