package com.swhd.user.api.tenant.client;

import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.common.dto.result.MenuInfoTreeSimpleResult;
import com.swj.i18ncentermagiccubeV2sdk.annotation.I18nMethod;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantUserMenuClient.BASE_PATH)
public interface TenantUserMenuClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/user/menu";

    @Operation(summary = "用户菜单树")
    @GetMapping("/menus")
    Rsp<List<MenuInfoTreeSimpleResult>> menus(@RequestParam("userId") Long userId,
                                              @RequestParam("teamId") Long teamId);

    @I18nMethod
    @Operation(summary = "应用所有菜单树")
    @GetMapping("/appAllMenus")
    Rsp<List<MenuInfoTreeSimpleResult>> appAllMenus(@RequestParam("menuCode") String menuCode,
                                                    @RequestParam(value = "domainTenantId", required = false) Long domainTenantId);

    @Operation(summary = "用户按钮code列表")
    @GetMapping("/buttonCodes")
    Rsp<List<String>> buttonCodes(@RequestParam("userId") Long userId,
                                  @RequestParam("teamId") Long teamId);

    @Operation(summary = "用户权限url列表")
    @GetMapping("/permissionUrls")
    Rsp<List<String>> permissionUrls(@RequestParam("userId") Long userId,
                                     @RequestParam("teamId") Long teamId);

}
