package com.swhd.user.api.tenant.dto.result;

import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserBaseGetResult对象")
public class TenantUserBaseGetResult {

    @Schema(description = "用户id")
    private Long userId;

    @LogMask(type = JsonMaskType.PASSWORD)
    @Schema(description = "密码")
    private String password;

}
