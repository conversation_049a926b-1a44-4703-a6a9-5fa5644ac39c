package com.swhd.user.api.tenant.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserInvitationNoticeRecordResult对象")
public class TenantUserInvitationNoticeRecordResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private String mobile;

    @Schema(description = "状态：0-未发送，1-发送成功，2-发送失败")
    private Integer state;

    @Schema(description = "通结果消息")
    private String noticeResponseMsg;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
