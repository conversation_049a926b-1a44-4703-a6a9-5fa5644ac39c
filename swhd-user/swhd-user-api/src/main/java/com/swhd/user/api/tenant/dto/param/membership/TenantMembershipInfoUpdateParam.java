package com.swhd.user.api.tenant.dto.param.membership;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-29
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantMembershipInfoUpdateParam对象")
public class TenantMembershipInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "会员code")
    private String membershipCode;

    @Schema(description = "开始时间")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    private LocalDate endDate;

    @Schema(description = "备注")
    private String remark;

}
