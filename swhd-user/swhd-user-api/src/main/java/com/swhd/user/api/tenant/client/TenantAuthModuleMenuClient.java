package com.swhd.user.api.tenant.client;

import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.auth.module.menu.TenantAuthModuleMenuBatchSaveParam;
import com.swhd.user.api.tenant.dto.result.TenantAuthModuleMenuResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-10
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantAuthModuleMenuClient.BASE_PATH)
public interface TenantAuthModuleMenuClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/auth/module/menu";

    @Operation(summary = "根据模块id获取")
    @GetMapping("/listByModuleId")
    Rsp<List<TenantAuthModuleMenuResult>> listByModuleId(@RequestParam("moduleId") Long moduleId);

    @Operation(summary = "批量保存")
    @PostMapping("/batchSave")
    Rsp<Void> batchSave(@RequestBody @Valid TenantAuthModuleMenuBatchSaveParam param);

}
