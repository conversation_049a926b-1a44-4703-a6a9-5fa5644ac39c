package com.swhd.user.api.platform.dto.param.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PlatformUserRoleSaveParam对象")
public class PlatformUserRoleSaveParam {

    @NotNull(message = "用户ID不能为空")
    @Schema(title = "用户ID")
    private Long userId;

    @NotNull(message = "角色ID列表不能为空")
    @Schema(title = "角色ID列表")
    private List<Long> roleIds;

}
