package com.swhd.user.api.platform.dto.result;

import com.swhd.user.api.common.dto.result.UserLoginDeviceResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PlatformUserLoginRecordResult对象")
public class PlatformUserLoginRecordResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "ip地址")
    private String ip;

    @Schema(description = "ip归属地")
    private String ipBelong;

    @Schema(description = "登陆设备信息")
    private UserLoginDeviceResult deviceInfo;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
