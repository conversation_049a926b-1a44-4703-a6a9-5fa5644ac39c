package com.swhd.user.api.tenant.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/8
 */
@Getter
public enum TeamMemberButton {

    /**
     * 团队信息修改
     */
    TEAM_INFO_UPDATE("user#teamInfoUpdate","user#teamManagement"),

    /**
     * 团队成员新增
     */
    TEAM_MEMBER_ADD("user#teamMemberMeAdd","user#teamManagement"),
    /**
     * 团队成员修改
     */
    TEAM_MEMBER_UPDATE("user#teamMemberMeUpdate","user#teamManagement"),
    /**
     * 团队成员删除
     */
    TEAM_MEMBER_REMOVE("user#teamMemberMeRemove","user#teamManagement"),
    /**
     * 团队资产新增
     */
    TEAM_ASSET_ADD("mcn#teamAssetsAdd"),
    /**
     * 团队资产修改
     */
    TEAM_ASSET_UPDATE("mcn#teamAssetsUpdate"),
    /**
     * 团队资产删除
     */
    TEAM_ASSET_REMOVE("mcn#teamAssetsRemove"),
    /**
     * 团队权益
     */
    TEAM_RIGHTS("user#teamRights","user#teamManagement"),
    /**
     * 团队权益购买
     */
    TEAM_RIGHTS_BUY("user#teamRightsBuy","user#teamManagement"),
    ;

    private final List<String> buttonCodes;

    TeamMemberButton(String... buttonCodes) {
        this.buttonCodes = Arrays.asList(buttonCodes);
    }

}
