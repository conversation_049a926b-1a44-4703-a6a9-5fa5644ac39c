package com.swhd.user.api.tenant.dto.param.oauth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-10-10
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserOauthUpdateParam对象")
public class TenantUserOauthUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "授权类型：1-巨量引擎群峰市场，2-三维家")
    private Integer oauthType;

    @Schema(description = "第三方授权应用ID")
    private String oauthAppId;

    @Schema(description = "第三方授权账号")
    private String oauthAccount;

}
