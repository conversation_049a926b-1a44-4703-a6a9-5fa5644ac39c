package com.swhd.crm.service.custom.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.crm.api.custom.client.CustomContractClient;
import com.swhd.crm.api.custom.dto.param.contract.CustomContractAddParam;
import com.swhd.crm.api.custom.dto.param.contract.CustomContractPageParam;
import com.swhd.crm.api.custom.dto.result.CustomContractResult;
import com.swhd.crm.service.custom.entity.CustomContract;
import com.swhd.crm.service.custom.service.CustomContractService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-23
 */
@RestController
@AllArgsConstructor
@RequestMapping(CustomContractClient.BASE_PATH)
public class CustomContractController implements CustomContractClient {

    private final CustomContractService customContractService;

    @Override
    public Rsp<PageResult<CustomContractResult>> page(CustomContractPageParam param) {
        IPage<CustomContract> iPage = customContractService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, CustomContractResult.class));
    }

    @Override
    public Rsp<CustomContractResult> getById(Long id) {
        CustomContract entity = customContractService.getById(id);
        return RspHd.data(Func.copy(entity, CustomContractResult.class));
    }

    @Override
    public Rsp<List<CustomContractResult>> listByIds(Collection<Long> ids) {
        List<CustomContract> list = customContractService.listByIds(ids);
        return RspHd.data(Func.copy(list, CustomContractResult.class));
    }

    @Override
    public Rsp<CustomContractResult> getLatestByTypeAndCustomId(Integer type, Long customId) {
        CustomContract entity = customContractService.getLatestByTypeAndCustomId(type, customId);
        return RspHd.data(Func.copy(entity, CustomContractResult.class));
    }

    @Override
    public Rsp<List<CustomContractResult>> listLatestByCustomId(Long customId) {
        List<CustomContract> list = customContractService.listLatestByIds(null, List.of(customId));
        return RspHd.data(Func.copy(list, CustomContractResult.class));
    }

    @Override
    public Rsp<List<CustomContractResult>> listLatestByTypeCustomIds(Integer type, Collection<Long> customIds) {
        List<CustomContract> list = customContractService.listLatestByIds(type, customIds);
        return RspHd.data(Func.copy(list, CustomContractResult.class));
    }

    @Override
    public Rsp<List<CustomContractResult>> listLatestByCustomIds(Collection<Long> customIds) {
        List<CustomContract> list = customContractService.listLatestByIds(null, customIds);
        return RspHd.data(Func.copy(list, CustomContractResult.class));
    }

    @Override
    public Rsp<Void> add(CustomContractAddParam param) {
        return customContractService.add(param);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = customContractService.removeByIds(ids);
        return RspHd.status(result);
    }

}
