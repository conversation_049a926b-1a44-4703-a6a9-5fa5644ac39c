package com.swhd.crm.service.custom.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.crm.api.custom.dto.param.contact.CustomContactPageParam;
import com.swhd.crm.api.custom.dto.param.info.CustomInfoContactSave;
import com.swhd.crm.service.custom.entity.CustomContact;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

import java.util.Collection;
import java.util.List;

/**
 * 客户联系人表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-19
 */
public interface CustomContactService extends IBaseHdService<CustomContact> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<CustomContact> page(CustomContactPageParam param);

    /**
     * 根据客户id获取
     *
     * @param customId 客户id
     * @return List
     */
    List<CustomContact> listByCustomId(Long customId);

    /**
     * 根据客户id列表获取
     *
     * @param customIds 客户id列表
     * @return List
     */
    List<CustomContact> listByCustomIds(Collection<Long> customIds);

    /**
     * 差异保存：
     * 1. 不存在id新增
     * 2. 修改带入id的数据
     * 3. 删除没有传入id的数据
     *
     * @param customId    客户id
     * @param contactList 联系人列表
     */
    void diffSave(Long customId, List<CustomInfoContactSave> contactList);

}
