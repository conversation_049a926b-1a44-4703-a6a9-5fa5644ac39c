<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.crm.service.custom.mapper.CustomContractMapper">

    <select id="listLatestByIds" resultType="com.swhd.crm.service.custom.entity.CustomContract">
        select cc.id,cc.tenant_id,cc.business_code,cc.custom_id,cc.type,cc.contract_name,cc.signing_date,cc.contract_oss_key,
            cc.creator_id,cc.create_time,cc.modifier_id,cc.modify_time,cc.is_delete
        from tcrm_custom_contract cc join (
            select tenant_id,custom_id,type,max(signing_date) as signing_date
            from tcrm_custom_contract
            where is_delete = 0
                <if test="type != null">
                    and type = #{type}
                </if>
                and custom_id in
                <foreach collection="customIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            group by tenant_id,custom_id,type
        ) s_cc on cc.tenant_id=s_cc.tenant_id and cc.custom_id=s_cc.custom_id and cc.type=s_cc.type and cc.signing_date=s_cc.signing_date
        where cc.is_delete = 0
    </select>

</mapper>
