package com.swhd.crm.web.tenant.custom.vo.param.info;

import com.swhd.crm.api.custom.dto.param.info.CustomInfoContactSave;
import com.swhd.crm.api.custom.dto.param.info.CustomInfoLiaisonSave;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CustomInfoAddParamVo对象")
public class CustomInfoAddParamVo {

    @NotEmpty(message = "客户名称不能为空")
    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "客户类型id：tagent_custom_type#id")
    private Long customTypeId;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "联系人名称")
    private String contactName;

    @Schema(description = "联系人手机号码")
    private String contactMobile;

    @Valid
    @Schema(description = "联系人列表")
    private List<CustomInfoContactSave> contactList;

    @Schema(description = "跟进人列表")
    private List<CustomInfoLiaisonSave> liaisonList;

}
