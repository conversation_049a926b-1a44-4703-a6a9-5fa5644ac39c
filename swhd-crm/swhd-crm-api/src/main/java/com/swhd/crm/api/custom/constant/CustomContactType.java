package com.swhd.crm.api.custom.constant;

import com.google.common.collect.Maps;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.tool.enumeration.EnumCodeDesc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/9/18 11:49
 */
@Getter
@AllArgsConstructor
public enum CustomContactType implements EnumCodeDesc<CustomContactType> {

    /**
     * 充值
     */
    RECHARGE("recharge", "充值"),
    /**
     * 风控
     */
    RISK_CONTROL("riskControl", "风控"),
    /**
     * 紧急
     */
    EMERGENCY("emergency", "紧急"),
    /**
     * 其他
     */
    OTHER("other", "其他"),

    ;

    private String code;

    private String desc;

    private static final Map<String, CustomContactType> CACHE;

    static {
        final Map<String, CustomContactType> map = Maps.newHashMapWithExpectedSize(values().length);
        for (CustomContactType value : values()) {
            map.put(value.code, value);
        }
        CACHE = Collections.unmodifiableMap(map);
    }

    public static CustomContactType of(String code) {
        return CACHE.get(StringUtils.lowerCase(code));
    }

    public static CustomContactType ofRequired(String code) {
        final CustomContactType type = of(code);
        if (Objects.nonNull(type)) {
            return type;
        }
        throw new ServiceException("不支持的客户联系人类型");
    }

}
