package com.swhd.crm.api.custom.dto.result;

import com.swhd.content.api.type.dto.result.TypeInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CustomInfoResult对象")
public class CustomInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "业务：agent-代理商客户，content_creative-内容创意客户")
    private String businessCode;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "客户名称拼音")
    private String namePinyin;

    @Schema(description = "客户名称拼音每个字的首字母")
    private String namePinyinInitial;

    @Schema(description = "客户类型id：tagent_custom_type#id")
    private Long customTypeId;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "客户类型")
    private TypeInfoResult customType;

}
