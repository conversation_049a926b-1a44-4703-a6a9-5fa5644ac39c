package com.swhd.crm.api.custom.dto.param.info;

import com.swhd.magiccube.core.constant.PatternConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CustomInfoContactSave对象")
public class CustomInfoContactSave {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "联系人名称")
    private String name;

    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "手机号格式不正确")
    @Schema(description = "联系人手机号码")
    private String mobile;

    @Schema(description = "联系人类型")
    private List<String> contactTypeCodes;

}
