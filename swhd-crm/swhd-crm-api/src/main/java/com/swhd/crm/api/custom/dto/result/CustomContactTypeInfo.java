package com.swhd.crm.api.custom.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/9/18 11:18
 */

@Data
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "CustomContactInfo对象")
public class CustomContactTypeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "联系人类型编码")
    private String contactTypeCode;

    @Schema(description = "联系人类型名称")
    private String contactTypeName;

}
