package com.swhd.crm.api.custom.dto.param.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2023-12-23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CustomContractAddParam对象")
public class CustomContractAddParam {

    @NotNull(message = "合同类型不能为空")
    @Schema(description = "合同类型：1-广告合同，2-授权协议")
    private Integer type;

    @NotNull(message = "客户id不能为空")
    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customId;

    @NotEmpty(message = "合同名称不能为空")
    @Schema(description = "合同名称")
    private String contractName;

    @NotNull(message = "签约日期不能为空")
    @Schema(description = "签约日期")
    private LocalDate signingDate;

    @Schema(description = "合同文件oss key")
    private String contractOssKey;

    @Schema(description = "合同编号")
    private String contractCode;

}
