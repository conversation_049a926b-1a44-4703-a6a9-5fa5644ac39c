package com.swhd.crm.api.custom.wrapper;

import cn.hutool.core.util.ObjectUtil;
import com.swhd.crm.api.custom.client.CustomContractClient;
import com.swhd.crm.api.custom.constant.CustomContractType;
import com.swhd.crm.api.custom.dto.result.CustomContractResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.Func;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
public class CustomContractWrapper {

    @Getter
    private static final CustomContractWrapper instance = new CustomContractWrapper();

    private final CustomContractClient customContractClient;

    private CustomContractWrapper() {
        this.customContractClient = SpringUtil.getBean(CustomContractClient.class);
    }

    public <T> T setInfoLatestByCustomId(T t, CustomContractType type, Function<T, Long> function, BiConsumer<T, CustomContractResult> consumer) {
        if (t == null) {
            return null;
        }
        Long customId = function.apply(t);
        if (Func.isEmpty(customId) || Objects.equals(customId, 0L)) {
            return t;
        }
        Rsp<CustomContractResult> rsp = customContractClient.getLatestByTypeAndCustomId(type.getType(), customId);
        RspHd.failThrowException(rsp);
        if (rsp.getData() != null) {
            consumer.accept(t, rsp.getData());
        }
        return t;
    }

    public <T> T setInfoLatestByCustomId(T t, Function<T, Long> function, BiConsumer<T, List<CustomContractResult>> consumer) {
        if (t == null) {
            return null;
        }
        Long customId = function.apply(t);
        if (Func.isEmpty(customId) || Objects.equals(customId, 0L)) {
            return t;
        }
        Rsp<List<CustomContractResult>> listRsp = customContractClient.listLatestByCustomId(customId);
        RspHd.failThrowException(listRsp);
        if (listRsp.getData() != null) {
            consumer.accept(t, listRsp.getData());
        }
        return t;
    }

    public <T> List<T> setListLatestByCustomIds(List<T> list, CustomContractType type, Function<T, Long> function, BiConsumer<T, CustomContractResult> consumer) {
        if (ObjectUtil.isEmpty(list)) {
            return list;
        }
        List<Long> customIds = list.stream()
                .map(function)
                .filter(id -> Func.isNotEmpty(id) && !Objects.equals(id, 0L))
                .distinct()
                .collect(Collectors.toList());
        if (ObjectUtil.isEmpty(customIds)) {
            return list;
        }
        Rsp<List<CustomContractResult>> listRsp = customContractClient.listLatestByTypeCustomIds(type.getType(), customIds);
        RspHd.failThrowException(listRsp);
        if (Func.isEmpty(listRsp.getData())) {
            return list;
        }
        Map<Long, CustomContractResult> resultMap = listRsp.getData().stream()
                .collect(Collectors.toMap(CustomContractResult::getCustomId, c -> c));
        list.stream()
                .filter(t -> resultMap.containsKey(function.apply(t)))
                .forEach(t -> consumer.accept(t, resultMap.get(function.apply(t))));
        return list;
    }

    public <T> List<T> setListLatestByCustomIds(List<T> list, Function<T, Long> function, BiConsumer<T, List<CustomContractResult>> consumer) {
        if (ObjectUtil.isEmpty(list)) {
            return list;
        }
        List<Long> customIds = list.stream()
                .map(function)
                .filter(id -> Func.isNotEmpty(id) && !Objects.equals(id, 0L))
                .distinct()
                .collect(Collectors.toList());
        if (ObjectUtil.isEmpty(customIds)) {
            return list;
        }
        Rsp<List<CustomContractResult>> listRsp = customContractClient.listLatestByCustomIds(customIds);
        RspHd.failThrowException(listRsp);
        if (Func.isEmpty(listRsp.getData())) {
            return list;
        }
        Map<Long, List<CustomContractResult>> resultMap = listRsp.getData().stream()
                .collect(Collectors.groupingBy(CustomContractResult::getCustomId));
        list.stream()
                .filter(t -> resultMap.containsKey(function.apply(t)))
                .forEach(t -> consumer.accept(t, resultMap.get(function.apply(t))));
        return list;
    }

}
