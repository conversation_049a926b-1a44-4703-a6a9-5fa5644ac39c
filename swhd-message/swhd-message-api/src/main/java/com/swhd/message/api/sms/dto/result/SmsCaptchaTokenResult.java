package com.swhd.message.api.sms.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SmsCaptchaTokenResult对象")
public class SmsCaptchaTokenResult {

    @Schema(description = "业务code")
    private String businessCode;

    @Schema(title ="手机号码")
    private String mobile;

}
