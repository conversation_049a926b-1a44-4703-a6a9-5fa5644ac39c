package com.swhd.message.api.captcha.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SmsCaptchaTokenResult对象")
public class CaptchaTokenResult {

    @Schema(title ="token")
    private String token;

}
