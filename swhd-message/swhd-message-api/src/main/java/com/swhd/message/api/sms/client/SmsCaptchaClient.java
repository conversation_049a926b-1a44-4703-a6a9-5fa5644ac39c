package com.swhd.message.api.sms.client;

import com.swhd.message.api.common.constant.ApiConstant;
import com.swhd.message.api.sms.dto.param.captcha.SmsCaptchaSendParam;
import com.swhd.message.api.sms.dto.param.captcha.SmsCaptchaVerifyParam;
import com.swhd.message.api.captcha.dto.result.CaptchaTokenResult;
import com.swhd.message.api.sms.dto.result.SmsCaptchaTokenResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = SmsCaptchaClient.BASE_PATH)
public interface SmsCaptchaClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/sms/captcha";

    @Operation(summary = "发送短信验证码")
    @PostMapping("/send")
    Rsp<Void> send(@RequestBody @Valid SmsCaptchaSendParam param);

    @Operation(summary = "校验短信验证码")
    @PostMapping("/verify")
    Rsp<Void> verify(@RequestBody @Valid SmsCaptchaVerifyParam param);

    @Operation(summary = "校验短信验证码并生成token")
    @PostMapping("/verifyAndGenToken")
    Rsp<CaptchaTokenResult> verifyAndGenToken(@RequestBody @Valid SmsCaptchaVerifyParam param);

    @Operation(summary = "token校验")
    @GetMapping("/verifyToken")
    Rsp<Void> verifyToken(@RequestParam("token") String token);

    @Operation(summary = "token获取数据")
    @GetMapping("/tokenGet")
    Rsp<SmsCaptchaTokenResult> tokenGet(@RequestParam("token") String token);

    @Operation(summary = "token获取数据并且删除", description = "token只能使用一次")
    @GetMapping("/tokenGetAndDelete")
    Rsp<SmsCaptchaTokenResult> tokenGetAndDelete(@RequestParam("token") String token);

}
