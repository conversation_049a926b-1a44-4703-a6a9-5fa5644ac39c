package com.swhd.message.web.tenant.behavior.web;

import com.swhd.magiccube.core.auth.Auth;
import com.swhd.message.api.behavior.client.BehaviorJigsawClient;
import com.swhd.message.api.behavior.dto.param.jigsaw.BehaviorJigsawGenerateParam;
import com.swhd.message.api.behavior.dto.param.jigsaw.BehaviorJigsawVerifyParam;
import com.swhd.message.api.behavior.dto.result.BehaviorJigsawInfoResult;
import com.swhd.message.api.behavior.dto.result.BehaviorJigsawVerifyResult;
import com.swhd.message.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.WebUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/12/24
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/behaviorJigsaw")
@Auth(way = Auth.Way.ANONYMOUS)
public class BehaviorJigsawController {

    private final BehaviorJigsawClient behaviorJigsawClient;

    @Operation(summary = "生成滑块")
    @GetMapping("/generate")
    public Rsp<BehaviorJigsawInfoResult> generate(HttpServletRequest request) {
        BehaviorJigsawGenerateParam param = new BehaviorJigsawGenerateParam();
        param.setIp(WebUtil.getIp(request));
        return behaviorJigsawClient.generate(param);
    }

    @Operation(summary = "验证滑块")
    @PostMapping("/verify")
    public Rsp<BehaviorJigsawVerifyResult> verify(@RequestBody @Valid BehaviorJigsawVerifyParam param) {
        return behaviorJigsawClient.verify(param);
    }

}
