package com.swhd.message.service.behavior.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/12/20
 */
@Getter
@Setter
@ConfigurationProperties(BehaviorProperties.PREFIX)
@Component
public class BehaviorProperties {

    public static final String PREFIX = "message.behavior";

    /**
     * 是否启用
     */
    private boolean enabled = Boolean.TRUE;

    /**
     * ip每分钟最大次数
     */
    private int ipMinuteMaxNum = 10;

    /**
     * ip每分钟最大次数
     */
    private int ipHourMaxNum = 200;

    /**
     * 坐标偏差值
     */
    private int pointOffset = 10;

    /**
     * 用户操作过期时长
     */
    private Duration userOperateExpireDuration = Duration.ofMinutes(1);

    /**
     * 用户验证过期时长
     */
    private Duration userVerifyExpireDuration = Duration.ofMinutes(2);

}
