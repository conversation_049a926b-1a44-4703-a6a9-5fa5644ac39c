package com.swhd.message.service.sms.provider.impl;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.magiccube.tool.MaskUtil;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swhd.message.api.sms.dto.result.SmsSendMsgResult;
import com.swhd.message.service.sms.properties.SmsBusinessConfig;
import com.swhd.message.service.sms.properties.SmsProperties;
import com.swhd.message.service.sms.properties.SmsProvider;
import com.swhd.message.service.sms.provider.SmsProviderApi;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.uuid.UUIDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.Closeable;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云短信
 *
 * <AUTHOR>
 * @since 2024/1/11
 */
@Slf4j
@Component
public class SmsProviderApiAliyunImpl implements SmsProviderApi, ApplicationContextAware, Closeable {

    private static final Map<String, Client> CLIENT_MAP = new ConcurrentHashMap<>();

    @Autowired
    private SmsProperties smsProperties;

    private ScheduledExecutorService executorService;

    @Override
    public SmsProvider provider() {
        return SmsProvider.ALIYUN;
    }

    @Override
    public Rsp<SmsSendMsgResult> sendMsg(SmsBusinessConfig config, String mobile, Map<String, String> templateParam) {
        try {
            Rsp<Client> clientRsp = getClient(config.getAccessKey());
            if (RspHd.isFail(clientRsp)) {
                return RspHd.fail(clientRsp);
            }

            SendSmsRequest request = new SendSmsRequest()
                    .setOutId(UUIDUtil.uuid())
                    .setPhoneNumbers(mobile)
                    .setTemplateCode(config.getTemplateCode())
                    .setSignName(config.getSignName())
                    .setTemplateParam(JsonUtil.toJsonString(Optional.ofNullable(templateParam).orElse(Collections.emptyMap())));

            SendSmsResponse response = clientRsp.getData().sendSms(request);
            SendSmsResponseBody responseBody = response.getBody();
            log.info("mobile：{}，outId：{}，发送结果：{}", MaskUtil.mobileMask(mobile),
                    request.getOutId(), JsonLogUtil.toJsonString(responseBody));
            if (!Objects.equals(responseBody.getCode(), "OK")) {
                return RspHd.fail("短信发送失败");
            }
            SmsSendMsgResult result = new SmsSendMsgResult();
            result.setRequestId(responseBody.getRequestId());
            return RspHd.success("短信发送成功", result);
        } catch (Exception e) {
            log.error("阿里云短信发送异常，mobile：{}", MaskUtil.mobileMask(mobile), e);
            return RspHd.fail("短信发送失败");
        }
    }

    private Rsp<Client> getClient(String accessKey) {
        Client client = CLIENT_MAP.get(accessKey);
        if (client != null) {
            return RspHd.data(client);
        }
        synchronized (CLIENT_MAP) {
            client = CLIENT_MAP.get(accessKey);
            if (client != null) {
                return RspHd.data(client);
            }

            String accessSecret = smsProperties.getProviderAccessConfig().get(accessKey);
            if (Func.isEmpty(accessSecret)) {
                log.error("阿里云accessKey[{}]对应的accessSecret未配置", accessKey);
                return RspHd.fail("短信配置数据异常");
            }

            try {
                client = new Client(new Config()
                        .setAccessKeyId(accessKey)
                        .setAccessKeySecret(accessSecret)
                        .setEndpoint(smsProperties.getAliyunEndpoint()));
            } catch (Exception e) {
                log.error("创建阿里云短信Client异常", e);
                return RspHd.fail("系统异常");
            }
            CLIENT_MAP.put(accessKey, client);

            return RspHd.data(client);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        executorService = new ScheduledThreadPoolExecutor(1,
                Thread.ofVirtual().name("smsProviderApiAliyunImpl").factory());
        executorService.scheduleAtFixedRate(() -> {
            // 清理无效的client
            List<String> accessKeyList = smsProperties.getBusinessConfig().values().stream()
                    .map(SmsBusinessConfig::getAccessKey)
                    .filter(Func::isNotBlank)
                    .distinct()
                    .toList();
            List<String> invalidAccessKeyList = CLIENT_MAP.keySet().stream()
                    .filter(key -> !accessKeyList.contains(key))
                    .toList();
            invalidAccessKeyList.forEach(CLIENT_MAP::remove);
        }, 10, 10, TimeUnit.MINUTES);
    }

    @Override
    public void close() throws IOException {
        if (executorService != null) {
            executorService.shutdown();
        }
    }

}
