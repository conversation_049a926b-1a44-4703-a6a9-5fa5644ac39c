package com.swhd.message.service.sms.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */
@Getter
@Setter
@ConfigurationProperties(SmsProperties.PREFIX)
@Component
public class SmsProperties {

    public static final String PREFIX = "message.sms";

    private String aliyunEndpoint = "dysmsapi.aliyuncs.com";

    private String tencentEndpoint = "sms.tencentcloudapi.com";

    /**
     * 业务code配置短信发送：Map<businessCode, SmsBusinessConfig>
     */
    private Map<String, SmsBusinessConfig> businessConfig = new HashMap<>();

    /**
     * 短信提供商access配置：Map<accessKey, accessSecret>
     */
    private Map<String, String> providerAccessConfig = new HashMap<>();

    /**
     * 短信提供商access配置：Map<accessKey, accessSecret>
     */
    @NestedConfigurationProperty
    private SmsCaptchaLimit captchaLimit = new SmsCaptchaLimit();

}
