package com.swj.api.ai.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Getter
@Setter
@Schema(description = "效果图搜索请求体")
@Accessors(chain = true)
public class SwjSearchImageReq {

    /**
     * 用户id
     */
    @NotEmpty
    @Schema(description = "用户id", example = "99e7335")
    private String userId;

    @Schema(description = "助手名称", example = "家装小助手")
    private String assistantName;

    @NotEmpty
    @Schema(description = "用户输入", example = "现代风格客厅效果图")
    private String query;

    @NotNull
    @Schema(description = "业务id", example = "997335")
    private Long businessId;

    @Schema(description = "会话id。注意：第一次对话时不需要携带，网关会在第一次对话后生成会话ID", example = "99e-7335-4b0a-bf98-3d8")
    private String conversationId;

    @Schema(description = "效果图搜索图库")
    private List<String> renderPartition = new ArrayList<>();

    /**
     * 添加partition
     */
    public void addRenderPartition(String partition) {
        if (StringUtils.isNoneEmpty(partition)) {
            renderPartition.add(partition);
        }
    }

}
