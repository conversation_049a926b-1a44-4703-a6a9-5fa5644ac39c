package com.swhd.creative.service.member.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.creative.api.member.dto.param.role.MemberRolePageParam;
import com.swhd.creative.service.member.entity.MemberRole;
import com.swhd.creative.service.member.mapper.MemberRoleMapper;
import com.swhd.creative.service.member.service.MemberRoleService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 创意成员角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@Service
@AllArgsConstructor
public class MemberRoleServiceImpl extends BaseHdServiceImpl<MemberRoleMapper, MemberRole> implements MemberRoleService {

    @Override
    public IPage<MemberRole> page(MemberRolePageParam param) {
        return lambdaQuery()
                .like(Func.isNotEmpty(param.getName()), MemberRole::getName, param.getName())
                .orderByDesc(MemberRole::getCreateTime)
                .orderByDesc(MemberRole::getId)
                .page(convertToPage(param));
    }

}
