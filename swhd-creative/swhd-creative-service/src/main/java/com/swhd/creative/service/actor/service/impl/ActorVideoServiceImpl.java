package com.swhd.creative.service.actor.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.dto.param.actor.video.ActorVideoPageParam;
import com.swhd.creative.service.actor.entity.ActorVideo;
import com.swhd.creative.service.actor.mapper.ActorVideoMapper;
import com.swhd.creative.service.actor.service.ActorVideoService;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 创意演员视频案例表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Service
@AllArgsConstructor
public class ActorVideoServiceImpl
        extends BaseHdServiceImpl<ActorVideoMapper, ActorVideo>
        implements ActorVideoService {

    @Override
    public IPage<ActorVideo> page(ActorVideoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getActorId()), ActorVideo::getActorId, param.getActorId())
                .orderByDesc(ActorVideo::getCreateTime)
                .orderByDesc(ActorVideo::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<ActorVideo> listByActorId(Long actorId) {
        return lambdaQuery().eq(ActorVideo::getActorId, actorId).list();
    }

    @Override
    public List<ActorVideo> listByActorIds(Collection<Long> actorIds) {
        if (Func.isEmpty(actorIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(ActorVideo::getActorId, actorIds).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByActorIds(Collection<Long> actorIds) {
        List<ActorVideo> list = listByActorIds(actorIds);
        if (Func.isEmpty(list)) {
            return;
        }
        this.removeByIds(list.stream().map(ActorVideo::getId).toList());
    }

}
