package com.swhd.creative.service.muse.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.content.api.oss.utils.OssPrivateHolder;
import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.creative.api.muse.client.MuseSwjSyncMediaClient;
import com.swhd.creative.api.muse.constant.MuseSyncFileType;
import com.swhd.creative.api.muse.constant.MuseSyncOperateType;
import com.swhd.creative.api.muse.constant.MuseSyncTaskState;
import com.swhd.creative.api.muse.dto.param.mywork.MuseMyWorkTenantPageParam;
import com.swhd.creative.api.muse.dto.param.swj.MuseSwjSyncMyWorkPageParam;
import com.swhd.creative.api.muse.dto.param.swj.MuseSwjSyncUploadMyMediaParam;
import com.swhd.creative.api.muse.dto.result.MuseMyWorkTenantDetailResult;
import com.swhd.creative.service.muse.entity.MuseAccountInfo;
import com.swhd.creative.service.muse.entity.MuseAccountTenant;
import com.swhd.creative.service.muse.entity.MuseMyMediaSyncTask;
import com.swhd.creative.service.muse.service.MuseAccountInfoService;
import com.swhd.creative.service.muse.service.MuseAccountTenantService;
import com.swhd.creative.service.muse.service.MuseMyMediaSyncTaskService;
import com.swhd.creative.service.muse.service.MuseMyWorkTenantService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(MuseSwjSyncMediaClient.BASE_PATH)
public class MuseSwjSyncMediaController implements MuseSwjSyncMediaClient {

    private static final Duration OSS_SIGN_DURATION = Duration.ofDays(30);

    private final MuseMyWorkTenantService museMyWorkTenantService;

    private final MuseMyMediaSyncTaskService museMyMediaSyncTaskService;

    private final MuseAccountTenantService museAccountTenantService;

    private final MuseAccountInfoService museAccountInfoService;

    @Override
    public Rsp<PageResult<MuseMyWorkTenantDetailResult>> myWorkPage(MuseSwjSyncMyWorkPageParam param) {
        MuseMyWorkTenantPageParam tenantPageParam = new MuseMyWorkTenantPageParam();
        tenantPageParam.setMediaId(param.getMediaId());
        tenantPageParam.setTitle(param.getTitle());
        tenantPageParam.setMediaCreateTimeBetween(param.getMediaCreateTimeBetween());
        tenantPageParam.setMediaUpdateTimeBetween(param.getMediaUpdateTimeBetween());
        tenantPageParam.setCurrent(param.getCurrent());
        tenantPageParam.setSize(param.getSize());
        IPage<MuseMyWorkTenantDetailResult> iPage;
        if (param.getTenantId() != null) {
            iPage = TenantHolder.methodTenant(param.getTenantId(), () -> museMyWorkTenantService.page(tenantPageParam));
        } else {
            iPage = TenantHolder.methodIgnoreTenant(() -> museMyWorkTenantService.page(tenantPageParam));
        }
        // oss文件签名
        OssPrivateHolder.methodTenantVoid(OSS_SIGN_DURATION, () -> OssPrivateUtil.preSignedUrlList(
                iPage.getRecords(),
                MuseMyWorkTenantDetailResult::getCoverUrl,
                MuseMyWorkTenantDetailResult::setCoverUrl,
                MuseMyWorkTenantDetailResult::getVideoUrl,
                MuseMyWorkTenantDetailResult::setVideoUrl));
        return RspHd.data(PageUtil.convertFromMyBatis(iPage));
    }

    @Override
    public Rsp<Void> uploadMyMedia(MuseSwjSyncUploadMyMediaParam param) {
        return TenantHolder.methodTenant(param.getTenantId(), () -> {
            MuseAccountTenant accountTenant = museAccountTenantService.getTenantDefault();
            if (accountTenant == null) {
                return RspHd.fail("租户未关联智能创作云默认账号");
            }
            MuseAccountInfo accountInfo = museAccountInfoService.getById(accountTenant.getAccountInfoId());
            if (accountInfo == null) {
                log.error("智能创作云账号信息ID[{}]数据不存在", accountTenant.getAccountInfoId());
                return RspHd.fail("智能创作云账号信息不存在");
            }
            if (Objects.equals(param.getOperateType(), MuseSyncOperateType.ADD)
                    && !Objects.equals(param.getFileType(), MuseSyncFileType.FOLDER)
                    && Func.isBlank(param.getFileUrl())) {
                return RspHd.fail("文件url不能为空");
            }
            MuseMyMediaSyncTask task = new MuseMyMediaSyncTask();
            task.setMuseUserId(accountInfo.getMuseUserId());
            task.setSwjFileId(param.getSwjFileId());
            task.setFilename(param.getFilename());
            task.setParentFolderList(param.getParentFolderList());
            task.setFileType(param.getFileType().getType());
            task.setOperateType(param.getOperateType().name());
            task.setFileUrl(param.getFileUrl());
            task.setTaskState(MuseSyncTaskState.WAIT.getState());
            boolean result = museMyMediaSyncTaskService.save(task);
            return RspHd.status(result);
        });
    }

}
