package com.swhd.creative.service.director.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.creative.api.director.dto.param.script.option.DirectorScriptOptionInfoPageParam;
import com.swhd.creative.api.director.dto.result.DirectorScriptOptionInfoResult;
import com.swhd.creative.service.director.entity.DirectorScriptOptionInfo;
import com.swhd.magiccube.mybatis.base.BaseHdMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 家居行业编导脚本选项信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
public interface DirectorScriptOptionInfoMapper extends BaseHdMapper<DirectorScriptOptionInfo> {

    IPage<DirectorScriptOptionInfoResult> page(@Param("tenantIds") List<Long> tenantIds,
                                               @Param("param") DirectorScriptOptionInfoPageParam param,
                                               @Param("page") Page<DirectorScriptOptionInfoResult> page);

}
