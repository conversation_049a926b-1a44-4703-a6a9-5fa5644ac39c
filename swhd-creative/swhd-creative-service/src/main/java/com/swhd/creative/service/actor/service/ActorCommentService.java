package com.swhd.creative.service.actor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.dto.param.actor.comment.ActorCommentAddParam;
import com.swhd.creative.api.actor.dto.param.actor.comment.ActorCommentPageParam;
import com.swhd.creative.service.actor.entity.ActorComment;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.Rsp;

import java.util.Collection;
import java.util.List;

/**
 * 创意演员评论表 服务类
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
public interface ActorCommentService extends IBaseHdService<ActorComment> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<ActorComment> page(ActorCommentPageParam param);

    /**
     * 根据演员id获取
     *
     * @param actorId 演员id
     * @return List
     */
    List<ActorComment> listByActorId(Long actorId);

    /**
     * 根据演员id统计数量
     *
     * @param actorId 演员id
     * @return 评论数
     */
    int countByActorId(Long actorId);

    /**
     * 根据演员id列表获取
     *
     * @param actorIds 演员id列表
     * @return List
     */
    List<ActorComment> listByActorIds(Collection<Long> actorIds);

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> add(ActorCommentAddParam param);

    /**
     * 根据演员id列表删除
     *
     * @param actorIds 演员id列表
     */
    void removeByActorIds(Collection<Long> actorIds);

    /**
     * 批量删除
     *
     * @param actorId 演员id
     * @param ids     主键id列表
     * @return Rsp
     */
    Rsp<Void> removeByIds(Long actorId, Collection<Long> ids);

}
