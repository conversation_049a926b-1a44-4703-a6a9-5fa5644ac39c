package com.swhd.creative.service.muse.web;

import com.swhd.creative.api.muse.client.MuseOpenCallbackClient;
import com.swhd.creative.api.muse.dto.message.MuseCallbackMediaMessage;
import com.swhd.creative.api.muse.dto.param.open.callback.MuseCallbackMediaParam;
import com.swhd.creative.service.muse.mq.producer.MuseOpenCallbackProducer;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@RestController
@AllArgsConstructor
@RequestMapping(MuseOpenCallbackClient.BASE_PATH)
public class MuseOpenCallbackController implements MuseOpenCallbackClient {

    @Override
    public Rsp<Void> callbackMedia(MuseCallbackMediaParam param) {
        MuseCallbackMediaMessage message = new MuseCallbackMediaMessage();
        message.setMediaId(param.getMediaId());
        message.setUserId(param.getUserId());
        message.setUpdateTime(param.getUpdateTime());
        MuseOpenCallbackProducer.callbackMedia(message);
        return RspHd.success();
    }

}
