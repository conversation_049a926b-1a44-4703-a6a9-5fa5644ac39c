package com.swhd.creative.service.muse.web;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.service.muse.api.properties.MuseApiProperties;
import com.swhd.creative.service.muse.api.properties.MuseIndexReplace;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.http.HttpClientRequestBuilder;
import com.swhd.magiccube.tool.http.HttpClientResponse;
import com.swhd.magiccube.tool.http.HttpClientUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.apache.hc.core5.http.Header;
import org.apache.hc.core5.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@RestController
@AllArgsConstructor
@RequestMapping(ApiConstant.BASE_PATH + "/muse/proxy")
public class MuseProxyController {

    private final MuseApiProperties apiProperties;

    @Operation(summary = "muse主页")
    @RequestMapping(
            value = {"/muse", "/muse/**"},
            method = {RequestMethod.GET, RequestMethod.POST}
    )
    public String muse(HttpServletRequest request, HttpServletResponse response) {
        StringBuilder urlSb = new StringBuilder(apiProperties.getWebUiUrl())
                .append("/muse");
        if (Func.isNotBlank(request.getQueryString())) {
            urlSb.append("?").append(request.getQueryString());
        }
        HttpClientRequestBuilder builder = HttpClientUtil.get(urlSb.toString());
        setRequestHeaders(builder, request);
        HttpClientResponse clientResponse = builder.execute().printlnFailErrorLog();
        String respBody = clientResponse.text();
        for (MuseIndexReplace replace :  apiProperties.getReplace()) {
            respBody = respBody.replace(replace.getTarget(), replace.getReplacement());
        }
        if (Func.isNotBlank(respBody) && Func.isNotBlank(apiProperties.getReplaceCss())) {
            respBody = respBody.replace("</head>",
                    "<link rel=\"stylesheet\" type=\"text/css\" href=\"" + apiProperties.getReplaceCss() + "\"></head>");
        }
        if (Func.isNotBlank(respBody) && Func.isNotBlank(apiProperties.getReplaceJs())) {
            respBody = respBody.replace("</body>",
                    "</body><script src=\"" + apiProperties.getReplaceJs() + "\"></script>");
        }

        setResponseHeaders(clientResponse.getHeaders(), response);

        return respBody;
    }

    private void setRequestHeaders(HttpClientRequestBuilder builder, HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (apiProperties.getNonForwardHeaders().stream()
                    .anyMatch(nonForwardHeader -> nonForwardHeader.equalsIgnoreCase(name))) {
                continue;
            }
            builder.setHeader(name, request.getHeader(name));
        }
        builder.setHeader(HttpHeaders.HOST, apiProperties.getWebUiHost());
        builder.removeHeaders(HttpHeaders.ACCEPT_ENCODING);
    }

    private void setResponseHeaders(Header[] headers, HttpServletResponse response) {
        Arrays.stream(headers)
                .filter(header -> !header.getName().equalsIgnoreCase(HttpHeaders.CONTENT_ENCODING))
                .filter(header -> !header.getName().equalsIgnoreCase("Transfer-Encoding"))
                .filter(header -> !header.getName().equalsIgnoreCase("Connection"))
                .forEach(header -> response.setHeader(header.getName(), header.getValue()));
    }

}
