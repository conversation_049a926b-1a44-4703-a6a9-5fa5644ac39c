package com.swhd.creative.service.muse.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.List;

import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 智能创作云我的作品实体类
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tcreative_muse_my_work", autoResultMap = true)
public class MuseMyWork extends BaseHdEntity {

    @Schema(description = "智能创作云用户ID")
    private Long museUserId;

    @Schema(description = "素材ID")
    private Long mediaId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "文件夹全路径")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<String> fullFolder;

    @Schema(description = "素材创建时间")
    private Long mediaCreateTime;

    @Schema(description = "素材修改时间")
    private Long mediaUpdateTime;

    @Schema(description = "封面图")
    private String coverUrl;

    @Schema(description = "视频URL")
    private String videoUrl;

    @Schema(description = "视频md5")
    private String videoMd5;

    @Schema(description = "视频时长，单位：ms")
    private Integer videoDuration;

    @Schema(description = "视频大小")
    private Long videoSize;

    @Schema(description = "视频宽")
    private Integer videoWidth;

    @Schema(description = "视频高")
    private Integer videoHeight;

    @Schema(description = "视频码率，单位：bps")
    private Integer videoBitrate;

}
