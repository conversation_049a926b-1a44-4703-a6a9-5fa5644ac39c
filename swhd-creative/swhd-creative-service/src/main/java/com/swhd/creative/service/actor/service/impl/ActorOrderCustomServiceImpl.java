package com.swhd.creative.service.actor.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.dto.param.actor.order.custom.ActorOrderCustomPageParam;
import com.swhd.creative.service.actor.entity.ActorOrderCustom;
import com.swhd.creative.service.actor.mapper.ActorOrderCustomMapper;
import com.swhd.creative.service.actor.service.ActorOrderCustomService;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 创意演员订单客户表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Service
@AllArgsConstructor
public class ActorOrderCustomServiceImpl
        extends BaseHdServiceImpl<ActorOrderCustomMapper, ActorOrderCustom>
        implements ActorOrderCustomService {

    @Override
    public IPage<ActorOrderCustom> page(ActorOrderCustomPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getOrderId()), ActorOrderCustom::getOrderId, param.getOrderId())
                .eq(Func.isNotEmpty(param.getCustomId()), ActorOrderCustom::getCustomId, param.getCustomId())
                .eq(Func.isNotEmpty(param.getCaptureNumber()), ActorOrderCustom::getCaptureNumber, param.getCaptureNumber())
                .orderByDesc(ActorOrderCustom::getCreateTime)
                .orderByDesc(ActorOrderCustom::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<ActorOrderCustom> listByOrderId(Long orderId) {
        return lambdaQuery().eq(ActorOrderCustom::getOrderId, orderId).list();
    }

    @Override
    public List<ActorOrderCustom> listByOrderIds(Collection<Long> orderIds) {
        if (Func.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(ActorOrderCustom::getOrderId, orderIds).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(Long orderId, List<ActorOrderCustom> orderCustomList) {
        if (orderCustomList == null) {
            return;
        }
        List<Long> customIds = orderCustomList.stream().map(ActorOrderCustom::getCustomId).toList();

        // 获取数据库存在的数据
        List<ActorOrderCustom> oldList = listByOrderId(orderId);
        List<Long> oldCustomIds = oldList.stream().map(ActorOrderCustom::getCustomId).toList();

        // 新增的数据
        List<ActorOrderCustom> saveList = orderCustomList.stream()
                .filter(orderCustom -> !oldCustomIds.contains(orderCustom.getCustomId()))
                .toList();
        this.saveBatch(saveList);

        // 修改
        List<ActorOrderCustom> updateList = orderCustomList.stream()
                .filter(orderCustom -> oldCustomIds.contains(orderCustom.getCustomId()))
                .peek(orderCustom -> oldList.stream()
                        .filter(old -> Objects.equals(orderCustom.getCustomId(), old.getCustomId()))
                        .forEach(old -> orderCustom.setId(old.getId())))
                .toList();
        this.updateBatchById(updateList);

        // 删除的数据
        List<Long> removeIdList = oldList.stream()
                .filter(old -> !customIds.contains(old.getCustomId()))
                .map(ActorOrderCustom::getId)
                .toList();
        this.removeByIds(removeIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByOrderIds(Collection<Long> orderIds) {
        List<ActorOrderCustom> list = listByOrderIds(orderIds);
        if (Func.isEmpty(list)) {
            return;
        }
        this.removeByIds(list.stream().map(ActorOrderCustom::getId).toList());
    }

}
