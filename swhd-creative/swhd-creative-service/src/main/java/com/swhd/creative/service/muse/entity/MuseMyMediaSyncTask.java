package com.swhd.creative.service.muse.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swhd.creative.api.muse.dto.result.MuseSwjParentFolder;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 智能创作云我的素材同步任务表实体类
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tcreative_muse_my_media_sync_task", autoResultMap = true)
public class MuseMyMediaSyncTask extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "智能创作云用户ID")
    private Long museUserId;

    @Schema(description = "三维家文件ID")
    private Long swjFileId;

    @Schema(description = "文件名称")
    private String filename;

    @Schema(description = "父文件夹路径")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<MuseSwjParentFolder> parentFolderList;

    @Schema(description = "文件类型，目前支持：video/audio/image/video-stream/folder")
    private String fileType;

    @Schema(description = "操作类型，ADD/UPDATE/DELETE")
    private String operateType;

    @Schema(description = "文件url")
    private String fileUrl;

    @Schema(description = "智能创作云文件（文件夹/素材）ID（同步成功）")
    private Long museFileId;

    @Schema(description = "任务状态：0-等待同步，1-同步中，2-同步成功，3-同步失败")
    private Integer taskState;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "失败次数")
    private Integer failNum;

}
