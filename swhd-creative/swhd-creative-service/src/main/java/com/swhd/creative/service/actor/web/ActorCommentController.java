package com.swhd.creative.service.actor.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.client.ActorCommentClient;
import com.swhd.creative.api.actor.dto.param.actor.comment.ActorCommentAddParam;
import com.swhd.creative.api.actor.dto.param.actor.comment.ActorCommentPageParam;
import com.swhd.creative.api.actor.dto.param.actor.comment.ActorCommentUpdateParam;
import com.swhd.creative.api.actor.dto.result.ActorCommentResult;
import com.swhd.creative.service.actor.entity.ActorComment;
import com.swhd.creative.service.actor.service.ActorCommentService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-21
 */
@RestController
@AllArgsConstructor
@RequestMapping(ActorCommentClient.BASE_PATH)
public class ActorCommentController implements ActorCommentClient {

    private final ActorCommentService actorCommentService;

    @Override
    public Rsp<PageResult<ActorCommentResult>> page(ActorCommentPageParam param) {
        IPage<ActorComment> iPage = actorCommentService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, ActorCommentResult.class));
    }

    @Override
    public Rsp<ActorCommentResult> getById(Long id) {
        ActorComment entity = actorCommentService.getById(id);
        return RspHd.data(Func.copy(entity, ActorCommentResult.class));
    }

    @Override
    public Rsp<List<ActorCommentResult>> listByIds(Collection<Long> ids) {
        List<ActorComment> list = actorCommentService.listByIds(ids);
        return RspHd.data(Func.copy(list, ActorCommentResult.class));
    }

    @Override
    public Rsp<Void> add(ActorCommentAddParam param) {
        return actorCommentService.add(param);
    }

    @Override
    public Rsp<Void> update(ActorCommentUpdateParam param) {
        boolean result = actorCommentService.updateById(Func.copy(param, ActorComment.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        List<ActorComment> commentList = actorCommentService.listByIds(ids);
        if (Func.isEmpty(commentList)) {
            return RspHd.success();
        }
        List<Long> actorIds = commentList.stream()
                .map(ActorComment::getActorId)
                .distinct()
                .toList();
        if (actorIds.size() > 1) {
            return RspHd.fail("不允许批量删除多个演员的评论");
        }
        return actorCommentService.removeByIds(actorIds.getFirst(), ids);
    }

}
