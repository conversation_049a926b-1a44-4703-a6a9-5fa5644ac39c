package com.swhd.creative.service.actor.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 创意演员评论表实体类
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcreative_actor_comment")
public class ActorComment extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "演员id")
    private Long actorId;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "评论人")
    private String commentator;

}
