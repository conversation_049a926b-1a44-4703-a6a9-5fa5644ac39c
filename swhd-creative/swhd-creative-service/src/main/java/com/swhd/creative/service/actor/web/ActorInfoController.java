package com.swhd.creative.service.actor.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.client.ActorInfoClient;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoAddParam;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoPageParam;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoUpdateParam;
import com.swhd.creative.api.actor.dto.result.ActorInfoResult;
import com.swhd.creative.service.actor.entity.ActorInfo;
import com.swhd.creative.service.actor.service.ActorInfoService;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-02-21
 */
@RestController
@AllArgsConstructor
@RequestMapping(ActorInfoClient.BASE_PATH)
public class ActorInfoController implements ActorInfoClient {

    private final ActorInfoService actorInfoService;

    @Override
    public Rsp<PageResult<ActorInfoResult>> page(ActorInfoPageParam param) {
        IPage<ActorInfo> iPage = actorInfoService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, ActorInfoResult.class));
    }

    @Override
    public Rsp<ActorInfoResult> getById(Long id) {
        ActorInfo entity = actorInfoService.getById(id);
        return RspHd.data(Func.copy(entity, ActorInfoResult.class));
    }

    @Override
    public Rsp<List<ActorInfoResult>> listByIds(Collection<Long> ids) {
        List<ActorInfo> list = actorInfoService.listByIds(ids);
        return RspHd.data(Func.copy(list, ActorInfoResult.class));
    }

    @Override
    public Rsp<List<AntdSelectResult>> selectAll() {
        List<ActorInfo> list = actorInfoService.lambdaQuery()
                .orderByDesc(ActorInfo::getCreateTime)
                .orderByDesc(ActorInfo::getId)
                .list();
        List<AntdSelectResult> antdSelectResultList = list.stream()
                .map(actor -> new AntdSelectResult(actor.getId(), String.format("%s(%s)", actor.getName(), actor.getStageName())))
                .toList();
        return RspHd.data(antdSelectResultList);
    }

    @Override
    public Rsp<Void> add(ActorInfoAddParam param) {
        return actorInfoService.add(param);
    }

    @Override
    public Rsp<Void> update(ActorInfoUpdateParam param) {
        return actorInfoService.update(param);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        return actorInfoService.remove(ids);
    }

}
