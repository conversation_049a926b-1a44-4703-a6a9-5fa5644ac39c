package com.swhd.creative.service.muse.service.impl;

import com.swhd.creative.api.muse.constant.MuseSyncTaskState;
import com.swhd.creative.service.muse.entity.MuseMyMediaSyncTask;
import com.swhd.creative.service.muse.mapper.MuseMyMediaSyncTaskMapper;
import com.swhd.creative.service.muse.service.MuseMyMediaSyncTaskService;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.swj.magiccube.api.Constant.Str.EMPTY;

/**
 * 智能创作云我的素材同步任务表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Service
@AllArgsConstructor
public class MuseMyMediaSyncTaskServiceImpl extends BaseHdServiceImpl<MuseMyMediaSyncTaskMapper, MuseMyMediaSyncTask>
        implements MuseMyMediaSyncTaskService {

    @Override
    public boolean lockTask(Long id) {
        return lambdaUpdate()
                .eq(MuseMyMediaSyncTask::getId, id)
                .in(MuseMyMediaSyncTask::getTaskState,
                        MuseSyncTaskState.WAIT.getState(), MuseSyncTaskState.FAIL.getState())
                .set(MuseMyMediaSyncTask::getTaskState, MuseSyncTaskState.ING.getState())
                .update();
    }

    @Override
    public void updateTaskState(Long id, MuseSyncTaskState state) {
        MuseMyMediaSyncTask task = new MuseMyMediaSyncTask();
        task.setId(id);
        task.setTaskState(state.getState());
        updateById(task);
    }

    @Override
    public void success(Long id, Long museFileId) {
        MuseMyMediaSyncTask task = new MuseMyMediaSyncTask();
        task.setId(id);
        task.setTaskState(MuseSyncTaskState.SUCCESS.getState());
        task.setMuseFileId(museFileId);
        task.setFailReason(EMPTY);
        updateById(task);
    }

    @Override
    public void fail(Long id, String failReason) {
        lambdaUpdate()
                .eq(MuseMyMediaSyncTask::getId, id)
                .set(MuseMyMediaSyncTask::getTaskState, MuseSyncTaskState.FAIL.getState())
                .set(MuseMyMediaSyncTask::getFailReason, Optional.ofNullable(failReason).orElse(EMPTY))
                .setSql("fail_num = fail_num + 1")
                .update();
    }

}
