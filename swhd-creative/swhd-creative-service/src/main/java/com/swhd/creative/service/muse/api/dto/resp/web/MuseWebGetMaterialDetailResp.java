package com.swhd.creative.service.muse.api.dto.resp.web;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
@Getter
@Setter
public class MuseWebGetMaterialDetailResp {

    @SerializedName("medias")
    private List<Medias> medias;

    @Getter
    @Setter
    public static class Medias {

        @SerializedName("aspect_ratio")
        private String aspectRatio;

        @SerializedName("can_not_be_downloaded")
        private Boolean canNotBeDownloaded;

        @SerializedName("cover_download_urls")
        private Map<String, String> coverDownloadUrls;

        @SerializedName("create_account_id")
        private String createAccountId;

        @SerializedName("create_time")
        private Long createTime;

        @SerializedName("create_user_id")
        private Long createUserId;

        @SerializedName("create_user_name")
        private String createUserName;

        private String description;

        private Integer duration;

        @SerializedName("dy_hot_music_count")
        private String dyHotMusicCount;

        @SerializedName("full_path")
        private List<String> fullPath;

        @SerializedName("media_extension")
        private String mediaExtension;

        @SerializedName("media_first_category")
        private String mediaFirstCategory;

        @SerializedName("media_id")
        private Long mediaId;

        @SerializedName("media_id_int64")
        private Long mediaIdInt64;

        @SerializedName("media_second_category")
        private String mediaSecondCategory;

        @SerializedName("modify_time")
        private Long modifyTime;

        @SerializedName("origin_template_id")
        private String originTemplateId;

        @SerializedName("show_user_name")
        private String showUserName;

        @SerializedName("source_from")
        private String sourceFrom;

        private Integer status;

        @SerializedName("store_id")
        private String storeId;

        @SerializedName("store_id_int64")
        private Long storeIdInt64;

        @SerializedName("store_info")
        private MuseWebStoreInfo storeInfo;

        @SerializedName("team_space_id")
        private Long teamSpaceId;

        @SerializedName("team_visibility")
        private Integer teamVisibility;

        private String title;

    }

}
