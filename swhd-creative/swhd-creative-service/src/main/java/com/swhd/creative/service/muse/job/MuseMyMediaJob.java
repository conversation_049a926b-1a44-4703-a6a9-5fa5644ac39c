package com.swhd.creative.service.muse.job;

import com.swhd.creative.api.muse.constant.MuseSyncFileType;
import com.swhd.creative.api.muse.constant.MuseSyncOperateType;
import com.swhd.creative.api.muse.constant.MuseSyncTaskState;
import com.swhd.creative.api.muse.dto.result.MuseSwjParentFolder;
import com.swhd.creative.service.muse.api.client.MuseMediaApiClient;
import com.swhd.creative.service.muse.api.client.MuseWebApiClient;
import com.swhd.creative.service.muse.api.dto.req.OwnerEntity;
import com.swhd.creative.service.muse.api.dto.req.media.MuseCreateUrlMaterialReq;
import com.swhd.creative.service.muse.api.dto.req.web.*;
import com.swhd.creative.service.muse.api.dto.resp.MuseResp;
import com.swhd.creative.service.muse.api.dto.resp.MuseWebResp;
import com.swhd.creative.service.muse.api.dto.resp.media.MuseCreateUrlMaterialResp;
import com.swhd.creative.service.muse.api.dto.resp.web.MuseWebCreateFolderResp;
import com.swhd.creative.service.muse.api.dto.resp.web.MuseWebFolderDetail;
import com.swhd.creative.service.muse.api.dto.resp.web.MuseWebGetMaterialDetailResp;
import com.swhd.creative.service.muse.api.dto.resp.web.MuseWebSearchListMediaResp;
import com.swhd.creative.service.muse.api.properties.MuseApiProperties;
import com.swhd.creative.service.muse.entity.MuseMyMediaSyncRelation;
import com.swhd.creative.service.muse.entity.MuseMyMediaSyncTask;
import com.swhd.creative.service.muse.service.MuseMyMediaSyncRelationService;
import com.swhd.creative.service.muse.service.MuseMyMediaSyncTaskService;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.xxl.job.core.ext.handler.annotation.SwjJob;
import jodd.util.ThreadUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Slf4j
@Component
@AllArgsConstructor
public class MuseMyMediaJob {

    /**
     * 我的素材顶级文件夹ID是：1
     */
    private static final Long TOP_FOLDER_ID = 1L;

    private final MuseMyMediaSyncTaskService museMyMediaSyncTaskService;

    private final MuseMyMediaSyncRelationService museMyMediaSyncRelationService;

    private final MuseWebApiClient museWebApiClient;

    private final MuseMediaApiClient museMediaApiClient;

    private final MuseApiProperties apiProperties;

    @SwjJob(jobDesc = "上传智能创作云我的素材", author = "zhongtingyuan",
            schedule = "${magiccube.job.schedule.MuseMyMediaJob.uploadMyMedia:0/30 * * * * ?}")
    public void uploadMyMedia() {
        if (!apiProperties.getUploadMyMedia()) {
            return;
        }
        List<MuseMyMediaSyncTask> list = TenantHolder.methodIgnoreTenant(() -> museMyMediaSyncTaskService.lambdaQuery()
                .in(MuseMyMediaSyncTask::getTaskState, MuseSyncTaskState.WAIT.getState(), MuseSyncTaskState.FAIL.getState())
                .lt(MuseMyMediaSyncTask::getFailNum, 3)
                .ge(MuseMyMediaSyncTask::getCreateTime, LocalDateTime.now().minusDays(3))
                .limit(30)
                .list());
        list.forEach(task -> TenantHolder.methodTenantVoid(task.getTenantId(), () -> {
            // 锁定任务状态
            boolean lockTask = museMyMediaSyncTaskService.lockTask(task.getId());
            if (!lockTask) {
                return;
            }
            try {
                Long parentFolderId = syncParentFolderList(task.getMuseUserId(), task.getParentFolderList());
                Long museFileId;
                if (Objects.equals(task.getFileType(), MuseSyncFileType.FOLDER.getType())) {
                    museFileId = syncFolder(task, parentFolderId);
                } else {
                    museFileId = syncMedia(task, parentFolderId);
                }
                museMyMediaSyncTaskService.success(task.getId(), museFileId);
            } catch (ServiceException e) {
                log.error("同步智能创作云我的素材失败，任务ID：{}, msg: {}", task.getId(), e.getMessage());
                museMyMediaSyncTaskService.fail(task.getId(), e.getMessage());
            } catch (Exception e) {
                log.error("同步智能创作云我的素材异常，任务ID：{}", task.getId(), e);
                museMyMediaSyncTaskService.fail(task.getId(), e.getMessage());
            }
        }));
    }

    /**
     * 同步素材
     */
    private Long syncMedia(MuseMyMediaSyncTask task, Long folderId) {
        Long museUserId = task.getMuseUserId();
        Long swjFileId = task.getSwjFileId();
        MuseMyMediaSyncRelation relation = museMyMediaSyncRelationService.getByMuseUserIdAndSwjFileId(museUserId, swjFileId);
        if (Objects.equals(task.getOperateType(), MuseSyncOperateType.DELETE.name())) {
            // 删除
            if (relation == null) {
                return null;
            }
            removeMedia(museUserId, relation.getMuseFileId(), relation.getId());
            return relation.getMuseFileId();
        }
        if (relation == null) {
            // 不存在关联，上传素材
            return uploadMedia(task, folderId);
        }
        MuseWebGetMaterialDetailResp.Medias mediaInfo = mediaInfo(museUserId, relation.getMuseFileId());
        if (mediaInfo == null) {
            // 存在关联但智能创作云素材信息不存在，上传素材
            return uploadMedia(task, folderId);
        }
        if (!Objects.equals(task.getFilename(), mediaInfo.getTitle())) {
            // 修改素材标题
            updateMedia(museUserId, mediaInfo.getMediaId(), task.getFilename());
        }
        List<String> folderNameList = Optional.ofNullable(task.getParentFolderList())
                .orElse(Collections.emptyList())
                .stream()
                .map(MuseSwjParentFolder::getTitle)
                .toList();
        if (!eq(mediaInfo.getFullPath(), folderNameList)) {
            // 修改素材路径
            updateMediaPath(museUserId, mediaInfo.getMediaId(), folderId);
        }
        return mediaInfo.getMediaId();
    }

    /**
     * 同步文件夹
     */
    private Long syncFolder(MuseMyMediaSyncTask task, Long parentFolderId) {
        Long museUserId = task.getMuseUserId();
        Long swjFileId = task.getSwjFileId();
        MuseMyMediaSyncRelation relation = museMyMediaSyncRelationService.getByMuseUserIdAndSwjFileId(museUserId, swjFileId);
        if (Objects.equals(task.getOperateType(), MuseSyncOperateType.DELETE.name())) {
            // 删除
            if (relation == null) {
                return null;
            }
            removeFolder(museUserId, relation.getMuseFileId(), relation.getId());
            return relation.getMuseFileId();
        }
        if (relation == null) {
            // 不存在关联，创建文件夹
            return createFolder(task.getMuseUserId(), parentFolderId, task.getFilename(), task.getSwjFileId());
        }
        MuseWebFolderDetail folderInfo = folderInfo(museUserId, relation.getMuseFileId());
        if (folderInfo == null) {
            // 存在关联但智能创作云文件夹信息不存在，创建文件夹
            return createFolder(museUserId, parentFolderId, task.getFilename(), task.getSwjFileId());
        }
        if (!Objects.equals(task.getFilename(), folderInfo.getTitle())) {
            // 修改文件夹标题
            updateFolder(museUserId, folderInfo.getFolderId(), task.getFilename());
        }
        if (!Objects.equals(folderInfo.getParentFolderId(), parentFolderId)) {
            // 修改文件夹路径
            updateFolderPath(museUserId, folderInfo.getFolderId(), parentFolderId);
        }
        return folderInfo.getFolderId();
    }

    /**
     * 同步父文件夹
     */
    private Long syncParentFolderList(Long museUserId, List<MuseSwjParentFolder> parentFolderList) {
        Long currentFolderId = TOP_FOLDER_ID;
        if (Func.isEmpty(parentFolderList)) {
            return currentFolderId;
        }
        for (MuseSwjParentFolder parentFolder : parentFolderList) {
            MuseMyMediaSyncRelation relation = museMyMediaSyncRelationService.getByMuseUserIdAndSwjFileId(
                    museUserId, parentFolder.getFolderId());
            if (relation == null) {
                // 不存在关联，创建文件夹
                currentFolderId = createFolder(museUserId, currentFolderId,
                        parentFolder.getTitle(), parentFolder.getFolderId());
                continue;
            }
            MuseWebFolderDetail folderInfo = folderInfo(museUserId, relation.getMuseFileId());
            if (folderInfo == null) {
                // 存在关联但智能创作云文件夹信息不存在，创建文件夹
                currentFolderId = createFolder(museUserId, currentFolderId,
                        parentFolder.getTitle(), parentFolder.getFolderId());
                continue;
            }
            currentFolderId = folderInfo.getFolderId();
            if (!Objects.equals(parentFolder.getTitle(), folderInfo.getTitle())) {
                // 修改文件夹标题
                updateFolder(museUserId, folderInfo.getFolderId(), parentFolder.getTitle());
            }
        }
        return currentFolderId;
    }

    private Long uploadMedia(MuseMyMediaSyncTask task, Long folderId) {
        MuseCreateUrlMaterialReq materialReq = new MuseCreateUrlMaterialReq();
        materialReq.setOwner(OwnerEntity.person(task.getMuseUserId()));
        MuseCreateUrlMaterialReq.CreateUrlMaterialInfo materialInfo = new MuseCreateUrlMaterialReq.CreateUrlMaterialInfo();
        materialInfo.setVisibility(0);
        materialInfo.setTitle(task.getFilename());
        materialInfo.setMediaFirstCategory(task.getFileType());
        materialInfo.setMaterialUrl(task.getFileUrl());
        materialInfo.setProviderMediaId(task.getSwjFileId().toString());
        materialInfo.setFolderId(Objects.equals(folderId, TOP_FOLDER_ID) ? null : folderId);
        materialReq.setCreateUrlMaterialInfo(materialInfo);
        MuseResp<MuseCreateUrlMaterialResp> resp = museMediaApiClient.createUrlMaterial(materialReq);
        if (resp.isFail()) {
            throw new ServiceException("URL素材导入失败：" + resp.getMessage());
        }
        Long museMediaId = Optional.ofNullable(resp.getResult())
                .map(MuseCreateUrlMaterialResp::getMediaId)
                .orElse(null);
        if (museMediaId != null) {
            // 保存关联关系
            MuseMyMediaSyncRelation relation = new MuseMyMediaSyncRelation();
            relation.setMuseUserId(task.getMuseUserId());
            relation.setMuseFileId(museMediaId);
            relation.setSwjFileId(task.getSwjFileId());
            relation.setFileType(task.getFileType());
            museMyMediaSyncRelationService.save(relation);
        }
        return museMediaId;
    }

    private void updateMedia(Long museUserId, Long mediaId, String title) {
        MuseWebUpdateMaterialReq req = new MuseWebUpdateMaterialReq();
        req.setMediaId(mediaId.toString());
        req.setTitle(title);
        MuseWebResp<Void> resp = museWebApiClient.updateMaterial(museUserId, req);
        if (resp.isFail()) {
            throw new ServiceException("修改素材失败：" + resp.getMessage());
        }
    }

    private void updateMediaPath(Long museUserId, Long mediaId, Long targetFolderId) {
        MuseWebUpdateFolderMediaPathReq updateFolderMediaPathReq = new MuseWebUpdateFolderMediaPathReq();
        updateFolderMediaPathReq.setFolderType(1);
        updateFolderMediaPathReq.setTargetFolderId(targetFolderId.toString());
        updateFolderMediaPathReq.setItemList(List.of(new MuseWebOperateItem(1, mediaId.toString())));
        MuseWebResp<Void> resp = museWebApiClient.updateFolderMediaPath(museUserId, updateFolderMediaPathReq);
        if (resp.isFail()) {
            throw new ServiceException("修改文件夹路径失败：" + resp.getMessage());
        }
    }

    private void removeMedia(Long museUserId, Long mediaId, Long relationId) {
        MuseWebBatchDeleteMediaReq req = new MuseWebBatchDeleteMediaReq();
        req.setItemList(List.of(new MuseWebOperateItem(1, mediaId.toString())));
        MuseWebResp<Object> resp = museWebApiClient.batchDeleteMedia(museUserId, req);
        if (resp.isFail()) {
            throw new ServiceException("删除素材失败：" + resp.getMessage());
        }
        // 删除关联
        museMyMediaSyncRelationService.removeById(relationId);
    }

    private MuseWebGetMaterialDetailResp.Medias mediaInfo(Long museUserId, Long mediaId) {
        MuseWebResp<MuseWebGetMaterialDetailResp> materialDetail = museWebApiClient.getMaterialDetail(museUserId,
                List.of(mediaId.toString()));
        if (materialDetail.isFail()) {
            throw new ServiceException("获取素材信息失败：" + materialDetail.getMessage());
        }
        if (materialDetail.getData() == null || Func.isEmpty(materialDetail.getData().getMedias())) {
            return null;
        }
        return materialDetail.getData().getMedias().getFirst();
    }

    private Long createFolder(Long museUserId, Long parentFolderId, String title, Long swjFolderId) {
        MuseWebCreateFolderReq req = new MuseWebCreateFolderReq();
        req.setTitle(title);
        req.setFolderType(1);
        req.setParentFolderId(parentFolderId.toString());
        req.setCreateFrom(0);
        req.setTeamVisibility(0);
        req.setTeamSpaceId("0");
        MuseWebResp<MuseWebCreateFolderResp> resp = museWebApiClient.createFolder(museUserId, req);
        if (resp.isFail()) {
            throw new ServiceException("创建文件夹失败：" + resp.getMessage());
        }
        if (resp.getData() == null || resp.getData().getFolderId() == null) {
            throw new ServiceException("创建文件夹失败");
        }

        // 保存关联
        MuseMyMediaSyncRelation relation = new MuseMyMediaSyncRelation();
        relation.setMuseUserId(museUserId);
        relation.setMuseFileId(resp.getData().getFolderId());
        relation.setSwjFileId(swjFolderId);
        relation.setFileType(MuseSyncFileType.FOLDER.getType());
        museMyMediaSyncRelationService.save(relation);

        return resp.getData().getFolderId();
    }

    private void updateFolder(Long museUserId, Long folderId, String title) {
        MuseWebUpdateFolderReq req = new MuseWebUpdateFolderReq();
        req.setFolderId(folderId.toString());
        req.setTitle(title);
        MuseWebResp<Void> resp = museWebApiClient.updateFolder(museUserId, req);
        if (resp.isFail()) {
            throw new ServiceException("修改文件夹失败：" + resp.getMessage());
        }
    }

    private void updateFolderPath(Long museUserId, Long folderId, Long targetFolderId) {
        MuseWebUpdateFolderMediaPathReq updateFolderMediaPathReq = new MuseWebUpdateFolderMediaPathReq();
        updateFolderMediaPathReq.setFolderType(1);
        updateFolderMediaPathReq.setTargetFolderId(targetFolderId.toString());
        updateFolderMediaPathReq.setItemList(List.of(new MuseWebOperateItem(2, folderId.toString())));
        MuseWebResp<Void> resp = museWebApiClient.updateFolderMediaPath(museUserId, updateFolderMediaPathReq);
        if (resp.isFail()) {
            throw new ServiceException("修改文件夹路径失败：" + resp.getMessage());
        }
    }

    private void removeFolder(Long museUserId, Long folderId, Long relationId) {
        MuseWebBatchDeleteMediaReq req = new MuseWebBatchDeleteMediaReq();
        req.setItemList(List.of(new MuseWebOperateItem(2, folderId.toString())));
        MuseWebResp<Object> resp = museWebApiClient.batchDeleteMedia(museUserId, req);
        if (resp.isFail()) {
            throw new ServiceException("删除文件夹失败：" + resp.getMessage());
        }
        // 删除关联
        museMyMediaSyncRelationService.removeById(relationId);
    }

    private MuseWebFolderDetail folderInfo(Long museUserId, Long folderId) {
        MuseWebSearchListMediaResp resp = searchFolderList(museUserId, folderId, 0, 18);
        return Optional.ofNullable(resp)
                .map(MuseWebSearchListMediaResp::getCurrentFolderInfo)
                .orElse(null);
    }

    private MuseWebSearchListMediaResp searchFolderList(Long museUserId, Long folderId, Integer page, Integer perpage) {
        MuseWebSearchListMediaReq req = new MuseWebSearchListMediaReq();
        req.setFieldInfos(List.of(
                new MuseWebSearchListMediaReq.FieldInfos("media_status", List.of(4)),
                new MuseWebSearchListMediaReq.FieldInfos("item_type", List.of("folder")),
                new MuseWebSearchListMediaReq.FieldInfos("team_space_id", List.of("0"))
        ));
        req.setFolderId(folderId.toString());
        req.setFolderType(1);
        req.setPage(page);
        req.setPerpage(perpage);
        req.setQuery("");
        req.setSortName("");
        req.setUserData(1);
        MuseWebResp<MuseWebSearchListMediaResp> resp = museWebApiClient.searchListMedia(museUserId, req);
        if (resp.isFail()) {
            ThreadUtil.sleep(500);
            resp = museWebApiClient.searchListMedia(museUserId, req);
            if (resp.isFail()) {
                ThreadUtil.sleep(500);
                resp = museWebApiClient.searchListMedia(museUserId, req);
                if (resp.isFail()) {
                    // 文件夹不存在会报错
                    log.warn("获取文件夹失败：{}", JsonLogUtil.toJsonString(resp));
                    return null;
                }
            }
        }
        return resp.getData();
    }

    private boolean eq(List<String> list1, List<String> list2) {
        if (Func.isEmpty(list1) && Func.isEmpty(list2)) {
            return true;
        }
        if (list1.size() != list2.size()) {
            return false;
        }
        for (int i = 0; i < list1.size(); i++) {
            if (!Objects.equals(list1.get(i).trim(), list2.get(i).trim())) {
                return false;
            }
        }
        return true;
    }

}
