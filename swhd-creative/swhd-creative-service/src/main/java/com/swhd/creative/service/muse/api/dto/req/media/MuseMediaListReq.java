package com.swhd.creative.service.muse.api.dto.req.media;

import com.google.gson.annotations.SerializedName;
import com.swhd.creative.service.muse.api.dto.req.BaseMuseReq;
import com.swhd.creative.service.muse.api.dto.req.OwnerEntity;
import com.swhd.creative.service.muse.api.dto.resp.media.MuseMediaListResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
@Getter
@Setter
@Accessors(chain = true)
public class MuseMediaListReq extends BaseMuseReq<MuseMediaListReq, MuseMediaListResp> {

    /**
     * 媒资归属的实体，即创作云用户，选填，如果带上就表示查用户的，如果不带就表示查整个租户的
     */
    @SerializedName("Owner")
    private OwnerEntity owner;

    /**
     * 团队空间 id，选填，如果带了就表示查询团队的作品，如果团队空间不存在会报错，如果传 0 表示查我的空间的内容
     */
    @SerializedName("TeamSpaceId")
    private Long teamSpaceId;

    /**
     * 1是素材，2是草稿，3是作品，目前只支持作品，素材
     */
    @SerializedName("MediaType")
    private Integer mediaType;

    /**
     * 第 n 页，从 0 开始，PageNum >= 0
     */
    @SerializedName("PageNum")
    private Integer pageNum;

    /**
     * 一页返回数量, PageSize >= 1, 默认为 10，最大支持 20
     */
    @SerializedName("PageSize")
    private Integer pageSize;

    @Override
    public String service() {
        return SERVICE_ICCLOUD_MUSE;
    }

    @Override
    public String action() {
        return "ListMediaInfoV2";
    }

}
