package com.swhd.creative.service.muse.mq.producer;

import com.swhd.creative.api.muse.dto.message.MuseCallbackMediaMessage;
import com.swj.magiccube.stream.messaging.MagiccubeMessageBuilder;
import com.swj.magiccube.stream.utils.StreamUtil;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@UtilityClass
public class MuseOpenCallbackProducer {

    /**
     * 智能创作云成片回调
     *
     * @param message 消息
     */
    public void callbackMedia(MuseCallbackMediaMessage message) {
        StreamUtil.send("creativeMuseOpenCallbackMedia-out-0", MagiccubeMessageBuilder
                .withPayload(message)
                .build());
    }

}
