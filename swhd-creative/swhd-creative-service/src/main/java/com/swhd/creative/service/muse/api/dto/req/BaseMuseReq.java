package com.swhd.creative.service.muse.api.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swhd.magiccube.core.annotation.GsonIgnore;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Getter
public abstract class BaseMuseReq<Req, Resp> {

    protected static final String SERVICE_IC_IAM = "ic_iam";

    protected static final String SERVICE_ICCLOUD_MUSE = "iccloud_muse";

    /**
     * 请求参数
     */
    @JsonIgnore
    @GsonIgnore
    private final Map<String, String> params = new HashMap<>();

    /**
     * 请求Service
     */
    public abstract String service();

    /**
     * 请求Action
     */
    public abstract String action();

    /**
     * 请求Version
     */
    public String version() {
        return "2022-02-01";
    }

    public Req putParams(String key, String value) {
        if (value == null) {
            return (Req) this;
        }
        this.params.put(key, value);
        return (Req) this;
    }

}
