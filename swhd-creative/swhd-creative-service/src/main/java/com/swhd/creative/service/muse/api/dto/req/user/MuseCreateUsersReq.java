package com.swhd.creative.service.muse.api.dto.req.user;

import com.google.gson.annotations.SerializedName;
import com.swhd.creative.service.muse.api.dto.req.BaseMuseReq;
import com.swhd.creative.service.muse.api.dto.resp.user.MuseListUsersResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Getter
@Setter
@Accessors(chain = true)
public class MuseCreateUsersReq extends BaseMuseReq<MuseCreateUsersReq, String> {

    @SerializedName("UserBasicInfo")
    private List<MuseUserBasicInfo> list;

    @Override
    public String service() {
        return SERVICE_IC_IAM;
    }

    @Override
    public String action() {
        return "CreateUsers";
    }

}
