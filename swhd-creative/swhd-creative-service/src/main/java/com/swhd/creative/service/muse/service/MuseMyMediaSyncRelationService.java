package com.swhd.creative.service.muse.service;

import com.swhd.creative.service.muse.entity.MuseMyMediaSyncRelation;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

/**
 * 智能创作云我的素材同步关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
public interface MuseMyMediaSyncRelationService extends IBaseHdService<MuseMyMediaSyncRelation> {

    /**
     * 获取
     *
     * @param museUserId 智能创作云用户ID
     * @param swjFileId  三维家文件ID
     * @return MuseMyMediaSyncRelation
     */
    MuseMyMediaSyncRelation getByMuseUserIdAndSwjFileId(Long museUserId, Long swjFileId);

    /**
     * 保存
     *
     * @param relation MuseMyMediaSyncRelation
     */
    void saveByMuseUserIdAndSwjFileId(MuseMyMediaSyncRelation relation);

}
