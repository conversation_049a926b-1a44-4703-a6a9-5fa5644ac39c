package com.swhd.creative.service.muse.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantAddParam;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantPageParam;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantUpdateParam;
import com.swhd.creative.service.muse.entity.MuseAccountTenant;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.Rsp;

import java.util.Collection;
import java.util.List;

/**
 * 智能创作云账号和租户关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
public interface MuseAccountTenantService extends IBaseHdService<MuseAccountTenant> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<MuseAccountTenant> page(MuseAccountTenantPageParam param);

    /**
     * 根据accountInfoId列表获取
     *
     * @param accountInfoIds 账号信息表id列表
     * @return List
     */
    List<MuseAccountTenant> listByAccountInfoIds(Collection<Long> accountInfoIds);

    /**
     * 获取租户默认数据
     *
     * @return MuseAccountTenant
     */
    MuseAccountTenant getTenantDefault();

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> add(MuseAccountTenantAddParam param);

    /**
     * 修改
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> update(MuseAccountTenantUpdateParam param);

}
