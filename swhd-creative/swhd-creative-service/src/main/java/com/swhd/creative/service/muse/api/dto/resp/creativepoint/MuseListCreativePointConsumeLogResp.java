package com.swhd.creative.service.muse.api.dto.resp.creativepoint;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Getter
@Setter
@Accessors(chain = true)
public class MuseListCreativePointConsumeLogResp {

    @SerializedName("Logs")
    private List<MuseCreativePointConsumeLog> logs;

    @SerializedName("Total")
    private Integer total;

}
