package com.swhd.creative.service.muse.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.swhd.creative.api.muse.dto.MuseContentItem;
import com.swhd.creative.api.muse.dto.param.log.MuseAccountPointConsumeLogAddParam;
import com.swhd.creative.api.muse.dto.param.log.MuseAccountPointConsumeLogBatchAddParam;
import com.swhd.creative.api.muse.dto.param.point.MuseAccountPointAddParam;
import com.swhd.creative.service.muse.api.client.MuseCreativePointApiClient;
import com.swhd.creative.service.muse.api.client.MuseWebApiClient;
import com.swhd.creative.service.muse.api.dto.req.creativepoint.MuseListCreativePointConsumeLogReq;
import com.swhd.creative.service.muse.api.dto.req.web.MuseWebPageAccountLimiterPolicyReq;
import com.swhd.creative.service.muse.api.dto.resp.MuseResp;
import com.swhd.creative.service.muse.api.dto.resp.MuseWebResp;
import com.swhd.creative.service.muse.api.dto.resp.creativepoint.MuseCreativePointConsumeLog;
import com.swhd.creative.service.muse.api.dto.resp.creativepoint.MuseListCreativePointConsumeLogResp;
import com.swhd.creative.service.muse.api.dto.resp.web.MuseWebPageAccountLimiterPolicyResp;
import com.swhd.creative.service.muse.service.MuseAccountPointConsumeLogService;
import com.swhd.creative.service.muse.service.MuseAccountPointService;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.ext.handler.annotation.SwjJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/29 11:43
 */

@Slf4j
@Component
@AllArgsConstructor
public class MuseCreativePointJob {

    private final MuseAccountPointConsumeLogService museAccountPointConsumeLogService;

    private final MuseAccountPointService museAccountPointService;

    private MuseCreativePointApiClient museCreativePointApiClient;

    private MuseWebApiClient museWebApiClient;

    @SwjJob(jobDesc = "同步创点消费日志", author = "zenghaoming",
            schedule = "${magiccube.job.schedule.MuseCreativePointJob.syncCreativePointConsumeLog:5 0/10 * * * ?}")
    public void syncCreativePointConsumeLog() {
        MuseListCreativePointConsumeLogReq logReq = new MuseListCreativePointConsumeLogReq();
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(param)) {
            logReq = JsonUtil.parseObject(param, MuseListCreativePointConsumeLogReq.class);
        }

        //没时间参数 默认当天的消费
        if (StrUtil.isEmpty(logReq.getFromDt()) || StrUtil.isEmpty(logReq.getToDt())) {
            logReq.setFromDt(String.valueOf(LocalDate.now().minusDays(50).atStartOfDay().toEpochSecond(ZoneOffset.UTC)));
            logReq.setToDt(String.valueOf(LocalDate.now().atTime(LocalTime.MAX).toEpochSecond(ZoneOffset.UTC)));
        }

        //请求muse接口
        MuseResp<MuseListCreativePointConsumeLogResp> logResp;
        boolean isSuccess;
        int page = 0;
        int loopNum = 0;
        int saveSize = 0;
        do {
            loopNum++;
            logResp = museCreativePointApiClient.listCreativePointConsumeLog(logReq);
            isSuccess = isFetchSuccess(logResp);
            if (isSuccess) {
                saveSize += saveConsumeLog(logResp.getResult().getLogs());
            } else if (logResp.isFail()) {
                log.error("同步创点消费日志获取数据失败，resp:{}", JsonUtil.toJsonString(logResp));
            }
            logReq.setPageNum(++page);
        } while (isSuccess && loopNum < 1000);

        log.info("同步创点消费日志，参数：{}，保存数：{}", JsonUtil.toJsonString(logReq), saveSize);
    }


    @SwjJob(jobDesc = "同步账号创点量", author = "zenghaoming",
            schedule = "${magiccube.job.schedule.MuseCreativePointJob.syncAccountLimiterPolicy:5 0/2 * * * ?}")
    public void syncAccountLimiterPolicy() {
        MuseWebPageAccountLimiterPolicyReq req = new MuseWebPageAccountLimiterPolicyReq();
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(param)) {
            req = JsonUtil.parseObject(param, MuseWebPageAccountLimiterPolicyReq.class);
        }
        syncAccountLimiterPolicy(req);
    }

    public void syncAccountLimiterPolicy(MuseWebPageAccountLimiterPolicyReq req) {
        MuseWebResp<MuseWebPageAccountLimiterPolicyResp> resp;
        boolean isSuccess;
        int page = 0;
        int loopNum = 0;
        do {
            loopNum++;
            resp = museWebApiClient.pageAccountLimiterPolicy(req);
            isSuccess = isFetchMuseWebSuccess(resp);
            if (isSuccess) {
                saveAccountPoint(resp);
            } else if (resp.isFail()) {
                log.error("同步账号创点量获取数据失败，resp:{}", JsonUtil.toJsonString(resp));
            }
            req.setPageNum(++page);
        } while (isSuccess && loopNum < 1000);
    }

    private void saveAccountPoint(MuseWebResp<MuseWebPageAccountLimiterPolicyResp> resp) {
        try {
            List<MuseAccountPointAddParam> addParams = resp.getData().getAllocateList().stream().map(d -> {
                MuseAccountPointAddParam addParam = new MuseAccountPointAddParam();
                addParam.setAccountId(d.getUserId());
                if (Objects.nonNull(d.getCreativePoint())) {
                    addParam.setCapacity(d.getCreativePoint().getQuota())
                            .setUsageCapacity(d.getCreativePoint().getUsed());
                }
                return addParam;
            }).toList();
            museAccountPointService.batchSaveOrUpdate(addParams);
        } catch (Exception e) {
            log.error("同步账号创点量,保存数据失败", e);
        }
    }

    private boolean isFetchMuseWebSuccess(MuseWebResp<MuseWebPageAccountLimiterPolicyResp> resp) {
        return Objects.nonNull(resp)
                && Objects.nonNull(resp.getData())
                && Func.isNotEmpty(resp.getData().getAllocateList());
    }


    private int saveConsumeLog(List<MuseCreativePointConsumeLog> logs) {
        try {
            MuseAccountPointConsumeLogBatchAddParam batchAddParam = new MuseAccountPointConsumeLogBatchAddParam();
            batchAddParam.setList(logs.stream().map(l -> {
                MuseAccountPointConsumeLogAddParam p = new MuseAccountPointConsumeLogAddParam();
                p.setConsumeUsage(l.getUsage());
                p.setEventTime(l.getEventTime());
                p.setContentItems(BeanUtil.copy(l.getContentItems(), MuseContentItem.class));
                p.setConsumeItemName(l.getConsumeItemName());
                p.setOperatorTeamNames(l.getOperatorTeamNames());
                p.setAccountId(l.getOperatorUserId());
                p.setAccountName(l.getOperatorUserName());
                return p;
            }).toList());
            return museAccountPointConsumeLogService.batchAdd(batchAddParam);
        } catch (Exception e) {
            log.error("同步创点消费日志，保存异常", e);
        }
        return 0;
    }

    private boolean isFetchSuccess(MuseResp<MuseListCreativePointConsumeLogResp> logResp) {
        return Objects.nonNull(logResp)
                && logResp.isSuccess()
                && Objects.nonNull(logResp.getResult())
                && CollectionUtil.isNotEmpty(logResp.getResult().getLogs());
    }


}
