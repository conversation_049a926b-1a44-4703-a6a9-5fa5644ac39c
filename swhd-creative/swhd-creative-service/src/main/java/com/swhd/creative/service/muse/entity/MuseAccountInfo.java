package com.swhd.creative.service.muse.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.creative.api.muse.dto.result.MuseApiUserRole;
import com.swhd.creative.api.muse.dto.result.MuseApiUserTeam;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 智能创作云账号信息表实体类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tcreative_muse_account_info", autoResultMap = true)
public class MuseAccountInfo extends BaseHdEntity {

    @Schema(description = "智能创作云用户id")
    private Long museUserId;

    @Schema(description = "是否授权子账号使用智创云 1-是 0-否")
    private Boolean permitted;

    @Schema(description = "是否为 admin 主账号 1-是 0-否")
    private Boolean admin;

    @Schema(description = "用户在火山侧的账号名")
    private String volcUserName;

    @Schema(description = "用户在火山侧的账号 id，如果有会返回")
    private String volcUserId;

    @Schema(description = "用户显示名")
    private String displayName;

    @Schema(description = "用户备注")
    private String description;

    @Schema(description = "所属团队信息")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<MuseApiUserTeam> teamInfos;

    @Schema(description = "所属角色信息")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<MuseApiUserRole> roleInfos;

}
