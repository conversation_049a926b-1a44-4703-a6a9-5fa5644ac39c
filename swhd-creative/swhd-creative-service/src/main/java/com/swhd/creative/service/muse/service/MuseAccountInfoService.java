package com.swhd.creative.service.muse.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.muse.dto.param.account.info.MuseAccountInfoAddParam;
import com.swhd.creative.api.muse.dto.param.account.info.MuseAccountInfoPageParam;
import com.swhd.creative.service.muse.entity.MuseAccountInfo;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.Rsp;

import java.util.List;

/**
 * 智能创作云账号信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface MuseAccountInfoService extends IBaseHdService<MuseAccountInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<MuseAccountInfo> page(MuseAccountInfoPageParam param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return IPage
     */
    List<MuseAccountInfo> list(MuseAccountInfoPageParam param);

    /**
     * 根据智能创作云用户ID获取关联租户
     *
     * @param museUserId 智能创作云用户ID
     * @return 租户列表
     */
    List<Long> listTenantByMuseUserId(Long museUserId);

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> add(MuseAccountInfoAddParam param);

    /**
     * 批量新增
     *
     * @param paramList 参数
     * @return Rsp
     */
    Rsp<Void> batchAdd(List<MuseAccountInfoAddParam> paramList);

    /**
     * 根据用户ID获取Muse账号
     * @param userId 用户ID
     * @return Muse 账号
     */
    MuseAccountInfo getByUserId(Long userId);
}
