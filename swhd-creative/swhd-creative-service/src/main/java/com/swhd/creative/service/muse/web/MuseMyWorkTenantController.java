package com.swhd.creative.service.muse.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.muse.client.MuseMyWorkTenantClient;
import com.swhd.creative.api.muse.dto.param.mywork.MuseMyWorkTenantPageParam;
import com.swhd.creative.api.muse.dto.result.MuseMyWorkTenantDetailResult;
import com.swhd.creative.api.muse.dto.result.MuseMyWorkTenantResult;
import com.swhd.creative.service.muse.entity.MuseMyWorkTenant;
import com.swhd.creative.service.muse.service.MuseMyWorkTenantService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-24
 */
@RestController
@AllArgsConstructor
@RequestMapping(MuseMyWorkTenantClient.BASE_PATH)
public class MuseMyWorkTenantController implements MuseMyWorkTenantClient {

    private final MuseMyWorkTenantService museMyWorkTenantService;

    @Override
    public Rsp<PageResult<MuseMyWorkTenantDetailResult>> page(MuseMyWorkTenantPageParam param) {
        IPage<MuseMyWorkTenantDetailResult> iPage = museMyWorkTenantService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage));
    }

    @Override
    public Rsp<MuseMyWorkTenantResult> getById(Long id) {
        MuseMyWorkTenant entity = museMyWorkTenantService.getById(id);
        return RspHd.data(Func.copy(entity, MuseMyWorkTenantResult.class));
    }

    @Override
    public Rsp<List<MuseMyWorkTenantResult>> listByIds(Collection<Long> ids) {
        List<MuseMyWorkTenant> list = museMyWorkTenantService.listByIds(ids);
        return RspHd.data(Func.copy(list, MuseMyWorkTenantResult.class));
    }

}
