package com.swhd.creative.service.muse.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.muse.client.MuseMyWorkClient;
import com.swhd.creative.api.muse.dto.param.mywork.MuseMyWorkPageParam;
import com.swhd.creative.api.muse.dto.result.MuseMyWorkResult;
import com.swhd.creative.service.muse.entity.MuseMyWork;
import com.swhd.creative.service.muse.service.MuseMyWorkService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-23
 */
@RestController
@AllArgsConstructor
@RequestMapping(MuseMyWorkClient.BASE_PATH)
public class MuseMyWorkController implements MuseMyWorkClient {

    private final MuseMyWorkService museMyWorkService;

    @Override
    public Rsp<PageResult<MuseMyWorkResult>> page(MuseMyWorkPageParam param) {
        IPage<MuseMyWork> iPage = museMyWorkService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, MuseMyWorkResult.class));
    }

    @Override
    public Rsp<MuseMyWorkResult> getById(Long id) {
        MuseMyWork entity = museMyWorkService.getById(id);
        return RspHd.data(Func.copy(entity, MuseMyWorkResult.class));
    }

    @Override
    public Rsp<List<MuseMyWorkResult>> listByIds(Collection<Long> ids) {
        List<MuseMyWork> list = museMyWorkService.listByIds(ids);
        return RspHd.data(Func.copy(list, MuseMyWorkResult.class));
    }

}
