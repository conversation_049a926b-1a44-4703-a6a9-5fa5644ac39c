package com.swhd.creative.service.muse.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.creative.api.muse.dto.param.log.MuseAccountPointConsumeLogAddParam;
import com.swhd.creative.api.muse.dto.param.log.MuseAccountPointConsumeLogPageParam;
import com.swhd.creative.api.muse.dto.param.log.MuseAccountPointConsumeLogUpdateParam;
import com.swhd.creative.api.muse.dto.result.MuseAccountPointConsumeLogResult;
import com.swhd.creative.api.muse.client.MuseAccountPointConsumeLogClient;
import com.swhd.creative.service.muse.entity.MuseAccountPointConsumeLog;
import com.swhd.creative.service.muse.service.MuseAccountPointConsumeLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-10-30
 */
@RestController
@AllArgsConstructor
@RequestMapping(MuseAccountPointConsumeLogClient.BASE_PATH)
public class MuseAccountPointConsumeLogController implements MuseAccountPointConsumeLogClient {

    private final MuseAccountPointConsumeLogService museAccountPointConsumeLogService;

    @Override
    public Rsp<PageResult<MuseAccountPointConsumeLogResult>> page(MuseAccountPointConsumeLogPageParam param) {
        IPage<MuseAccountPointConsumeLog> iPage = museAccountPointConsumeLogService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, MuseAccountPointConsumeLogResult.class));
    }

    @Override
    public Rsp<MuseAccountPointConsumeLogResult> getById(Long id) {
        MuseAccountPointConsumeLog entity = museAccountPointConsumeLogService.getById(id);
        return RspHd.data(Func.copy(entity, MuseAccountPointConsumeLogResult.class));
    }

    @Override
    public Rsp<List<MuseAccountPointConsumeLogResult>> listByIds(Collection<Long> ids) {
        List<MuseAccountPointConsumeLog> list = museAccountPointConsumeLogService.listByIds(ids);
        return RspHd.data(Func.copy(list, MuseAccountPointConsumeLogResult.class));
    }

    @Override
    public Rsp<Void> add(MuseAccountPointConsumeLogAddParam param) {
        boolean result = museAccountPointConsumeLogService.save(Func.copy(param, MuseAccountPointConsumeLog.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(MuseAccountPointConsumeLogUpdateParam param) {
        boolean result = museAccountPointConsumeLogService.updateById(Func.copy(param, MuseAccountPointConsumeLog.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = museAccountPointConsumeLogService.removeByIds(ids);
        return RspHd.status(result);
    }

}
