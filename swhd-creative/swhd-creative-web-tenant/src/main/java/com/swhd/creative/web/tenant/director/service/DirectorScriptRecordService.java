package com.swhd.creative.web.tenant.director.service;

import com.swhd.creative.api.director.client.DirectorScriptRecordClient;
import com.swhd.creative.api.director.dto.param.record.DirectorScriptRecordAddParam;
import com.swhd.creative.web.tenant.director.vo.markdown.DirectorScriptHomeIndustryMarkdownVisitor;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import lombok.AllArgsConstructor;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Service
@AllArgsConstructor
public class DirectorScriptRecordService {

    private final DirectorScriptRecordClient directorScriptRecordClient;

    @Async
    public void asyncAdd(Long tenantId, DirectorScriptRecordAddParam param) {
        TenantHolder.methodTenantVoid(tenantId, () -> {
            if (Func.isNotEmpty(param.getContentMarkdown())) {
                Parser parser = Parser.builder().build();
                Node document = parser.parse(param.getContentMarkdown());
                DirectorScriptHomeIndustryMarkdownVisitor visitor = new DirectorScriptHomeIndustryMarkdownVisitor();
                document.accept(visitor);
                param.setContentJson(JsonUtil.toJsonString(visitor.getContent()));
            }
            directorScriptRecordClient.add(param);
        });
    }

}
