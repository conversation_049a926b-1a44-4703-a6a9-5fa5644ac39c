package com.swhd.creative.web.tenant.actor.web;

import com.swhd.creative.api.actor.client.ActorVideoClient;
import com.swhd.creative.api.actor.dto.param.actor.video.ActorVideoAddParam;
import com.swhd.creative.api.actor.dto.result.ActorVideoResult;
import com.swhd.creative.web.tenant.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/28
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/actorVideo")
public class ActorVideoController {

    private final ActorVideoClient actorVideoClient;

    @Operation(summary = "根据演员id获取")
    @GetMapping("/listByActorId")
    public Rsp<List<ActorVideoResult>> listByActorId(@RequestParam("actorId") Long actorId) {
        return actorVideoClient.listByActorId(actorId);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid ActorVideoAddParam param) {
        return actorVideoClient.add(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return actorVideoClient.removeByIds(ids);
    }

}
