package com.swhd.creative.web.tenant.actor.web;

import com.swhd.content.api.type.wrapper.TypeInfoWrapper;
import com.swhd.creative.api.actor.client.ActorInfoClient;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoAddParam;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoPageParam;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoUpdateParam;
import com.swhd.creative.api.actor.dto.result.ActorInfoResult;
import com.swhd.creative.web.tenant.actor.vo.result.ActorInfoResultVo;
import com.swhd.creative.web.tenant.constant.WebConstant;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/actorInfo")
public class ActorInfoController {

    private final ActorInfoClient actorInfoClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<ActorInfoResultVo>> page(@RequestBody @Valid ActorInfoPageParam param) {
        Rsp<PageResult<ActorInfoResult>> pageRsp = actorInfoClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<ActorInfoResultVo> pageVo = PageUtil.convert(pageRsp.getData(), ActorInfoResultVo.class);
        // 风格类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoResultVo::getStyleTypeIds, ActorInfoResultVo::setStyleTypes);
        // 擅长行业类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoResultVo::getProficientIndustryTypeIds, ActorInfoResultVo::setProficientIndustryTypes);
        // 擅长演出类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoResultVo::getProficientShowTypeIds, ActorInfoResultVo::setProficientShowTypes);
        return RspHd.data(pageVo);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<ActorInfoResultVo> getById(@RequestParam("id") Long id) {
        Rsp<ActorInfoResult> rsp = actorInfoClient.getById(id);
        if (RspHd.failOrDataIsNull(rsp)) {
            return RspHd.fail(rsp);
        }
        ActorInfoResultVo vo = Func.copy(rsp.getData(), ActorInfoResultVo.class);
        // 风格类型
        TypeInfoWrapper.getInstance().setInfoOfIds(vo,
                ActorInfoResultVo::getStyleTypeIds, ActorInfoResultVo::setStyleTypes);
        // 擅长行业类型
        TypeInfoWrapper.getInstance().setInfoOfIds(vo,
                ActorInfoResultVo::getProficientIndustryTypeIds, ActorInfoResultVo::setProficientIndustryTypes);
        // 擅长演出类型
        TypeInfoWrapper.getInstance().setInfoOfIds(vo,
                ActorInfoResultVo::getProficientShowTypeIds, ActorInfoResultVo::setProficientShowTypes);
        return RspHd.data(vo);
    }

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    public Rsp<List<AntdSelectResult>> selectAll() {
        return actorInfoClient.selectAll();
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public  Rsp<Void> add(@RequestBody @Valid ActorInfoAddParam param) {
        return actorInfoClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid ActorInfoUpdateParam param) {
        return actorInfoClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return actorInfoClient.removeByIds(ids);
    }

}
