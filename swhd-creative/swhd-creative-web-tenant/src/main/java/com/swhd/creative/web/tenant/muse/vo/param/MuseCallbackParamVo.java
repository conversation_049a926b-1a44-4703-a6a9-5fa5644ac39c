package com.swhd.creative.web.tenant.muse.vo.param;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(defaultValue = "MuseCallbackParamVo对象")
public class MuseCallbackParamVo {

    @SerializedName("Payload")
    private String payload;

    @Schema(defaultValue = "0 表示成片回调，1 表示任务回调, 2 表示 url 素材状态回传")
    @SerializedName("CallbackType")
    private Integer callbackType;

    @SerializedName("Meta")
    private Meta Meta;

    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(name = "MuseCallbackParamVoMeta")
    public static class Meta {

        @SerializedName("Version")
        private Integer version;

        @SerializedName("Alg")
        private String alg;

        @SerializedName("Nonce")
        private String nonce;

        @SerializedName("Timestamp")
        private Long timestamp;

        @SerializedName("RawPayload")
        private String rawPayload;

        @SerializedName("Sign")
        private String sign;

    }

}
