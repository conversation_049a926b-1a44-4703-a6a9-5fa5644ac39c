package com.swhd.creative.web.tenant.actor.web;

import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.creative.api.actor.client.ActorOrderMaterialClient;
import com.swhd.creative.api.actor.dto.param.actor.order.material.ActorOrderMaterialAddParam;
import com.swhd.creative.api.actor.dto.param.actor.order.material.ActorOrderMaterialPageParam;
import com.swhd.creative.api.actor.dto.result.ActorOrderMaterialResult;
import com.swhd.creative.web.tenant.constant.WebConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/27
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/actorOrderMaterial")
public class ActorOrderMaterialController {

    private final ActorOrderMaterialClient actorOrderMaterialClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<ActorOrderMaterialResult>> page(@RequestBody @Valid ActorOrderMaterialPageParam param) {
        Rsp<PageResult<ActorOrderMaterialResult>> pageRsp = actorOrderMaterialClient.page(param);
        OssPrivateUtil.preSignedUrlPageRsp(pageRsp,
                ActorOrderMaterialResult::getOssKey, ActorOrderMaterialResult::setOssKey);
        return pageRsp;
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<ActorOrderMaterialResult> getById(@RequestParam("id") Long id) {
        Rsp<ActorOrderMaterialResult> rsp = actorOrderMaterialClient.getById(id);
        OssPrivateUtil.preSignedUrlRsp(rsp,
                ActorOrderMaterialResult::getOssKey, ActorOrderMaterialResult::setOssKey);
        return rsp;
    }

    @Operation(summary = "根据订单id获取")
    @GetMapping("/listByOrderId")
    public Rsp<List<ActorOrderMaterialResult>> listByOrderId(Long orderId) {
        Rsp<List<ActorOrderMaterialResult>> listRsp = actorOrderMaterialClient.listByOrderId(orderId);
        OssPrivateUtil.preSignedUrlListRsp(listRsp,
                ActorOrderMaterialResult::getOssKey, ActorOrderMaterialResult::setOssKey);
        return listRsp;
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid ActorOrderMaterialAddParam param) {
        return actorOrderMaterialClient.add(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return actorOrderMaterialClient.removeByIds(ids);
    }

}
