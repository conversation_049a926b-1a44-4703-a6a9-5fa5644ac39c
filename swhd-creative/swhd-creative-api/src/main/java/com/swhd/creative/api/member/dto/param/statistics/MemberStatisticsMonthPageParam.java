package com.swhd.creative.api.member.dto.param.statistics;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-27
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MemberStatisticsMaterialMonthPageParam对象")
public class MemberStatisticsMonthPageParam extends PageReq {

    @Schema(description = "成员id")
    private Long memberId;

    @Schema(description = "统计日期")
    private List<LocalDate> statisticDateBetween;

}
