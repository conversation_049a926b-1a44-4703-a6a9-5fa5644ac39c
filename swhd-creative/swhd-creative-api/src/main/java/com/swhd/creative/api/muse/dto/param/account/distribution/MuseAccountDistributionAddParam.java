package com.swhd.creative.api.muse.dto.param.account.distribution;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MuseAccountDistributionAddParam对象")
public class MuseAccountDistributionAddParam {

    @NotNull(message = "用户id不能为空")
    @Schema(description = "用户id")
    private Long userId;

    @NotNull(message = "账号信息id不能为空")
    @Schema(description = "账号信息id")
    private Long accountInfoId;

}
