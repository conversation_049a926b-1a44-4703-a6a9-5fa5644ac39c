package com.swhd.creative.api.muse.client;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.api.muse.dto.param.open.api.MuseApiRedirectToMuseParam;
import com.swhd.creative.api.muse.dto.param.open.api.MuseApiUserCreateParam;
import com.swhd.creative.api.muse.dto.param.open.api.MuseApiUserUpdateParam;
import com.swhd.creative.api.muse.dto.result.MuseApiUserResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MuseOpenApiClient.BASE_PATH)
public interface MuseOpenApiClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/muse/openApi";

    @Operation(summary = "用户列表")
    @PostMapping("/userList")
    Rsp<List<MuseApiUserResult>> userList();

    @Operation(summary = "新建子账号")
    @PostMapping("/userCreate")
    Rsp<Void> userCreate(@RequestBody @Valid MuseApiUserCreateParam param);

    @Operation(summary = "修改子账号")
    @PostMapping("/userUpdate")
    Rsp<Void> userUpdate(@RequestBody @Valid MuseApiUserUpdateParam param);

    @Operation(summary = "跳转到智创云各个功能")
    @PostMapping("/redirectToMuse")
    Rsp<String> redirectToMuse(@RequestBody @Valid MuseApiRedirectToMuseParam param);

    @Operation(summary = "登出用户")
    @GetMapping("/logout")
    Rsp<Void> logout(@RequestParam("uid") Long uid);

}
