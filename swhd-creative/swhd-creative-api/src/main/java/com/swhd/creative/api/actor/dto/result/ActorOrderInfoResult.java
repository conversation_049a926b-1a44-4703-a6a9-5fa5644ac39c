package com.swhd.creative.api.actor.dto.result;

import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ActorOrderInfoResult对象")
public class ActorOrderInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "订单日期")
    private LocalDate date;

    @Schema(description = "总费用(单位:元)")
    private BigDecimal totalCost;

    @Schema(description = "付费客户")
    private Long payCustomId;

    @Schema(description = "付费类型")
    private Long payTypeId;

    @Schema(description = "演员")
    private Long actorId;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
