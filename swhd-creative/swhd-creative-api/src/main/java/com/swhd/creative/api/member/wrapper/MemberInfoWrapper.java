package com.swhd.creative.api.member.wrapper;

import com.swhd.creative.api.member.client.MemberInfoClient;
import com.swhd.creative.api.member.dto.result.MemberInfoResult;
import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
public class MemberInfoWrapper extends BaseLongApiWrapper<MemberInfoResult, MemberInfoResult> {

    @Getter
    private static final MemberInfoWrapper instance = new MemberInfoWrapper();

    private final MemberInfoClient memberInfoClient;

    private MemberInfoWrapper() {
        this.memberInfoClient = SpringUtil.getBean(MemberInfoClient.class);
    }

    @Override
    protected Rsp<MemberInfoResult> getRspById(Long id) {
        return memberInfoClient.getById(id);
    }

    @Override
    protected Rsp<List<MemberInfoResult>> getRspByIds(Collection<Long> ids) {
        return memberInfoClient.listByIds(ids);
    }

    @Override
    protected Long getListId(MemberInfoResult memberInfoResult) {
        return memberInfoResult.getId();
    }

}
