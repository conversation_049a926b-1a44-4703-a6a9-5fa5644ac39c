package com.swhd.creative.api.material.client;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.api.material.dto.param.info.MaterialOceanengineVideoInfoPageParam;
import com.swhd.creative.api.material.dto.result.MaterialOceanengineVideoInfoResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-21
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MaterialOceanengineVideoInfoClient.BASE_PATH)
public interface MaterialOceanengineVideoInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/material/oceanengine/video/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MaterialOceanengineVideoInfoResult>> page(@RequestBody @Valid MaterialOceanengineVideoInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MaterialOceanengineVideoInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据materialId获取")
    @GetMapping("/getByMaterialId")
    Rsp<MaterialOceanengineVideoInfoResult> getByMaterialId(@RequestParam("materialId") Long materialId);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MaterialOceanengineVideoInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据materialId列表获取")
    @PostMapping("/listByMaterialIds")
    Rsp<List<MaterialOceanengineVideoInfoResult>> listByMaterialIds(@RequestBody @Valid @NotEmpty Collection<Long> materialIds);

}
