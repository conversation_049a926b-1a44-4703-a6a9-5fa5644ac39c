package com.swhd.creative.api.muse.dto.param.account.tenant;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-09-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MuseAccountTenantPageParam对象")
public class MuseAccountTenantPageParam extends PageReq {

    @Schema(description = "账号信息表id")
    private Long accountInfoId;

    @Schema(description = "是否默认")
    private Boolean isDefault;

}
