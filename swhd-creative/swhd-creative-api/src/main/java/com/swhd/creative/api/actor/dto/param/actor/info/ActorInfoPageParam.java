package com.swhd.creative.api.actor.dto.param.actor.info;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ActorInfoPageParam对象")
public class ActorInfoPageParam extends PageReq {

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "艺名")
    private String stageName;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "性别：0-女，1-男")
    private Integer sex;

    @Schema(description = "风格类型")
    private Long styleTypeId;

    @Schema(description = "擅长行业类型")
    private Long proficientIndustryTypeId;

    @Schema(description = "擅长演出类型")
    private Long proficientShowTypeId;

    @Schema(description = "是否有评论")
    private Boolean hasComment;

}
