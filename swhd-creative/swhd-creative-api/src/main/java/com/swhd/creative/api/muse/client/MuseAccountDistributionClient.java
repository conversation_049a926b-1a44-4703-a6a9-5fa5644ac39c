package com.swhd.creative.api.muse.client;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.api.muse.dto.param.account.distribution.MuseAccountDistributionAddParam;
import com.swhd.creative.api.muse.dto.param.account.distribution.MuseAccountDistributionLimiterPolicyAllocateParam;
import com.swhd.creative.api.muse.dto.param.account.distribution.MuseAccountDistributionPageParam;
import com.swhd.creative.api.muse.dto.result.MuseAccountDistributionResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-19
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MuseAccountDistributionClient.BASE_PATH)
public interface MuseAccountDistributionClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/muse/account/distribution";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MuseAccountDistributionResult>> page(@RequestBody @Valid MuseAccountDistributionPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MuseAccountDistributionResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据企业账号id获取账号信息表id", description = "如果不存在关联，使用租户默认数据")
    @GetMapping("/getAccountInfoIdByUserId")
    Rsp<Long> getAccountInfoIdByUserId(@RequestParam("id") Long userId);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MuseAccountDistributionResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MuseAccountDistributionAddParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新建分配用量规则")
    @PostMapping("/limiterPolicyAllocate")
    Rsp<Void> limiterPolicyAllocate(@RequestBody @Valid MuseAccountDistributionLimiterPolicyAllocateParam param);

}
