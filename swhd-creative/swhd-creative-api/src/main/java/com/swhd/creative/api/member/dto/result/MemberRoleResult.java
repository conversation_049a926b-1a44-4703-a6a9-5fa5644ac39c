package com.swhd.creative.api.member.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-08-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MemberRoleResult对象")
public class MemberRoleResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
