package com.swhd.creative.api.muse.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/25
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MuseVolcAccountRole对象")
public class MuseApiUserRole {

    @Schema(description = "角色id")
    private Long id;

    @Schema(description = "角色名字")
    private String name;

}
