package com.swhd.creative.api.muse.dto.param.log;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.swhd.creative.api.muse.dto.MuseContentItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-10-30
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MuseAccountPointConsumeLogUpdateParam对象")
public class MuseAccountPointConsumeLogUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "消耗项")
    private String consumeItemName;

    @Schema(description = "消耗量")
    private BigDecimal consumeUsage;

    @Schema(description = "操作账号")
    private Long accountId;

    @Schema(description = "账号名称")
    private String accountName;

    @Schema(description = "消耗时间")
    private LocalDateTime eventTime;

    @Schema(description = "内容列表")
    private List<MuseContentItem> contentItems;

    @Schema(description = "团队列表")
    private List<String> operatorTeamNames;

    @Schema(description = "hash值")
    private String hashKey;

}
