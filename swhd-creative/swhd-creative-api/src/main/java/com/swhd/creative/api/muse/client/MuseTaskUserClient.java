package com.swhd.creative.api.muse.client;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.api.muse.dto.param.user.MuseTaskUserAddParam;
import com.swhd.creative.api.muse.dto.param.user.MuseTaskUserBatchAddParam;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2025-03-22
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MuseTaskUserClient.BASE_PATH)
public interface MuseTaskUserClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/muse/task/user";

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MuseTaskUserAddParam param);

    @Operation(summary = "批量新增")
    @PostMapping("/batchAdd")
    Rsp<Void> batchAdd(@RequestBody @Valid MuseTaskUserBatchAddParam param);

}
