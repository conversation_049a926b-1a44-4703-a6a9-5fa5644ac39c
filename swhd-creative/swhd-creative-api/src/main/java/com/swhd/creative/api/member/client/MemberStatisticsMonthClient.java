package com.swhd.creative.api.member.client;

import com.swhd.creative.api.common.constant.ApiConstant;
import com.swhd.creative.api.member.dto.param.statistics.MemberStatisticsMonthPageParam;
import com.swhd.creative.api.member.dto.result.MemberStatisticsMonthResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2024-08-27
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MemberStatisticsMonthClient.BASE_PATH)
public interface MemberStatisticsMonthClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/member/statistics/month";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MemberStatisticsMonthResult>> page(@RequestBody @Valid MemberStatisticsMonthPageParam param);

}
