package com.swhd.creative.api.muse.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RedirectType {
    // 首页
    MUSE(0, "/muse"),
    // 编辑器
    EDITOR(1, "/muse/editor/create"),
    // 图文转视频
    TEXT_TO_VIDEO(2, "/muse/text2video"),
    // 添加品牌
    BRSNDS_ATTACH(3, "/muse/brands/attach"),
    // 电商拆条
    VIDEOSEGMENT_CREATE(4, "/muse/videosegment-create"),
    // 智能配音
    INTELLIGENT_DUB(5, "/muse/intelligent-dub"),
    // 智能剪裁
    SMART_CROP_INTELLIGENT(6, "/muse/smart-crop?from=intelligent"),
    // 自定义剪裁
    SMART_CORP_CUSTOM(7, "/muse/smart-crop?from=custom"),
    // 智能橡皮擦
    SMART_CRASER(8, "/muse/smart-eraser"),
    // 模板广场
    TEMPLATES_LIBRARY(9, "/muse/templatesLibrary"),
    // 高级成片-智能模版推荐
    RECOMMEND_TEMPLATE(10, "/muse/recommend?intelligentType=template"),
    // 高级成片-自动剪辑成片
    RECOMMEND_MOVIE(11, "/muse/recommend?intelligentType=movie"),
    // 数据视频-折线图
    DATA_VIDEO(12, "/muse/data-video"),
    // 图文编辑器
    GRAHPIC_EDITOR(13, "/muse/graphic-editor"),
    // 我的素材
    MY_MATERIAL(15, "/muse/home/<USER>/space?activeKey=my_material"),
    // 我的模板
    MY_TEMPLATE(16, "/muse/home/<USER>/space?activeKey=my_template"),
    // 我的作品
    MY_FILM(17, "/muse/home/<USER>/space?activeKey=my_film"),
    // 团队素材
    TEAM_MATERIAL(18, "/muse/home/<USER>/space?activeKey=team_material"),
    // 团队模板
    TEAM_TEMPLATE(19, "/muse/home/<USER>/space?activeKey=team_template"),
    // 团队作品
    TEAM_FILM(20, "/muse/home/<USER>/space?activeKey=team_film"),
    // 视频拆条
    VIDEO_SEGMENT(21, "/muse/video-segment-new/home"),
    ;

    private final int type;
    private final String path;

}
