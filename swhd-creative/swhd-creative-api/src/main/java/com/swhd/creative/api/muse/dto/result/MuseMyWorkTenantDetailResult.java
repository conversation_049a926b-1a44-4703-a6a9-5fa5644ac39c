package com.swhd.creative.api.muse.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MuseMyWorkTenantDetailResult对象")
public class MuseMyWorkTenantDetailResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "团队id")
    private Long teamId;

    @Schema(description = "智能创作云用户ID")
    private Long museUserId;

    @Schema(description = "素材ID")
    private Long mediaId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "文件夹全路径")
    private List<String> fullFolder;

    @Schema(description = "素材创建时间")
    private Long mediaCreateTime;

    @Schema(description = "素材修改时间")
    private Long mediaUpdateTime;

    @Schema(description = "封面图")
    private String coverUrl;

    @Schema(description = "视频URL")
    private String videoUrl;

    @Schema(description = "视频md5")
    private String videoMd5;

    @Schema(description = "视频时长，单位：ms")
    private Integer videoDuration;

    @Schema(description = "视频大小")
    private Long videoSize;

    @Schema(description = "视频宽")
    private Integer videoWidth;

    @Schema(description = "视频高")
    private Integer videoHeight;

    @Schema(description = "视频码率，单位：bps")
    private Integer videoBitrate;

}
