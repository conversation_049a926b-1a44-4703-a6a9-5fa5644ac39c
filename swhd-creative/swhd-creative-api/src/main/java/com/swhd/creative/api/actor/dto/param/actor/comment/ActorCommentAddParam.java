package com.swhd.creative.api.actor.dto.param.actor.comment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-02-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ActorCommentAddParam对象")
public class ActorCommentAddParam {

    @NotNull(message = "演员不能为空")
    @Schema(description = "演员id")
    private Long actorId;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "评论人")
    private String commentator;

}
