package com.swhd.creative.api.material.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-08-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MaterialOceanengineVideoMemberResult对象")
public class MaterialOceanengineVideoMemberResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "成员id")
    private Long memberId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
