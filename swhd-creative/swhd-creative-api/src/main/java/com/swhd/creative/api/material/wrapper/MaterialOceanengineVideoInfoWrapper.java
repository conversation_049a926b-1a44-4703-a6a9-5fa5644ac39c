package com.swhd.creative.api.material.wrapper;

import com.swhd.creative.api.material.client.MaterialOceanengineVideoInfoClient;
import com.swhd.creative.api.material.dto.result.MaterialOceanengineVideoInfoResult;
import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

/**
 * 使用素材ID作为ID
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
public class MaterialOceanengineVideoInfoWrapper extends BaseLongApiWrapper<MaterialOceanengineVideoInfoResult, MaterialOceanengineVideoInfoResult> {

    @Getter
    private static final MaterialOceanengineVideoInfoWrapper instance = new MaterialOceanengineVideoInfoWrapper();

    private final MaterialOceanengineVideoInfoClient materialOceanengineVideoInfoClient;

    private MaterialOceanengineVideoInfoWrapper() {
        this.materialOceanengineVideoInfoClient = SpringUtil.getBean(MaterialOceanengineVideoInfoClient.class);
    }

    @Override
    protected Rsp<MaterialOceanengineVideoInfoResult> getRspById(Long materialId) {
        return materialOceanengineVideoInfoClient.getByMaterialId(materialId);
    }

    @Override
    protected Rsp<List<MaterialOceanengineVideoInfoResult>> getRspByIds(Collection<Long> materialIds) {
        return materialOceanengineVideoInfoClient.listByMaterialIds(materialIds);
    }

    @Override
    protected Long getListId(MaterialOceanengineVideoInfoResult materialOceanengineVideoInfoResult) {
        return materialOceanengineVideoInfoResult.getMaterialId();
    }

}
