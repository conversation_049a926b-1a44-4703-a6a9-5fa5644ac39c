package com.swhd.creative.web.platform.muse.service;

import com.swhd.creative.api.muse.client.MuseOpenApiClient;
import com.swhd.creative.api.muse.dto.result.MuseApiUserResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Service
@AllArgsConstructor
public class MuseOpenApiService {

    private final MuseOpenApiClient museOpenApiClient;

    public List<MuseApiUserResult> userList() {
        Rsp<List<MuseApiUserResult>> userListRsp = museOpenApiClient.userList();
        RspHd.failThrowException(userListRsp);
        List<MuseApiUserResult> userList = userListRsp.getData();
        if (Func.isEmpty(userList)) {
            return Collections.emptyList();
        }
        userList = userList.stream()
                .filter(user -> user.getPermitted() != null && user.getPermitted())
                .toList();
        return userList.reversed();
    }

}
