package com.swhd.shop.api.shop.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/22
 */
@Data
@Schema(name = "ShopInfoBatchRegisterAndAddParam", description = "批量注册店铺并添加店铺信息参数")
public class ShopInfoBatchRegisterAndAddParam {

    @Schema(description = "租户ID（为空时创建新租户和新用户）")
    private Long tenantId;

    @Schema(description = "父租户ID（租户ID为空时创建新租户和新用户，父租户ID不能为空）")
    private Long parentTenantId;

    @Schema(description = "店铺名称")
    @NotEmpty
    private List<ShopInfoAddParam> addParamList;

}
