package com.swhd.shop.api.connect.dto.param.activity;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "BannerActivityUpdateParam对象")
public class BannerActivityUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "活动图片key")
    private String imageKey;

    @Schema(description = "活动链接")
    private String activityUrl;

    @Schema(description = "生效日期")
    private LocalDateTime effectDate;

    @Schema(description = "失效日期")
    private LocalDateTime expireDate;

}
