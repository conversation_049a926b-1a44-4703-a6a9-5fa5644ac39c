package com.swhd.shop.api.connect.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.shop.api.common.constant.ApiConstant;
import com.swhd.shop.api.connect.dto.param.publish.ContentPublishAddParam;
import com.swhd.shop.api.connect.dto.param.publish.ContentPublishPageParam;
import com.swhd.shop.api.connect.dto.param.publish.ContentPublishUpdateParam;
import com.swhd.shop.api.connect.dto.result.ContentPublishResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-19
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = ContentPublishClient.BASE_PATH)
public interface ContentPublishClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/content/publish";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<ContentPublishResult>> page(@RequestBody @Valid ContentPublishPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<ContentPublishResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<ContentPublishResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid ContentPublishAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid ContentPublishUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
