package com.swhd.shop.api.amap.dto.param.callback;

import lombok.Data;

/**
 * 高德回调基础请求
 */
@Data
public class AmapCallbackParam {
    /**
     * 请求时间戳(毫秒)
     */
    private String utcTimestamp;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 字符集编码
     */
    private String charset;
    
    /**
     * 接口名称
     */
    private String method;
    
    /**
     * 签名
     */
    private String sign;
    
    /**
     * 签名类型
     */
    private String signType;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 业务参数
     */
    private String bizContent;
} 