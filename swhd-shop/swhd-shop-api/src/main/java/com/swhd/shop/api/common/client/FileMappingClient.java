package com.swhd.shop.api.common.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.shop.api.common.constant.ApiConstant;
import com.swhd.shop.api.common.dto.param.mapping.FileMappingAddParam;
import com.swhd.shop.api.common.dto.param.mapping.FileMappingPageParam;
import com.swhd.shop.api.common.dto.param.mapping.FileMappingUpdateParam;
import com.swhd.shop.api.common.dto.result.FileMappingResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-09
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = FileMappingClient.BASE_PATH)
public interface FileMappingClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/file/mapping";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<FileMappingResult>> page(@RequestBody @Valid FileMappingPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<FileMappingResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<FileMappingResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid FileMappingAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid FileMappingUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
