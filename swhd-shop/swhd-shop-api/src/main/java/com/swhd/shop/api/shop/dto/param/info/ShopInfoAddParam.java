package com.swhd.shop.api.shop.dto.param.info;

import java.math.BigDecimal;
import java.util.List;

import com.swhd.shop.api.shop.validation.NoOverlappingWeekDays;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ShopInfoAddParam对象")
public class ShopInfoAddParam {

    @Schema(description = "主体id")
    private Long subjectId;

    @Schema(description = "类目ID")
    private Long categoryId;

    @Schema(description = "商家名称")
    private String businessName;

    @NotEmpty
    @Schema(description = "门店名称")
    private String shopName;

    @Schema(description = "门店地址")
    private String shopAddress;

    @Schema(description = "联系手机号码")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String contactPhone;

    @Schema(description = "省份编码")
    private String province;

    @Schema(description = "城市编码")
    private String city;

    @Schema(description = "区县编码")
    private String district;

    @Schema(description = "省份")
    private String provinceName;

    @Schema(description = "城市")
    private String cityName;

    @Schema(description = "区县")
    private String districtName;

    @DecimalMin(value = "-180.0", message = "经度必须大于等于-180")
    @DecimalMax(value = "180.0", message = "经度必须小于等于180")
    @Schema(description = "经度")
    private BigDecimal longitude;

    @DecimalMin(value = "-90.0", message = "纬度必须大于等于-90")
    @DecimalMax(value = "90.0", message = "纬度必须小于等于90")
    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "门店图片列表")
    private List<String> shopImages;

    @Valid
    @Schema(description = "门店电话列表")
    private List<ShopPhone> shopPhones;

    @Valid
    @NoOverlappingWeekDays
    @Schema(description = "营业时间列表")
    private List<ShopBusinessHour> businessHours;

    @Schema(description = "关联的资质ID")
    private Long qualificationId;

    @Schema(description = "门店运营类型(1:官方授权 2:官方自营)")
    private Integer operationType;

    @Schema(description = "来源:workbench-工作台;quick-快速开店;crmQuick-Crm快速开店;")
    private String sourceCode;

}
