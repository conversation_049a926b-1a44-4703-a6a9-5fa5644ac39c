package com.swhd.shop.api.marketing.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/26 20:24
 */

@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ArrivalGiftPageWithPublishResult对象")
public class ArrivalGiftPageWithPublishResult extends ArrivalGiftResult {

    @Schema(description = "平台内容ID")
    private String platformContentId;

    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "上架状态：1 已上架 2 已下架")
    private Integer shelfStatus;

    @Schema(description = "状态：1、审核中  2、审核成功  3、审核失败")
    private Integer auditStatus;

    @Schema(description = "发布ID")
    private Long publishId;

    @Schema(description = "操作类型： 0-未操作；1-保存；2-上线；3-下线；")
    private Integer operateType;

    @Schema(description = "平台内容修改时间")
    private LocalDateTime contentModifyTime;

    @Schema(description = "操作时间")
    private LocalDateTime operateTime;

    @Schema(description = "同步状态：0-待操作，1-同步确认中，2-同步成功，3-同步失败")
    private Integer syncStatus;

}
