package com.swhd.shop.api.connect.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AlbumInfoUpdateParam对象")
public class AlbumInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "标题")
    private String albumTitle;

    @Schema(description = "链接")
    private String albumUrl;

    @Schema(description = "素材类型。1：图片；2：视频")
    private Integer albumType;

    @Schema(description = "文件夹，0-视频；1-门店；2-产品；3-活动；4-其他")
    private Integer folderType;

    @Schema(description = "权重，数字越大排越前")
    private Integer weight;

}
