package com.swhd.shop.api.connect.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/24 14:24
 */

@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AlbumListGroupByFolderParam对象")
public class AlbumListGroupByFolderParam {

    @NotNull
    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "文件夹列表，0-视频；1-门店；2-产品；3-活动；4-其他")
    private List<Integer> folderTypes;

    @Schema(description = "素材类型。1：图片；2：视频")
    private Integer albumType;

    @Schema(description = "上架状态：1 已上架 2 已下架")
    private Integer shelfStatus;

    @Schema(description = "状态：1、审核中  2、审核成功  3、审核失败")
    private Integer auditStatus;

    @Schema(description = "平台编码")
    private String platformCode;
}
