package com.swhd.shop.api.shop.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/6 16:54
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopEventMessage {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "门店id")
    private Long shopId;

    @Schema(description = "门店联系人电话")
    private String contactPhone;

    @Schema(description = "门店事件类型")
    private String shopEventType;
    
    /**
     * 门店事件类型枚举
     */
    @Getter
    public enum ShopEventType {
        /**
         * 门店创建
         */
        CREATED("created", "门店创建"),
        
        /**
         * 提交高德审核
         */
        AMAP_AUDIT_SUBMIT("amapAuditSubmit", "提交高德审核"),
        
        /**
         * 高德审核失败
         */
        AMAP_AUDIT_FAILED("amapAuditFailed", "高德审核失败"),
        
        /**
         * 高德审核成功
         */
        AMAP_AUDIT_SUCCESS("amapAuditSuccess", "高德审核成功"),
        
        /**
         * 提交高德下单
         */
        AMAP_ORDER_SUBMIT("amapOrderSubmit", "提交高德下单"),
        
        /**
         * 高德下单失败
         */
        AMAP_ORDER_FAILED("amapOrderFailed", "高德下单失败"),
        
        /**
         * 高德下单成功
         */
        AMAP_ORDER_SUCCESS("amapOrderSuccess", "高德下单成功");
        
        private final String code;
        private final String desc;
        
        ShopEventType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }
}
