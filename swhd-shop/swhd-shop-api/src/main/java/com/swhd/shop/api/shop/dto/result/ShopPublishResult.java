package com.swhd.shop.api.shop.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ShopPublishResult对象")
public class ShopPublishResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "平台门店ID")
    private String platformShopId;

    @Schema(description = "发布状态(1:待发布 2:发布中 3:发布成功 4:发布失败)")
    private Integer publishStatus;

    @Schema(description = "发布错误信息")
    private String publishErrorMsg;

    @Schema(description = "最后发布时间")
    private LocalDateTime lastPublishTime;

    @Schema(description = "最后提交时间")
    private LocalDateTime lastSubmitTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
