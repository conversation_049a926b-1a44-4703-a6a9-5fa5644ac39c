package com.swhd.shop.api.marketing.dto.result;

import java.math.BigDecimal;

import com.swhd.magiccube.core.annotation.JsonOss;
import com.swhd.shop.api.shop.dto.result.ShopInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ArrivalGiftResult对象")
public class ArrivalGiftResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "礼品名称")
    private String giftName;

    @Schema(description = "礼品类型(1:到店礼 2:订单礼 3:其他优惠 4:只发消息)")
    private Integer giftType;

    @Schema(description = "礼品价值")
    private BigDecimal giftValue;

    @Schema(description = "活动开始时间")
    private LocalDateTime startTime;

    @Schema(description = "活动结束时间")
    private LocalDateTime endTime;

    @Schema(description = "活动日库存")
    private Integer dayStock;

    @Schema(description = "活动总库存")
    private Integer totalStock;

    @Schema(description = "本店日库存")
    private Integer shopDayStock;

    @Schema(description = "本店总库存")
    private Integer shopTotalStock;

    @Schema(description = "单用户领取限制")
    private Integer userLimit;

    @Schema(description = "单门店用户领取数量限制")
    private Integer shopUserLimit;

    @Schema(description = "活动简介")
    private String activityDesc;

    @JsonOss
    @Schema(description = "活动图片URL")
    private String activityImage;

    @JsonOss
    @Schema(description = "IconURL")
    private String iconUrl;

    @Schema(description = "门店ID列表")
    private List<Long> shopIds;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "门店列表")
    private List<ShopInfoResult> shops;

}
