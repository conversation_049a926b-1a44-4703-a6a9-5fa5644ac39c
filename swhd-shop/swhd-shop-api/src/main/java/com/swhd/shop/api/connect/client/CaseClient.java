package com.swhd.shop.api.connect.client;

import com.swhd.shop.api.common.constant.ApiConstant;
import com.swhd.shop.api.connect.dto.param.caseinfo.*;
import com.swhd.shop.api.connect.dto.result.CasePageWithPublishResult;
import com.swhd.shop.api.connect.dto.result.CaseResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = CaseClient.BASE_PATH)
public interface CaseClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/case";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<CaseResult>> page(@RequestBody @Valid CasePageParam param);


    @Operation(summary = "分页查询(发布状态)")
    @PostMapping("/pageWithPublish")
    Rsp<PageResult<CasePageWithPublishResult>> pageWithPublish(@RequestBody @Valid CasePageWithPublishParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<CaseResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<CaseResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid CaseAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid CaseUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "上下架")
    @PostMapping("/shelf")
    Rsp<Void> shelf(@RequestBody @Valid CaseShelfParam param);
}
