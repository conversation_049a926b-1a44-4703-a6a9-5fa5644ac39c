package com.swhd.shop.api.connect.dto.param.service;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SpecialServiceUpdateParam对象")
public class SpecialServiceUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "门店id")
    private Long shopId;

    @Schema(description = "服务名称")
    private String serviceName;

    @Schema(description = "按钮iconKey")
    private String iconKey;

    @Schema(description = "服务详情图片Key")
    private String detailImageKey;

}
