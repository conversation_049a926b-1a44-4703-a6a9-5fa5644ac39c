package com.swhd.shop.api.shop.dto.param.publish;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ShopPublishUpdateParam对象")
public class ShopPublishUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "平台门店ID")
    private String platformShopId;

    @Schema(description = "发布状态(1:待发布 2:发布中 3:发布成功 4:发布失败)")
    private Integer publishStatus;

    @Schema(description = "发布错误信息")
    private String publishErrorMsg;

    @Schema(description = "最后发布时间")
    private LocalDateTime lastPublishTime;

    @Schema(description = "最后提交时间")
    private LocalDateTime lastSubmitTime;

}
