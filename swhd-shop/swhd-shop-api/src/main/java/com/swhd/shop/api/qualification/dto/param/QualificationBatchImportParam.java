package com.swhd.shop.api.qualification.dto.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "导入资质参数")
public class QualificationBatchImportParam {

    @Valid
    @NotEmpty(message = "资质列表不能为空")
    @Schema(description = "资质信息列表")
    private List<QualificationImportParam> qualificationList;

}
