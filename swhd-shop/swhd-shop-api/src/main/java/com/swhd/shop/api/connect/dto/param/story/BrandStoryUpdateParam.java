package com.swhd.shop.api.connect.dto.param.story;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "BrandStoryUpdateParam对象")
public class BrandStoryUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "公司logo")
    private String logoKey;

    @Schema(description = "背景图")
    private String backgroundImageKey;

    @Schema(description = "简述")
    private String briefDesc;

    @Schema(description = "成立时间")
    private LocalDate establishTime;

    @Schema(description = "覆盖城市")
    private Integer coverCities;

    @Schema(description = "门店规模")
    private Integer storeScale;

    @Schema(description = "员工数量")
    private Integer employeeCount;

    @Schema(description = "品牌背景列表（描述）")
    private String brandBackground;

    @Schema(description = "公司介绍列表（图片、描述）")
    private String companyIntro;

}
