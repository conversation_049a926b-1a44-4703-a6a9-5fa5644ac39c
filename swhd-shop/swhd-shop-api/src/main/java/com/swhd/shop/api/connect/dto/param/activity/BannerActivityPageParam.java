package com.swhd.shop.api.connect.dto.param.activity;

import com.swj.magiccube.api.PageReq;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "BannerActivityPageParam对象")
public class BannerActivityPageParam extends PageReq {

    @Schema(description = "活动图片key")
    private String imageKey;

    @Schema(description = "活动链接")
    private String activityUrl;

    @Schema(description = "生效日期")
    private LocalDateTime effectDate;

    @Schema(description = "失效日期")
    private LocalDateTime expireDate;

}
