package com.swhd.shop.api.common.dto.param.category;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CategoryAddParam对象")
public class CategoryAddParam {

    @Schema(description = "分类名字")
    @NotEmpty
    private String categoryName;

    @Schema(description = "分类描述")
    private String categoryDescribe;

    @Schema(description = "父id")
    private Long parentId;

    @NotEmpty
    @Schema(description = "模块")
    private String module;

    @Schema(description = "分类层级id(路径枚举)")
    private String levelCode;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "图片url")
    private String imgUrl;

    @Schema(description = "序号")
    private Integer ordered;

    @Schema(description = "扩展信息")
    private List<CategoryExtendSaveParam> extendList;

}
