package com.swhd.shop.api.connect.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AlbumInfoAddParam对象")
public class AlbumInfoAddParam {

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "标题")
    private String albumTitle;

    @Schema(description = "链接")
    private String albumUrl;

    @Schema(description = "素材类型。1：图片；2：视频")
    private Integer albumType;

    @Schema(description = "文件夹，0-视频；1-门店；2-产品；3-活动；4-其他")
    private Integer folderType;

    @Schema(description = "权重，数字越大排越前")
    private Integer weight;

}
