package com.swhd.shop.api.connect.dto.param.caseinfo;

import com.swhd.magiccube.core.annotation.JsonOss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/19 14:13
 */
@Data
@Schema(description = "CaseDetail 对象")
public class CaseDetail {

    @NotNull
    @Schema(description = "标题")
    private String title;

    @Schema(description = "描述")
    private String describe;

    @NotEmpty
    @JsonOss
    @Schema(description = "图片列表")
    private List<String> imgs;


}
