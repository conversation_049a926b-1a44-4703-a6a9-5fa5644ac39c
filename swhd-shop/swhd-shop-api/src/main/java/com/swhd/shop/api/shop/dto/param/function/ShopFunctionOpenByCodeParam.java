package com.swhd.shop.api.shop.dto.param.function;

import com.swhd.shop.api.shop.enums.ShopFunctionType;
import com.swj.magiccube.tool.enumeration.EnumCode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/12/12 16:16
 */

@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ShopFunctionOpenByCodeParam对象")
public class ShopFunctionOpenByCodeParam {

    @Schema(description = "功能编码，amapOrder：高德商户通")
    @EnumCode(value = ShopFunctionType.class)
    private String shopFunctionCode;

    @Schema(description = "门店ID")
    @NotNull(message = "门店ID不能为空")
    private Long shopId;

    @Schema(description = "平台编码(amap:高德)")
    private String platformCode;

    @Schema(description = "发布ID")
    private Long shopPublishId;

}
