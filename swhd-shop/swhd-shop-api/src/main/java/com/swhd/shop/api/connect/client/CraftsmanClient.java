package com.swhd.shop.api.connect.client;

import com.swhd.shop.api.connect.dto.param.craftsman.*;
import com.swhd.shop.api.connect.dto.result.CraftsmanPageWithPublishResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.shop.api.common.constant.ApiConstant;
import com.swhd.shop.api.connect.dto.result.CraftsmanResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = CraftsmanClient.BASE_PATH)
public interface CraftsmanClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/craftsman";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<CraftsmanResult>> page(@RequestBody @Valid CraftsmanPageParam param);

    @Operation(summary = "分页查询(发布状态)")
    @PostMapping("/pageWithPublish")
    Rsp<PageResult<CraftsmanPageWithPublishResult>> pageWithPublish(@RequestBody @Valid CraftsmanPageWithPublishParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<CraftsmanResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<CraftsmanResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid CraftsmanAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid CraftsmanUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "上下架")
    @PostMapping("/shelf")
    Rsp<Void> shelf(@RequestBody @Valid CraftsmanShelfParam param);

}
