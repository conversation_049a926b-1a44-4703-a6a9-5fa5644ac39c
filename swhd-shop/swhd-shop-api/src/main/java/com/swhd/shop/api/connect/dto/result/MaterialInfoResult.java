package com.swhd.shop.api.connect.dto.result;

import com.swhd.magiccube.core.annotation.JsonOss;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MaterialInfoResult对象")
public class MaterialInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "素材名称")
    private String materialName;

    @Schema(description = "素材链接")
    @JsonOss
    private String materialUrl;

    @Schema(description = "所属目录id")
    private Long categoryId;

    @Schema(description = "素材类型。1：图片；2：视频")
    private Integer materialType;

    @Schema(description = "文件夹，0-视频；1-门店；2-产品；3-活动；4-其他")
    private Integer folderType;

    @Schema(description = "素材版本")
    private String materialVersion;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
