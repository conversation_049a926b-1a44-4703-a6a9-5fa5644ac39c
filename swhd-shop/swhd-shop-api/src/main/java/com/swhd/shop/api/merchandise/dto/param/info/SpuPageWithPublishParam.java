package com.swhd.shop.api.merchandise.dto.param.info;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "CasePageParam对象")
public class SpuPageWithPublishParam extends PageReq {

    @Schema(description = "spu名称")
    private String spuName;

    @Schema(description = "上架状态：1 已上架 2 已下架")
    private Integer shelfStatus;

    @Schema(description = "状态：1、审核中  2、审核成功  3、审核失败")
    private Integer auditStatus;


}
