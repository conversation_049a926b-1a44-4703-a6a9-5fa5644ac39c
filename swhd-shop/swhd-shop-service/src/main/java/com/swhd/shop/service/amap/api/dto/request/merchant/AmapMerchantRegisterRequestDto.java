package com.swhd.shop.service.amap.api.dto.request.merchant;

import lombok.Data;

import java.util.Map;

@Data
public class AmapMerchantRegisterRequestDto  {
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 认证方式：PERSONAL_IDENTIFICATION-个人，ENTERPRISE_INFO-企业
     */
    private String authWay;
    
    /**
     * 主体名称
     */
    private String name;
    
    /**
     * 经营类型：agent-服务商，merchant-商家
     */
    private String engageType;
    
    /**
     * 认证信息
     */
    private Map<String, String> certInfo;
    
    /**
     * 联系人信息
     */
    private ContactInfoDto contactInfo;
} 