package com.swhd.shop.service.amap.api.dto.request.category;

import lombok.Data;
import java.util.List;

@Data
public class AmapAddCategoryRequestDto {
    /**
     * 传入comnanylD
     * 请和行业产品确认
     */
    private String businessName;
    
    /**
     * 类目信息
     */
    private List<GroupItemInfo> groupItemList;
    
    @Data
    public static class GroupItemInfo {
        /**
         * 分组标签ID，cp唯一
         * 需加cpname前缀，请和行业产品确认
         */
        private String cpGroupId;
        
        /**
         * 分组标签名称
         */
        private String groupName;
        
        /**
         * 图片
         */
        private String image;
        
        /**
         * 分组排序
         */
        private Integer rank;
    }
} 