package com.swhd.shop.service.connect.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.shop.api.common.enums.PlatformType;
import com.swhd.shop.api.connect.dto.param.caseinfo.*;
import com.swhd.shop.api.connect.dto.result.CasePageWithPublishResult;
import com.swhd.shop.api.connect.enums.ContentType;
import com.swhd.shop.service.connect.entity.Case;
import com.swhd.shop.service.connect.entity.ContentPublish;
import com.swhd.shop.service.connect.mapper.CaseMapper;
import com.swhd.shop.service.connect.service.CaseService;
import com.swhd.shop.service.connect.service.ContentPublishService;
import com.swj.magiccube.api.SortOrder;
import com.swj.magiccube.tool.json.JsonUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 云店案例表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@AllArgsConstructor
public class CaseServiceImpl extends BaseHdServiceImpl<CaseMapper, Case> implements CaseService {

    private final ContentPublishService contentPublishService;

    @Override
    public IPage<Case> page(CasePageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getCraftsmanId()), Case::getCraftsmanId, param.getCraftsmanId())
                .eq(Func.isNotEmpty(param.getCaseName()), Case::getCaseName, param.getCaseName())
                .eq(Func.isNotEmpty(param.getShopId()),Case::getShopId,param.getShopId())
                .eq(Func.isNotEmpty(param.getCaseDescription()), Case::getCaseDescription, param.getCaseDescription())
                .orderByDesc(Case::getCreateTime)
                .orderByDesc(Case::getId)
                .page(convertToPage(param));
    }

    @Override
    public IPage<CasePageWithPublishResult> pageWithPublish(CasePageWithPublishParam param) {
        if(CollectionUtil.isEmpty(param.getSort())){
            param.setSort(new ArrayList<>());
            param.getSort().add(new SortOrder(BaseHdEntity.Fields.createTime,false,false));
        }
        return baseMapper.pageWithPublish(convertToPage(param), param);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(CaseAddParam param) {
        Case caseInfo = Func.copy(param, Case.class);
        if (Objects.isNull(param.getExtendScheme())) {
            param.setExtendScheme(JsonUtil.getInstance().createObjectNode());
        }
        this.save(caseInfo);
        Long id = caseInfo.getId();
        publishToAmap(id, param.getShopId(), 1);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(CaseUpdateParam param) {
        Case entity = Func.copy(param, Case.class);
        boolean result = this.updateById(entity);
        if (result) {
            Case caseInfo = this.getById(param.getId());
            publishToAmap(param.getId(), caseInfo.getShopId(), 1);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shelf(CaseShelfParam param) {
        Case caseInfo = this.getById(param.getId());
        if (Objects.isNull(caseInfo)) {
            throw new ServiceException("案例不存在");
        }

        ContentPublish dbContentPublish = contentPublishService.lambdaQuery()
                .eq(ContentPublish::getShopId, caseInfo.getShopId())
                .eq(ContentPublish::getContentId, param.getId())
                .eq(ContentPublish::getContentType, ContentType.CASE.getCode())
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode()).one();

       /* if (dbContentPublish.getSyncStatus() == 0 || dbContentPublish.getSyncStatus() == 1) {
            throw new ServiceException("上个操作还没审核完成");
        }*/

        if (param.getShelfStatus() == 1) { //上架
            if (dbContentPublish.getShelfStatus() == 1) {
                throw new ServiceException("案例已在上架状态");
            }
            //上架
            publishToAmap(caseInfo.getId(), caseInfo.getShopId(), 2);
        } else if (param.getShelfStatus() == 2) { //下架
            if (dbContentPublish.getShelfStatus() == 2) {
                throw new ServiceException("案例已在下架状态");
            }
            //下架
            publishToAmap(caseInfo.getId(), caseInfo.getShopId(), 3);
        }

    }

    @Override
    public List<Case> listByShopId(Long sampleShopId) {
        return lambdaQuery().eq(Case::getShopId, sampleShopId).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByShopId(Long shopId) {
        if (shopId == null) {
            return;
        }

        List<Case> list = lambdaQuery().eq(Case::getShopId, shopId).list();
        if (Func.isEmpty(list)) {
            return;
        }

        Set<Long> ids = list.stream().map(Case::getId).collect(Collectors.toSet());
        // 删除记录
        this.removeByIds(ids);

        // 更新发布记录
        contentPublishService.lambdaUpdate()
                .in(ContentPublish::getContentId, ids)
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .eq(ContentPublish::getContentType, ContentType.CASE.getCode())
                .set(ContentPublish::getOperateType, 3)
                .set(ContentPublish::getOperateTime, LocalDateTime.now())
                .set(ContentPublish::getAuditStatus, 1)
                .set(ContentPublish::getSyncStatus, 0)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<Case> caseList) {
        // 保存记录
        this.saveBatch(caseList);

        // 保存发布记录
        List<ContentPublish> contentPublishList = caseList.stream().map(caseInfo -> {
            ContentPublish contentPublish = new ContentPublish();
            contentPublish.setContentType(ContentType.CASE.getCode())
                    .setContentId(caseInfo.getId())
                    .setOperateTime(LocalDateTime.now())
                    .setShopId(caseInfo.getShopId())
                    .setOperateType(1)
                    .setSyncStatus(0)
                    .setAuditStatus(1)
                    .setPlatformCode(PlatformType.AMAP.getCode());
            return contentPublish;
        }).toList();
        contentPublishService.saveBatch(contentPublishList);
    }

    private void publishToAmap(Long id, Long shopId, Integer operateType) {
        //查询是否存在记录
        ContentPublish dbContentPublish = contentPublishService.lambdaQuery()
                .eq(ContentPublish::getShopId, shopId)
                .eq(ContentPublish::getContentId, id)
                .eq(ContentPublish::getContentType, ContentType.CASE.getCode())
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode()).one();

        //保存发布记录
        ContentPublish contentPublishData = new ContentPublish();
        if (Objects.nonNull(dbContentPublish)) {
            contentPublishData.setId(dbContentPublish.getId());
            /*if (operateType != 1 && (dbContentPublish.getSyncStatus() == 0 || dbContentPublish.getSyncStatus() == 1)) {
                throw new ServiceException("上个操作还没审核完成");
            }*/
        }
        contentPublishData.setContentType(ContentType.CASE.getCode())
                .setContentId(id)
                .setOperateTime(LocalDateTime.now())
                .setShopId(shopId)
                .setOperateType(operateType)
                .setSyncStatus(0)
                .setAuditStatus(1)
                .setPlatformCode(PlatformType.AMAP.getCode());
        contentPublishService.saveOrUpdate(contentPublishData);
    }

    @Override
    public Case getByIdIncludeDeleted(Long id) {
        return this.baseMapper.selectByIdWithLogicDelete(id);
    }

}
