package com.swhd.shop.service.shop.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 云店门店功能配置表实体类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcloud_shop_shop_function")
public class ShopFunction extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "发布id")
    private Long shopPublishId;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "平台编码")
    private String platformCode;

    @Schema(description = "功能编码")
    private String functionCode;

    @Schema(description = "状态(0:待开启 1:开启中 2开启 3开启失败 4待关闭 5关闭中 6关闭 7关闭失败)")
    private Integer functionStatus;

    @Schema(description = "关闭类型(1:商家关闭 2:系统关闭)")
    private Integer closeType;

    @Schema(description = "状态描述")
    private String statusDescribe;

    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

}
