package com.swhd.shop.service.connect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.shop.api.connect.dto.param.info.MaterialInfoPageParam;
import com.swhd.shop.service.connect.entity.MaterialInfo;
import com.swhd.shop.service.connect.mapper.MaterialInfoMapper;
import com.swhd.shop.service.connect.service.MaterialInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 云店素材信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@AllArgsConstructor
public class MaterialInfoServiceImpl extends BaseHdServiceImpl<MaterialInfoMapper, MaterialInfo> implements MaterialInfoService {

    @Override
    public IPage<MaterialInfo> page(MaterialInfoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getMaterialName()), MaterialInfo::getMaterialName, param.getMaterialName())
                .eq(Func.isNotEmpty(param.getCategoryId()), MaterialInfo::getCategoryId, param.getCategoryId())
                .eq(Func.isNotEmpty(param.getMaterialType()), MaterialInfo::getMaterialType, param.getMaterialType())
                .eq(Func.isNotEmpty(param.getFolderType()), MaterialInfo::getFolderType, param.getFolderType())
                .eq(Func.isNotEmpty(param.getMaterialVersion()), MaterialInfo::getMaterialVersion, param.getMaterialVersion())
                .orderByDesc(MaterialInfo::getCreateTime)
                .orderByDesc(MaterialInfo::getId)
                .page(convertToPage(param));
    }

}
