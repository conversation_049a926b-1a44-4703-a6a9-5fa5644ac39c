package com.swhd.shop.service.amap.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.swhd.shop.service.amap.api.properties.AmapProperties;
import com.swhd.shop.api.amap.dto.param.callback.AmapCallbackParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.TreeMap;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * 高德回调基础控制器
 */
@Slf4j
public abstract class AmapBaseCallbackController {
    
    @Autowired
    protected ObjectMapper objectMapper;
    
    @Autowired
    protected AmapProperties amapProperties;
    
    /**
     * 验证请求参数
     */
    protected boolean validateRequest(AmapCallbackParam request) {
        return StringUtils.isNotBlank(request.getUtcTimestamp()) &&
               StringUtils.isNotBlank(request.getVersion()) &&
               StringUtils.isNotBlank(request.getCharset()) &&
               StringUtils.isNotBlank(request.getMethod()) &&
               StringUtils.isNotBlank(request.getSign()) &&
               StringUtils.isNotBlank(request.getSignType()) &&
               StringUtils.isNotBlank(request.getAppId()) &&
               StringUtils.isNotBlank(request.getBizContent());
    }
    
    /**
     * 验证签名
     */
    protected boolean verifySign(AmapCallbackParam request) {
        try {
            // 1. 构建签名字符串
            TreeMap<String, String> params = new TreeMap<>();
            params.put("utc_timestamp", request.getUtcTimestamp());
            params.put("version", request.getVersion());
            params.put("charset", request.getCharset());
            params.put("method", request.getMethod());
            params.put("app_id", request.getAppId());
            params.put("biz_content", request.getBizContent());
            
            StringBuilder signContent = new StringBuilder();
            params.forEach((key, value) -> {
                if (StringUtils.isNotBlank(value)) {
                    signContent.append(key).append("=").append(value).append("&");
                }
            });
            
            // 移除最后一个&
            String contentToSign = signContent.substring(0, signContent.length() - 1);
            
            // 2. 准备公钥
            String publicKeyStr = amapProperties.getCheckSignPublicKey();
            if (StringUtils.isBlank(publicKeyStr)) {
                log.error("高德验签公钥未配置");
                return false;
            }
            
            byte[] keyBytes = Base64.getDecoder().decode(publicKeyStr);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            
            // 3. 验证签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);
            signature.update(contentToSign.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.getDecoder().decode(request.getSign()));
        } catch (Exception e) {
            log.error("验证签名异常", e);
            return false;
        }
    }
} 