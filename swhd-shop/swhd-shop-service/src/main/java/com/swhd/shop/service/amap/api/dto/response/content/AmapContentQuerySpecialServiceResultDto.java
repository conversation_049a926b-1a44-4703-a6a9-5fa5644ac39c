package com.swhd.shop.service.amap.api.dto.response.content;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/23 14:25
 */
@Data
public class AmapContentQuerySpecialServiceResultDto {

    private Long id;

    private String elementId;


    /**
     * 上下架状态
     * 上架：RELATION_UP
     * 下架：MERCHANT_DEL、ADMIN_DEL
     */
    private String baseStatusREnum;

    /**
     *     EDIT(-1, "编辑"),
     *     AUDIT_WAIT(1, "待审核"),
     *     AUDIT_START(2, "审核中"),
     *     AUDIT_FAIL(3, "审核失败"),
     *     AUDIT_PASS(4, "审核成功"),
     *     MERCHANT_DEL(5, "外部商家删除"),
     *     ADMIN_DEL(6, "内部小二删除"),
     *     DEL(7, "删除"),
     */
    private String baseStatusEnum;

    private String minorId;

    private Integer weight;

    private String url;

    private Integer type;

    private String title;

    private String iconUrl;

    private String serviceLinkUrl;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
