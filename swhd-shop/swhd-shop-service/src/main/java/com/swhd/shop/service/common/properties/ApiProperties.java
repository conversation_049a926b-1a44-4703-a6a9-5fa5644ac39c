package com.swhd.shop.service.common.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@Getter
@Setter
@ConfigurationProperties(ApiProperties.PREFIX)
@Component
public class ApiProperties {

    public static final String PREFIX = "shop.api";

    /**
     * 从连接池获取连接的超时时间
     */
    private Duration connectionRequestTimeout = Duration.ofSeconds(3);

    /**
     * 建立连接的超时时间
     */
    private Duration connectTimeout = Duration.ofSeconds(3);

    /**
     * 请求的超时时间
     */
    private Duration socketTimeout = Duration.ofSeconds(15);

    /**
     * 最大的连接数
     */
    private int connectionMaxTotal = 500;

    /**
     * 每个路由最大连接数
     */
    private int connectionMaxPerRoute = 100;

}
