<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.shop.service.merchandise.mapper.SpuInfoMapper">

    <resultMap id="spuPageWithPublishMap" type="com.swhd.shop.api.merchandise.dto.result.SpuPageWithPublishResult" autoMapping="true">
        <result column="main_images" property="mainImages" typeHandler="com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler"/>
        <result column="detail_images" property="detailImages" typeHandler="com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler"/>
        <result column="shop_ids" property="shopIds" javaType="java.util.List" jdbcType="VARCHAR" typeHandler="com.swj.magiccube.mp.generic.typehandler.LongListTypeHandler"/>
    </resultMap>

    <select id="pageWithPublish" resultMap="spuPageWithPublishMap">
        SELECT
            s.*,
            p.platform_content_id,
            p.platform_code,
            p.shelf_status,
            p.audit_status,
            p.id as publish_id,
            p.operate_type,
            p.content_modify_time,
            p.operate_time,
            p.sync_status
        FROM
            tcloud_shop_spu_info s
        LEFT JOIN
            tcloud_shop_content_publish p
                ON s.id = p.content_id AND p.content_type = 'SPU' and p.is_delete = 0
        WHERE s.is_delete = 0
            <if test="param.spuName != null and param.spuName != ''">
                AND s.spu_name LIKE CONCAT('%', #{param.spuName}, '%')
            </if>
            <if test="param.shelfStatus != null">
                AND p.shelf_status = #{param.shelfStatus}
            </if>
            <if test="param.auditStatus != null">
                AND p.audit_status = #{param.auditStatus}
            </if>
    </select>
</mapper>