package com.swhd.shop.service;

import com.swj.magiccube.MagiccubeApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @since 2024/10/9
 */
@SpringBootApplication
@MapperScan("com.swhd.shop.service.**.mapper.**")
@EnableFeignClients("com.swhd")
public class ShopServiceApplication {

    public static void main(String[] args) {
        MagiccubeApplication.run(ShopServiceApplication.class, args);
    }

}
