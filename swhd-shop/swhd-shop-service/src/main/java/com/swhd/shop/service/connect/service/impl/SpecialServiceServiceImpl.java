package com.swhd.shop.service.connect.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.shop.api.common.enums.PlatformType;
import com.swhd.shop.api.connect.dto.param.service.*;
import com.swhd.shop.api.connect.dto.result.SpecialServicePageWithPublishResult;
import com.swhd.shop.api.connect.enums.ContentType;
import com.swhd.shop.service.connect.entity.ContentPublish;
import com.swhd.shop.service.connect.entity.SpecialService;
import com.swhd.shop.service.connect.mapper.SpecialServiceMapper;
import com.swhd.shop.service.connect.service.ContentPublishService;
import com.swhd.shop.service.connect.service.SpecialServiceService;
import com.swj.magiccube.api.SortOrder;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 云店特色服务表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@AllArgsConstructor
public class SpecialServiceServiceImpl extends BaseHdServiceImpl<SpecialServiceMapper, SpecialService> implements SpecialServiceService {

    private final ContentPublishService contentPublishService;


    @Override
    public IPage<SpecialService> page(SpecialServicePageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getServiceName()), SpecialService::getServiceName, param.getServiceName())
                .eq(Func.isNotEmpty(param.getIconKey()), SpecialService::getIconKey, param.getIconKey())
                .eq(Func.isNotEmpty(param.getDetailImageKey()), SpecialService::getDetailImageKey, param.getDetailImageKey())
                .orderByDesc(SpecialService::getCreateTime)
                .orderByDesc(SpecialService::getId)
                .page(convertToPage(param));
    }

    @Override
    public IPage<SpecialServicePageWithPublishResult> pageWithPublish(SpecialServicePageWithPublishParam param) {
        if(CollectionUtil.isEmpty(param.getSort())){
            param.setSort(new ArrayList<>());
            param.getSort().add(new SortOrder(BaseHdEntity.Fields.createTime,false,false));
        }
        return baseMapper.pageWithPublish(convertToPage(param), param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(SpecialServiceAddParam param) {
        SpecialService entity = Func.copy(param, SpecialService.class);
        this.save(entity);
        Long id = entity.getId();
        publishToAmap(id,param.getShopId(),1);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(SpecialServiceUpdateParam param) {
        SpecialService entity = Func.copy(param, SpecialService.class);
        boolean result = this.updateById(entity);
        if(result){
            SpecialService s = this.getById(entity.getId());
            publishToAmap(s.getId(),s.getShopId(),1);
        }
        return result;
    }

    @Override
    public void shelf(SpecialServiceShelfParam param) {
        SpecialService dbSpecialService = this.getById(param.getId());
        if(Objects.isNull(dbSpecialService)){
            throw new ServiceException("特色服务不存在");
        }

        ContentPublish dbContentPublish = contentPublishService.lambdaQuery()
                .eq(ContentPublish::getShopId, dbSpecialService.getShopId())
                .eq(ContentPublish::getContentId, param.getId())
                .eq(ContentPublish::getContentType, ContentType.SPECIAL_SERVICE.getCode())
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .one();

        /*if(dbContentPublish.getSyncStatus() == 0 || dbContentPublish.getSyncStatus() == 1){
            throw new ServiceException("上个操作还没审核完成");
        }*/

        if(param.getShelfStatus() == 1){ //上架
            if(dbContentPublish.getShelfStatus() == 1){
                throw new ServiceException("特色服务已在上架状态");
            }
            //没有上架接口，只能修改保存
            publishToAmap(dbSpecialService.getId(),dbSpecialService.getShopId(),1);
        }else if(param.getShelfStatus() == 2){ //下架
            if(dbContentPublish.getShelfStatus() == 2){
                throw new ServiceException("特色服务已在下架状态");
            }
            //下架
            publishToAmap(dbSpecialService.getId(),dbSpecialService.getShopId(),3);
        }
    }

    @Override
    public List<SpecialService> listByShopId(Long shopId) {
        return lambdaQuery().eq(SpecialService::getShopId, shopId).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByShopId(Long shopId) {
        if (shopId == null) {
            return;
        }
        List<SpecialService> list = lambdaQuery().eq(SpecialService::getShopId, shopId).list();
        if (Func.isEmpty(list)) {
            return;
        }
        Set<Long> ids = list.stream().map(SpecialService::getId).collect(Collectors.toSet());
        // 删除记录
        this.removeByIds(ids);

        // 更新发布记录
        contentPublishService.lambdaUpdate()
                .eq(ContentPublish::getShopId, shopId)
                .in(ContentPublish::getContentId, ids)
                .eq(ContentPublish::getContentType, ContentType.SPECIAL_SERVICE.getCode())
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .set(ContentPublish::getOperateType, 3)
                .set(ContentPublish::getSyncStatus, 0)
                .set(ContentPublish::getOperateTime, LocalDateTime.now())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<SpecialService> specialServiceList) {
        this.saveBatch(specialServiceList);

        // 保存发布记录
        List<ContentPublish> contentPublishList = specialServiceList.stream().map(specialService -> {
            ContentPublish contentPublish = new ContentPublish();
            contentPublish.setContentType(ContentType.SPECIAL_SERVICE.getCode())
                    .setContentId(specialService.getId())
                    .setOperateTime(LocalDateTime.now())
                    .setShopId(specialService.getShopId())
                    .setOperateType(1)
                    .setSyncStatus(0)
                    .setAuditStatus(1)
                    .setPlatformCode(PlatformType.AMAP.getCode());
            return contentPublish;
        }).toList();
        contentPublishService.saveBatch(contentPublishList);
    }

    @Override
    public SpecialService getByIdIncludeDeleted(Long id) {
        return this.baseMapper.selectByIdWithLogicDelete(id);
    }

    private void publishToAmap(Long id, Long shopId, Integer operateType) {
        //查询是否存在记录
        ContentPublish dbContentPublish = contentPublishService.lambdaQuery()
                .eq(ContentPublish::getShopId, shopId)
                .eq(ContentPublish::getContentId, id)
                .eq(ContentPublish::getContentType, ContentType.SPECIAL_SERVICE.getCode())
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .one();

        //保存发布记录
        ContentPublish contentPublishData = new ContentPublish();
        if(Objects.nonNull(dbContentPublish)){
            contentPublishData.setId(dbContentPublish.getId());
            /*if (operateType != 1 && (dbContentPublish.getSyncStatus() == 0 || dbContentPublish.getSyncStatus() == 1)) {
                throw new ServiceException("上个操作还没审核完成");
            }*/
        }
        contentPublishData.setContentType(ContentType.SPECIAL_SERVICE.getCode())
                .setContentId(id)
                .setOperateTime(LocalDateTime.now())
                .setShopId(shopId)
                .setOperateType(operateType)
                .setSyncStatus(0)
                .setAuditStatus(1)
                .setPlatformCode(PlatformType.AMAP.getCode());
        contentPublishService.saveOrUpdate(contentPublishData);
    }


}
