package com.swhd.shop.service.amap.api.dto.request.content;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/16 14:59
 */
@Data
public class AmapContentCraftsmanSaveDto {

    private String craftsmanId;

    private String bizType = "230";

    private String subType = "2302";

    private List<String> tags;

    private LocalDateTime jobTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 头像****
     */
    private String pic;


    private String shopId;

    private String cpId;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;


}
