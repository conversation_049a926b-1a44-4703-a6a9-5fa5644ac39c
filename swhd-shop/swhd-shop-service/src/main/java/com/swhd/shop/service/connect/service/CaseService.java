package com.swhd.shop.service.connect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.shop.api.connect.dto.param.caseinfo.*;
import com.swhd.shop.api.connect.dto.result.CasePageWithPublishResult;
import com.swhd.shop.service.connect.entity.Case;

import java.util.List;

/**
 * 云店案例表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface CaseService extends IBaseHdService<Case> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<Case> page(CasePageParam param);

    IPage<CasePageWithPublishResult> pageWithPublish(CasePageWithPublishParam param);

    Long add(CaseAddParam param);

	Boolean update(CaseUpdateParam param);


	void shelf(CaseShelfParam param);

    List<Case> listByShopId(Long sampleShopId);

	void removeByShopId(Long shopId);

	void addBatch(List<Case> caseList);

	/**
	 * 根据ID获取数据，包括逻辑删除的数据
	 *
	 * @param id 案例ID
	 * @return Case
	 */
	Case getByIdIncludeDeleted(Long id);
}
