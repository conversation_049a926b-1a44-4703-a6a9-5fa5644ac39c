package com.swhd.shop.service.qualification.mq;

import com.swhd.ai.api.client.AiOcrClient;
import com.swhd.ai.api.dto.param.ocr.AiOcrParam;
import com.swhd.ai.api.dto.result.AiOcrBusinessLicenseResult;
import com.swhd.ai.api.dto.result.AiOcrIdCardResult;
import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.shop.service.qualification.entity.Qualification;
import com.swhd.shop.service.qualification.service.QualificationService;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/22
 */
@Slf4j
@Component
@AllArgsConstructor
public class QualificationImportConsumer {

    private final QualificationService qualificationService;
    private final AiOcrClient aiOcrClient;

    @Bean
    public Consumer<String> shopQualificationImport() {
        return message -> TenantHolder.methodIgnoreTenantVoid(() -> {
            Qualification qualification = qualificationService.lambdaQuery()
                    .eq(Qualification::getUploadStatus, 1)
                    .last("limit 1").one();
            while (Objects.nonNull(qualification)) {
                parseUpdate(qualification);

                qualification = qualificationService.lambdaQuery()
                        .eq(Qualification::getUploadStatus, 1)
                        .last("limit 1").one();
            }
        });
    }

    /**
     * 解析更新
     */
    private void parseUpdate(Qualification qualification) {
        try {
            // 解析营业执照
            String businessLicenseUrl = OssPrivateUtil.preSignedUrl(qualification.getBusinessLicenseUrl());
            AiOcrParam businessLicenseParam = new AiOcrParam().setImageUrl(businessLicenseUrl);
            Rsp<AiOcrBusinessLicenseResult> businessRsp = aiOcrClient.businessLicense(businessLicenseParam);
            if (Rsp.failOrDataIsNull(businessRsp) || StringUtils.isBlank(businessRsp.getData().getSocialCreditCode())) {
                qualification.setUploadStatus(3).setUploadErrMsg("营业执照解析失败");
                qualificationService.updateById(qualification);
                return;
            }
            AiOcrBusinessLicenseResult businessLicenseResult = businessRsp.getData();

            // 解析身份证
            String idCardUrl = OssPrivateUtil.preSignedUrl(qualification.getIdCardFrontUrl());
            AiOcrParam idCardParam = new AiOcrParam().setImageUrl(idCardUrl);
            Rsp<AiOcrIdCardResult> idCardRsp = aiOcrClient.idCard(idCardParam);
            if (Rsp.failOrDataIsNull(idCardRsp) || StringUtils.isBlank(idCardRsp.getData().getNumber())) {
                qualification.setUploadStatus(3).setUploadErrMsg("身份证解析失败");
                qualificationService.updateById(qualification);
                return;
            }
            AiOcrIdCardResult idCardResult = idCardRsp.getData();

            qualification.setSocialCreditCode(businessLicenseResult.getSocialCreditCode())
                    .setCompanyName(businessLicenseResult.getName())
                    .setBusinessAddress(businessLicenseResult.getAddress())
                    .setLegalPerson(idCardResult.getName())
                    .setLegalPersonIdCard(idCardResult.getNumber())
                    .setUploadStatus(2);
            qualificationService.updateById(qualification);
        } catch (Exception e) {
            qualification.setUploadStatus(3).setUploadErrMsg(e.getMessage());
            qualificationService.updateById(qualification);
        }
    }


}
