package com.swhd.shop.service.merchandise.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 云店SKU表实体类
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcloud_shop_sku_info")
public class SkuInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "spuId")
    private Long spuId;

    @Schema(description = "商品名称")
    private String skuName;

    @Schema(description = "商品售价")
    private BigDecimal salePrice;

}
