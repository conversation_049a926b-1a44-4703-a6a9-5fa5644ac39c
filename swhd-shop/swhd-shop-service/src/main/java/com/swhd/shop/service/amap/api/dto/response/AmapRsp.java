package com.swhd.shop.service.amap.api.dto.response;

import com.fasterxml.jackson.annotation.JsonView;
import com.google.gson.annotations.SerializedName;
import com.swj.magiccube.api.BizStatusCode;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.api.SubError;
import com.swj.magiccube.config.SwjConfig;
import com.swj.magiccube.exception.CustomCodeException;
import com.swj.magiccube.jackson.BasicJacksonView;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Objects;

/**
 * 通用响应结构
 *
 * <AUTHOR> <EMAIL>
 * @since 2020-12-08 11:45
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonView(BasicJacksonView.class)
public class AmapRsp<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    private int code = BizStatusCode.SUCCESS_CODE;
    private String msg = DEFAULT_SUCCESS_MSG;
    private T data;
    @SerializedName("subCode")
    private String subCode;
    @SerializedName("subMsg")
    private String subMsg;
    public static final String DEFAULT_SUCCESS_MSG = "操作成功";
    private static final String DEFAULT_FAIL_MSG = "操作失败";

    public AmapRsp(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public AmapRsp(int code, T data) {
        this(code, DEFAULT_SUCCESS_MSG, data);
    }

    public AmapRsp(T data) {
        this(BizStatusCode.SUCCESS_CODE, DEFAULT_SUCCESS_MSG, data);
    }

    /**
     * 响应码是否等于 {@link BizStatusCode#SUCCESS_CODE} 或者 {@link BizStatusCode#SUCCESS_CODE_200}。
     * 更具体的响应应该通过状态码判断
     *
     * @return 响应成功返回 {@code true}，否则返回 {@code false}
     * <AUTHOR> <EMAIL>
     * @since 2021/3/8
     */
    public boolean isSuccess() {
        return Objects.equals(BizStatusCode.SUCCESS_CODE, this.code)
                || Objects.equals(10000, this.code);
    }

    /**
     * 设置细分异常
     *
     * @param subError 细分异常
     * @return 当前响应
     * <AUTHOR> <EMAIL>
     * @date 2022/4/25
     */
    public AmapRsp<T> with(@NotNull SubError subError) {
        this.setSubCode(subError.subErrorCode())
                .setSubMsg(subError.subErrorMsg());
        return this;
    }

    /**
     * 响应是否成功
     *
     * @param rsp 响应
     * @return {@code true}表示响应成功，{@code false}表示响应失败
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static boolean isSuccess(Rsp<?> rsp) {
        return Objects.nonNull(rsp) && rsp.isSuccess();
    }

    /**
     * 响应是否失败
     *
     * @param rsp 响应
     * @return {@code true}表示响应失败，{@code false}表示响应成功
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static boolean isFail(Rsp<?> rsp) {
        return !isSuccess(rsp);
    }

    /**
     * 响应是否成功并且负载数据非 {@code null}
     *
     * @param rsp 响应
     * @return 响应成功并且负载数据非 {@code null} 时返回 {@code true}，反之返回 {@code false}
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static boolean successAndDataNotNull(Rsp<?> rsp) {
        return isSuccess(rsp) && Objects.nonNull(rsp.getData());
    }


    /**
     * 响应是否失败或者负载数据为 {@code null}
     *
     * @param rsp 响应
     * @return 响应失败或者负载数据为 {@code null} 时返回 {@code true}，反之返回 {@code false}
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static boolean failOrDataIsNull(Rsp<?> rsp) {
        return !successAndDataNotNull(rsp);
    }

    /**
     * 响应是否成功并且分页负载数据非 {@code null}
     *
     * @param pageRsp 分页响应
     * @return 响应成功并且分页负载数据非 {@code null} 时返回 {@code true}，反之返回 {@code false}
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static <T> boolean successAndNotEmpty(Rsp<PageResult<T>> pageRsp) {
        return successAndDataNotNull(pageRsp) && pageRsp.getData().isNotEmpty();
    }

    /**
     * 响应是否失败或者分页负载数据为 {@code null}
     *
     * @param pageRsp 分页响应
     * @return 响应失败或者分页负载数据为 {@code null} 时返回 {@code true}，反之返回 {@code false}
     * <AUTHOR> <EMAIL>
     * @since 2020/12/8
     */
    public static <T> boolean failOrEmpty(Rsp<PageResult<T>> pageRsp) {
        return !successAndNotEmpty(pageRsp);
    }

    /**
     * 构造请求成功响应
     *
     * @param data 承载数据
     * @return 请求成功响应
     * <AUTHOR> <EMAIL>
     * @since 2020/12/9
     */
    public static <T> Rsp<T> data(T data) {
        return new Rsp<>(data);
    }

    /**
     * 构造包含承载数据的响应
     *
     * @param code 响应码
     * @param data 承载数据
     * @return 包含承载数据的响应
     * <AUTHOR> <EMAIL>
     * @since 2020/12/9
     */
    public static <T> Rsp<T> data(int code, T data) {
        return new Rsp<>(code, data);
    }

    /**
     * 构造不包含承载数据的成功响应
     *
     * @return 不包含承载数据的成功响应
     * <AUTHOR> <EMAIL>
     * @since 2021/4/7
     */
    public static <T> Rsp<T> success() {
        return new Rsp<>(null);
    }

    /**
     * 构造不包含承载数据，但指定返回消息的成功响应
     *
     * @param msg 返回消息
     * @return 成功响应
     * <AUTHOR> <EMAIL>
     * @since 2021/7/15
     */
    public static <T> Rsp<T> success(String msg) {
        Rsp<T> res = new Rsp<>(null);
        res.setMsg(msg);
        return res;
    }

    /**
     * 构造请求失败响应
     *
     * @param code 响应码
     * @param msg  返回消息
     * @return 请求失败响应
     * <AUTHOR> <EMAIL>
     * @since 2020/12/14
     */
    public static <T> Rsp<T> fail(int code, String msg) {
        return new Rsp<>(code, msg, null);
    }

    /**
     * 构造请求失败响应
     *
     * @param code     响应码
     * @param msg      返回消息
     * @param subError 细分异常
     * @return 请求失败响应
     * <AUTHOR> <EMAIL>
     * @date 2022/4/25
     */
    public static <T> Rsp<T> fail(int code, String msg, @NotNull SubError subError) {
        final Rsp<T> rsp = new Rsp<>(code, msg, null);
        return rsp.with(subError);
    }

    /**
     * 构造请求失败响应，主要是数据转换以兼容不同泛型
     *
     * @param rsp 响应
     * @return 请求失败响应
     * <AUTHOR> <EMAIL>
     * @since 2020/12/16
     */
    public static <T> Rsp<T> fail(Rsp<?> rsp) {
        return new Rsp<>(rsp.getCode(), rsp.getMsg(), null);
    }

    /**
     * 根据操作状态构造响应，如果 {@code success} 为 {@code true}，那么返回不包含承载数据的成功响应；<br/>
     * 否则根据 {@link SwjConfig#getDefaultFailCode()} 和 {@link #DEFAULT_FAIL_MSG} 构造失败响应。<br/>
     * 强烈建议使用 {@link #status(boolean, int, String)} 方法，以便能够采集到足够清晰的异常码
     *
     * @param success 操作状态
     * @return 根据 {@code success} 构造的响应
     * <AUTHOR> <EMAIL>
     * @since 2021/4/7
     */
    public static <T> Rsp<T> status(boolean success) {
        return Rsp.status(success, SwjConfig.getDefaultFailCode(), DEFAULT_FAIL_MSG);
    }

    /**
     * 根据操作状态构造响应，如果 {@code success} 为 {@code true}，那么返回不包含承载数据的成功响应；<br/>
     * 否则根据 {@link SwjConfig#getDefaultFailCode()} 和 {@code failMsg} 构造失败响应。<br/>
     * 强烈建议使用 {@link #status(boolean, int, String)} 方法，以便能够采集到足够清晰的异常码
     *
     * @param success 操作状态
     * @return 根据 {@code success} 构造的响应
     * <AUTHOR> <EMAIL>
     * @since 2021/4/7
     */
    public static <T> Rsp<T> status(boolean success, String failMsg) {
        return Rsp.status(success, SwjConfig.getDefaultFailCode(), failMsg);
    }

    /**
     * 根据操作状态构造响应，如果 {@code success} 为 {@code true}，那么返回不包含承载数据的成功响应；
     * 否则根据 {@code failCode} 和 {@code failMsg} 构造失败响应。
     *
     * @param success 操作状态
     * @return 根据 {@code success} 构造的响应
     * <AUTHOR> <EMAIL>
     * @since 2021/4/7
     */
    public static <T> Rsp<T> status(boolean success, int failCode, String failMsg) {
        if (success) {
            return new Rsp<>(null);
        }
        return Rsp.fail(failCode, failMsg);
    }

    public static <T> void assertSuccess(@NotNull Rsp<T> rsp) {
        if (Rsp.isSuccess(rsp)) {
            return;
        }
        throw new CustomCodeException(rsp.getCode(), rsp.getMsg(), true);
    }

    public static <T> void assertSuccess(Rsp<T> rsp, int defaultCode) {
        assertSuccess(rsp, defaultCode, "响应结果断言失败");
    }

    public static <T> void assertSuccess(Rsp<T> rsp, int defaultCode, String defaultMessage) {
        if (Rsp.isSuccess(rsp)) {
            return;
        }
        if (Objects.nonNull(rsp)) {
            throw new CustomCodeException(rsp.getCode(), rsp.getMsg(), true);
        } else {
            throw new CustomCodeException(defaultCode, defaultMessage, true);
        }
    }

    public static <T> void assertSuccessAndNotNull(@NotNull Rsp<T> rsp) {
        if (Rsp.isSuccess(rsp)) {
            if (Objects.nonNull(rsp.getData())) {
                return;
            }
            throw new CustomCodeException(SwjConfig.getDefaultFailCodeOrZero(), "响应结果数据为空", true);
        }
        // 不打印堆栈
        throw new CustomCodeException(rsp.getCode(), rsp.getMsg(), true);
    }

    public static <T> void assertSuccessAndNotNull(Rsp<T> rsp, int defaultCode) {
        assertSuccessAndNotNull(rsp, defaultCode, "响应结果断言失败");
    }

    public static <T> void assertSuccessAndNotNull(Rsp<T> rsp, int defaultCode, String defaultMessage) {
        if (Rsp.successAndDataNotNull(rsp)) {
            return;
        }
        if (Objects.nonNull(rsp)) {
            throw new CustomCodeException(rsp.getCode(), rsp.getMsg(), true);
        } else {
            throw new CustomCodeException(defaultCode, defaultMessage, true);
        }
    }

    public static <T> void assertSuccessAndNotEmpty(@NotNull Rsp<PageResult<T>> rsp) {
        assertSuccessAndNotNull(rsp);
        if (rsp.getData().isEmpty()) {
            throw new CustomCodeException(SwjConfig.getDefaultFailCodeOrZero(), "响应结果分页数据为空", true);
        }
    }

    public static <T> void assertSuccessAndNotEmpty(Rsp<PageResult<T>> rsp, int defaultCode) {
        assertSuccessAndNotEmpty(rsp, defaultCode, "响应结果断言失败");
    }

    public static <T> void assertSuccessAndNotEmpty(Rsp<PageResult<T>> rsp, int defaultCode, String defaultMessage) {
        if (Rsp.successAndNotEmpty(rsp)) {
            return;
        }
        if (Objects.nonNull(rsp)) {
            throw new CustomCodeException(rsp.getCode(), rsp.getMsg(), true);
        } else {
            throw new CustomCodeException(defaultCode, defaultMessage, true);
        }
    }

}