package com.swhd.shop.service.amap.api.dto.request.content;

import lombok.Data;
import java.util.List;

@Data
public class AmapContentSaveMoreRequestDto {
    /**
     * 门店: shop_id+类型
     * 门店+元素类型 shoppid_相册#moduletype
     */
    private String majorId;
    
    /**
     * shopId, 示例: xgc_20230711150116616yyju
     */
    private String cpId;
    
    /**
     * 集团ID
     */
    private String merchantId;
    
    /**
     * 公司id
     */
    private String comId;
    
    /**
     * 来源，写死，BGC
     */
    private String source;
    
    /**
     * 运营用的type，审核模板
     */
    private Integer type;
    
    /**
     * 内容参数
     */
    private ContentBaseCommendParam parameter;
    
    /**
     * 送审时，Name字段取值
     */
    private String auditName;
    
    /**
     * aTagId
     */
    private String aTagId;
    
    @Data
    public static class ContentBaseCommendParam {
        /**
         * 模板类型
         * 相册: 8 主图: 12
         * 活动Banner: 29
         * 活动宣传: 9
         * 特色服务: 10
         */
        private Integer moduleType;
        
        /**
         * 状态: 默认从1
         */
        private Integer baseStatus;
        
        /**
         * 用户类型: 默认从1
         */
        private Integer userType;
        
        /**
         * 行业: 默认从5
         */
        private Integer industry;
        
        /**
         * cpName_cpId_moduleType 需要安规则拼接
         */
        private String majorId;

        private String minorId;
        
        /**
         * 过滤时，xgc，bgc
         * 供应商：api
         */
        private String cpName;
        
        /**
         * cpId
         */
        private String cpId;
        
        /**
         * 具体各商产品
         */
        private String moduleTypeEnum;
        
        /**
         * 具体各商产品
         */
        private String moduleTypeEnumSource;
        
        /**
         * 元素信息
         */
        private List<ContentBaseCommend> contentBaseCommends;
    }
    
    @Data
    public static class ContentBaseCommend {
        /**
         * 模块ID
         */
        private String majorId;

        private String minorId;
        
        /**
         * 三方元素id，cpname_元素id
         */
        private String lastId;
        
        /**
         * 模块类型，可咨询产品
         */
        private String moduleTypeEnum;
        
        /**
         * 模块类型，可咨询产品
         */
        private String moduleTypeEnumSource;
        
        /**
         * 权重，默认100
         */
        private Integer weight;
        
        /**
         * json数据
         */
        private String data;
    }
} 