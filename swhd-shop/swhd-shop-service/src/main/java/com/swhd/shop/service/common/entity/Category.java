package com.swhd.shop.service.common.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 云店分类表实体类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcloud_shop_category")
public class Category extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "分类名字")
    private String categoryName;

    @Schema(description = "分类描述")
    private String categoryDescribe;

    @Schema(description = "父id")
    private Long parentId;

    @Schema(description = "模块")
    private String module;

    @Schema(description = "分类层级id(路径枚举)")
    private String levelCode;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "图片url")
    private String imgUrl;

    @Schema(description = "序号")
    private Integer ordered;

}
