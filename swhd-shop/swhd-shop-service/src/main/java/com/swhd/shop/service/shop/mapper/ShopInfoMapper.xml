<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.shop.service.shop.mapper.ShopInfoMapper">

    <select id="page" resultType="com.swhd.shop.service.shop.entity.ShopInfo">
        select ssi.*
        from tcloud_shop_shop_info ssi
        <if test="param.parentTenantId != null">
            join tuser_tenant_info ti on ti.id = ssi.tenant_id
            and (ti.parent_id = #{param.parentTenantId} or ti.id = #{param.parentTenantId}) and ti.is_delete = 0
        </if>
        where ssi.is_delete = 0
        <if test="param.subjectId != null">
            and ssi.subject_id = #{param.subjectId}
        </if>
        <if test="param.categoryId != null">
            and ssi.category_id = #{param.categoryId}
        </if>
        <if test="param.businessName != null and businessName != ''">
            and ssi.business_name = #{param.businessName}
        </if>
        <if test="param.shopName != null and param.shopName != ''">
            and ssi.shop_name like concat('%', #{param.shopName}, '%')
        </if>
        <if test="param.regionCode != null and regionCode != ''">
            and ssi.region_code = #{param.regionCode}
        </if>
        <if test="param.qualificationId != null">
            and ssi.qualification_id = #{param.qualificationId}
        </if>
        <if test="param.operationType != null">
            and ssi.operation_type = #{param.operationType}
        </if>
        <if test="param.sourceCode != null and sourceCode != ''">
            and ssi.source_code = #{param.sourceCode}
        </if>
        <if test="param.contactPhone != null and contactPhone != ''">
            and ssi.contact_phone = #{param.contactPhone}
        </if>
        order by ssi.create_time desc, ssi.id desc
    </select>

    <select id="selectPageWithPublish" resultType="com.swhd.shop.api.shop.dto.result.PageShopWithPublishResult">
        SELECT
        si.*,
        sp.platform_code,
        sp.platform_shop_id,
        COALESCE(sp.publish_status, 0) as publish_status,
        sp.publish_error_msg,
        sp.last_publish_time,
        sp.last_submit_time,
        sp.id as shop_publish_id
        FROM tcloud_shop_shop_info si
        <if test="param.parentTenantId != null">
            join tuser_tenant_info ti on ti.id = si.tenant_id
            and (ti.parent_id = #{param.parentTenantId} or ti.id = #{param.parentTenantId}) and ti.is_delete = 0
        </if>
        LEFT JOIN tcloud_shop_shop_publish sp ON si.id = sp.shop_id and sp.is_delete = 0
        where si.is_delete = 0
        <!-- 门店名称模糊查询 -->
        <if test="param.shopName != null and param.shopName != ''">
            AND si.shop_name LIKE CONCAT('%', #{param.shopName}, '%')
        </if>

        <if test="param.ids != null and param.ids.size() > 0">
            AND
            si.id IN
            <foreach collection="param.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <!-- 资质ID -->
        <if test="param.qualificationId != null">
            AND si.qualification_id = #{param.qualificationId}
        </if>

        <!-- 商家名称模糊查询 -->
        <if test="param.businessName != null and param.businessName != ''">
            AND si.business_name LIKE CONCAT('%', #{param.businessName}, '%')
        </if>

        <!-- 区域编码匹配（省市区） -->
        <if test="param.areaCode != null and param.areaCode != ''">
            AND (
            si.province = #{param.areaCode} OR
            si.city = #{param.areaCode} OR
            si.district = #{param.areaCode}
            )
        </if>

        <!-- 门店属性 -->
        <if test="param.operationType != null">
            AND si.operation_type = #{param.operationType}
        </if>

        <!-- 门店来源 -->
        <if test="param.sourceCode != null and param.sourceCode != ''">
            AND si.source_code = #{param.sourceCode}
        </if>

        <!-- 创建时间区间 -->
        <if test="param.createTimeStart != null">
            AND si.create_time >= #{param.createTimeStart}
        </if>

        <if test="param.createTimeEnd != null">
            AND si.create_time &lt;= #{param.createTimeEnd}
        </if>

        <if test="param.platformCode != null and param.platformCode != ''">
            AND sp.platform_code = #{param.platformCode}
        </if>

        <if test="param.publishStatusList != null and param.publishStatusList.size() > 0">
            AND (
            sp.publish_status IN
            <foreach collection="param.publishStatusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
            <if test="param.publishStatusList.contains(0)">
                OR sp.id IS NULL
            </if>
            )
        </if>
        <!-- 最后发布时间区间 -->
        <if test="param.lastPublishTimeStart != null">
            AND sp.last_publish_time >= #{param.lastPublishTimeStart}
        </if>
        <if test="param.lastPublishTimeEnd != null">
            AND sp.last_publish_time &lt;= #{param.lastPublishTimeEnd}
        </if>

    </select>


</mapper>