package com.swhd.shop.service.connect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.shop.api.connect.dto.param.publish.ContentPublishPageParam;
import com.swhd.shop.service.connect.entity.ContentPublish;

/**
 * 云店内容发布表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface ContentPublishService extends IBaseHdService<ContentPublish> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<ContentPublish> page(ContentPublishPageParam param);

}
