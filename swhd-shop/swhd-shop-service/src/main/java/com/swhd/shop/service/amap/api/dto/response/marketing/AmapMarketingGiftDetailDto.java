package com.swhd.shop.service.amap.api.dto.response.marketing;

import lombok.Data;
import java.util.List;

@Data
public class AmapMarketingGiftDetailDto {
    /**
     * 礼品ID
     */
    private Long giftId;
    
    /**
     * 店铺ID
     */
    private String shopId;
    
    /**
     * 店铺名称
     */
    private String shopName;
    
    /**
     * POI ID
     */
    private String poiId;
    
    /**
     * POI 名称
     */
    private String poiName;
    
    /**
     * 类型
     */
    private Integer type;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 图标文本
     */
    private String iconText;
    
    /**
     * 总限制数量
     */
    private Integer totalLimit;
    
    /**
     * 每日限制数量
     */
    private Integer dailyLimit;
    
    /**
     * 店铺总限制数量
     */
    private Integer shopTotalLimit;
    
    /**
     * 店铺每日限制数量
     */
    private Integer shopDailyLimit;
    
    /**
     * 每用户限制数量
     */
    private Integer perUserLimit;
    
    /**
     * 有效开始时间
     */
    private Long validStartTime;
    
    /**
     * 有效结束时间
     */
    private Long validEndTime;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 状态备注
     */
    private String statusRemark;
    
    /**
     * 创建时间
     */
    private Long gmtCreate;
    
    /**
     * 修改时间
     */
    private Long gmtModified;
    
    /**
     * 使用条款
     */
    private String termsConditions;
    
    /**
     * 已使用数量
     */
    private Integer usedCount;
    
    /**
     * 已领取数量
     */
    private Integer recvedCount;
    
    /**
     * 用户领取状态
     */
    private Integer userRecvStatus;
    
    /**
     * 用户是否有可用礼品
     */
    private Integer userHasUsableGift;
    
    /**
     * 礼品记录ID
     */
    private Long giftRecordId;
    
    /**
     * 副标题
     */
    private String subtitles;
    
    /**
     * 礼品任务
     */
    private GiftTaskDto giftTask;
    
    /**
     * 是否推送客户
     */
    private Integer isPushCustomer;
    
    /**
     * 推送客户字段
     */
    private List<String> pushCustomerField;
    
    /**
     * 活动URL
     */
    private String actUrl;
    
    /**
     * 核销标识
     */
    private Integer writeOff;
    
    /**
     * CP名称
     */
    private String cpName;
    
    /**
     * 店铺用户限制
     */
    private Integer shopUserLimit;
    
    /**
     * 缩略图URL
     */
    private String thumbnailUrl;
    
    /**
     * 礼品图片
     */
    private String giftImage;
    
    /**
     * 已领取用户列表
     */
    private Object receivedUserList;
    
    /**
     * 原价
     */
    private Integer priceOrigin;
    
    /**
     * 销售价
     */
    private Integer priceSale;
    
    /**
     * 礼品图片
     */
    private String giftPicture;
    
    /**
     * 店铺每日消费
     */
    private Integer shopDailyConsume;
    
    /**
     * VIP类型
     */
    private Integer vipType;
    
    /**
     * 是否VIP
     */
    private Integer vip;
    
    /**
     * 规则文本
     */
    private String ruleText;
    
    /**
     * 商户类型
     */
    private Integer merchantType;
    
    /**
     * 广告收费类型
     */
    private Integer adChargeType;
    
    /**
     * 礼品来源
     */
    private Integer giftSource;
} 