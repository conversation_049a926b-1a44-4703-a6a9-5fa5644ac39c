package com.swhd.shop.service.connect.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.shop.api.common.enums.PlatformType;
import com.swhd.shop.api.connect.dto.param.info.*;
import com.swhd.shop.api.connect.dto.result.AlbumInfoPublishResult;
import com.swhd.shop.api.connect.enums.ContentType;
import com.swhd.shop.service.connect.entity.AlbumInfo;
import com.swhd.shop.service.connect.entity.ContentPublish;
import com.swhd.shop.service.connect.mapper.AlbumInfoMapper;
import com.swhd.shop.service.connect.service.AlbumInfoService;
import com.swhd.shop.service.connect.service.ContentPublishService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 云店相册信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
@AllArgsConstructor
public class AlbumInfoServiceImpl extends BaseHdServiceImpl<AlbumInfoMapper, AlbumInfo> implements AlbumInfoService {


    private final ContentPublishService contentPublishService;

    private final static int FLODER_MAX_COUNT = 30;


    @Override
    public IPage<AlbumInfo> page(AlbumInfoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getShopId()), AlbumInfo::getShopId, param.getShopId())
                .eq(Func.isNotEmpty(param.getAlbumTitle()), AlbumInfo::getAlbumTitle, param.getAlbumTitle())
                .eq(Func.isNotEmpty(param.getAlbumUrl()), AlbumInfo::getAlbumUrl, param.getAlbumUrl())
                .eq(Func.isNotEmpty(param.getAlbumType()), AlbumInfo::getAlbumType, param.getAlbumType())
                .eq(Func.isNotEmpty(param.getFolderType()), AlbumInfo::getFolderType, param.getFolderType())
                .eq(Func.isNotEmpty(param.getWeight()), AlbumInfo::getWeight, param.getWeight())
                .orderByDesc(AlbumInfo::getCreateTime)
                .orderByDesc(AlbumInfo::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<AlbumInfo> listByShopId(Long shopId) {
        return lambdaQuery().eq(AlbumInfo::getShopId, shopId).list();
    }

    @Override
    public List<AlbumInfoPublishResult> listPublish(AlbumListGroupByFolderParam param) {
        return this.baseMapper.listPublish(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(AlbumInfoAddParam param) {

        Long count = this.lambdaQuery().eq(AlbumInfo::getShopId, param.getShopId())
                .eq(AlbumInfo::getFolderType, param.getFolderType())
                .count();

        if(count >= FLODER_MAX_COUNT){
            throw new ServiceException("超过了相册的最大容量");
        }

        AlbumInfo entity = Func.copy(param, AlbumInfo.class);
        this.save(entity);
        Long contentId = entity.getId();

        //保存发布记录
        ContentPublish contentPublishData = new ContentPublish();
        contentPublishData.setContentType(ContentType.ALBUM.getCode())
                .setContentId(contentId)
                .setOperateTime(LocalDateTime.now())
                .setShopId(entity.getShopId())
                .setOperateType(1)
                .setSyncStatus(0)
                .setAuditStatus(1)
                .setPlatformCode(PlatformType.AMAP.getCode());
        contentPublishService.save(contentPublishData);
        return contentId;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fullFolderUpdate(AlbumFullFolderUpdateParam param) {
        List<AlbumInfo> list = this.lambdaQuery().eq(AlbumInfo::getShopId, param.getShopId())
                .eq(AlbumInfo::getFolderType, param.getFolderType())
                .orderByDesc(AlbumInfo::getWeight)
                .list();

        Map<Long, AlbumInfo> dbIdMap = list.stream().collect(Collectors.toMap(AlbumInfo::getId, Function.identity()));

        // 获取更新列表
        List<AlbumInfoUpdateParam> updateParams = param.getUpdateParams();

        // 计算需要删除的记录
        List<Long> updateIds = updateParams.stream().map(AlbumInfoUpdateParam::getId).toList();
        List<Long> removeIds = CollectionUtil.subtractToList(dbIdMap.keySet(), updateIds);

        // 删除不再需要的记录
        if (CollectionUtil.isNotEmpty(removeIds)) {
            this.removeByIds(removeIds);
        }

        // 计算需要更新weight的记录
        List<AlbumInfo> needUpdateList = new ArrayList<>();
        int baseWeight = 100;
        int weightStep = 10; // weight间隔，方便以后插入数据

        for (int i = 0; i < updateParams.size(); i++) {
            AlbumInfoUpdateParam updateParam = updateParams.get(i);
            AlbumInfo dbInfo = dbIdMap.get(updateParam.getId());

            // 计算新的weight值
            int newWeight = baseWeight + (updateParams.size() - i) * weightStep;

            // 只有当weight值发生变化时才更新
            if (dbInfo != null && dbInfo.getWeight() != newWeight) {
                dbInfo.setWeight(newWeight);
                needUpdateList.add(dbInfo);
            }
        }

        // 批量更新需要修改weight的记录
        if (CollectionUtil.isNotEmpty(needUpdateList)) {
            this.updateBatchById(needUpdateList);
        }
    }

    @Override
    public boolean remove(Long id) {

        AlbumInfo albumInfo = this.getById(id);
        if(Objects.isNull(albumInfo)){
            throw new ServiceException("内容不存在");
        }

        ContentPublish contentPublish = contentPublishService.lambdaQuery()
                .eq(ContentPublish::getContentId, id)
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .one();
        if(Objects.nonNull(contentPublish)){
            /*if(contentPublish.getSyncStatus() == 0 || contentPublish.getSyncStatus() == 1){
                throw new ServiceException("上个操作还没审核完成");
            }*/
            //更新操作记录
            ContentPublish updateData = new ContentPublish();
            updateData.setId(contentPublish.getId());
            updateData.setOperateType(3);
            updateData.setOperateTime(LocalDateTime.now());
            updateData.setAuditStatus(1);
            updateData.setSyncStatus(0);
            contentPublishService.updateById(updateData);
        }

        return super.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByShopId(Long shopId) {
        if (shopId == null) {
            return;
        }
        List<AlbumInfo> list = lambdaQuery().eq(AlbumInfo::getShopId, shopId).list();
        if (CollectionUtil.isEmpty(list)) {
           return;
        }
        Set<Long> ids = list.stream().map(BaseHdEntity::getId).collect(Collectors.toSet());
        this.removeByIds(ids);

        // 更新操作记录
        contentPublishService.lambdaUpdate().in(ContentPublish::getContentId, ids)
                .eq(ContentPublish::getPlatformCode, PlatformType.AMAP.getCode())
                .eq(ContentPublish::getContentType, ContentType.ALBUM.getCode())
                .set(ContentPublish::getOperateType, 3)
                .set(ContentPublish::getOperateTime, LocalDateTime.now())
                .set(ContentPublish::getAuditStatus, 1)
                .set(ContentPublish::getSyncStatus, 0)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<AlbumInfo> albumInfoList) {
        // 保存记录
        this.saveBatch(albumInfoList);

        //保存发布记录
        List<ContentPublish> contentPublishList = albumInfoList.stream().map(albumInfo -> {
            ContentPublish contentPublish = new ContentPublish();
            contentPublish.setContentType(ContentType.ALBUM.getCode())
                    .setContentId(albumInfo.getId())
                    .setOperateTime(LocalDateTime.now())
                    .setShopId(albumInfo.getShopId())
                    .setOperateType(1)
                    .setSyncStatus(0)
                    .setAuditStatus(1)
                    .setPlatformCode(PlatformType.AMAP.getCode());
            return contentPublish;
        }).toList();

        contentPublishService.saveBatch(contentPublishList);
    }

    @Override
    public AlbumInfo getByIdIncludeDeleted(Long id) {
        return this.baseMapper.selectByIdWithLogicDelete(id);
    }

}
