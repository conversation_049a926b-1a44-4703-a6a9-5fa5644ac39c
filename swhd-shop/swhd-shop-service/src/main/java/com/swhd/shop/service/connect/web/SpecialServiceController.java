package com.swhd.shop.service.connect.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.shop.api.connect.client.SpecialServiceClient;
import com.swhd.shop.api.connect.dto.param.service.*;
import com.swhd.shop.api.connect.dto.result.SpecialServicePageWithPublishResult;
import com.swhd.shop.api.connect.dto.result.SpecialServiceResult;
import com.swhd.shop.service.connect.entity.SpecialService;
import com.swhd.shop.service.connect.service.SpecialServiceService;
import com.swhd.shop.service.shop.service.ShopInfoService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@RestController
@AllArgsConstructor
@RequestMapping(SpecialServiceClient.BASE_PATH)
public class SpecialServiceController implements SpecialServiceClient {

    private final SpecialServiceService specialServiceService;
    private final ShopInfoService shopInfoService;

    @Override
    public Rsp<PageResult<SpecialServiceResult>> page(SpecialServicePageParam param) {
        IPage<SpecialService> iPage = specialServiceService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, SpecialServiceResult.class));
    }

    @Override
    public Rsp<SpecialServiceResult> getById(Long id) {
        SpecialService entity = specialServiceService.getById(id);
        return RspHd.data(Func.copy(entity, SpecialServiceResult.class));
    }

    @Override
    public Rsp<List<SpecialServiceResult>> listByIds(Collection<Long> ids) {
        List<SpecialService> list = specialServiceService.listByIds(ids);
        return RspHd.data(Func.copy(list, SpecialServiceResult.class));
    }

    @Override
    public Rsp<Void> add(SpecialServiceAddParam param) {
        // 校验门店
        shopInfoService.getOrThrow(param.getShopId());
        Long id = specialServiceService.add(param);
        return RspHd.success();
    }

    @Override
    public Rsp<Void> update(SpecialServiceUpdateParam param) {
        boolean result = specialServiceService.update(param);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = specialServiceService.removeByIds(ids);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> shelf(SpecialServiceShelfParam param) {
        specialServiceService.shelf(param);
        return Rsp.success();
    }

    @Override
    public Rsp<PageResult<SpecialServicePageWithPublishResult>> pageWithPublish(SpecialServicePageWithPublishParam param) {
        return RspHd.data(PageUtil.convertFromMyBatis(specialServiceService.pageWithPublish(param), SpecialServicePageWithPublishResult.class));
    }

}
