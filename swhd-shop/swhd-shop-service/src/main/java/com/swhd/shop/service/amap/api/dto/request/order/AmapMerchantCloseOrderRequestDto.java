package com.swhd.shop.service.amap.api.dto.request.order;

import lombok.Data;
import java.util.List;

@Data
public class AmapMerchantCloseOrderRequestDto {
    /**
     * 调用来源
     */
    private String source;
    
    /**
     * 审批类型
     * 关单：8（无审批流不用传）
     */
    private String auditType;
    
    /**
     * 场景
     */
    private String scene;
    
    /**
     * 幂等id
     */
    private String requestId;
    
    /**
     * 订单id列表
     */
    private List<String> orderIdList;
} 