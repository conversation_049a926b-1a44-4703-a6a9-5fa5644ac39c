package com.swhd.shop.service.shop.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.shop.api.shop.dto.param.info.*;
import com.swhd.shop.api.shop.dto.result.BatchPublishResult;
import com.swhd.shop.api.shop.dto.result.PageShopWithPublishResult;
import com.swhd.shop.api.shop.dto.result.ShopInfoResult;
import com.swhd.shop.service.shop.entity.ShopInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 云店门店信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
public interface ShopInfoService extends IBaseHdService<ShopInfo> {

	/**
	 * 根据id获取门店信息，不存在时抛出异常
	 */
	ShopInfo getOrThrow(Long id);

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<ShopInfo> page(ShopInfoPageParam param);

	List<ShopInfoResult> list(ShopInfoListParam param);

    Long add(ShopInfoAddParam param);

	List<Long> batchRegisterAndAdd(Long tenantId, Long parentTenantId, List<ShopInfoAddParam> addParamList);

	boolean update(ShopInfoUpdateParam param);
	
	/**
	 * 修改已发布门店信息
	 *
	 * @param param 修改参数
	 * @return 是否成功
	 */
	boolean updateBase(ShopInfoUpdateBaseParam param);

	BatchPublishResult batchPublish(BatchPublishParam param);

	IPage<PageShopWithPublishResult> pageWithPublish(PageShopWithPublishParam param);

	ShopInfoResult getDetailById(Long id);

	Boolean removeById(Long id);

	/**
	 * 数据清洗接：为所有门店插入权益账户数据
	 * @param shopIds 门店id集合
	 */
	void initRightAccountForAll(Collection<Long> shopIds);

    List<ShopInfoResult> listByNames(Collection<String> shopNames);

}
