package com.swhd.shop.web.tenant.common.constant;

/**
 * <AUTHOR> <EMAIL>
 * @since 2025/1/21
 */
public class QualificationConstant {

    /**
     * 文件的最大大小，单位：MB
     */
    public static final int MAX_ZIP_SIZE = 300;

    /**
     * 身份证正面文件名后缀
     */
    public static final String ID_CARD_FRONT_SUFFIX = "_正面";

    /**
     * 身份证反面文件名后缀
     */
    public static final String ID_CARD_BACK_SUFFIX = "_反面";

    /**
     * 社会信用代码正则
     */
    public static final String SOCIAL_CREDIT_CODE_REGEX = "^[A-Z0-9]{18}$";

    /**
     * 导入结果缓存key前缀
     */
    public static final String CACHE_KEY_PREFIX = "swhd:shop:qualification_import_result_";

    /**
     * 导入结果缓存key过期时间，单位：分钟
     */
    public static final int CACHE_KEY_EXPIRE_MINUTES = 180;

    /**
     * 导入结果excel模版
     */
    public static final String QUALIFICATION_IMPORT_RESULT_TEMPLATE_PATH = "classpath:easypoi/qualification_import_result.xlsx";
}
