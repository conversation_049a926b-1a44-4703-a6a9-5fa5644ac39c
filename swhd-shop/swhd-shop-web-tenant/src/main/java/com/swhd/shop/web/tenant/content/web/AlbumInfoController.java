package com.swhd.shop.web.tenant.content.web;

import com.swhd.shop.api.connect.client.AlbumInfoClient;
import com.swhd.shop.api.connect.dto.param.info.AlbumInfoAddParam;
import com.swhd.shop.api.connect.dto.param.info.AlbumListGroupByFolderParam;
import com.swhd.shop.api.connect.dto.result.AlbumListGroupByFolderResult;
import com.swhd.shop.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/24 19:55
 */

@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping(WebConstant.BASE_PATH+"/albumInfo")
public class AlbumInfoController {

    private AlbumInfoClient albumInfoClient;

    @Operation(summary = "列表（根据文件分组）")
    @PostMapping("/listPublishGroupByFolder")
    Rsp<List<AlbumListGroupByFolderResult>> listPublishGroupByFolder(@RequestBody @Valid AlbumListGroupByFolderParam param){
        return albumInfoClient.listPublishGroupByFolder(param);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Long> add(@RequestBody @Valid AlbumInfoAddParam param){
        return albumInfoClient.add(param);
    }

    @Operation(summary = "删除")
    @GetMapping("/removeById")
    Rsp<Void> removeById(@RequestParam("id") Long id){
        return albumInfoClient.removeById(id);
    }

}
