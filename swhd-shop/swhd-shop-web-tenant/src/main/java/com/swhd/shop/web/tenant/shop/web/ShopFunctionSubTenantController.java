package com.swhd.shop.web.tenant.shop.web;

import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.shop.api.shop.client.ShopFunctionClient;
import com.swhd.shop.api.shop.dto.param.function.ShopFunctionCloseByCodeParam;
import com.swhd.shop.api.shop.dto.param.function.ShopFunctionOpenByCodeParam;
import com.swhd.shop.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.swhd.user.auth.permission.tenant.constant.AuthUserConstant.SUB_TENANT_ID_PARAMETER_NAME;

/**
 * <AUTHOR>
 * @since 2024/12/6 14:02
 */

@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping(WebConstant.BASE_PATH + "/shopFunctionSubTenant")
public class ShopFunctionSubTenantController {

    private final ShopFunctionClient shopFunctionClient;

    @Operation(summary = "根据功能编码开启")
    @PostMapping("/openByCode")
    public Rsp<Boolean> openByCode(@RequestBody @Valid ShopFunctionOpenByCodeParam param,
                            @RequestParam(value = SUB_TENANT_ID_PARAMETER_NAME) Long subTenantId) {
        return TenantHolder.methodTenant(subTenantId, () -> shopFunctionClient.openByCode(param));
    }

    @Operation(summary = "根据功能编码关闭")
    @PostMapping("/closeByCode")
    public Rsp<Boolean> closeByCode(@RequestBody @Valid ShopFunctionCloseByCodeParam param,
                             @RequestParam(value = SUB_TENANT_ID_PARAMETER_NAME) Long subTenantId) {
        return TenantHolder.methodTenant(subTenantId, () -> shopFunctionClient.closeByCode(param));
    }

}


