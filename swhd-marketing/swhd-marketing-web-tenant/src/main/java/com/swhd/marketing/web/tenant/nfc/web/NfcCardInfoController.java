package com.swhd.marketing.web.tenant.nfc.web;

import com.swhd.marketing.api.nfc.client.NfcCardInfoClient;
import com.swhd.marketing.api.nfc.dto.param.info.NfcCardInfoAddParam;
import com.swhd.marketing.api.nfc.dto.param.info.NfcCardInfoPageParam;
import com.swhd.marketing.api.nfc.dto.param.info.NfcCardInfoUpdateParam;
import com.swhd.marketing.api.nfc.dto.result.NfcCardInfoResult;
import com.swhd.marketing.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2025/2/25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/nfcCardInfo")
public class NfcCardInfoController {

    private final NfcCardInfoClient nfcCardInfoClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<NfcCardInfoResult>> page(@RequestBody @Valid NfcCardInfoPageParam param) {
        return nfcCardInfoClient.page(param);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<NfcCardInfoResult> getById(@RequestParam("id") Long id) {
        return nfcCardInfoClient.getById(id);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid NfcCardInfoAddParam param) {
        return nfcCardInfoClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid NfcCardInfoUpdateParam param) {
        return nfcCardInfoClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return nfcCardInfoClient.removeByIds(ids);
    }

}
