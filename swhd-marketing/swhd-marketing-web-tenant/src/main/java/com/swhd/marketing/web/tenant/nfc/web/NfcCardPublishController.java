package com.swhd.marketing.web.tenant.nfc.web;

import com.swhd.magiccube.core.auth.Auth;
import com.swhd.marketing.api.nfc.client.NfcCardPublishClient;
import com.swhd.marketing.api.nfc.dto.param.publish.NfcPublishToXhsParam;
import com.swhd.marketing.web.tenant.common.constant.WebConstant;
import com.swhd.marketing.api.nfc.dto.param.publish.NfcPublishToDouyinParam;
import com.swhd.marketing.api.nfc.dto.result.NfcPublishToDouyinResult;
import com.swj.magiccube.api.Rsp;
import com.swj.mcn.dto.resp.channel.xhs.XhsShareSignatureResp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/2/26 8:49
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/nfcCardPublish")
public class NfcCardPublishController {

    private final NfcCardPublishClient nfcCardPublishClient;

    @Auth(way = Auth.Way.ANONYMOUS)
    @Operation(summary = "发布到抖音")
    @PostMapping("/publishToDouyin")
    Rsp<NfcPublishToDouyinResult> publishToDouyin(@RequestBody @Valid NfcPublishToDouyinParam param) {
        return nfcCardPublishClient.publishToDouyin(param);
    }


    @Auth(way = Auth.Way.ANONYMOUS)
    @Operation(summary = "发布到小红书")
    @PostMapping("/publishToXhs")
    Rsp<XhsShareSignatureResp> publishToXhs(@RequestBody @Valid NfcPublishToXhsParam param){
        return nfcCardPublishClient.publishToXhs(param);
    }

}
