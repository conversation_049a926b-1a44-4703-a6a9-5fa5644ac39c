package com.swhd.marketing.service.nfc.web;

import cn.hutool.core.util.StrUtil;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.marketing.api.nfc.client.NfcCardPublishClient;
import com.swhd.marketing.api.nfc.dto.param.publish.NfcPublishToDouyinParam;
import com.swhd.marketing.api.nfc.dto.param.publish.NfcPublishToXhsParam;
import com.swhd.marketing.api.nfc.dto.result.NfcPublishToDouyinResult;
import com.swhd.marketing.service.nfc.entity.NfcCardInfo;
import com.swhd.marketing.service.nfc.entity.NfcCardMcnTask;
import com.swhd.marketing.service.nfc.entity.NfcCardPublishRecord;
import com.swhd.marketing.service.nfc.service.NfcCardInfoService;
import com.swhd.marketing.service.nfc.service.NfcCardMcnTaskService;
import com.swhd.marketing.service.nfc.service.NfcCardPublishRecordService;
import com.swj.magiccube.api.Rsp;
import com.swj.mcn.api.channel.douyin.DouyinScaningNoteClient;
import com.swj.mcn.api.channel.xhs.XhsScaningNoteClient;
import com.swj.mcn.dto.req.channel.douyin.GetDraftSchemaReq;
import com.swj.mcn.dto.req.channel.xhs.XhsTaskItemGetReq;
import com.swj.mcn.dto.resp.channel.douyin.DouyinDraftSchemaResp;
import com.swj.mcn.dto.resp.channel.xhs.XhsShareSignatureResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.swj.magiccube.mp.tx.MagiccubeTransactionManager;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025-02-22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(NfcCardPublishClient.BASE_PATH)
public class NfcCardPublishController implements NfcCardPublishClient {

    private final NfcCardInfoService nfcCardInfoService;
    private final NfcCardMcnTaskService nfcCardMcnTaskService;
    private final DouyinScaningNoteClient douyinScaningNoteClient;
    private final NfcCardPublishRecordService nfcCardPublishRecordService;
    private final XhsScaningNoteClient xhsScaningNoteClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "swhd:marketing:nfc:content:publish", key = {"#param.fid"}, waitTime = 6000,tenant = false)
    public Rsp<XhsShareSignatureResp> publishToXhs(NfcPublishToXhsParam param) {
        log.info("开始发布小红书内容，参数：{}", param);
        return publishContent(param.getFid(), "小红书", (nfcCardMcnTask) -> {
            // 调用小红书接口获取任务项
            XhsTaskItemGetReq taskItemGetReq = new XhsTaskItemGetReq();
            taskItemGetReq.setTaskId(nfcCardMcnTask.getTaskId().toString());
            taskItemGetReq.setTenantId(StrUtil.toString(TenantHolder.getTenantId()));
            Rsp<XhsShareSignatureResp> taskItemRsp = xhsScaningNoteClient.getCommonTaskItem(taskItemGetReq);
            if (!taskItemRsp.isSuccess()) {
                throw new ServiceException(taskItemRsp.getCode() == 100604006 ?"任务内容不足，请联系管理员":"获取内容失败：" + taskItemRsp.getMsg());
            }
            return new PublishResult<>(taskItemRsp, taskItemRsp.getData().getDraftId());
        });
    }

    @Override
    @Lockable(prefixKey = "swhd:marketing:nfc:content:publish", key = {"#param.fid"}, waitTime = 6000,tenant = false)
    public Rsp<NfcPublishToDouyinResult> publishToDouyin(NfcPublishToDouyinParam param) {
        log.info("开始发布抖音内容，参数：{}", param);
        return publishContent(param.getFid(), "抖音", (nfcCardMcnTask) -> {
            GetDraftSchemaReq getSchemaReq = new GetDraftSchemaReq();
            getSchemaReq.setTaskId(nfcCardMcnTask.getTaskId().toString());
            getSchemaReq.setPublishTo(param.getPublishTo());
            Rsp<DouyinDraftSchemaResp> draftSchemaRsp = douyinScaningNoteClient.getDraftSchema(getSchemaReq);
            if (!draftSchemaRsp.isSuccess()) {
                throw new ServiceException(draftSchemaRsp.getCode() == 100604006 ?"任务内容不足，请联系管理员":"获取内容失败：" + draftSchemaRsp.getMsg());
            }

            NfcPublishToDouyinResult result = new NfcPublishToDouyinResult();
            result.setContentSchema(draftSchemaRsp.getData().getContentSchema());
            return new PublishResult<>(Rsp.data(result), draftSchemaRsp.getData().getDraftId());
        });
    }



    /**
     * 发布内容通用方法
     *
     * @param fid NFC卡FID
     * @param platform 平台类型
     * @param publishFunction 具体平台的发布逻辑
     * @param <T> 返回结果类型
     * @return 发布结果
     */
    private <T> Rsp<T> publishContent(String fid, String platform, Function<NfcCardMcnTask, PublishResult<T>> publishFunction) {
        // 获取NFC卡信息
        NfcCardInfo nfcCardInfo = TenantHolder.methodIgnoreTenant(() -> nfcCardInfoService.lambdaQuery()
                .eq(NfcCardInfo::getFid, fid)
                .one());

        // 验证NFC卡
        validateNfcCard(nfcCardInfo);

        return TenantHolder.methodTenant(nfcCardInfo.getTenantId(), () -> {
            // 获取任务信息
            NfcCardMcnTask nfcCardMcnTask = nfcCardMcnTaskService.lambdaQuery()
                    .eq(NfcCardMcnTask::getCardId, nfcCardInfo.getId())
                    .one();

            if (Objects.isNull(nfcCardMcnTask) || Objects.isNull(nfcCardMcnTask.getTaskId())) {
                throw new ServiceException("卡不存在");
            }

            TransactionStatus tcStatus = null;
            try {
                // 执行平台特定的发布逻辑
                PublishResult<T> publishResult = publishFunction.apply(nfcCardMcnTask);

                tcStatus = MagiccubeTransactionManager.open();
                // 创建发布记录
                NfcCardPublishRecord publishRecord = new NfcCardPublishRecord()
                        .setTaskId(nfcCardMcnTask.getTaskId())
                        .setFid(nfcCardInfo.getFid())
                        .setDraftId(publishResult.getDraftId())
                        .setPublishStatus(2) // 2-发布成功
                        .setPayStatus(1)     // 1-已支付
                        .setPublishTime(LocalDateTime.now());
                nfcCardPublishRecordService.save(publishRecord);

                // 更新卡信息消耗
                nfcCardInfo.setConsumption(nfcCardInfo.getConsumption() + 1);
                nfcCardInfoService.updateById(nfcCardInfo);
                MagiccubeTransactionManager.commit(tcStatus);
                log.info("发布{}内容成功，FID：{}，任务ID：{}，草稿ID：{}", 
                    platform, fid, nfcCardMcnTask.getTaskId(), publishResult.getDraftId());
                return publishResult.getResponse();
            } catch (Exception e) {
                log.error("发布{}内容失败，FID：{}，任务ID：{}，错误：{}", 
                    platform, fid, nfcCardMcnTask.getTaskId(), e.getMessage());
                if(Objects.nonNull(tcStatus)){
                    MagiccubeTransactionManager.rollback(tcStatus);
                }
                if(e instanceof ServiceException){
                    return RspHd.fail(e.getMessage());
                }
                return RspHd.fail("发布失败");
            }
        });
    }

    /**
     * 验证NFC卡
     *
     * @param nfcCardInfo NFC卡信息
     */
    private void validateNfcCard(NfcCardInfo nfcCardInfo) {
        if (Objects.isNull(nfcCardInfo)) {
            throw new ServiceException("卡不存在");
        }

        if (nfcCardInfo.getState() == 0) {
            throw new ServiceException("卡已禁用");
        }

        if (nfcCardInfo.getQuota() - nfcCardInfo.getConsumption() <= 0) {
            throw new ServiceException("卡额度不足");
        }
    }

    @Data
    private static class PublishResult<T> {
        private final Rsp<T> response;
        private final Long draftId;
    }
}
