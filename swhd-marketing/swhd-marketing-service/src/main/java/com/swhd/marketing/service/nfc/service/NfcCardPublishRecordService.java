package com.swhd.marketing.service.nfc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.marketing.api.nfc.dto.param.record.NfcCardPublishRecordPageParam;
import com.swhd.marketing.service.nfc.entity.NfcCardPublishRecord;

/**
 * NFC卡任务发布记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface NfcCardPublishRecordService extends IBaseHdService<NfcCardPublishRecord> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<NfcCardPublishRecord> page(NfcCardPublishRecordPageParam param);

}
