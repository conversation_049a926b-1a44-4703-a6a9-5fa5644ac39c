package com.swhd.marketing.service.nfc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 任务NFC卡绑定关系表实体类
 *
 * <AUTHOR>
 * @since 2025-02-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tmarketing_nfc_card_mcn_task")
public class NfcCardMcnTask extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "NFC卡ID")
    private Long cardId;

}
