<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.marketing.service.nfc.mapper.NfcCardInfoMapper">

    <!-- 分页查询NFC卡信息 -->
    <select id="page" resultType="com.swhd.marketing.api.nfc.dto.result.NfcCardInfoResult">
        select ci.*, case when count(cmt.id) > 0 then 1 else 0 end as is_bind
        from tmarketing_nfc_card_info ci
            left join tmarketing_nfc_card_mcn_task cmt on ci.id = cmt.card_id and ci.tenant_id = cmt.tenant_id and cmt.is_delete = 0
        where ci.is_delete = 0
        <if test="param.name != null and param.name != ''">
            AND ci.name LIKE CONCAT('%', #{param.name}, '%')
        </if>
        <if test="param.fid != null and param.fid != ''">
            AND ci.fid LIKE CONCAT('%', #{param.fid}, '%')
        </if>
        <if test="param.geQuota != null">
            AND ci.quota &gt;= #{param.geQuota}
        </if>
        <if test="param.leQuota != null">
            AND ci.quota &lt;= #{param.leQuota}
        </if>
        <if test="param.state != null">
            AND ci.state = #{param.state}
        </if>
        <if test="param.remark != null and param.remark != ''">
            AND ci.remark LIKE CONCAT('%', #{param.remark}, '%')
        </if>
        group by ci.id
        <if test="param.isBind != null and param.isBind == true">
            having count(cmt.id) > 0
        </if>
        <if test="param.isBind != null and param.isBind == false">
            having count(cmt.id) = 0
        </if>
        order by ci.create_time desc, ci.id desc
    </select>

</mapper>
