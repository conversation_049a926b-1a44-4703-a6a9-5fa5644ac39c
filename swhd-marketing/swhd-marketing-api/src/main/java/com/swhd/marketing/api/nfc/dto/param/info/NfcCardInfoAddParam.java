package com.swhd.marketing.api.nfc.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-02-22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "NfcCardInfoAddParam对象")
public class NfcCardInfoAddParam {

    @NotEmpty(message = "名称不能为空")
    @Schema(description = "名称")
    private String name;

    @NotEmpty(message = "FID不能为空")
    @Schema(description = "NFC卡FID")
    private String fid;

    @Schema(description = "NFC卡额度")
    private Integer quota;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

    @Schema(description = "备注")
    private String remark;

}
