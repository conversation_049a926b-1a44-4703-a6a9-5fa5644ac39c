package com.swhd.content.web.tenant.type.web;

import com.swhd.content.api.type.client.TypeInfoClient;
import com.swhd.content.api.type.dto.param.info.TypeInfoAddParam;
import com.swhd.content.api.type.dto.param.info.TypeInfoPageParam;
import com.swhd.content.api.type.dto.param.info.TypeInfoUpdateParam;
import com.swhd.content.api.type.dto.result.TypeInfoResult;
import com.swhd.content.web.tenant.common.constant.WebConstant;
import com.swhd.content.web.tenant.type.service.TypeClassifyService;
import com.swhd.content.web.tenant.type.vo.result.TypeInfoResultVo;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/20
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/typeInfo")
public class TypeInfoController {

    private final TypeInfoClient typeInfoClient;

    private final TypeClassifyService typeClassifyService;

    @Operation(summary = "分页查询")
    @PostMapping("/page/{businessCode}")
    public Rsp<PageResult<TypeInfoResultVo>> page(@PathVariable("businessCode") String businessCode,
                                                  @RequestBody @Valid TypeInfoPageParam param) {
        Map<String, String> classifyCodeMap = typeClassifyService.mapClassifyCode(businessCode);
        if (Func.isEmpty(classifyCodeMap)) {
            return RspHd.data(PageResult.empty(param));
        }
        param.setClassifyCodeList(new ArrayList<>(classifyCodeMap.keySet()));
        Rsp<PageResult<TypeInfoResult>> pageRsp = typeInfoClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<TypeInfoResultVo> pageVo = PageUtil.convert(pageRsp.getData(), TypeInfoResultVo.class);
        pageVo.getRecords().forEach(vo -> vo.setClassifyName(classifyCodeMap.get(vo.getClassifyCode())));
        return RspHd.data(pageVo);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById/{businessCode}")
    public Rsp<TypeInfoResult> getById(@PathVariable("businessCode") String businessCode,
                                       @RequestParam("id") Long id) {
        return RspHd.data(get(businessCode, id));
    }

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    @Auth(way = Auth.Way.ALL_USER)
    public Rsp<List<AntdSelectResult>> selectAll(@RequestParam("classifyCode") String classifyCode) {
        return typeInfoClient.selectAll(classifyCode);
    }

    @Operation(summary = "新增")
    @PostMapping("/add/{businessCode}")
    public Rsp<Void> add(@PathVariable("businessCode") String businessCode,
                         @RequestBody @Valid TypeInfoAddParam param) {
        typeClassifyService.checkClassifyCode(businessCode, param.getClassifyCode());
        return typeInfoClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update/{businessCode}")
    public Rsp<Void> update(@PathVariable("businessCode") String businessCode,
                            @RequestBody @Valid TypeInfoUpdateParam param) {
        if (get(businessCode, param.getId()) == null) {
            return RspHd.fail("分类不存在");
        }
        return typeInfoClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeById/{businessCode}")
    public Rsp<Void> removeById(@PathVariable("businessCode") String businessCode,
                                @RequestParam("id") Long id) {
        if (get(businessCode, id) == null) {
            return RspHd.fail("分类不存在");
        }
        return typeInfoClient.removeByIds(List.of(id));
    }

    private TypeInfoResult get(String businessCode, Long id) {
        Rsp<TypeInfoResult> rsp = typeInfoClient.getById(id);
        RspHd.failThrowException(rsp);
        if (rsp.getData() == null) {
            return null;
        }
        typeClassifyService.checkClassifyCode(businessCode, rsp.getData().getClassifyCode());
        return rsp.getData();
    }

}
