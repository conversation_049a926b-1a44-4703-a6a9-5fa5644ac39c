package com.swhd.content.web.tenant.type.service;

import com.swhd.content.api.type.client.TypeClassifyClient;
import com.swhd.content.api.type.dto.result.TypeClassifyResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/20
 */
@Service
@AllArgsConstructor
public class TypeClassifyService {

    private final TypeClassifyClient typeClassifyClient;

    /**
     * 根据业务code获取分类code列表
     *
     * @param businessCode 业务code
     * @return Map<>
     */
    public Map<String, String> mapClassifyCode(String businessCode) {
        Rsp<List<TypeClassifyResult>> rsp = typeClassifyClient.listByBusinessCode(businessCode);
        RspHd.failThrowException(rsp);
        return rsp.getData().stream().collect(Collectors.toMap(TypeClassifyResult::getClassifyCode, TypeClassifyResult::getClassifyName));
    }

    /**
     * 检测classifyCode是否存在businessCode下
     *
     * @param businessCode 业务code
     * @param classifyCode 分类code
     */
    public void checkClassifyCode(String businessCode, String classifyCode) {
        Set<String> businessCodeSet = mapClassifyCode(businessCode).keySet();
        if (businessCodeSet.stream().noneMatch(code -> Objects.equals(code, classifyCode))) {
            throw new ServiceException("分类code不存在");
        }
    }

}
