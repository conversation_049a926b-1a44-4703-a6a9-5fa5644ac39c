package com.swhd.content.service.material.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.content.api.material.dto.param.info.MaterialInfoPageParam;
import com.swhd.content.service.material.entity.MaterialInfo;
import com.swhd.content.service.material.mapper.MaterialInfoMapper;
import com.swhd.content.service.material.service.MaterialInfoService;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 素材信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Service
@AllArgsConstructor
public class MaterialInfoServiceImpl extends BaseHdServiceImpl<MaterialInfoMapper, MaterialInfo> implements MaterialInfoService {

    @Override
    public IPage<MaterialInfo> page(MaterialInfoPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getUserId()), MaterialInfo::getUserId, param.getUserId())
                .eq(Func.isNotEmpty(param.getType()), MaterialInfo::getType, param.getType())
                .eq(Func.isNotEmpty(param.getClassifyId()), MaterialInfo::getClassifyId, param.getClassifyId())
                .like(Func.isNotEmpty(param.getFileName()), MaterialInfo::getFileName, param.getFileName())
                .orderByDesc(MaterialInfo::getCreateTime)
                .orderByDesc(MaterialInfo::getId)
                .page(convertToPage(param));
    }

}
