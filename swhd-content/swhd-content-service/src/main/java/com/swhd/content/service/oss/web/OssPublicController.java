package com.swhd.content.service.oss.web;

import com.swhd.content.api.oss.client.OssPublicClient;
import com.swhd.content.api.oss.dto.param.OssUploadBatchSignParam;
import com.swhd.content.api.oss.dto.param.OssUploadParam;
import com.swhd.content.api.oss.dto.param.OssUploadSignParam;
import com.swhd.content.api.oss.dto.param.OssUploadUrlParam;
import com.swhd.content.api.oss.dto.result.OssUploadSignResult;
import com.swhd.content.service.oss.service.OssPublicService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(OssPublicClient.BASE_PATH)
public class OssPublicController implements OssPublicClient {

    private final OssPublicService ossPublicService;

    @Override
    public Rsp<OssUploadSignResult> uploadSign(OssUploadSignParam param) {
        return RspHd.data(ossPublicService.uploadSign(param));
    }

    @Override
    public Rsp<List<OssUploadSignResult>> batchUploadSign(OssUploadBatchSignParam param) {
        return RspHd.data(ossPublicService.batchUploadSign(param));
    }

    @Override
    public Rsp<String> upload(OssUploadParam param) {
        try {
            byte[] fileBytes;
            if (param.getFileBytes() != null) {
                fileBytes = param.getFileBytes();
            } else if (param.getFileText() != null) {
                fileBytes = param.getFileText().getBytes(StandardCharsets.UTF_8);
            } else {
                return RspHd.fail("文件不能为空");
            }
            String ossKey = ossPublicService.upload(param.getBizOssKey(), fileBytes, param.getMediaType());
            return RspHd.data(ossKey);
        } catch (Exception e) {
            log.error("bizOssKey[{}]文件上传异常", param.getBizOssKey(), e);
            return RspHd.fail("文件上传异常");
        }
    }

    @Override
    public Rsp<String> uploadUrl(OssUploadUrlParam param) {
        try {
            String ossKey = ossPublicService.upload(param);
            return RspHd.data(ossKey);
        } catch (Exception e) {
            log.error("bizOssKey[{}]文件上传异常", param.getBizOssKey(), e);
            return RspHd.fail("文件上传异常");
        }
    }

}
