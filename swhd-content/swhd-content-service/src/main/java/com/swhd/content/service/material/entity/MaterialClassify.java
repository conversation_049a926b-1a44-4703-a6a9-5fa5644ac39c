package com.swhd.content.service.material.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swhd.magiccube.mybatis.base.tree.BaseHdTreeEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 素材分类表实体类
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcontent_material_classify")
public class MaterialClassify extends BaseHdTreeEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "类型：1-图片，2-音频，3-视频，4-文档")
    private Integer type;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "标题")
    private String title;

    @Override
    @JsonIgnore
    public String getName() {
        return this.title;
    }

}
