package com.swhd.content.service.oss.service.impl;

import com.swhd.content.api.common.constant.FileType;
import com.swhd.content.api.oss.dto.param.OssUploadBatchSignParam;
import com.swhd.content.api.oss.dto.param.OssUploadSignParam;
import com.swhd.content.api.oss.dto.param.OssUploadUrlParam;
import com.swhd.content.api.oss.dto.result.OssUploadSignResult;
import com.swhd.content.service.oss.properties.OssFileContentLimit;
import com.swhd.content.service.oss.properties.OssProperties;
import com.swhd.content.service.oss.service.OssBaseService;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.FrameworkStatusCode;
import com.swj.magiccube.oss.OssBucketLifecycleRule;
import com.swj.magiccube.oss.OssImageInfo;
import com.swj.magiccube.oss.OssObjectMetadata;
import com.swj.magiccube.oss.OssTags;
import com.swj.magiccube.oss.ali.properties.AliOssProperties;
import com.swj.magiccube.oss.signature.OssGenerateSignUploadReq;
import com.swj.magiccube.oss.signature.OssGenerateSignUploadResult;
import com.swj.magiccube.oss.signature.OssSignUploadCallbackBodyBuilder;
import com.swj.magiccube.oss.signature.OssSignUploadCallbackConfig;
import com.swj.magiccube.tool.annotation.Nullable;
import com.swj.magiccube.tool.http.OkHttpUtil;
import com.swj.magiccube.tool.url.UrlUtil;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.InputStream;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
public abstract class OssBaseServiceImpl implements OssBaseService {

    protected static final String UNKNOWN_OSS_ERROR_MSG = "oss未实现异常";

    @Autowired
    protected OssProperties ossProperties;

    @Autowired
    private AliOssProperties aliOssProperties;

    @Override
    public String bucket() {
        return getOssOperations().bucket();
    }

    @Override
    public String normalizeKey(String bizKey) {
        return getOssOperations().normalizeKey(bizKey);
    }

    @Override
    public String upload(String bizKey, @NotNull byte[] data) {
        return getOssOperations().upload(bizKey, data);
    }

    @Override
    public String upload(String bizKey, @NotNull byte[] data, @Nullable String mediaType) {
        return getOssOperations().upload(bizKey, data, mediaType);
    }

    @Override
    public String upload(String bizKey, InputStream dataStream) {
        return getOssOperations().upload(bizKey, dataStream);
    }

    @Override
    public String upload(String bizKey, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().upload(bizKey, dataStream, mediaType);
    }

    @Override
    public String uploadAndClose(String bizKey, InputStream dataStream) {
        return getOssOperations().uploadAndClose(bizKey, dataStream);
    }

    @Override
    public String uploadAndClose(String bizKey, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().uploadAndClose(bizKey, dataStream, mediaType);
    }

    @Override
    public String upload(String bizKey, InputStream dataStream, @Nullable String mediaType,
                         @Nullable String filenameWhenDownload, @Nullable Boolean closeStream) {
        return getOssOperations().upload(bizKey, dataStream, mediaType, filenameWhenDownload, closeStream);
    }

    @Override
    public String upload(OssUploadUrlParam param) {
        OkHttpUtil.Req req = OkHttpUtil.get(param.getUrl())
                .connectTimeoutInSecond(3)
                .timeoutInSecond(180);
        if (Func.isNotEmpty(param.getHeaders())) {
            param.getHeaders().stream()
                    .filter(header -> Func.isNotEmpty(header.getKey()))
                    .filter(header -> Func.isNotEmpty(header.getValue()))
                    .forEach(header -> req.header(header.getKey(), header.getValue()));
        }
        try (ResponseBody body = req
                .execute()
                .whenSuccess()
                .getRsp()
                .body()) {
            if (body == null) {
                throw new ServiceException(FrameworkStatusCode.DOWNLOAD_FAIL, "对象上传失败（网络异常）");
            }
            try (InputStream inputStream = body.byteStream()) {
                return getOssOperations().upload(param.getBizOssKey(), inputStream);
            } catch (IOException e) {
                throw new ServiceException(FrameworkStatusCode.DOWNLOAD_FAIL, "对象上传失败（网络异常）");
            }
        }
    }

    @Override
    public String upload(String bizKey, String url, @Nullable String mediaType) {
        return getOssOperations().upload(bizKey, url, mediaType);
    }

    @Override
    public String uploadWithKey(String key, @NotNull byte[] data, @Nullable String mediaType) {
        return getOssOperations().uploadWithKey(key, data, mediaType);
    }

    @Override
    public String uploadWithKey(String key, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().uploadWithKey(key, dataStream, mediaType);
    }

    @Override
    public String uploadWithKey(String key, InputStream dataStream, @Nullable String mediaType,
                                @Nullable String filenameWhenDownload, @Nullable Boolean closeStream) {
        return getOssOperations().uploadWithKey(key, dataStream, mediaType, filenameWhenDownload, closeStream);
    }

    @Override
    public String upload(String bizKey, @NotNull byte[] data, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().upload(bizKey, data, metadata);
    }

    @Override
    public String upload(String bizKey, InputStream dataStream, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().upload(bizKey, dataStream, metadata);
    }

    @Override
    public String uploadAndClose(String bizKey, InputStream dataStream, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().uploadAndClose(bizKey, dataStream, metadata);
    }

    @Override
    public String upload(String bizKey, String url, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().upload(bizKey, url, metadata);
    }

    @Override
    public String uploadWithKey(String key, @NotNull byte[] data, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().uploadWithKey(key, data, metadata);
    }

    @Override
    public String uploadWithKey(String key, InputStream dataStream, @Nullable OssObjectMetadata metadata) {
        return getOssOperations().uploadWithKey(key, dataStream, metadata);
    }

    @Override
    public String uploadWithKey(String key, InputStream dataStream, @Nullable OssObjectMetadata metadata, @Nullable Boolean closeStream) {
        return getOssOperations().uploadWithKey(key, dataStream, metadata, closeStream);
    }

    @Override
    public String uploadImage(String bizKey, @NotNull byte[] data) {
        return getOssOperations().uploadImage(bizKey, data);
    }

    @Override
    public String uploadImage(String bizKey, @NotNull byte[] data, @Nullable String mediaType) {
        return getOssOperations().uploadImage(bizKey, data, mediaType);
    }

    @Override
    public String uploadImage(String bizKey, InputStream dataStream) {
        return getOssOperations().uploadImage(bizKey, dataStream);
    }

    @Override
    public String uploadImage(String bizKey, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().uploadImage(bizKey, dataStream, mediaType);
    }

    @Override
    public String uploadImageAndClose(String bizKey, InputStream dataStream) {
        return getOssOperations().uploadImageAndClose(bizKey, dataStream);
    }

    @Override
    public String uploadImageAndClose(String bizKey, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().uploadImageAndClose(bizKey, dataStream, mediaType);
    }

    @Override
    public String uploadImage(String bizKey, InputStream dataStream, @Nullable String mediaType,
                              @Nullable String filenameWhenDownload, @Nullable Boolean closeStream) {
        return getOssOperations().uploadImage(bizKey, dataStream, mediaType, filenameWhenDownload, closeStream);
    }

    @Override
    public String uploadImage(String bizKey, String url) {
        return getOssOperations().uploadImage(bizKey, url);
    }

    @Override
    public String uploadImage(String bizKey, String url, @Nullable String mediaType) {
        return getOssOperations().uploadImage(bizKey, url, mediaType);
    }

    @Override
    public String uploadImageWithKey(String key, @NotNull byte[] data, @Nullable String mediaType) {
        return getOssOperations().uploadImageWithKey(key, data, mediaType);
    }

    @Override
    public String uploadImageWithKey(String key, InputStream dataStream, @Nullable String mediaType) {
        return getOssOperations().uploadImageWithKey(key, dataStream, mediaType);
    }

    @Override
    public String uploadImageWithKey(String key, InputStream dataStream, @Nullable String mediaType,
                                     @Nullable String filenameWhenDownload, @Nullable Boolean closeStream) {
        return getOssOperations().uploadImageWithKey(key, dataStream, mediaType, filenameWhenDownload, closeStream);
    }

    @Override
    public boolean objectExists(String key) {
        return getOssOperations().objectExists(key);
    }

    @Override
    public void delete(String firstKey, @NotEmpty String... moreKeys) {
        getOssOperations().delete(firstKey, moreKeys);
    }

    @Override
    public void delete(Collection<String> keys) {
        getOssOperations().delete(keys);
    }

    @Override
    public byte[] download(String key) {
        return getOssOperations().download(key);
    }

    @Override
    public byte[] getObjectData(String key) {
        return getOssOperations().getObjectData(key);
    }

    @Override
    public OssObjectMetadata getMetadata(String key) {
        return getOssOperations().getMetadata(key);
    }

    @Override
    public OssObjectMetadata getMetadataAndTag(String key) {
        return getOssOperations().getMetadataAndTag(key);
    }

    @Override
    public void setMetadata(String key, OssObjectMetadata newMetadata) {
        getOssOperations().setMetadata(key, newMetadata);
    }

    @Override
    public void setMetadataAndTag(String key, OssObjectMetadata metadata) {
        getOssOperations().setMetadataAndTag(key, metadata);
    }

    @Override
    public OssTags getObjectTags(String key) {
        return getOssOperations().getObjectTags(key);
    }

    @Override
    public void setObjectTags(String key, OssTags tags) {
        getOssOperations().setObjectTags(key, tags);
    }

    @Override
    public InputStream getObjectStream(String key) {
        return getOssOperations().getObjectStream(key);
    }

    @Override
    public OssImageInfo getImageInfo(String key) {
        return getOssOperations().getImageInfo(key);
    }

    @Override
    public void copy(String sourceKey, String targetKey) {
        getOssOperations().copy(sourceKey, targetKey);
    }

    @Override
    public void copy(String sourceKey, String targetKey, OssObjectMetadata newMetadata) {
        getOssOperations().copy(sourceKey, targetKey, newMetadata);
    }

    @Override
    public void rename(String sourceKey, String targetKey) {
        getOssOperations().rename(sourceKey, targetKey);
    }

    @Override
    public String absoluteUrl(String key) {
        return getOssOperations().absoluteUrl(key);
    }

    @Override
    public String generatePreSignedUrl(String key, long seconds) {
        return generatePreSignedUrl(key, seconds, ChronoUnit.SECONDS);
    }

    @Override
    public String generatePreSignedUrl(String key, long amount, ChronoUnit unit) {
        try {
            if (UrlUtil.startWithHttpScheme(key)) {
                // 非私有对象，无需签名
                String host = UrlUtil.urlHost(key);
                String privateHost = UrlUtil.urlHost(aliOssProperties.getPrivateBucket().getReadDomain());
                if (!Objects.equals(host, privateHost)) {
                    return key;
                }
            }

            return getOssOperations().sign(key, amount, unit);
        } catch (Exception e) {
            return key;
        }
    }

    @Override
    public boolean objectExistsByBizKey(String bizKey) {
        return getOssOperations().objectExistsByBizKey(bizKey);
    }

    @Override
    public void deleteByBizKeys(String firstBizKey, @NotEmpty String... moreBizKeys) {
        getOssOperations().deleteByBizKeys(firstBizKey, moreBizKeys);
    }

    @Override
    public void deleteByBizKeys(Collection<String> bizKeys) {
        getOssOperations().deleteByBizKeys(bizKeys);
    }

    @Override
    public byte[] downloadByBizKey(String bizKey) {
        return getOssOperations().downloadByBizKey(bizKey);
    }

    @Override
    public byte[] getObjectDataByBizKey(String bizKey) {
        return getOssOperations().getObjectDataByBizKey(bizKey);
    }

    @Override
    public InputStream getObjectStreamByBizKey(String bizKey) {
        return getOssOperations().getObjectStreamByBizKey(bizKey);
    }

    @Override
    public OssImageInfo getImageInfoByBizKey(String bizKey) {
        return getOssOperations().getImageInfoByBizKey(bizKey);
    }

    @Override
    public List<OssBucketLifecycleRule> getBucketLifecycle() {
        return getOssOperations().getBucketLifecycle();
    }

    @Override
    public void addBucketLifecycleRule(Collection<OssBucketLifecycleRule> rules) {
        getOssOperations().addBucketLifecycleRule(rules);
    }

    @Override
    public void enableBucketLifecycleRule(Collection<String> ruleIds) {
        getOssOperations().enableBucketLifecycleRule(ruleIds);
    }

    @Override
    public void disableBucketLifecycleRule(Collection<String> ruleIds) {
        getOssOperations().disableBucketLifecycleRule(ruleIds);
    }

    @Override
    public OssUploadSignResult uploadSign(OssUploadSignParam param) {
        OssFileContentLimit contentLimit = getOssFileContentLimit(param);

        OssGenerateSignUploadReq req = new OssGenerateSignUploadReq();
        req.setContentType(contentLimit.getFileExtensionContentType()
                .getOrDefault(param.getFileExtension().toLowerCase(), contentLimit.getContentType()));
        req.setContentTypePrefix(contentLimit.getContentTypePrefix());
        req.setDuration(ossProperties.getSignExpire());
        String key = this.normalizeKey(param.getBizOssKey());
        req.setKey(key);
        switch (param.getFileType()) {
            case IMAGE -> req.setMaxSize(ossProperties.getImageMaxSize());
            case AUDIO -> req.setMaxSize(ossProperties.getAudioMaxSize());
            case VIDEO -> req.setMaxSize(ossProperties.getVideoMaxSize());
            case DOC -> req.setMaxSize(ossProperties.getDocMaxSize());
            case TEXT -> req.setMaxSize(ossProperties.getTextMaxSize());
        }
        if (Func.isNotEmpty(param.getCallbackUrl())) {
            OssSignUploadCallbackConfig callbackConfig = new OssSignUploadCallbackConfig();
            callbackConfig.url(param.getCallbackUrl());
            OssSignUploadCallbackBodyBuilder bodyBuilder = getOssSignUploadCallbackBodyBuilder();
            bodyBuilder.returnSize();
            bodyBuilder.custom("ossKey", key);
            if (Objects.equals(param.getFileType(), FileType.IMAGE)) {
                bodyBuilder.returnImageInfo();
            }
            if (Func.isNotEmpty(param.getCallbackId())) {
                bodyBuilder.custom("callbackId", param.getCallbackId());
            }
            callbackConfig.body(bodyBuilder);
            callbackConfig.form();
            req.setCallbackConfig(callbackConfig);
        }
        OssGenerateSignUploadResult uploadResult = getOssOperations().ossSignUploadOperations().signature(req);
        return convertToOssUploadSignResult(uploadResult);
    }

    private OssFileContentLimit getOssFileContentLimit(OssUploadSignParam param) {
        OssFileContentLimit contentLimit;
        if (param.getFileType() == FileType.FILE) {
            contentLimit = ossProperties.getFileContentLimit().entrySet().stream()
                    .filter(item -> item.getValue().getFileExtensionList().stream()
                            .anyMatch(fileExtension -> fileExtension.equalsIgnoreCase(param.getFileExtension())))
                    .findAny()
                    .map(Map.Entry::getValue)
                    .orElse(null);
        } else {
            contentLimit = ossProperties.getFileContentLimit().get(param.getFileType());
        }
        if (contentLimit == null
                || contentLimit.getFileExtensionList().stream()
                .noneMatch(fileExtension -> fileExtension.equalsIgnoreCase(param.getFileExtension()))) {
            throw new ServiceException(String.format("%s不支持后缀[%s]类型文件上传",
                    param.getFileType().getName(), param.getFileExtension()));
        }
        return contentLimit;
    }

    @Override
    public List<OssUploadSignResult> batchUploadSign(OssUploadBatchSignParam param) {
        return param.getBizOssKeyList().stream()
                .map(bizOssKey -> new OssUploadSignParam()
                        .setBizOssKey(bizOssKey)
                        .setFileType(param.getFileType())
                        .setCallbackUrl(param.getCallbackUrl())
                        .setCallbackId(param.getCallbackId()))
                .map(this::uploadSign)
                .toList();
    }

    protected abstract OssSignUploadCallbackBodyBuilder getOssSignUploadCallbackBodyBuilder();

    protected abstract OssUploadSignResult convertToOssUploadSignResult(OssGenerateSignUploadResult uploadResult);

    protected OssUploadSignResult convertAliToOssUploadSignResult(OssGenerateSignUploadResult uploadResult, String uploadDomain) {
        String uploadUrl = Func.emptyOrDefault(uploadDomain, uploadResult.getOssHost());
        if (Func.isNotEmpty(uploadUrl) && uploadUrl.endsWith("/")) {
            uploadUrl = uploadUrl.replaceAll("/+$", "");
        }
        Map<String, String> formData = new LinkedHashMap<>();
        formData.put("key", uploadResult.getKey());
        formData.put("policy", uploadResult.getPolicy());
        formData.put("OSSAccessKeyId", uploadResult.getAccessKeyId());
        formData.put("success_action_status", "200");
        formData.put("signature", uploadResult.getSignature());
        return new OssUploadSignResult()
                .setUploadUrl(uploadUrl)
                .setExpireAt(uploadResult.getExpireAt())
                .setUrl(generatePreSignedUrl(uploadResult.getKey(), uploadResult.getExpireAt() * 2))
                .setOssKey(uploadResult.getKey())
                .setFormData(formData);
    }

}
