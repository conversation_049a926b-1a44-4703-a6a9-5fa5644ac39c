package com.swhd.content.service.type.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.content.api.type.client.TypeInfoClient;
import com.swhd.content.api.type.dto.param.info.TypeInfoAddParam;
import com.swhd.content.api.type.dto.param.info.TypeInfoPageParam;
import com.swhd.content.api.type.dto.param.info.TypeInfoUpdateParam;
import com.swhd.content.api.type.dto.result.TypeInfoResult;
import com.swhd.content.service.type.entity.TypeInfo;
import com.swhd.content.service.type.service.TypeInfoService;
import com.swhd.magiccube.core.constant.SwitchConstant;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-02-20
 */
@RestController
@AllArgsConstructor
@RequestMapping(TypeInfoClient.BASE_PATH)
public class TypeInfoController implements TypeInfoClient {

    private final TypeInfoService typeInfoService;

    @Override
    public Rsp<PageResult<TypeInfoResult>> page(TypeInfoPageParam param) {
        IPage<TypeInfo> iPage = typeInfoService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, TypeInfoResult.class));
    }

    @Override
    public Rsp<TypeInfoResult> getById(Long id) {
        TypeInfo entity = typeInfoService.getById(id);
        return RspHd.data(Func.copy(entity, TypeInfoResult.class));
    }

    @Override
    public Rsp<List<TypeInfoResult>> listByIds(Collection<Long> ids) {
        List<TypeInfo> list = typeInfoService.listByIds(ids);
        return RspHd.data(Func.copy(list, TypeInfoResult.class));
    }

    @Override
    public Rsp<List<AntdSelectResult>> selectAll(String classifyCode) {
        List<AntdSelectResult> list = typeInfoService.listByClassifyCode(classifyCode).stream()
                .map(info -> {
                    AntdSelectResult result = new AntdSelectResult(info.getId(), info.getTitle());
                    result.setDisabled(Objects.equals(info.getState(), SwitchConstant.NO));
                    return result;
                })
                .toList();
        return RspHd.data(list);
    }

    @Override
    public Rsp<Void> add(TypeInfoAddParam param) {
        boolean result = typeInfoService.save(Func.copy(param, TypeInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(TypeInfoUpdateParam param) {
        boolean result = typeInfoService.updateById(Func.copy(param, TypeInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = typeInfoService.removeByIds(ids);
        return RspHd.status(result);
    }

}
