package com.swhd.content.service.oss.web;

import com.swhd.content.api.oss.client.OssPrivateClient;
import com.swhd.content.api.oss.dto.param.*;
import com.swhd.content.api.oss.dto.result.OssUploadSignResult;
import com.swhd.content.service.oss.service.OssPrivateService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(OssPrivateClient.BASE_PATH)
public class OssPrivateController implements OssPrivateClient {

    private final OssPrivateService ossPrivateService;

    @Override
    public Rsp<String> generatePreSignedUrl(OssGeneratePreSignedUrlParam param) {
        return RspHd.data(ossPrivateService.generatePreSignedUrl(param.getKey(), param.getAmount(), param.getUnit()));
    }

    @Override
    public Rsp<List<String>> batchGeneratePreSignedUrl(List<OssGeneratePreSignedUrlParam> listParam) {
        return RspHd.data(ossPrivateService.batchGeneratePreSignedUrl(listParam));
    }

    @Override
    public Rsp<List<List<String>>> batchGeneratePreSignedUrlList(List<OssGeneratePreSignedUrlListParam> listParam) {
        List<List<String>> list = listParam.stream()
                .map(param -> {
                    if (Func.isNotEmpty(param.getKeys())) {
                        return new ArrayList<String>();
                    }
                    return param.getKeys().stream()
                            .map(key -> ossPrivateService.generatePreSignedUrl(key, param.getAmount(), param.getUnit()))
                            .toList();
                })
                .toList();
        return RspHd.data(list);
    }

    @Override
    public Rsp<OssUploadSignResult> uploadSign(OssUploadSignParam param) {
        return RspHd.data(ossPrivateService.uploadSign(param));
    }

    @Override
    public Rsp<List<OssUploadSignResult>> batchUploadSign(OssUploadBatchSignParam param) {
        return RspHd.data(ossPrivateService.batchUploadSign(param));
    }

    @Override
    public Rsp<String> upload(OssUploadParam param) {
        try {
            byte[] fileBytes;
            if (param.getFileBytes() != null) {
                fileBytes = param.getFileBytes();
            } else if (param.getFileText() != null) {
                fileBytes = param.getFileText().getBytes(StandardCharsets.UTF_8);
            } else {
                return RspHd.fail("文件不能为空");
            }
            String ossKey = ossPrivateService.upload(param.getBizOssKey(), fileBytes, param.getMediaType());
            return RspHd.data(ossKey);
        } catch (Exception e) {
            log.error("bizOssKey[{}]文件上传异常", param.getBizOssKey(), e);
            return RspHd.fail("文件上传异常");
        }
    }

    @Override
    public Rsp<String> uploadUrl(OssUploadUrlParam param) {
        try {
            String ossKey = ossPrivateService.upload(param);
            return RspHd.data(ossKey);
        } catch (Exception e) {
            log.error("bizOssKey[{}]文件上传异常", param.getBizOssKey(), e);
            return RspHd.fail("文件上传异常");
        }
    }

}
