package com.swhd.content.service.download.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.content.api.download.constant.DownloadExportState;
import com.swhd.content.api.download.dto.message.DownloadRecordAddMessage;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordPageParam;
import com.swhd.content.service.download.entity.DownloadExportRecord;
import com.swhd.content.service.download.mapper.DownloadExportRecordMapper;
import com.swhd.content.service.download.mq.producer.DownloadProducer;
import com.swhd.content.service.download.properties.DownloadExport;
import com.swhd.content.service.download.properties.DownloadProperties;
import com.swhd.content.service.download.service.DownloadExportRecordService;
import com.swhd.content.service.oss.service.OssPrivateService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.constant.ConstantApp;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.idgenerator.IdGenerator;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.ParameterUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.WebUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 下载中心导出记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
@Service
@AllArgsConstructor
public class DownloadExportRecordServiceImpl extends BaseHdServiceImpl<DownloadExportRecordMapper, DownloadExportRecord>
        implements DownloadExportRecordService {

    private final OssPrivateService ossPrivateService;

    private final IdGenerator<Long> idGenerator;

    private final DownloadProperties downloadProperties;

    @Override
    public IPage<DownloadExportRecord> page(DownloadExportRecordPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getUserId()), DownloadExportRecord::getUserId, param.getUserId())
                .eq(Func.isNotEmpty(param.getExportType()), DownloadExportRecord::getExportType, param.getExportType())
                .like(Func.isNotEmpty(param.getFileName()), DownloadExportRecord::getFileName, param.getFileName())
                .eq(Func.isNotEmpty(param.getExportState()), DownloadExportRecord::getExportState, param.getExportState())
                .orderByDesc(DownloadExportRecord::getCreateTime)
                .orderByDesc(DownloadExportRecord::getId)
                .page(convertToPage(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "content:download:export:record:add", key = "#param.userId", waitTime = 6000)
    public Rsp<Void> add(DownloadExportRecordAddParam param) {
        Optional<DownloadExport> exportOptional = downloadProperties.getExportList().stream()
                .filter(export -> Objects.equals(param.getExportType(), export.getExportType()))
                .findAny();
        if (exportOptional.isEmpty()) {
            return RspHd.fail("未知的导出类型");
        }

        LocalDateTime now = LocalDateTime.now();
        boolean exists = lambdaQuery()
                .eq(DownloadExportRecord::getUserId, param.getUserId())
                .eq(DownloadExportRecord::getExportType, exportOptional.get().getExportType())
                .eq(DownloadExportRecord::getExportState, DownloadExportState.ING.getState())
                .between(DownloadExportRecord::getCreateTime, now.minusHours(1), now)
                .exists();
        if (exists) {
            return RspHd.fail("存在正在下载中的任务");
        }

        String fileName = ParameterUtil.format(exportOptional.get().getFileName(), getParameter(param));
        String ossBizKey = String.format("%scontent/download/%s/%s/%s/%s",
                downloadProperties.getOssKeyPre(), param.getExportType(), param.getUserId(), idGenerator.generateId(), fileName);

        DownloadExportRecord exportRecord = Func.copy(param, DownloadExportRecord.class);
        HttpServletRequest request = WebUtil.getRequest();
        if (request != null) {
            exportRecord.setLoginTokenId(request.getHeader(ConstantApp.ACCESS_TOKEN_ID));
        }
        exportRecord.setFileName(fileName);
        exportRecord.setOssKey(ossPrivateService.normalizeKey(ossBizKey));
        exportRecord.setBizOssKey(ossBizKey);
        exportRecord.setExportState(DownloadExportState.ING.getState());
        boolean result = save(exportRecord);
        if (result) {
            DownloadRecordAddMessage message = new DownloadRecordAddMessage();
            message.setId(exportRecord.getId());
            message.setTenantId(TenantHolder.getRequiredTenantId());
            message.setExportType(param.getExportType());
            DownloadProducer.downloadRecordAdd(message);
        }
        return RspHd.status(result);
    }

    private Map<String, String> getParameter(DownloadExportRecordAddParam param) {
        Map<String, String> parameter = new HashMap<>();
        parameter.put("id", idGenerator.generateId().toString());
        parameter.put("userId", param.getUserId().toString());
        parameter.put("exportType", param.getExportType());
        putParameter(parameter, param.getExportParams(), "exportParams");
        return parameter;
    }

    private void putParameter(Map<String, String> parameter, JsonNode jsonNode, String fieldName) {
        if (jsonNode == null) {
            return;
        }
        if (jsonNode.isObject()) {
            // 处理对象
            jsonNode.fieldNames().forEachRemaining(subFieldName -> {
                JsonNode value = jsonNode.get(subFieldName);
                // 递归遍历属性值
                putParameter(parameter, value, String.format("%s.%s", fieldName, subFieldName));
            });
        } else if (jsonNode.isArray()) {
            // 处理数组
            for (int i = 0; i < jsonNode.size(); i++) {
                putParameter(parameter, jsonNode.get(i), String.format("%s[%s]", fieldName, i));
            }
        } else {
            // 基本类型
            parameter.put(fieldName, jsonNode.asText());
        }
    }

}
