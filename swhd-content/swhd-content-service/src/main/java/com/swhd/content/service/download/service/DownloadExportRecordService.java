package com.swhd.content.service.download.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordPageParam;
import com.swhd.content.service.download.entity.DownloadExportRecord;
import com.swj.magiccube.api.Rsp;

/**
 * 下载中心导出记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
public interface DownloadExportRecordService extends IBaseHdService<DownloadExportRecord> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<DownloadExportRecord> page(DownloadExportRecordPageParam param);

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> add(DownloadExportRecordAddParam param);

}
