package com.swhd.content.api.download.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-07-01
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "DownloadExportRecordListResult对象")
public class DownloadExportRecordListResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "登陆token id")
    private String loginTokenId;

    @Schema(description = "导出类型")
    private String exportType;

    @Schema(description = "导出类型名称")
    private String exportTypeName;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "oss key")
    private String ossKey;

    @Schema(description = "状态：0-下载中，1-下载完成，2-下载失败")
    private Integer exportState;

    @Schema(description = "oss文件是否有效")
    private Boolean ossFileEffective;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
