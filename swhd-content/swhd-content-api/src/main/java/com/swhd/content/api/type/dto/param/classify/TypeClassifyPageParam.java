package com.swhd.content.api.type.dto.param.classify;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-02-20
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TypeClassifyPageParam对象")
public class TypeClassifyPageParam extends PageReq {

    @Schema(description = "业务code")
    private String businessCode;

    @Schema(description = "分类code")
    private String classifyCode;

    @Schema(description = "分类名称")
    private String classifyName;

    @Schema(description = "备注")
    private String remark;

}
