package com.swhd.content.api.material.client;

import com.swhd.content.api.common.constant.ApiConstant;
import com.swhd.content.api.material.dto.param.classify.MaterialClassifyAddParam;
import com.swhd.content.api.material.dto.param.classify.MaterialClassifyUpdateOrderedParam;
import com.swhd.content.api.material.dto.param.classify.MaterialClassifyUpdateParam;
import com.swhd.content.api.material.dto.result.MaterialClassifyResult;
import com.swhd.content.api.material.dto.result.MaterialClassifyTreeResult;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-14
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MaterialClassifyClient.BASE_PATH)
public interface MaterialClassifyClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/material/classify";

    @Operation(summary = "用户分类树")
    @GetMapping("/treeByUserType")
    Rsp<List<MaterialClassifyTreeResult>> treeByUserType(@RequestParam("userId") Long userId,
                                                         @RequestParam("type") Integer type);

    @Operation(summary = "获取用户下拉列表树")
    @GetMapping("/selectTreeByUserType")
    Rsp<List<AntdSelectResult>> selectTreeByUserType(@RequestParam("userId") Long userId,
                                                     @RequestParam("type") Integer type);

    @Operation(summary = "获取用户父节点下拉列表")
    @GetMapping("/selectByUserParentType")
    Rsp<List<AntdSelectResult>> selectByUserParentType(@RequestParam("userId") Long userId,
                                                       @RequestParam(value = "parentId", required = false, defaultValue = "0") Long parentId,
                                                       @RequestParam("type") Integer type);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MaterialClassifyResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MaterialClassifyResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MaterialClassifyAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid MaterialClassifyUpdateParam param);

    @Operation(summary = "修改序号")
    @PostMapping("/updateOrdered")
    Rsp<Void> updateOrdered(@RequestBody @Valid MaterialClassifyUpdateOrderedParam param);

    @Operation(summary = "删除")
    @PostMapping("/removeById")
    Rsp<Void> removeById(@RequestParam("id") Long id);

}
