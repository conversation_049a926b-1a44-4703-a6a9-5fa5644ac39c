package com.swhd.content.api.material.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.content.api.common.constant.ApiConstant;
import com.swhd.content.api.material.dto.param.info.MaterialInfoAddParam;
import com.swhd.content.api.material.dto.param.info.MaterialInfoPageParam;
import com.swhd.content.api.material.dto.param.info.MaterialInfoUpdateParam;
import com.swhd.content.api.material.dto.result.MaterialInfoResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-14
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MaterialInfoClient.BASE_PATH)
public interface MaterialInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/material/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MaterialInfoResult>> page(@RequestBody @Valid MaterialInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MaterialInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MaterialInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MaterialInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid MaterialInfoUpdateParam param);

    @Operation(summary = "删除")
    @PostMapping("/removeById")
    Rsp<Void> removeById(@RequestParam("id") Long id);

}
