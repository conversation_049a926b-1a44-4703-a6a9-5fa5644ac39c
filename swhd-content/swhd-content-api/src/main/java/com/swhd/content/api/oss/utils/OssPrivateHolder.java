package com.swhd.content.api.oss.utils;

import com.swhd.magiccube.core.function.VoidFunction;
import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/9/28
 */
@UtilityClass
public class OssPrivateHolder {

    private static final Duration DEFAULT_DURATION = Duration.ofHours(2);

    private final static ThreadLocal<Duration> DURATION_THREAD_LOCAL = new ThreadLocal<>();

    public Duration getDuration() {
        return Optional.ofNullable(DURATION_THREAD_LOCAL.get()).orElse(DEFAULT_DURATION);
    }

    public <T> T methodTenant(Duration duration, Supplier<T> supplier) {
        DURATION_THREAD_LOCAL.set(duration);
        try {
            return supplier.get();
        } finally {
            DURATION_THREAD_LOCAL.remove();
        }
    }

    public void methodTenantVoid(Duration duration, VoidFunction voidFunction) {
        DURATION_THREAD_LOCAL.set(duration);
        try {
            voidFunction.accept();
        } finally {
            DURATION_THREAD_LOCAL.remove();
        }
    }

}
