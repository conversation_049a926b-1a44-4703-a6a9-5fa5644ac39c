package com.swhd.content.api.material.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-12-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MaterialInfoUpdateParam对象")
public class MaterialInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "分类ID：tcontent_material_classify#id")
    private Long classifyId;

    @Schema(description = "文件名称")
    private String fileName;

}
