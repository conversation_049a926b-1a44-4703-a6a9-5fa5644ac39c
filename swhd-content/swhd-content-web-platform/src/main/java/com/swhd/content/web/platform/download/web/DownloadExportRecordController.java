package com.swhd.content.web.platform.download.web;

import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordPageParam;
import com.swhd.content.api.download.dto.result.DownloadExportRecordListResult;
import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.content.web.platform.common.constant.WebConstant;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.constant.TenantConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/downloadExportRecord")
public class DownloadExportRecordController {

    private final DownloadExportRecordClient downloadExportRecordClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    @Auth(way = Auth.Way.ALL_USER)
    public Rsp<PageResult<DownloadExportRecordListResult>> page(@RequestBody @Valid DownloadExportRecordPageParam param) {
        param.setUserId(CurrentUserHolder.currentUserId());
        return TenantHolder.methodTenant(TenantConstant.PLATFORM_TENANT_ID, () -> {
            Rsp<PageResult<DownloadExportRecordListResult>> rsp = downloadExportRecordClient.page(param);
            if (RspHd.isSuccess(rsp)) {
                // oss key签名
                List<DownloadExportRecordListResult> list = rsp.getData().getRecords().stream()
                        .filter(r -> r.getOssFileEffective() != null && r.getOssFileEffective())
                        .toList();
                OssPrivateUtil.preSignedUrlList(list,
                        DownloadExportRecordListResult::getOssKey, DownloadExportRecordListResult::setOssKey);
            }
            return rsp;
        });
    }

}
