package com.swhd.oauth.service.xhs.api.dto.req.oauth;

import com.google.gson.annotations.SerializedName;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.oauth.service.douyin.api.dto.req.BaseDouyinReq;
import com.swhd.oauth.service.xhs.api.dto.req.BaseXhsReq;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@Getter
@Setter
@Accessors(chain = true)
public class XhsOauthAccessTokenReq extends BaseXhsReq<XhsOauthAccessTokenReq> {

    /**
     * 申请应用后小红书返回的 app_id
     */
    @SerializedName("app_id")
    private Long appId;

    /**
     * 申请应用后小红书返回的 secret
     */
    @LogMask(type = JsonMaskType.PASSWORD)
    @SerializedName("secret")
    private String secret;

    /**
     * 授权码
     */
    @SerializedName("auth_code")
    private String authCode;

    @Override
    public String reqUrl() {
        return "https://adapi.xiaohongshu.com/api/open/oauth2/access_token";
    }

}
