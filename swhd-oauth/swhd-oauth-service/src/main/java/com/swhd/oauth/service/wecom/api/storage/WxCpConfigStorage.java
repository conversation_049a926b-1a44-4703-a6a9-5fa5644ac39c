package com.swhd.oauth.service.wecom.api.storage;

import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.service.wecom.api.properties.WecomApiProperties;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpGetService;
import com.swhd.oauth.service.wecom.entity.WecomOauthApp;
import com.swhd.oauth.service.wecom.service.WecomOauthAppService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import org.redisson.api.RedissonClient;

import java.util.Objects;

import static com.swhd.magiccube.core.constant.SwitchConstant.YES;

/**
 * <AUTHOR>
 * @since 2025/1/2
 */
@Slf4j
public class WxCpConfigStorage extends WxCpRedissonConfigImpl {

    private final ApiWxCpGetService apiWxCpGetService;

    private final WecomApiProperties apiProperties;

    public WxCpConfigStorage(ApiWxCpGetService apiWxCpGetService,
                             RedissonClient redissonClient,
                             WecomApiProperties apiProperties) {
        super(redissonClient, ApiConstant.APP_NAME + "wecom:provider");
        this.apiWxCpGetService = apiWxCpGetService;
        this.apiProperties = apiProperties;
    }

    @Override
    public String getCorpSecret() {
        WecomOauthApp oauthApp = getOauthApp();
        if (!Objects.equals(oauthApp.getState(), YES)) {
            log.warn("企业[{}]应用[{}]应用未授权", getCorpId(), getAgentId());
            throw new ServiceException("企业应用未授权");
        }
        return oauthApp.getSecret();
    }

    @Override
    public String getToken() {
        String token = super.getToken();
        if (Func.isNotEmpty(token)) {
            return token;
        }
        return apiProperties.getProvider().getToken();
    }

    @Override
    public String getAesKey() {
        String aesKey = super.getAesKey();
        if (Func.isNotEmpty(aesKey)) {
            return aesKey;
        }
        return apiProperties.getProvider().getAesKey();
    }

    public WecomOauthApp getOauthApp() {
        return apiWxCpGetService.getOauthApp(getCorpId(), getAgentId());
    }

}
