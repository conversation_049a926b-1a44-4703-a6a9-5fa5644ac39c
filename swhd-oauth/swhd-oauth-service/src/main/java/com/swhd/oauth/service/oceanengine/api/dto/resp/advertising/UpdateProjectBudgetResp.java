package com.swhd.oauth.service.oceanengine.api.dto.resp.advertising;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class UpdateProjectBudgetResp {

    /**
     * 更新成功的项目ID列表
     */
    @SerializedName("project_ids")
    private List<Long> projectIds;

    /**
     * 更新失败的广告项目列表
     */
    private List<ErrorItem> errors;

    @Data
    public static class ErrorItem {
        /**
         * 项目ID
         */
        @SerializedName("project_id")
        private Long projectId;

        /**
         * 错误信息
         */
        @SerializedName("error_message")
        private String errorMessage;
    }
} 