package com.swhd.oauth.service.oceanengine.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.constant.SwitchConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.client.OceanengineAgentOauthCookiesClient;
import com.swhd.oauth.api.oceanengine.dto.param.cookies.OceanengineAgentOauthCookiesAddParam;
import com.swhd.oauth.api.oceanengine.dto.param.cookies.OceanengineAgentOauthCookiesPageParam;
import com.swhd.oauth.api.oceanengine.dto.param.cookies.OceanengineAgentOauthCookiesUpdateParam;
import com.swhd.oauth.api.oceanengine.dto.result.OceanengineAgentOauthCookiesResult;
import com.swhd.oauth.service.oceanengine.entity.OceanengineAgentOauthCookies;
import com.swhd.oauth.service.oceanengine.service.OceanengineAgentOauthCookiesService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@AllArgsConstructor
@RequestMapping(OceanengineAgentOauthCookiesClient.BASE_PATH)
public class OceanengineAgentOauthCookiesController implements OceanengineAgentOauthCookiesClient {

    private final OceanengineAgentOauthCookiesService oceanengineAgentOauthCookiesService;

    @Override
    public Rsp<PageResult<OceanengineAgentOauthCookiesResult>> page(OceanengineAgentOauthCookiesPageParam param) {
        IPage<OceanengineAgentOauthCookies> iPage = oceanengineAgentOauthCookiesService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, OceanengineAgentOauthCookiesResult.class));
    }

    @Override
    public Rsp<OceanengineAgentOauthCookiesResult> getById(Long id) {
        OceanengineAgentOauthCookies entity = oceanengineAgentOauthCookiesService.getById(id);
        return RspHd.data(Func.copy(entity, OceanengineAgentOauthCookiesResult.class));
    }

    @Override
    public Rsp<List<OceanengineAgentOauthCookiesResult>> listByIds(Collection<Long> ids) {
        List<OceanengineAgentOauthCookies> list = oceanengineAgentOauthCookiesService.listByIds(ids);
        return RspHd.data(Func.copy(list, OceanengineAgentOauthCookiesResult.class));
    }

    @Override
    public Rsp<List<OceanengineAgentOauthCookiesResult>> effectiveList() {
        List<OceanengineAgentOauthCookies> list = oceanengineAgentOauthCookiesService.effectiveList();
        return RspHd.data(Func.copy(list, OceanengineAgentOauthCookiesResult.class));
    }

    @Override
    public Rsp<List<OceanengineAgentOauthCookiesResult>> listAll() {
        List<OceanengineAgentOauthCookies> list = oceanengineAgentOauthCookiesService.list();
        return RspHd.data(Func.copy(list, OceanengineAgentOauthCookiesResult.class));
    }

    @Override
    @Lockable(prefixKey = "oauth:oceanengine:agent:oauth:cookies:add", key = "#param.companyName", waitTime = 6000)
    public Rsp<Void> add(OceanengineAgentOauthCookiesAddParam param) {
        String companyName = param.getCompanyName().trim();
        boolean exists = oceanengineAgentOauthCookiesService.lambdaQuery()
                .eq(OceanengineAgentOauthCookies::getCompanyName, companyName)
                .exists();
        if (exists) {
            return RspHd.fail("公司名称已存在");
        }
        OceanengineAgentOauthCookies oauthCookies = new OceanengineAgentOauthCookies();
        oauthCookies.setCompanyName(companyName);
        oauthCookies.setCookiesValue(param.getCookiesValue());
        oauthCookies.setState(Optional.ofNullable(param.getState()).orElse(SwitchConstant.YES));
        boolean result = oceanengineAgentOauthCookiesService.save(oauthCookies);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(OceanengineAgentOauthCookiesUpdateParam param) {
        OceanengineAgentOauthCookies update = new OceanengineAgentOauthCookies();
        update.setId(param.getId());
        if (Func.isNoneBlank(param.getCookiesValue())) {
            update.setCookiesValue(param.getCookiesValue());
        }
        update.setState(param.getState());
        boolean result = oceanengineAgentOauthCookiesService.updateById(update);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = oceanengineAgentOauthCookiesService.removeByIds(ids);
        return RspHd.status(result);
    }

}
