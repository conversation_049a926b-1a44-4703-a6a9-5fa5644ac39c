package com.swhd.oauth.service.douyin.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.service.douyin.api.dto.req.oauth.*;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.oauth.*;
import com.swhd.oauth.service.douyin.api.properties.DouyinApiProperties;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/3/25
 */
@Component
@AllArgsConstructor
public class DouyinOauthApiClient extends BaseDouyinApiClient {

    private final DouyinApiProperties apiProperties;

    /**
     * 创建授权链接
     *
     * @param redirectUri 重定向地址
     * @param state       state
     * @return 授权链接
     */
    public String createPreAuthUrl(String redirectUri, String state) {
        return String.format("https://open.douyin.com/platform/oauth/connect/?client_key=%s&response_type=code&scope=%s&state=%s&redirect_uri=%s",
                apiProperties.getClientKey(), Func.urlEncode(apiProperties.getOauthScope()), state, Func.urlEncode(redirectUri));
    }

    /**
     * 获取 access_token
     *
     * @see <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/get-access-token">文档</a>
     */
    public DouyinRsp<DouyinOauthAccessTokenResp> accessToken(DouyinOauthAccessTokenReq request) {
        request.setClientKey(apiProperties.getClientKey());
        request.setClientSecret(apiProperties.getClientSecret());
        return postJson(request, new TypeToken<>() {
        });
    }

    /**
     * 刷新 refresh_token
     *
     * @see <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/refresh-token">文档</a>
     */
    public DouyinRsp<DouyinOauthRenewRefreshTokenResp> renewRefreshToken(DouyinOauthRenewRefreshTokenReq request) {
        request.setClientKey(apiProperties.getClientKey());
        return postJson(request, new TypeToken<>() {
        });
    }

    /**
     * 生成 client_token
     *
     * @see <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/client-token">文档</a>
     */
    public DouyinRsp<DouyinOauthClientTokenResp> clientToken() {
        DouyinOauthClientTokenReq request = new DouyinOauthClientTokenReq();
        request.setClientKey(apiProperties.getClientKey());
        request.setClientSecret(apiProperties.getClientSecret());
        return postJson(request, new TypeToken<>() {
        });
    }

    /**
     * 刷新 access_token
     *
     * @see <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-permission/refresh-access-token">文档</a>
     */
    public DouyinRsp<DouyinOauthRefreshTokenResp> refreshToken(DouyinOauthRefreshTokenReq request) {
        request.setClientKey(apiProperties.getClientKey());
        return postJson(request, new TypeToken<>() {
        });
    }

    /**
     * 获取用户公开信息
     *
     * @see <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/account-management/get-account-open-info">文档</a>
     */
    public DouyinRsp<DouyinOauthUserInfoResp> userInfo(DouyinOauthUserInfoReq request) {
        return postJson(request, new TypeToken<>() {
        });
    }

}
