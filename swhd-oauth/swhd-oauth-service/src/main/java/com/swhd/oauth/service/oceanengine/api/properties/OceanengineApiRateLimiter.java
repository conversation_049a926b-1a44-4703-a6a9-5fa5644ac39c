package com.swhd.oauth.service.oceanengine.api.properties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/8/16
 */
@Getter
@Setter
public class OceanengineApiRateLimiter {

    /**
     * 秒级超时
     */
    private Duration secondTimeout = Duration.ofSeconds(5);

    /**
     * 分钟级超时
     */
    private Duration minuteTimeout = Duration.ofSeconds(30);

    /**
     * 获取广告主信息
     */
    @NestedConfigurationProperty
    private Config advertiserInfo = new Config(45, 4500);

    /**
     * 星图账户信息
     */
    @NestedConfigurationProperty
    private Config startInfo = new Config(9, 950);

    /**
     * 获取广告主公开信息
     */
    @NestedConfigurationProperty
    private Config advertiserPublicInfo = new Config(9, 950);

    /**
     * 代理商管理账户列表
     */
    @NestedConfigurationProperty
    private Config agentAdvertiserSelect = new Config(30, 3000);

    /**
     * 代理商管理账户列表
     */
    @NestedConfigurationProperty
    private Config agentInfo = new Config(9, 950);

    /**
     * 代理商管理账户列表
     */
    @NestedConfigurationProperty
    private Config agentTransactionRecord = new Config(9, 950);

    /**
     * 代理商管理账户列表
     */
    @NestedConfigurationProperty
    private Config agentAdvertiserInfoQuery = new Config(9, 950);

    /**
     * 查询账号余额
     */
    @NestedConfigurationProperty
    private Config fundGet = new Config(195, 19500);

    /**
     * 查询账号流水明细
     */
    @NestedConfigurationProperty
    private Config fundDailyStat = new Config(45, 4500);

    /**
     * 查询账号流水明细
     */
    @NestedConfigurationProperty
    private Config fundTransactionGet = new Config(45, 4500);

    /**
     * 自定义报表
     */
    @NestedConfigurationProperty
    private Config reportCustomGet = new Config(450, 45000);

    /**
     * 获取视频素材
     */
    @NestedConfigurationProperty
    private Config fileVideoGet = new Config(190, 19000);

    /**
     * 获取视频素材(千川)
     */
    @NestedConfigurationProperty
    private Config fileQcVideoGet = new Config(9, 950);

    /**
     * 获取视频素材(千川)
     */
    @NestedConfigurationProperty
    private Config fileLocalVideoGet = new Config(9, 950);

    /**
     * 自定义报表(千川)
     */
    @NestedConfigurationProperty
    private Config reportQcCustomGet = new Config(45, 4500);

    /**
     * 获取广告账户数据(千川)
     */
    @NestedConfigurationProperty
    private Config reportQcAdvertiser = new Config(290, 29000);

    /**
     * 获取广告素材数据(千川)
     */
    @NestedConfigurationProperty
    private Config reportQcMaterial = new Config(9, 950);

    /**
     * 获取线索推广广告数据(本地推)
     */
    @NestedConfigurationProperty
    private Config reportLocalPromotion = new Config(9, 950);

    /**
     * 获取线索推广素材数据(本地推)
     */
    @NestedConfigurationProperty
    private Config reportLocalMaterial = new Config(9, 950);

    /**
     * Ad广告项目列表
     */
    @NestedConfigurationProperty
    private Config adProjectList = new Config(45, 4500);

    /**
     * 发起转账
     */
    @NestedConfigurationProperty
    private Config transferCreate = new Config(9, 950);

    /**
     * 查询转账
     */
    @NestedConfigurationProperty
    private Config transferQuery = new Config(45, 4500);

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {

        /**
         * 秒级频次限制
         */
        private int secondRate;

        /**
         * 分钟级频次限制
         */
        private int minuteRate;

    }

}
