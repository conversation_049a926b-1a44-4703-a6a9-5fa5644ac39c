package com.swhd.oauth.service.xhs.util;

import cn.hutool.core.util.ObjectUtil;
import lombok.experimental.UtilityClass;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 小红书加解密工具类
 *
 * <AUTHOR> <EMAIL>
 * @since 2025/3/5
 */
@UtilityClass
public class XhsSecretUtil {

    //内容加密
    public static String encrypt(String content, String secretKey) throws Exception {
        byte[] key = Base64.getDecoder().decode(secretKey);
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");

        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] cipherText = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
        String res = Base64.getEncoder().encodeToString(cipherText);
        return String.join("~split~", Base64.getEncoder().encodeToString(iv), res);
    }

    //内容解密
    public static String decrypt(String cipherText, String secretKey) throws Exception {
        byte[] key = Base64.getDecoder().decode(secretKey);
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");

        String[] arr = cipherText.split("~split~");
        if (ObjectUtil.isNull(arr) || arr.length != 2) {
            throw new InvalidParameterException();
        }
        byte[] iv = Base64.getDecoder().decode(arr[0]);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] decryptedText = cipher.doFinal(Base64.getDecoder().decode(arr[1]));
        return new String(decryptedText, StandardCharsets.UTF_8);
    }

}
