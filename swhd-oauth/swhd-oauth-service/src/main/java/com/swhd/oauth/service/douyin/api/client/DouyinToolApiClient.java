package com.swhd.oauth.service.douyin.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.douyin.api.dto.req.tool.DouyinToolImageUploadReq;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.tool.DouyinToolImageUploadResp;
import com.swhd.oauth.service.douyin.api.utils.ClientTokenUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/3/27
 */
@Component
@AllArgsConstructor
public class DouyinToolApiClient extends BaseDouyinApiClient {

    /**
     * 图片上传
     * <a href="https://developer.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/interaction-management/business-tool/image-upload">文档</a>
     */
    public DouyinRsp<DouyinToolImageUploadResp> imageUpload(DouyinToolImageUploadReq request) {
        return ClientTokenUtil.execute(request, () -> postForm(request, new TypeToken<>() {
        }));
    }

}
