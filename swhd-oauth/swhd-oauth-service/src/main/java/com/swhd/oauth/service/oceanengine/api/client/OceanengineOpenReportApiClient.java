package com.swhd.oauth.service.oceanengine.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.oceanengine.api.dto.req.report.*;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.report.*;
import com.swhd.oauth.service.oceanengine.api.utils.AgentTokenUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Component
public class OceanengineOpenReportApiClient extends BaseOceanengineOpenApiClient {

    /**
     * 自定义报表
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1741387668314126">文档</a>
     */
    public OceanengineRsp<ReportCustomGetResp> customGet(ReportCustomGetReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportCustomGet", "自定义报表", apiProperties.getRateLimiter().getReportCustomGet());
    }

    /**
     * 获取自定义报表可用指标和维度
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1755261744248832">文档</a>
     */
    public OceanengineRsp<ReportCustomConfigGetResp> customConfigGet(ReportCustomConfigGetReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 自定义报表(千川)
     *
     * @see <a href="https://open.oceanengine.com/labels/12/docs/1786524924620875">文档</a>
     */
    public OceanengineRsp<ReportCustomGetResp> qcCustomGet(ReportQcCustomGetReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportQcCustomGet", "自定义报表(千川)", apiProperties.getRateLimiter().getReportQcCustomGet());
    }

    /**
     * 获取自定义报表可用指标和维度(千川)
     *
     * @see <a href="https://open.oceanengine.com/labels/12/docs/1786512680588292">文档</a>
     */
    public OceanengineRsp<ReportQcCustomConfigGetResp> qcCustomConfigGet(ReportQcCustomConfigGetReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 代理商数据（实时）
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710550053903">文档</a>
     */
    public OceanengineRsp<ReportAgentGetTodayResp> agentToday(ReportAgentGetTodayReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 代理商数据（历史）
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710550053903">文档</a>
     */
    public OceanengineRsp<ReportAgentGetHistoryResp> agentHistory(ReportAgentGetHistoryReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 代理商消耗报表数据（商务-流水查询-消耗报表）
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1784979080790218">文档</a>
     */
    public OceanengineRsp<ReportAgentAdvCostResp> agentAdvCost(ReportAgentAdvCostReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 获取广告账户数据(千川)
     *
     * @see <a href="https://open.oceanengine.com/labels/12/docs/1697466393573376">文档</a>
     */
    public OceanengineRsp<ReportQcAdvertiserResp> qcAdvertiser(ReportQcAdvertiserReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportQcAdvertiser", "获取广告账户数据(千川)", apiProperties.getRateLimiter().getReportQcAdvertiser());
    }

    /**
     * 获取广告素材数据(千川)
     *
     * @see <a href="https://open.oceanengine.com/labels/12/docs/1745207002572807">文档</a>
     */
    public OceanengineRsp<ReportQcMaterialResp> qcMaterial(ReportQcMaterialReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportQcMaterial", "获取广告素材数据(千川)", apiProperties.getRateLimiter().getReportQcMaterial());
    }

    /**
     * 获取线索推广广告数据(本地推)
     *
     * @see <a href="https://open.oceanengine.com/labels/37/docs/1804001121909835">文档</a>
     */
    public OceanengineRsp<ReportLocalPromotionResp> localPromotion(ReportLocalPromotionReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportLocalPromotion", "获取线索推广广告数据(本地推)", apiProperties.getRateLimiter().getReportLocalPromotion());
    }

    /**
     * 获取线索推广素材数据(本地推)
     *
     * @see <a href="https://open.oceanengine.com/labels/37/docs/1804001258474595">文档</a>
     */
    public OceanengineRsp<ReportLocalMaterialResp> localMaterial(ReportLocalMaterialReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "reportLocalMaterial", "获取线索推广素材数据(本地推)", apiProperties.getRateLimiter().getReportLocalMaterial());
    }

    /**
     * 返点明点化素材数据：创建下载任务
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1800368282559564">文档</a>
     */
    public OceanengineRsp<ReportRebateMaterialDownloadCreateTaskResp> rebateMaterialDownloadCreateTask(
            ReportRebateMaterialDownloadCreateTaskReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 返点明点化素材数据：查询下载任务
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1800368596087963">文档</a>
     */
    public OceanengineRsp<ReportRebateMaterialDownloadGetTaskListResp> rebateMaterialDownloadGetTaskList(
            ReportRebateMaterialDownloadGetTaskListReq request) {
        return execute(request, new TypeToken<>() {
        });
    }

    /**
     * 返点明点化素材数据：下载任务结果
     *
     * @return csv文件文本数据
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1800369859697737">文档</a>
     */
    public String rebateMaterialDownloadFile(ReportRebateMaterialDownloadFileReq request) {
        request.putAccessTokenHeader(AgentTokenUtil.getAccessToken(request.getAgentId()));
        return execute(request);
    }

}
