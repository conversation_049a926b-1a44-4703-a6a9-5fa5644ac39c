package com.swhd.oauth.service.douyin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.douyin.dto.param.goodlife.oauth.DouyinGoodlifeOauthPageParam;
import com.swhd.oauth.service.douyin.entity.DouyinGoodlifeOauth;
import com.swhd.oauth.service.douyin.mapper.DouyinGoodlifeOauthMapper;
import com.swhd.oauth.service.douyin.service.DouyinGoodlifeOauthService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 抖音生活服务授权表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@AllArgsConstructor
public class DouyinGoodlifeOauthServiceImpl extends BaseHdServiceImpl<DouyinGoodlifeOauthMapper, DouyinGoodlifeOauth>
        implements DouyinGoodlifeOauthService {

    @Override
    public IPage<DouyinGoodlifeOauth> page(DouyinGoodlifeOauthPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getBizId()), DouyinGoodlifeOauth::getBizId, param.getBizId())
                .like(Func.isNotEmpty(param.getBizName()), DouyinGoodlifeOauth::getBizName, param.getBizName())
                .orderByDesc(DouyinGoodlifeOauth::getCreateTime)
                .orderByDesc(DouyinGoodlifeOauth::getId)
                .page(convertToPage(param));
    }

}
