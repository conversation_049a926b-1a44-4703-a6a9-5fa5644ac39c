# 巨量引擎转账API客户端

## 概述

本模块提供了巨量引擎转账相关的API接口封装，包括发起转账和查询转账状态两个核心功能。

## 接口文档

1. **发起转账接口**: https://open.oceanengine.com/labels/34/docs/1789755060558916
2. **查询转账接口**: https://open.oceanengine.com/labels/34/docs/1789755120706634

## 主要组件

### 1. API客户端
- `OceanengineOpenTransferApiClient` - 转账API客户端

### 2. 请求DTO
- `TransferCreateReq` - 发起转账请求
- `TransferQueryReq` - 查询转账请求

### 3. 响应DTO
- `TransferCreateResp` - 发起转账响应
- `TransferQueryResp` - 查询转账响应

## 使用示例

### 发起转账

```java
@Autowired
private OceanengineOpenTransferApiClient transferApiClient;

public void createTransfer() {
    // 创建转账资金详情
    TransferCreateReq.TransferCapitalDetail capitalDetail = new TransferCreateReq.TransferCapitalDetail()
            .setCapitalType("PREPAY_GENERAL")    // 预付通用
            .setTransferAmount(100000L)          // 转账金额，单位：分（1000元）
            .setTransferDirection("TRANSFER_OUT"); // 转账方向：转出

    // 创建目标账户详情
    TransferCreateReq.TargetAccountDetail targetAccount = new TransferCreateReq.TargetAccountDetail()
            .setAccountId(789012L)               // 目标账户ID
            .setTransferCapitalDetailList(List.of(capitalDetail));

    // 创建转账请求
    TransferCreateReq request = new TransferCreateReq()
            .setBizRequestNo("REQ_" + System.currentTimeMillis()) // 请求唯一编号
            .setAgentId(123456L)                 // 代理商账户ID
            .setAccountId(654321L)               // 锁定账户ID
            .setTargetAccountDetailList(List.of(targetAccount)) // 目标账户列表
            .setRemark("业务转账");               // 备注

    // 设置访问令牌
    request.putAccessTokenHeader("your_access_token");

    // 执行转账
    OceanengineRsp<TransferCreateResp> response = transferApiClient.transferCreate(request);

    if (response.isSuccess()) {
        TransferCreateResp data = response.getData();
        System.out.println("转账单号: " + data.getTransferOrderId());
        System.out.println("转账状态: " + data.getStatus());
    } else {
        System.out.println("转账失败: " + response.getMessage());
    }
}
```

### 使用构建器简化转账请求

为了简化转账请求的构建，提供了 `TransferRequestBuilder` 工具类：

```java
// 使用构建器创建复杂转账请求
TransferCreateReq request = TransferRequestBuilder.builder()
        .agentId(123456L)                    // 代理商账户ID
        .accountId(654321L)                  // 锁定账户ID
        .addTransfer(789012L, TransferCapitalType.PREPAY_GENERAL, 100000L, TransferDirection.TRANSFER_OUT)
        .addTransfer(789013L, TransferCapitalType.CREDIT_BIDDING, 50000L, TransferDirection.TRANSFER_OUT)
        .remark("批量转账")
        .build();

// 快速创建单个目标账户的转账
TransferCreateReq simpleRequest = TransferRequestBuilder.createSimpleTransfer(
        123456L,                           // 代理商账户ID
        654321L,                           // 锁定账户ID
        789012L,                           // 目标账户ID
        TransferCapitalType.PREPAY_GENERAL, // 预付通用
        100000L,                           // 1000元
        TransferDirection.TRANSFER_OUT,     // 转出
        "简单转账"
);
```

### 查询转账状态

```java
public void queryTransfer() {
    // 创建查询请求
    TransferQueryReq request = new TransferQueryReq()
            .setAdvertiserId(123456L)           // 广告主ID
            .setTransferOrderId("ORDER_123456789"); // 转账单号

    // 设置访问令牌
    request.putAccessTokenHeader("your_access_token");

    // 执行查询
    OceanengineRsp<TransferQueryResp> response = transferApiClient.transferQuery(request);

    if (response.isSuccess()) {
        TransferQueryResp data = response.getData();
        System.out.println("转账单号: " + data.getTransferOrderId());
        System.out.println("转账状态: " + data.getStatus());
        System.out.println("转账金额: " + data.getAmount());
        System.out.println("创建时间: " + data.getCreateTime());
        System.out.println("完成时间: " + data.getFinishTime());
    } else {
        System.out.println("查询失败: " + response.getMessage());
    }
}
```

## 转账状态说明

- `SUCCESS`: 转账成功
- `PROCESSING`: 转账处理中
- `FAILED`: 转账失败

## 参数说明

### 发起转账请求参数

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| biz_request_no | string | 是 | 请求唯一编号，建议用uuid，防止重复转账 |
| agent_id | number | 是 | 代理商账户id，用于鉴权 |
| account_id | number | 是 | 锁定账户id，1:N的1 |
| target_account_detail_list | object[] | 是 | 目标账户列表，1:N的N，最多支持100个 |
| └─ account_id | number | 是 | 目标账户id |
| └─ transfer_capital_detail_list | object[] | 是 | 锁定账户与目标账户转账资金列表 |
| └─ capital_type | string | 是 | 转账资金类型 |
| └─ transfer_amount | number | 是 | 转账金额（单位：分） |
| └─ transfer_direction | string | 是 | 转账方向 |
| remark | string | 否 | 备注 |

### 资金类型（capital_type）

- `CREDIT_BIDDING`：授信竞价
- `CREDIT_BRAND`：授信品牌
- `CREDIT_GENERAL`：授信通用
- `PREPAY_BIDDING`：预付竞价
- `PREPAY_BRAND`：预付品牌
- `PREPAY_GENERAL`：预付通用

### 转账方向（transfer_direction）

- `TRANSFER_IN`：转入
- `TRANSFER_OUT`：转出

## 注意事项

1. **金额单位**: 所有金额字段均以分为单位
2. **请求唯一编号**: 必须保证唯一性，建议使用UUID，防止重复转账
3. **访问令牌**: 需要有效的巨量引擎访问令牌
4. **目标账户数量**: 最多支持100个目标账户
5. **频率限制**:
   - 发起转账: 9次/秒，950次/分钟
   - 查询转账: 45次/秒，4500次/分钟
6. **错误处理**: 建议检查响应的`isSuccess()`方法，失败时查看`errorCode`和`errorMsg`

## 配置

转账接口的频率限制配置在 `OceanengineApiRateLimiter` 中：

```yaml
oauth:
  oceanengine:
    api:
      rate-limiter:
        transfer-create:
          second-rate: 9
          minute-rate: 950
        transfer-query:
          second-rate: 45
          minute-rate: 4500
```
