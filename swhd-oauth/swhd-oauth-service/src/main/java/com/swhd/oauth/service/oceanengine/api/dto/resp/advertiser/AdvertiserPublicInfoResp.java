package com.swhd.oauth.service.oceanengine.api.dto.resp.advertiser;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Getter
@Setter
public class AdvertiserPublicInfoResp {

    /**
     * 广告主ID
     */
    @SerializedName("id")
    private Long id;

    /**
     * 账户名
     */
    private String name;

    /**
     * 公司名
     */
    @SerializedName("company")
    private String company;

    /**
     * 一级行业名称（新版）
     */
    @SerializedName("first_industry_name")
    private String firstIndustryName;

    /**
     * 二级行业名称（新版）
     */
    @SerializedName("second_industry_name")
    private String secondIndustryName;

}
