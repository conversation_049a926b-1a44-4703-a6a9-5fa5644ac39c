package com.swhd.oauth.service.oceanengine.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.oceanengine.api.dto.req.agent.AgentAdvertiserInfoQueryReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.agent.AgentAdvertiserSelectReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.agent.AgentInfoReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.agent.AgentTransactionRecordReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.agent.AgentAdvertiserInfoQueryResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.agent.AgentAdvertiserSelectResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.agent.AgentInfoResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.agent.AgentTransactionRecordResp;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Component
public class OceanengineOpenAgentApiClient extends BaseOceanengineOpenApiClient {

    /**
     * 代理商管理账户列表
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710516003852">文档</a>
     */
    public OceanengineRsp<AgentAdvertiserSelectResp> advertiserSelect(AgentAdvertiserSelectReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "agentAdvertiserSelect", "代理商管理账户列表", apiProperties.getRateLimiter().getAgentAdvertiserSelect());
    }

    /**
     * 获取代理商信息
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710518158351">文档</a>
     */
    public OceanengineRsp<List<AgentInfoResp>> agentInfo(AgentInfoReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "agentInfo", "获取代理商信息", apiProperties.getRateLimiter().getAgentInfo());
    }

    /**
     * 查询代理商转账记录
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1795124749017235">文档</a>
     */
    public OceanengineRsp<AgentTransactionRecordResp> transactionRecord(AgentTransactionRecordReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "agentTransactionRecord", "查询代理商转账记录", apiProperties.getRateLimiter().getAgentTransactionRecord());
    }

    /**
     * 广告主账户信息查询
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1809915654787136">文档</a>
     */
    public OceanengineRsp<AgentAdvertiserInfoQueryResp> agentAdvertiserInfoQuery(AgentAdvertiserInfoQueryReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "agentInfo", "广告主账户信息查询(代理商)", apiProperties.getRateLimiter().getAgentAdvertiserInfoQuery());
    }

}
