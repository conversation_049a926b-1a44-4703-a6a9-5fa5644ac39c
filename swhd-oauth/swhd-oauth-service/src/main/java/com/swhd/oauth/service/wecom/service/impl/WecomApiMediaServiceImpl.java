package com.swhd.oauth.service.wecom.service.impl;

import com.swhd.content.api.oss.client.OssPublicClient;
import com.swhd.content.api.oss.dto.param.OssUploadParam;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.launcher.utils.OssKeyUtil;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.wecom.dto.result.api.WecomApiMediaUploadResult;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpGetService;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpService;
import com.swhd.oauth.service.wecom.service.WecomApiMediaService;
import com.swhd.oauth.service.wecom.utils.WecomApiUtil;
import com.swj.magiccube.api.Rsp;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.Duration;

import static me.chanjar.weixin.cp.constant.WxCpApiPathConsts.Media.MEDIA_GET;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Slf4j
@Service
public class WecomApiMediaServiceImpl implements WecomApiMediaService {

    @Autowired
    private ApiWxCpGetService apiWxCpGetService;

    @Autowired
    private OssPublicClient ossPublicClient;

    @Override
    public Rsp<WecomApiMediaUploadResult> tempUpload(String corpId, String mediaType, String fileUrl) {
        try {
            ApiWxCpService wxCpService = apiWxCpGetService.getWxCpService(corpId);
            String redisKey = String.format("%s:oauth:wecom:api:media:tempUpload:%s:%s:%s:%s",
                    ApiConstant.APP_NAME, corpId, wxCpService.getWxCpConfigStorage().getAgentId(), mediaType, fileUrl);
            WecomApiMediaUploadResult result  = RedisUtil.get(redisKey, WecomApiMediaUploadResult.class);
            if (result != null && Func.isNotEmpty(result.getMediaId())) {
                return RspHd.data(result);
            }
            WxMediaUploadResult wxResult = wxCpService.getMediaService().upload(
                    mediaType, FilenameUtils.getName(fileUrl), fileUrl);
            result = Func.copy(wxResult, WecomApiMediaUploadResult.class);
            RedisUtil.set(redisKey, result, Duration.ofDays(2));
            return RspHd.data(result);
        } catch (Exception e) {
            WecomApiUtil.log(log, "上传临时素材异常", e);
            return WecomApiUtil.exception2rsp(e, "上传临时素材失败");
        }
    }

    @Override
    public Rsp<String> download(String corpId, String mediaId) {
        File file = null;
        try {
            Long tenantId = TenantHolder.getRequiredTenantId();
            ApiWxCpService wxCpService = apiWxCpGetService.getWxCpService(corpId);
            wxCpService.getWxCpConfigStorage().getApiUrl(MEDIA_GET);
            file = wxCpService.getMediaService().download(mediaId);
            if (file == null || !file.exists() || file.length() == 0) {
                log.warn("下载租户[{}]corpId[{}]mediaId[{}]素材失败", tenantId, corpId, mediaId);
                return RspHd.fail("素材下载失败");
            }
            OssUploadParam uploadParam = new OssUploadParam();
            uploadParam.setBizOssKey(String.format("oauth/wecom/media/%s/%s/%s", tenantId, corpId, mediaId));
            uploadParam.setFileBytes(FileUtils.readFileToByteArray(file));
            Rsp<String> uploadRsp = ossPublicClient.upload(uploadParam);
            if (RspHd.isFail(uploadRsp)) {
                log.error("上传企微客服素材[{}]失败：{}", mediaId, JsonLogUtil.toJsonString(uploadParam));
                return RspHd.fail(uploadRsp);
            }
            return RspHd.data(OssKeyUtil.absoluteUrl(uploadRsp.getData()));
        } catch (Exception e) {
            WecomApiUtil.log(log, "下载临时素材异常", e);
            return WecomApiUtil.exception2rsp(e, "下载临时素材失败");
        } finally {
            if (file != null) {
                try {
                    file.delete();
                } catch (Exception e) {
                    log.error("文件删除异常", e);
                }
            }
        }
    }

}
