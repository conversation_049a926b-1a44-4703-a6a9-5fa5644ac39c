package com.swhd.oauth.service.douyin.mq.producer;

import com.swhd.oauth.api.douyin.dto.message.DouyinGoodlifeClueSyncMessage;
import com.swhd.oauth.api.douyin.dto.message.DouyinMessagePushEnterMsgEventMessage;
import com.swhd.oauth.api.douyin.dto.message.DouyinMessagePushUserMsgEventMessage;
import com.swj.magiccube.stream.messaging.MagiccubeMessageBuilder;
import com.swj.magiccube.stream.utils.StreamUtil;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@UtilityClass
public class DouyinMessagePushProducer {

    /**
     * 抖音用户进入私信会话页事件
     *
     * @param message 消息
     */
    public void oauthDouyinMessagePushEnterMsgEvent(DouyinMessagePushEnterMsgEventMessage message) {
        StreamUtil.send("oauthDouyinMessagePushEnterMsgEvent-out-0", MagiccubeMessageBuilder
                .withPayload(message)
                .build());
    }

    /**
     * 抖音用户私信消息事件
     *
     * @param message 消息
     */
    public void oauthDouyinMessagePushUserMsgEvent(DouyinMessagePushUserMsgEventMessage message) {
        StreamUtil.send("oauthDouyinMessagePushUserMsgEvent-out-0", MagiccubeMessageBuilder
                .withPayload(message)
                .build());
    }

    /**
     * 抖音生活服务线索同步
     *
     * @param message 消息
     */
    public void oauthDouyinGoodlifeClueSync(DouyinGoodlifeClueSyncMessage message) {
        StreamUtil.send("oauthDouyinGoodlifeClueSync-out-0", MagiccubeMessageBuilder
                .withPayload(message)
                .build());
    }

}
