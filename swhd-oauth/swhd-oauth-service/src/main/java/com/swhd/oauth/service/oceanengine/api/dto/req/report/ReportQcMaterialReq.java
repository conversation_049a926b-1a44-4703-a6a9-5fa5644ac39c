package com.swhd.oauth.service.oceanengine.api.dto.req.report;

import com.google.gson.annotations.SerializedName;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanenginePageReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.report.ReportQcMaterialResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 获取千川广告素材数据
 *
 * <AUTHOR>
 * @see <a href="https://open.oceanengine.com/labels/12/docs/1745207002572807">文档</a>
 * @since 2024/8/19
 */
@Getter
@Setter
@Accessors(chain = true)
public class ReportQcMaterialReq extends BaseOceanenginePageReq<ReportQcMaterialResp> {

    public static final List<String> DEF_FIELDS = List.of("stat_cost", "show_cnt", "ctr", "cpm_platform",
            "click_cnt", "material_arpu", "pay_order_count", "pay_order_amount", "prepay_and_pay_order_roi",
            "create_order_count", "create_order_amount", "create_order_roi", "prepay_order_count", "prepay_order_amount",
            "dy_follow", "luban_live_enter_cnt", "live_watch_one_minute_count", "live_fans_club_join_cnt",
            "luban_live_slidecart_click_cnt", "luban_live_click_product_cnt", "luban_live_comment_cnt", "luban_live_share_cnt",
            "luban_live_gift_cnt", "luban_live_gift_amount", "dy_share", "dy_comment", "dy_like", "total_play",
            "play_duration_2s_rate", "play_duration_3s_rate", "play_duration_5s_rate", "play_duration_10s_rate",
            "play_25_feed_break", "play_50_feed_break", "play_75_feed_break", "play_over", "play_over_rate", "valid_play",
            "average_play_time_per_play", "valid_play_rate", "dislike_cnt", "report_cnt", "ecp_convert_rate",
            "ecp_convert_cnt", "ecp_cpa_platform", "unfinished_estimate_order_gmv", "indirect_order_unfinished_estimate_gmv_7days",
            "pay_order_coupon_amount", "create_order_coupon_amount");

    /**
     * 千川广告主账户id
     */
    @SerializedName("advertiser_id")
    private Long advertiserId;

    /**
     * 开始时间，格式 2021-04-05，开始时间不得早于今日-180天
     */
    @SerializedName("start_date")
    private LocalDate startDate;

    /**
     * 结束时间，格式 2021-04-05，
     * 若不传time_granularity，则时间跨度不能超过180天
     * 若传time_granularity为TIME_GRANULARITY_DAILY 天维度，则时间跨度不能超过30天
     * 若传time_granularity为TIME_GRANULARITY_HOURLY 小时纬度，则时间跨度不能超过7天
     */
    @SerializedName("end_date")
    private LocalDate endDate;

    /**
     * 需要查询的消耗指标，见返回参数
     */
    @LogMask(type = JsonMaskType.OMIT)
    private List<String> fields;

    /**
     * 过滤条件
     */
    private Filtering filtering;

    /**
     * 时间粒度 ，如果不传，返回查询日期内的聚合数据
     * 允许值:
     * TIME_GRANULARITY_DAILY (按天维度),会返回每天的数据
     * TIME_GRANULARITY_HOURLY (按小时维度)，会返回每小时维度的数据
     */
    @SerializedName("time_granularity")
    private String timeGranularity;

    /**
     * 排序字段，允许值参考数据指标，见返回字段，默认不传为stat_cost
     */
    @SerializedName("order_field")
    private String orderField;

    /**
     * 排序方式，允许值：
     * ASC 升序（默认）
     * DESC 降序
     */
    @SerializedName("order_type")
    private String orderType;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v1.0/qianchuan/report/material/get/";
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Filtering {

        /**
         * 素材类型，可选值:
         * video 视频素材
         * image 图片素材
         * carousel 图文素材
         */
        @SerializedName("material_type")
        private String materialType;

        /**
         * 视频来源筛选，以平台素材库接口的视频来源枚举值为准，允许值：
         * E_COMMERCE：本地上传
         * LIVE_HIGHLIGHT：直播剪辑素材
         * BP：巨量纵横共享素材
         * VIDEO_CAPTURE：易拍APP共享素材
         * ARTHUR：亚瑟共享素材
         * STAR：星图&即合共享素材
         * TADA：tada共享素材
         * CREATIVE_CENTER：巨量创意PC共享素材
         * AWEME：抖音主页视频
         * AGENT 巨量方舟
         * 注意：仅素材类型为视频素材时，支持
         */
        @SerializedName("video_source")
        private List<String> videoSource;

        /**
         * 素材id列表，一个素材唯一对应一个素材id，相同素材上传多次对应一个material_id
         */
        @SerializedName("material_id")
        private List<Long> materialId;

        /**
         * 素材样式筛选
         * 素材类型为视频素材时允许值：
         * VIDEO_LARGE：横版视频
         * VIDEO_VERTICAL：竖版视频
         * 素材类型为图片素材时允许值：
         * SMALL：横版小图
         * LARGE：横版大图
         * LARGE_VERTICAL：竖版图片
         * SQUARE ：商品卡方图
         */
        @SerializedName("material_mode")
        private List<String> materialMode;

        /**
         * 图片来源筛选，允许值
         * E_COMMERCE：本地上传
         * CREATIVE_CENTER：巨量创意PC共享素材
         * SQUARE：商品图
         * JI_CHUANG：即创共享素材
         */
        @SerializedName("image_source")
        private List<String> imageSource;

        /**
         * 图文来源
         * JI_CHUANG：即创共享素材
         * AWEME：抖音主页视频
         * E_COMMERCE：本地上传
         */
        @SerializedName("carousel_source")
        private List<String> carouselSource;

        /**
         * 素材建议，允许值
         * first_publish：首发素材
         * high_quality：优质素材
         * low_efficiency：低效素材
         * poor_quality：低质素材
         * improvable：可提升素材
         */
        @SerializedName("analysis_type")
        private List<String> analysisType;

    }

}
