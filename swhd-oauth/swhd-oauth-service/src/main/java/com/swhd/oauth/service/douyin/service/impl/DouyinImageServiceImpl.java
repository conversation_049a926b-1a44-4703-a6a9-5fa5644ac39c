package com.swhd.oauth.service.douyin.service.impl;

import cn.hutool.core.io.file.FileNameUtil;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.oauth.service.douyin.api.client.DouyinToolApiClient;
import com.swhd.oauth.service.douyin.api.dto.req.tool.DouyinToolImageUploadReq;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.tool.DouyinToolImageUploadResp;
import com.swhd.oauth.service.douyin.api.properties.DouyinApiProperties;
import com.swhd.oauth.service.douyin.entity.DouyinImage;
import com.swhd.oauth.service.douyin.mapper.DouyinImageMapper;
import com.swhd.oauth.service.douyin.service.DouyinImageService;
import com.swj.magiccube.tool.http.OkHttpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * 抖音图片表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Slf4j
@Service
@AllArgsConstructor
public class DouyinImageServiceImpl extends BaseHdServiceImpl<DouyinImageMapper, DouyinImage> implements DouyinImageService {

    private final DouyinApiProperties apiProperties;

    private final DouyinToolApiClient douyinToolApiClient;

    @Override
    public String getImageIdByUrl(String imageUrl) {
        String clientKey = apiProperties.getClientKey();
        DouyinImage douyinImage = lambdaQuery()
                .eq(DouyinImage::getClientKey, clientKey)
                .eq(DouyinImage::getImageUrl, imageUrl)
                .orderByDesc(DouyinImage::getCreateTime)
                .limitOne();
        if (douyinImage != null) {
            return douyinImage.getImageId();
        }

        // 下载图片
        ResponseBody responseBody = OkHttpUtil.get(imageUrl).execute().whenSuccess().body();
        File tempFile;
        try {
            tempFile = File.createTempFile("douyin-image-", FileNameUtil.getName(imageUrl));
        } catch (IOException e) {
            log.error("系统临时文件创建异常", e);
            throw new ServiceException("系统临时文件创建异常", e);
        }
        String imageId;
        try (InputStream inputStream = responseBody.byteStream()) {
            FileUtils.copyInputStreamToFile(inputStream, tempFile);
            // 上传图片到抖音
            DouyinToolImageUploadReq imageUploadReq = new DouyinToolImageUploadReq();
            imageUploadReq.setImage(tempFile);
            DouyinRsp<DouyinToolImageUploadResp> douyinRsp = douyinToolApiClient.imageUpload(imageUploadReq);
            if (douyinRsp.isFail()) {
                throw new ServiceException(HttpStatus.INTERNAL_SERVER_ERROR.value(), douyinRsp.getDescription());
            }
            imageId = (String) douyinRsp.getAdditionalProperty().get("image_id");
        } catch (IOException e) {
            log.warn("图片下载异常", e);
            throw new ServiceException("图片下载异常", e);
        } finally {
            tempFile.delete();
        }

        // 保存图片id
        douyinImage = new DouyinImage();
        douyinImage.setClientKey(clientKey);
        douyinImage.setImageUrl(imageUrl);
        douyinImage.setImageId(imageId);
        save(douyinImage);

        return imageId;
    }

}
