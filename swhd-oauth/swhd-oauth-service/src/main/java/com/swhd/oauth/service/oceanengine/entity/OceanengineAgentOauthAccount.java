package com.swhd.oauth.service.oceanengine.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 巨量引擎授权代理商表实体类
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("toauth_oceanengine_agent_oauth_account")
public class OceanengineAgentOauthAccount extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "代理商类型：1-巨量广告，2-巨量千川，3-巨量星图，4-巨量本地推")
    private Integer agentType;

    @Schema(description = "代理商名称")
    private String agentName;

    @Schema(description = "公司名称")
    private String companyName;

}
