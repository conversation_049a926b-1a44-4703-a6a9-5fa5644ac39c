package com.swhd.oauth.service.douyin.api.dto.req.im;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.douyin.api.dto.req.BaseDouyinPageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 抖音IM高级在线预约卡片 入参
 * <AUTHOR> <EMAIL>
 * @since 2024/12/17
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "抖音IM咨询卡片 入参")
public class DouyinImMarketingToolReq extends BaseDouyinPageReq<DouyinImMarketingToolReq> {

    @Override
    public String reqUrl() {
        return "https://open.douyin.com/api/enterprise/v1/im/list_marketing_tool/";
    }

    /**
     * 调用/oauth/access_token/生成的token，此token需要经营者授权，用户唯一标识
     */
    @SerializedName("open_id")
    private String openId;

    /**
     * 组件类型：45-高级在线预约，当前仅支持查询高级在线预约
     */
    @SerializedName("link_types")
    private List<Integer> linkTypes;

}
