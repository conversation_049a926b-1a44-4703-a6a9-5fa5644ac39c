package com.swhd.oauth.service.xhs.web;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Base64Util;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.magiccube.web.log.utils.GatewayUtil;
import com.swhd.oauth.api.xhs.client.XhsOauthPreClient;
import com.swhd.oauth.api.xhs.dto.param.oauth.pre.XhsAccountBindingParam;
import com.swhd.oauth.api.xhs.dto.param.oauth.pre.XhsOauthPreStateDto;
import com.swhd.oauth.service.common.OauthStatusCode;
import com.swhd.oauth.service.xhs.api.client.XhsOauthApiClient;
import com.swhd.oauth.service.xhs.api.dto.resp.oauth.XhsOauthAccessTokenResp;
import com.swhd.oauth.service.xhs.entity.XhsOauthAccount;
import com.swhd.oauth.service.xhs.properties.XhsApiProperties;
import com.swhd.oauth.service.xhs.service.XhsOauthAccountService;
import com.swhd.oauth.service.xhs.service.XhsOauthPreService;
import com.swhd.oauth.service.xhs.util.XhsSecretUtil;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(XhsOauthPreClient.BASE_PATH)
public class XhsOauthPreController implements XhsOauthPreClient {

    private final XhsOauthApiClient xhsOauthApiClient;
    private final XhsOauthPreService xhsOauthPreService;
    private final XhsOauthAccountService xhsOauthAccountService;

    private final XhsApiProperties xhsApiProperties;

    @Override
    public Rsp<String> createPreAuthUrl(String redirectUri) {
        Long tenantId = TenantHolder.getRequiredTenantId();
        XhsOauthPreStateDto preState = new XhsOauthPreStateDto(tenantId, redirectUri);
        String state = Base64Util.encodeToUrlSafeString(JsonUtil.toJsonBytes(preState));
        redirectUri = String.format(xhsApiProperties.getOauthCallbackUrl(), GatewayUtil.getGatewayHost());
        String preAuthUrl = xhsOauthApiClient.createPreAuthUrl(redirectUri, state);
        return RspHd.data(preAuthUrl);
    }

    @Override
    @SneakyThrows
    public Rsp<String> callback(String state, String authCode) {
        XhsOauthPreStateDto preState = new XhsOauthPreStateDto();
        XhsOauthAccessTokenResp tokenResp = new XhsOauthAccessTokenResp();
        try {
            state = Base64Util.decodeUrlSafe(state);
            preState = JsonUtil.parseObject(state, XhsOauthPreStateDto.class);
            if (preState == null) {
                log.warn("小红书授权成功回调，缺少必要参数：state");
                throw new ServiceException("授权失败，缺少必要参数：state");
            }

            if (preState.getTenantId() == null) {
                log.warn("小红书授权成功回调，缺少必要参数：tenantId");
                throw new ServiceException("授权失败，缺少必要参数：tenantId");
            }

            // 保存授权码等信息
            Rsp<XhsOauthAccessTokenResp> rsp = TenantHolder.methodTenant(preState.getTenantId(),
                    () -> xhsOauthPreService.callback(authCode));
            Rsp.assertSuccessAndNotNull(rsp);
            tokenResp = rsp.getData();
        } catch (Exception e) {
            // 失败跳转结果页显示错误信息
            String redirectUrl = String.format("%s%scode=%s&message=%s", preState.getRedirectUrl(),
                    preState.getRedirectUrl().contains("?") ? "&" : "?",  OauthStatusCode.XHS_OAUTH_FAIL, e.getMessage());
            log.debug("小红书授权失败，跳转url：{}", redirectUrl);
            return Rsp.data(URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8));
        }

        // 绑定主账号
        XhsOauthAccessTokenResp finalTokenResp = tokenResp;
        XhsOauthAccount mainAccount = TenantHolder.methodTenant(preState.getTenantId(), () -> {
            XhsOauthAccount xhsOauthAccount = xhsOauthAccountService.getMainAccount(finalTokenResp.getUserId());
            if (Objects.isNull(xhsOauthAccount)) {
                xhsOauthAccount = new XhsOauthAccount();
                xhsOauthAccount.setAppId(finalTokenResp.getAppId());
                xhsOauthAccount.setAccountType(1);
                xhsOauthAccount.setUserId(finalTokenResp.getUserId());
                xhsOauthAccount.setKosUserId(finalTokenResp.getUserId());
                xhsOauthAccountService.save(xhsOauthAccount);
            }
            return xhsOauthAccount;
        });


        XhsAccountBindingParam accountBindingParam = new XhsAccountBindingParam(mainAccount.getId().toString());
        // 加密
        String token = XhsSecretUtil.encrypt(JsonUtil.toJsonString(accountBindingParam), xhsApiProperties.getMsgSecretKey());
        // URL编码
        token = URLEncoder.encode(token, StandardCharsets.UTF_8);
        // 拼接绑定url（成功直接条装小红书绑定账号）
        String redirectUrl = String.format(xhsApiProperties.getOauthSuccessRedirectUrl(), xhsApiProperties.getAppId(), token);
        return RspHd.data(redirectUrl);
    }

}
