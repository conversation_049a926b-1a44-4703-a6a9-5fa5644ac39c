package com.swhd.oauth.service.oceanengine.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.oceanengine.api.dto.req.fund.FundDailyStatReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.fund.FundGetReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.fund.FundTransactionGetReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.fund.FundDailyStatResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.fund.FundGetResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.fund.FundTransactionGetResp;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Component
public class OceanengineOpenFundApiClient extends BaseOceanengineOpenApiClient {

    /**
     * 查询账号余额
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710526192652">文档</a>
     */
    public OceanengineRsp<FundGetResp> fundGet(FundGetReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "fundGet", "查询账号余额", apiProperties.getRateLimiter().getFundGet());
    }

    /**
     * 查询账户日流水
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710526682112">文档</a>
     */
    public OceanengineRsp<FundDailyStatResp> dailyStat(FundDailyStatReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "fundDailyStat", "查询账号流水明细", apiProperties.getRateLimiter().getFundDailyStat());
    }

    /**
     * 查询账号流水明细
     *
     * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710527205388">文档</a>
     */
    public OceanengineRsp<FundTransactionGetResp> transactionGet(FundTransactionGetReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "fundTransactionGet", "查询账号流水明细", apiProperties.getRateLimiter().getFundTransactionGet());
    }

}
