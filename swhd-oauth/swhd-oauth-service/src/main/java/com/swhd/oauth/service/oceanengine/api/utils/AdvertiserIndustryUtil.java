package com.swhd.oauth.service.oceanengine.api.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.swhd.magiccube.redis.utils.CacheConfig;
import com.swhd.magiccube.redis.utils.RedisHttpRequestCacheUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.service.oceanengine.api.client.OceanengineOpenToolsApiClient;
import com.swhd.oauth.service.oceanengine.api.dto.req.tools.ToolsIndustryGetReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.tools.ToolsIndustryResp;
import com.swhd.oauth.service.oceanengine.api.properties.OceanengineApiProperties;
import com.swhd.oauth.service.oceanengine.api.utils.industry.FirstIndustry;
import com.swhd.oauth.service.oceanengine.api.utils.industry.Industry;
import com.swhd.oauth.service.oceanengine.api.utils.industry.SecondIndustry;
import com.swj.magiccube.util.SpringUtil;
import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.util.*;

/**
 * 巨量引擎广告主行业util
 *
 * <AUTHOR>
 * @since 2023/11/20
 */
@UtilityClass
public class AdvertiserIndustryUtil {

    private static final String INDUSTRY_LIST_CACHE_KEY = "oauth:oceanengine:api:industry:list";

    private static OceanengineOpenToolsApiClient oceanengineOpenToolsApiClient;

    private static OceanengineApiProperties oceanengineProperties;

    private static OceanengineOpenToolsApiClient getOceanengineOpenToolsApiClient() {
        if (oceanengineOpenToolsApiClient == null) {
            oceanengineOpenToolsApiClient = SpringUtil.getBean(OceanengineOpenToolsApiClient.class);
        }
        return oceanengineOpenToolsApiClient;
    }

    private static OceanengineApiProperties getOceanengineProperties() {
        if (oceanengineProperties == null) {
            oceanengineProperties = SpringUtil.getBean(OceanengineApiProperties.class);
        }
        return oceanengineProperties;
    }

    public Map<Long, Industry> map() {
        List<FirstIndustry> list = list();
        Map<Long, Industry> map = new HashMap<>();
        // 遍历一级行业
        list.forEach(fi -> {
            Industry industry = new Industry(fi.getIndustryId(), fi.getIndustryName());
            map.put(fi.getIndustryId(), industry);
            if (Func.isEmpty(fi.getSecondIndustryList())) {
                return;
            }
            // 遍历二级行业
            fi.getSecondIndustryList().forEach(si ->
                    map.put(si.getIndustryId(), new Industry(si.getIndustryId(), si.getIndustryName(), industry)));
        });
        return map;
    }

    public List<FirstIndustry> list() {
        Long appId = getOceanengineProperties().getAppSecret().keySet().stream().findFirst().orElse(null);
        if (appId == null) {
            return Collections.emptyList();
        }
        List<FirstIndustry> industryList = RedisHttpRequestCacheUtil.get(() -> {
            ToolsIndustryGetReq req = new ToolsIndustryGetReq()
                    .setLevel(2)
                    .setType("ADVERTISER");
            OceanengineRsp<ToolsIndustryResp> rsp = AppTokenUtil.execute(appId, appAccessToken -> {
                req.putAccessTokenHeader(appAccessToken);
                return getOceanengineOpenToolsApiClient().industryGet(req);
            });
            List<ToolsIndustryResp.Item> list = Optional.ofNullable(rsp.getData()).map(ToolsIndustryResp::getList).orElse(null);
            if (Func.isEmpty(list)) {
                return null;
            }
            List<FirstIndustry> firstIndustryList = list.stream()
                    .map(item -> new FirstIndustry(item.getFirstIndustryId(), item.getFirstIndustryName()))
                    .distinct()
                    .toList();
            firstIndustryList.forEach(firstIndustry -> {
                List<SecondIndustry> secondIndustryList = list.stream()
                        .filter(item -> Objects.equals(firstIndustry.getIndustryId(), item.getFirstIndustryId()))
                        .map(item -> new SecondIndustry(item.getSecondIndustryId(), item.getSecondIndustryName()))
                        .distinct()
                        .toList();
                firstIndustry.setSecondIndustryList(secondIndustryList);
            });
            return firstIndustryList;
        }, INDUSTRY_LIST_CACHE_KEY, Duration.ofHours(12), new TypeReference<>() {
        }, CacheConfig.NO_TENANT);
        return Optional.ofNullable(industryList).orElse(Collections.emptyList());
    }

}
