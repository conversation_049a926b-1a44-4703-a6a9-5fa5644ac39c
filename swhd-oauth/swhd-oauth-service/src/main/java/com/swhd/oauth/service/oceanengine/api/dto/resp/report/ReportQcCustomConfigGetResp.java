package com.swhd.oauth.service.oceanengine.api.dto.resp.report;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.api.oceanengine.constant.OceanengineReportQcDataTopics;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/20
 */
@Getter
@Setter
public class ReportQcCustomConfigGetResp {

    /**
     * 数据主题配置列表
     */
    @SerializedName("custom_config_datas")
    private List<CustomConfigData> customConfigDataList;

    @Getter
    @Setter
    public static class CustomConfigData {

        /**
         * 数据主题
         */
        @SerializedName("data_topic")
        private OceanengineReportQcDataTopics dataTopic;

        /**
         * 维度列表
         */
        private List<Dimension> dimensions;

        /**
         * 指标列表
         */
        private List<Metric> metrics;

    }

    @Getter
    @Setter
    public static class Dimension {

        /**
         * 维度字段
         */
        private String field;

        /**
         * 维度名称
         */
        private String name;

        /**
         * 维度描述
         */
        private String description;

        /**
         * 是否支持排序
         */
        @SerializedName("sortable")
        private String sortAble;

        /**
         * 是否支持筛选
         */
        @SerializedName("filterable")
        private String filterAble;

        /**
         * 维度描述
         */
        @SerializedName("filter_config")
        private FilterConfig filterConfig;

        /**
         * 互斥的维度列表
         */
        @SerializedName("exclusion_dims")
        private List<String> exclusionDims;

        /**
         * 互斥的指标列表
         */
        @SerializedName("exclusion_metrics")
        private List<String> exclusionMetrics;

        /**
         * 是否必填
         * 注意：对于必填参数，需要在自定义报表中传入，否则会导致查询的数据不准确
         */
        @SerializedName("is_required")
        private Boolean isRequired;

    }

    @Getter
    @Setter
    public static class FilterConfig {

        /**
         * 字段类型
         * 1 -固定枚举值
         * 2 - 固定输入值
         * 3 -数值类型
         */
        private Integer type;

        /**
         * 筛选方式，枚举值：
         * 1 等于
         * 2 小于
         * 3 小于等于
         * 4 大于
         * 5 大于等于
         * 6 不等于
         * 7 包含
         * 8 不包含
         * 9 范围查询
         * 10 多个值匹配包含
         * 11 多个值匹配都要包含
         */
        private Integer operator;

        /**
         * 筛选字段传入数量上限
         */
        @SerializedName("value_limit")
        private Integer valueLimit;

        /**
         * 筛选字段枚举列表
         */
        @SerializedName("range_value")
        private List<RangeValue> rangeValue;

    }

    @Getter
    @Setter
    public static class RangeValue {

        /**
         * 筛选字段名称
         */
        private String label;

        /**
         * 筛选字段具体值
         */
        private String value;

    }

    @Getter
    @Setter
    public static class Metric {

        /**
         * 字段
         */
        private String field;

        /**
         * 字段名称
         */
        private String name;

        /**
         * 指标单位
         * 0： 无单位
         * 1 ：千分之一分
         * 2 ：分
         * 3 ：元
         * 4 ：个
         * 10 ：百分比
         */
        private Integer unit;

        /**
         * 字段描述
         */
        private String description;

        /**
         * 与指标互斥的维度
         */
        @SerializedName("exclusion_dims")
        private List<String> exclusionDims;

    }

}
