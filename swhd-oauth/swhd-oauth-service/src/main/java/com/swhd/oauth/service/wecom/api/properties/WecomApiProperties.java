package com.swhd.oauth.service.wecom.api.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Getter
@Setter
@ConfigurationProperties(WecomApiProperties.PREFIX)
@Component
public class WecomApiProperties {

    public static final String PREFIX = "oauth.wecom.api";

    /**
     * 企业微信第三方服务商
     */
    @NestedConfigurationProperty
    private WecomProvider provider = new WecomProvider();

    /**
     * service最大缓存数
     */
    private int serviceCacheMaxSize = 10000;

    /**
     * service缓存时间
     */
    private Duration serviceCacheDuration = Duration.ofMinutes(10);

    /**
     * 企微客服消息时长，超过时间丢弃
     */
    private Duration kefuMsgDuration = Duration.ofMinutes(10);

}
