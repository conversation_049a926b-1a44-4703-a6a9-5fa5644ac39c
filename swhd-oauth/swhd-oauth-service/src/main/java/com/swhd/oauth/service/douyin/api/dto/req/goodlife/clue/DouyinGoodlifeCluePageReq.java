package com.swhd.oauth.service.douyin.api.dto.req.goodlife.clue;

import com.swhd.magiccube.tool.DateTimeUtil;
import com.swhd.magiccube.tool.GsonUtil;
import com.swhd.oauth.service.douyin.api.dto.req.BaseDouyinOauthReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "线索查询")
public class DouyinGoodlifeCluePageReq extends BaseDouyinOauthReq<DouyinGoodlifeCluePageReq> {

    /**
     * 页数
     */
    public DouyinGoodlifeCluePageReq setPage(int page) {
        this.putParams("page", String.valueOf(page));
        return this;
    }

    /**
     * 页面大小
     */
    public DouyinGoodlifeCluePageReq setPageSize(int pageSIze) {
        this.putParams("page_size", String.valueOf(pageSIze));
        return this;
    }

    /**
     * 来客品牌账户
     */
    public DouyinGoodlifeCluePageReq setAccountId(String accountId) {
        this.putParams("account_id", accountId);
        return this;
    }

    /**
     * 跟进子账户，不是门店id
     */
    public DouyinGoodlifeCluePageReq setLifeAccountId(List<String> lifeAccountId) {
        this.putParams("life_account_id", GsonUtil.toJsonString(lifeAccountId));
        return this;
    }

    /**
     * 查询起始时间
     */
    public DouyinGoodlifeCluePageReq setStartTime(LocalDateTime startTime) {
        this.putParams("start_time", DateTimeUtil.formatDateTime(startTime));
        return this;
    }

    /**
     * 查询截止时间
     */
    public DouyinGoodlifeCluePageReq setEndTime(LocalDateTime endTime) {
        this.putParams("end_time", DateTimeUtil.formatDateTime(endTime));
        return this;
    }

    @Override
    public String reqUrl() {
        return "https://open.douyin.com/goodlife/v1/open_api/crm/clue/query/";
    }

}
