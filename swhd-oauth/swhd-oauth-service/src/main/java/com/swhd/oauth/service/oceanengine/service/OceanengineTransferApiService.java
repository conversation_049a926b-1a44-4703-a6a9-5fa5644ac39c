package com.swhd.oauth.service.oceanengine.service;

import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferCreateParam;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferQueryParam;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferCreateResult;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferQueryResult;
import com.swj.magiccube.api.Rsp;

/**
 * 巨量引擎转账API服务
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
public interface OceanengineTransferApiService {

    /**
     * 发起账户转账
     *
     * @param param 转账请求参数
     * @return 转账结果
     */
    Rsp<OceanengineTransferCreateResult> transferCreate(OceanengineTransferCreateParam param);

    /**
     * 查询账户转账
     *
     * @param param 查询请求参数
     * @return 查询结果
     */
    Rsp<OceanengineTransferQueryResult> transferQuery(OceanengineTransferQueryParam param);
}
