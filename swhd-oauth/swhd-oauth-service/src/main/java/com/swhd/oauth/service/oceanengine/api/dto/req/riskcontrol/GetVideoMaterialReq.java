package com.swhd.oauth.service.oceanengine.api.dto.req.riskcontrol;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.AdvertiserViolationInfoResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.GetPictureMaterialResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.GetVideoMaterialResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取广告主信息
 *
 * <AUTHOR>
 * @see <a href="https://open.oceanengine.com/labels/7/docs/1696710601820172">文档</a>
 * @since 2024/09/05
 */
@Getter
@Setter
@Accessors(chain = true)
public class GetVideoMaterialReq extends BaseOceanengineReq<GetVideoMaterialResp> {

    /**
     * 广告主id
     */
    @SerializedName("advertiser_id")
    private Long advertiserId;

    /**
     * 视频过滤条件
     */
    private Filtering filtering;

    /**
     * 页码，默认值1
     */
    private Long page;

    /**
     * 页面大小，默认值20
     */
    @SerializedName("page_size")
    private Long pageSize;


    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/2/file/video/get/";
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Filtering {

        /**
         * 视频宽度
         */
        private Long width;

        /**
         * 视频高度
         */
        private Long height;

        /**
         * 视频宽高比，示例: [1.7, 2.5]
         */
        private List<Float> ratio;

        /**
         * 视频ids，示例: ["86adb23eaa21229fc04ef932b5089bb8"]
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        @SerializedName("video_ids")
        private List<String> videoIds;

        /**
         * 素材id列表，可以根据material_ids（素材报表使用的id，一个素材唯一对应一个素材id）进行过滤
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        @SerializedName("material_ids")
        private List<Long> materialIds;

        /**
         * md5值列表，可以根据素材的md5进行过滤
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        private List<String> signatures;

        /**
         * 根据视频上传时间进行过滤的起始时间，与end_time搭配使用，格式：yyyy-mm-dd
         */
        @SerializedName("start_time")
        private LocalDateTime startTime;

        /**
         * 根据视频上传时间进行过滤的截止时间，与start_time搭配使用，格式：yyyy-mm-dd
         */
        @SerializedName("end_time")
        private LocalDateTime endTime;

        /**
         * 视频标签
         */
        private List<String> labels;

        /**
         * 素材来源，详见【附录-素材来源】
         * 枚举值大小写敏感，请严格按照定义的名称传参
         */
        private List<String> source;

        /**
         * 星图达人 id 检索，仅当source = STAR 时，支持通过星图达人ID进行筛选，单次最多支持传入20 个id进行检索
         */
        @SerializedName("star_author_ids")
        private List<String> starAuthorIds;
    }
}
