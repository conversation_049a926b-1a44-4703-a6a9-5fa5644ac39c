package com.swhd.oauth.service.wechat.api.service;

import com.swhd.oauth.service.common.client.ApiApacheHttpClient;
import me.chanjar.weixin.open.api.WxOpenConfigStorage;
import me.chanjar.weixin.open.api.WxOpenMaService;
import me.chanjar.weixin.open.api.WxOpenMpService;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.api.impl.WxOpenComponentServiceImpl;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
public class WechatOpenComponentServiceImpl extends WxOpenComponentServiceImpl {

    private static final Map<String, WxOpenMaService> WX_OPEN_MA_SERVICE_MAP = new ConcurrentHashMap<>();
    private static final Map<String, WxOpenMpService> WX_OPEN_MP_SERVICE_MAP = new ConcurrentHashMap<>();

    private final ApiApacheHttpClient apiApacheHttpClient;

    public WechatOpenComponentServiceImpl(ApiApacheHttpClient apiApacheHttpClient,
                                          WxOpenService wxOpenService) {
        super(wxOpenService);
        this.apiApacheHttpClient = apiApacheHttpClient;
    }

    @Override
    public WxOpenMpService getWxMpServiceByAppid(String appId) {
        WxOpenMpService wxMpService = WX_OPEN_MP_SERVICE_MAP.get(appId);
        if (wxMpService == null) {
            synchronized (WX_OPEN_MP_SERVICE_MAP) {
                wxMpService = WX_OPEN_MP_SERVICE_MAP.get(appId);
                if (wxMpService == null) {
                    WxOpenConfigStorage storage = this.getWxOpenConfigStorage();
                    wxMpService = new WechatOpenMpServiceImpl(apiApacheHttpClient, this, appId, storage.getWxMpConfigStorage(appId));
                    // 配置重试次数和重试间隔
                    wxMpService.setMaxRetryTimes(storage.getMaxRetryTimes());
                    wxMpService.setRetrySleepMillis(storage.getRetrySleepMillis());
                    WX_OPEN_MP_SERVICE_MAP.put(appId, wxMpService);
                }
            }
        }
        return wxMpService;
    }

    @Override
    public WxOpenMaService getWxMaServiceByAppid(String appId) {
        WxOpenMaService wxOpenMaService = WX_OPEN_MA_SERVICE_MAP.get(appId);
        if (wxOpenMaService == null) {
            synchronized (WX_OPEN_MA_SERVICE_MAP) {
                wxOpenMaService = WX_OPEN_MA_SERVICE_MAP.get(appId);
                if (wxOpenMaService == null) {
                    WxOpenConfigStorage storage = this.getWxOpenConfigStorage();
                    wxOpenMaService = new WechatOpenMaServiceImpl(apiApacheHttpClient, this, appId, storage.getWxMaConfig(appId));
                    // 配置重试次数和重试间隔
                    wxOpenMaService.setMaxRetryTimes(storage.getMaxRetryTimes());
                    wxOpenMaService.setRetrySleepMillis(storage.getRetrySleepMillis());
                    WX_OPEN_MA_SERVICE_MAP.put(appId, wxOpenMaService);
                }
            }
        }
        return wxOpenMaService;
    }

}
