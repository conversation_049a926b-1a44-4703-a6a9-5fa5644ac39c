package com.swhd.oauth.service.oceanengine.api.dto.resp.transfer;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 查询账户转账响应
 */
@Getter
@Setter
public class TransferQueryResp {
    /**
     * 转账单号
     */
    @SerializedName("transfer_serial")
    private String transferSerial;

    /**
     * 业务请求id
     */
    @SerializedName("biz_request_no")
    private String bizRequestNo;

    /**
     * 转账方向（以目标账户视角确定）
     * TRANSFER_IN: 转入
     * TRANSFER_OUT: 转出
     */
    @SerializedName("transfer_direction")
    private String transferDirection;

    /**
     * 转账总金额（单位：分）
     */
    @SerializedName("transfer_amount")
    private Long transferAmount;

    /**
     * 转账总状态
     * NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS
     */
    @SerializedName("transfer_status")
    private String transferStatus;

    /**
     * 转账完成时间
     */
    @SerializedName("transfer_finish_time")
    private String transferFinishTime;

    /**
     * 转账创建时间
     */
    @SerializedName("transfer_create_time")
    private String transferCreateTime;

    /**
     * 账户信息列表
     */
    @SerializedName("transfer_target_record_list")
    private List<TargetRecord> transferTargetRecordList;

    /**
     * 备注
     */
    private String remark;

    @Getter
    @Setter
    public static class TargetRecord {
        /**
         * 错误账号id
         */
        @SerializedName("account_id")
        private Long accountId;

        /**
         * 目标账号id
         */
        @SerializedName("target_account_id")
        private Long targetAccountId;

        /**
         * 转账金额（单位：分）
         */
        @SerializedName("transfer_amount")
        private Long transferAmount;

        /**
         * 转账状态
         * NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS
         */
        @SerializedName("transfer_status")
        private String transferStatus;

        /**
         * 转账资金类型列表
         */
        @SerializedName("transfer_capital_record_list")
        private List<CapitalRecord> transferCapitalRecordList;
    }

    @Getter
    @Setter
    public static class CapitalRecord {
        /**
         * 转账资金类型
         * CREDIT_BIDDING/CREDIT_BRAND/CREDIT_GENERAL/PREPAY_BIDDING/PREPAY_BRAND/PREPAY_GENERAL
         */
        @SerializedName("capital_type")
        private String capitalType;

        /**
         * 转账资金金额（单位：分）
         */
        @SerializedName("transfer_amount")
        private Long transferAmount;

        /**
         * 转账资金状态
         * NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS
         */
        @SerializedName("transfer_status")
        private String transferStatus;

        /**
         * 失败原因
         */
        @SerializedName("fail_reason")
        private String failReason;
    }
}
