package com.swhd.oauth.service.oceanengine.api.dto.req;

import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;

import java.net.URI;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
public class BaseOceanengineHttpGet extends HttpEntityEnclosingRequestBase {

    public BaseOceanengineHttpGet() {
        super();
    }

    /**
     * @throws IllegalArgumentException if the uri is invalid.
     */
    public BaseOceanengineHttpGet(final String uri) {
        this(URI.create(uri));
    }

    public BaseOceanengineHttpGet(final URI uri) {
        super();
        setURI(uri);
    }

    @Override
    public String getMethod() {
        return "GET";
    }

}
