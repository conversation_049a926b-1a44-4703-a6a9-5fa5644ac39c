package com.swhd.oauth.service.oceanengine.api.dto.req.transfer;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferQueryResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 查询账户转账请求
 * 
 * @see <a href="https://open.oceanengine.com/labels/34/docs/1789755120706634">文档</a>
 */
@Getter
@Setter
@Accessors(chain = true)
public class TransferQueryReq extends BaseOceanengineReq<TransferQueryResp> {


    /**
     * 请求唯一编号，建议用uuid，防止重复转账
     */
    @SerializedName("biz_request_no")
    private String bizRequestNo;

    /**
     * 代理商账户id，用于鉴权
     */
    @SerializedName("agent_id")
    private Long agentId;

    /**
     * 发起转账的幂等id
     */
    @SerializedName("transfer_biz_request_no")
    private String transferBizRequestNo;

    /**
     * 转账单号
     */
    @SerializedName("transfer_serial")
    private String transferSerial;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/cg_transfer/query_transfer_detail/";
    }
}
