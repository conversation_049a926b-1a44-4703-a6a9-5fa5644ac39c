package com.swhd.oauth.service.douyin.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.douyin.api.dto.req.im.DouyinImCardPageReq;
import com.swhd.oauth.service.douyin.api.dto.req.im.DouyinImMarketingToolReq;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.im.DouyinImCardPageResp;
import com.swhd.oauth.service.douyin.api.dto.resp.im.DouyinImMarketingToolPageResp;
import com.swhd.oauth.service.douyin.api.utils.AccessTokenUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 抖音IM线索组件
 *
 * <AUTHOR> <EMAIL>
 * @since 2024/12/16
 */
@Component
@AllArgsConstructor
public class DouyinImComponentApiClient extends BaseDouyinApiClient {

    /**
     * 查询咨询列表
     *
     * @param request
     * @return
     */
    public DouyinRsp<DouyinImCardPageResp> listCard(DouyinImCardPageReq request) {
        return AccessTokenUtil.execute(request, () -> postJson(request, new TypeToken<>() {
        }));
    }

    /**
     * 查询高级在线预约卡片
     */
    public DouyinRsp<DouyinImMarketingToolPageResp> listMarketingTool(DouyinImMarketingToolReq request) {
        return AccessTokenUtil.execute(request, () -> postJson(request, new TypeToken<>() {
        }));
    }

}
