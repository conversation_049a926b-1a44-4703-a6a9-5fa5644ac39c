package com.swhd.oauth.service.wecom.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.common.constant.KefuOauthType;
import com.swhd.oauth.api.common.message.OauthAddMessage;
import com.swhd.oauth.api.wecom.dto.param.oauth.kefu.WecomOauthKefuPageParam;
import com.swhd.oauth.api.wecom.dto.result.WecomOauthKefuResult;
import com.swhd.oauth.service.common.mq.producer.OauthAddProducer;
import com.swhd.oauth.service.wecom.entity.WecomOauthKefu;
import com.swhd.oauth.service.wecom.mapper.WecomOauthKefuMapper;
import com.swhd.oauth.service.wecom.service.WecomOauthKefuService;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.swhd.magiccube.core.constant.SwitchConstant.YES;

/**
 * 企微授权客服表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
@AllArgsConstructor
public class WecomOauthKefuServiceImpl extends BaseHdServiceImpl<WecomOauthKefuMapper, WecomOauthKefu>
        implements WecomOauthKefuService {

    @Override
    public IPage<WecomOauthKefuResult> page(WecomOauthKefuPageParam param) {
        return baseMapper.page(param, convertToPage(param));
    }

    @Override
    public List<WecomOauthKefu> listByCorpId(String corpId) {
        return lambdaQuery().eq(WecomOauthKefu::getCorpId, corpId).list();
    }

    @Override
    public List<WecomOauthKefu> listByCorpIds(List<String> corpIds) {
        if (Func.isEmpty(corpIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(WecomOauthKefu::getCorpId, corpIds).list();
    }

    @Override
    public WecomOauthKefu getByKefuId(String kefuId) {
        return lambdaQuery().eq(WecomOauthKefu::getKefuId, kefuId).limitOne();
    }

    @Override
    public List<WecomOauthKefu> listByKefuIds(Collection<String> kefuIds) {
        if (Func.isEmpty(kefuIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(WecomOauthKefu::getKefuId, kefuIds).list();
    }

    @Override
    public Rsp<String> getCorpId(@Nullable String corpId, String kefuId) {
        if (Func.isNotBlank(corpId)) {
            return RspHd.data(corpId);
        }
        WecomOauthKefu oauthKefu = getByKefuId(kefuId);
        if (oauthKefu == null) {
            return RspHd.fail("客服不存在");
        }
        if (!Objects.equals(oauthKefu.getState(), YES)) {
            return RspHd.fail("客服已被禁用");
        }
        return RspHd.data(oauthKefu.getCorpId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(WecomOauthKefu oauthKefu) {
        save(oauthKefu);

        OauthAddMessage oauthAddMessage = new OauthAddMessage();
        oauthAddMessage.setId(oauthKefu.getId());
        oauthAddMessage.setTenantId(TenantHolder.getRequiredTenantId());
        oauthAddMessage.setOauthType(KefuOauthType.WECOM_KEFU);
        oauthAddMessage.setOauthOpenId(oauthKefu.getKefuId());
        oauthAddMessage.setOauthNickname(oauthKefu.getKefuName());
        OauthAddProducer.oauthAdd(oauthAddMessage);
    }

}
