package com.swhd.oauth.service.oceanengine.api.dto.req.report;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.report.ReportRebateMaterialDownloadCreateTaskResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 返点明点化素材数据：创建下载任务
 *
 * <AUTHOR>
 * @see <a href="https://open.oceanengine.com/labels/7/docs/1800368282559564">文档</a>
 * @since 2024/7/10
 */
@Getter
@Setter
@Accessors(chain = true)
public class ReportRebateMaterialDownloadCreateTaskReq extends BaseOceanengineReq<ReportRebateMaterialDownloadCreateTaskResp> {

    /**
     * 代理商id
     */
    @SerializedName("agent_id")
    private Long agentId;

    /**
     * 年
     */
    private Integer year;

    /**
     * 季度
     */
    private Integer quarter;

    /**
     * 月度，可以传多个，以逗号分隔，如1,2,3
     */
    private String month;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/2/file/rebate/common_download/create_task/";
    }

}
