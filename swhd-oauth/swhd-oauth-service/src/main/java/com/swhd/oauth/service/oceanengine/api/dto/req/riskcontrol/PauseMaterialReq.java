package com.swhd.oauth.service.oceanengine.api.dto.req.riskcontrol;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.GetVideoMaterialResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.PauseMaterialResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取广告主信息
 *
 * <AUTHOR>
 * @see <a href="https://open.oceanengine.com/labels/7/docs/1755355780973568">文档</a>
 * @since 2024/09/05
 */
@Getter
@Setter
@Accessors(chain = true)
public class PauseMaterialReq extends BaseOceanengineReq<PauseMaterialResp> {

    /**
     * 广告账户ID
     */
    @SerializedName("advertiser_id")
    private Long advertiserId;

    /**
     * 广告ID
     */
    @SerializedName("promotion_id")
    private Long promotionId;

    /**
     * 批量更新广告启用状态，包含广告ID和操作状态，list长度限制1～10
     */
    private List<PauseMaterial> data;

    /**
     * 查询字段集合, 默认:查询所有。字段详见下方response字段定义
     * 允许值: id、name、role、status、address、reason、license_url、license_no、license_province、license_city、company、
     * brand、promotion_area、promotion_center_province、promotion_center_city、industry、create_time、note
     */
    private List<String> fields;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/material/status/update/";
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PauseMaterial {

        /**
         * 素材ID
         */
        @SerializedName("material_id")
        private Long materialId;

        /**
         * 允许值：DISABLE 暂停、ENABLE 启用
         */
        @SerializedName("opt_status")
        private String optStatus;

    }
}
