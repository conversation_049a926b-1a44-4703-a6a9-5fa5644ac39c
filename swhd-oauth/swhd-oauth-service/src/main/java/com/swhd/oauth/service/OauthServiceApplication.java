package com.swhd.oauth.service;

import com.swj.magiccube.MagiccubeApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@SpringBootApplication
@MapperScan("com.swhd.oauth.service.**.mapper.**")
@EnableFeignClients("com.swhd")
@EnableAsync
public class OauthServiceApplication {

    public static void main(String[] args) {
        MagiccubeApplication.run(OauthServiceApplication.class, args);
    }

}
