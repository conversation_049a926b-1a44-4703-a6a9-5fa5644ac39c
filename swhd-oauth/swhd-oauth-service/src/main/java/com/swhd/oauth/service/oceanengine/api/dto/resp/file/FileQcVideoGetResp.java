package com.swhd.oauth.service.oceanengine.api.dto.resp.file;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanenginePageInfo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
@Getter
@Setter
public class FileQcVideoGetResp {

    /**
     * 列表
     */
    private List<Item> list;

    /**
     * 分页信息
     */
    @SerializedName("page_info")
    private OceanenginePageInfo pageInfo;

    @Getter
    @Setter
    public static class Item {

        /**
         * 视频ID
         */
        private String id;

        /**
         * 视频大小
         */
        private Long size;

        /**
         * 视频宽度
         */
        private Integer width;

        /**
         * 视频高度
         */
        private Integer height;

        /**
         * 视频地址，仅限同主体进行素材预览查看，若非同主体会返回“素材所属主体与开发者主体不一致无法获取URL”
         * 链接仅做预览使用，预览链接有效期为1小时
         */
        private String url;

        /**
         * 视频md5值
         */
        private String signature;

        /**
         * 视频首帧截图，仅限同主体进行素材预览查看，若非同主体会返回“素材所属主体与开发者主体不一致无法获取URL”
         */
        @SerializedName("poster_url")
        private String posterUrl;

        /**
         * 码率，单位bps
         */
        @SerializedName("bit_rate")
        private String bitRate;

        /**
         * 视频时长
         */
        private BigDecimal duration;

        /**
         * 素材id，即多合一报表中的素材id，一个素材唯一对应一个素材id
         */
        @SerializedName("material_id")
        private Long materialId;

        /**
         * 素材的文件名
         */
        private String filename;

        /**
         * 素材类型，枚举值:
         * LARGE 大图
         * SMALL 小图
         * LARGE_VERTICAL 大图竖图
         * UNION_SPLASH 穿山甲开屏图片
         * VIDEO_LARGE 横版视频
         * VIDEO_VERTICAL 竖版视频
         */
        @SerializedName("image_mode")
        private String imageMode;

        /**
         * 素材标签
         */
        private List<String> tags;

        /**
         * 素材来源，枚举值：
         * ARTHUR 亚瑟共享素材
         * BP 巨量纵横共享素材
         * CREATIVE_CENTER 巨量创意PC共享素材
         * E_COMMERCE 本地上传
         * LIVE_HIGHLIGHT 直播剪辑素材
         * STAR 星图&即合共享素材
         * TADA tada共享素材
         * VIDEO_CAPTURE 易拍APP共享素材
         * AGENT 巨量方舟
         */
        private String source;

        /**
         * 创建时间
         */
        @SerializedName("create_time")
        private LocalDateTime createTime;

        /**
         * 视频格式
         */
        private String format;

        /**
         * 是否AI生成
         * true：AI生成
         * false：不是AI生成
         */
        @SerializedName("is_ai_create")
        private Boolean isAiCreate;

    }

}
