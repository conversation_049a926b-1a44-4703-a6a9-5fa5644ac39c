package com.swhd.oauth.service.oceanengine.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 转账资金类型枚举
 */
@Getter
@AllArgsConstructor
public enum TransferCapitalType {

    /**
     * 授信竞价
     */
    CREDIT_BIDDING("CREDIT_BIDDING", "授信竞价"),

    /**
     * 授信品牌
     */
    CREDIT_BRAND("CREDIT_BRAND", "授信品牌"),

    /**
     * 授信通用
     */
    CREDIT_GENERAL("CREDIT_GENERAL", "授信通用"),

    /**
     * 预付竞价
     */
    PREPAY_BIDDING("PREPAY_BIDDING", "预付竞价"),

    /**
     * 预付品牌
     */
    PREPAY_BRAND("PREPAY_BRAND", "预付品牌"),

    /**
     * 预付通用
     */
    PREPAY_GENERAL("PREPAY_GENERAL", "预付通用");

    private final String code;
    private final String desc;
}
