package com.swhd.oauth.service.oceanengine.api.util;

import com.swhd.oauth.service.oceanengine.api.constant.TransferCapitalType;
import com.swhd.oauth.service.oceanengine.api.constant.TransferDirection;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferCreateReq;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 转账请求构建工具类
 */
public class TransferRequestBuilder {

    private String bizRequestNo;
    private Long agentId;
    private Long accountId;
    private String remark;
    private final List<TransferCreateReq.TargetAccountDetail> targetAccountDetailList = new ArrayList<>();

    /**
     * 创建构建器
     */
    public static TransferRequestBuilder builder() {
        return new TransferRequestBuilder();
    }

    /**
     * 设置请求唯一编号（如果不设置，将自动生成UUID）
     */
    public TransferRequestBuilder bizRequestNo(String bizRequestNo) {
        this.bizRequestNo = bizRequestNo;
        return this;
    }

    /**
     * 设置代理商账户ID
     */
    public TransferRequestBuilder agentId(Long agentId) {
        this.agentId = agentId;
        return this;
    }

    /**
     * 设置锁定账户ID
     */
    public TransferRequestBuilder accountId(Long accountId) {
        this.accountId = accountId;
        return this;
    }

    /**
     * 设置备注
     */
    public TransferRequestBuilder remark(String remark) {
        this.remark = remark;
        return this;
    }

    /**
     * 添加目标账户转账
     *
     * @param targetAccountId 目标账户ID
     * @param capitalType     资金类型
     * @param transferAmount  转账金额（单位：分）
     * @param direction       转账方向
     */
    public TransferRequestBuilder addTransfer(Long targetAccountId, 
                                              TransferCapitalType capitalType, 
                                              Long transferAmount, 
                                              TransferDirection direction) {
        return addTransfer(targetAccountId, capitalType.getCode(), transferAmount, direction.getCode());
    }

    /**
     * 添加目标账户转账
     *
     * @param targetAccountId 目标账户ID
     * @param capitalType     资金类型
     * @param transferAmount  转账金额（单位：分）
     * @param direction       转账方向
     */
    public TransferRequestBuilder addTransfer(Long targetAccountId, 
                                              String capitalType, 
                                              Long transferAmount, 
                                              String direction) {
        // 查找是否已存在该目标账户
        TransferCreateReq.TargetAccountDetail existingTarget = targetAccountDetailList.stream()
                .filter(target -> target.getAccountId().equals(targetAccountId))
                .findFirst()
                .orElse(null);

        if (existingTarget == null) {
            // 创建新的目标账户
            existingTarget = new TransferCreateReq.TargetAccountDetail()
                    .setAccountId(targetAccountId)
                    .setTransferCapitalDetailList(new ArrayList<>());
            targetAccountDetailList.add(existingTarget);
        }

        // 添加转账资金详情
        TransferCreateReq.TransferCapitalDetail capitalDetail = new TransferCreateReq.TransferCapitalDetail()
                .setCapitalType(capitalType)
                .setTransferAmount(transferAmount)
                .setTransferDirection(direction);

        existingTarget.getTransferCapitalDetailList().add(capitalDetail);
        return this;
    }

    /**
     * 构建转账请求
     */
    public TransferCreateReq build() {
        if (agentId == null) {
            throw new IllegalArgumentException("代理商账户ID不能为空");
        }
        if (accountId == null) {
            throw new IllegalArgumentException("锁定账户ID不能为空");
        }
        if (targetAccountDetailList.isEmpty()) {
            throw new IllegalArgumentException("目标账户列表不能为空");
        }

        return new TransferCreateReq()
                .setBizRequestNo(bizRequestNo != null ? bizRequestNo : UUID.randomUUID().toString())
                .setAgentId(agentId)
                .setAccountId(accountId)
                .setTargetAccountDetailList(targetAccountDetailList)
                .setRemark(remark);
    }

    /**
     * 快速创建单个目标账户的转账请求
     *
     * @param agentId         代理商账户ID
     * @param accountId       锁定账户ID
     * @param targetAccountId 目标账户ID
     * @param capitalType     资金类型
     * @param transferAmount  转账金额（单位：分）
     * @param direction       转账方向
     * @param remark          备注
     */
    public static TransferCreateReq createSimpleTransfer(Long agentId,
                                                         Long accountId,
                                                         Long targetAccountId,
                                                         TransferCapitalType capitalType,
                                                         Long transferAmount,
                                                         TransferDirection direction,
                                                         String remark) {
        return builder()
                .agentId(agentId)
                .accountId(accountId)
                .addTransfer(targetAccountId, capitalType, transferAmount, direction)
                .remark(remark)
                .build();
    }
}
