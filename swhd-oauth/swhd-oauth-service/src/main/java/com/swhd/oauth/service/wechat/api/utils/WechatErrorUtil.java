package com.swhd.oauth.service.wechat.api.utils;

import com.swhd.magiccube.tool.Func;
import lombok.experimental.UtilityClass;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@UtilityClass
public class WechatErrorUtil {

    public String getErrorMsg(WxErrorException e, String errorMsgPre) {
        if (e.getError() == null  || Func.isEmpty(e.getError().getErrorMsg())) {
            return errorMsgPre;
        }
        return String.format("%s：%s", errorMsgPre, e.getError().getErrorMsg());
    }

}
