package com.swhd.oauth.service.oceanengine.api.dto.req.advertising;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.advertising.PromotionUpdateScheduleResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PromotionUpdateScheduleReq extends BaseOceanengineReq<PromotionUpdateScheduleResp> {

    /**
     * 广告主ID
     */
    @SerializedName("advertiser_id")
    private Long advertiserId;

    /**
     * 投放时段信息列表
     */
    private List<Data> data;

    @lombok.Data
    public static class Data {
        /**
         * 广告计划ID
         */
        @SerializedName("promotion_id")
        private Long promotionId;

        /**
         * 投放时段，格式为48*7位字符串，代表一周7天，每天48个半小时段的投放状态。0表示不投放，1表示投放
         */
        @SerializedName("schedule_time")
        private String scheduleTime;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/promotion/schedule_time/update/";
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Override
    public boolean isProxy() {
        return true;
    }
} 