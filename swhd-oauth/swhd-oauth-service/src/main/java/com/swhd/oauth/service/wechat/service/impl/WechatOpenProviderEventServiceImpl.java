package com.swhd.oauth.service.wechat.service.impl;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swhd.oauth.api.wechat.dto.message.WechatMsgEventMessage;
import com.swhd.oauth.api.wechat.dto.param.open.provider.WechatOpenProviderEventAuthParam;
import com.swhd.oauth.api.wechat.dto.param.open.provider.WechatOpenProviderEventMsgParam;
import com.swhd.oauth.service.wechat.entity.WechatOauthInfo;
import com.swhd.oauth.service.wechat.handler.WechatOpenAuthEventHandler;
import com.swhd.oauth.service.wechat.mq.producer.WechatEventProducer;
import com.swhd.oauth.service.wechat.service.WechatOauthInfoService;
import com.swhd.oauth.service.wechat.service.WechatOpenProviderEventService;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static me.chanjar.weixin.common.api.WxConsts.XmlMsgType.EVENT;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Slf4j
@Service
@AllArgsConstructor
public class WechatOpenProviderEventServiceImpl implements WechatOpenProviderEventService {

    private static final String SUCCESS_STR = "success";
    private static final List<String> ENTER_EVENT = List.of("subscribe", "SCAN");

    private final WxOpenService wxOpenService;

    private final WechatOauthInfoService wechatOauthInfoService;

    private final List<WechatOpenAuthEventHandler> authEventHandlerList;

    @Override
    public Rsp<String> auth(WechatOpenProviderEventAuthParam param) {
        // 消息校验
        if (!"aes".equalsIgnoreCase(param.getEncryptType()) ||
                !wxOpenService.getWxOpenComponentService().checkSignature(param.getTimestamp(), param.getNonce(), param.getSignature())) {
            log.error("认证消息校验失败， params: {}", JsonLogUtil.toJsonString(param));
            return RspHd.fail("微信第三方平台回调校验失败");
        }

        // 消息解密
        WxOpenXmlMessage message = WxOpenXmlMessage.fromEncryptedXml(param.getPostData(),
                wxOpenService.getWxOpenConfigStorage(), param.getTimestamp(), param.getNonce(), param.getMsgSignature());
        log.info("解密消息: {}", JsonLogUtil.toJsonString(message));

        // 消息处理
        String infoType = message.getInfoType();
        authEventHandlerList.stream()
                .filter(handler -> Objects.equals(infoType, handler.onEvent()))
                .forEach(handler -> handler.handle(message));

        return RspHd.data(SUCCESS_STR);
    }

    @Override
    public Rsp<String> msg(WechatOpenProviderEventMsgParam param) {
        // 消息校验
        if (!"aes".equalsIgnoreCase(param.getEncryptType()) ||
                !wxOpenService.getWxOpenComponentService().checkSignature(param.getTimestamp(), param.getNonce(), param.getSignature())) {
            log.warn("微信第三方平台回调校验失败，params: {}", JsonLogUtil.toJsonString(param));
            return RspHd.fail("微信第三方平台回调校验失败");
        }


        WxMpXmlMessage wxMessage = WxOpenXmlMessage.fromEncryptedMpXml(param.getReqBody(),
                wxOpenService.getWxOpenConfigStorage(), param.getTimestamp(), param.getNonce(), param.getMsgSignature());

        // 全网发布测试用例
        String event = wxMessage.getEvent();
        String appId = param.getAppId();
        try {
            String result = "";
            if (StringUtils.equalsAnyIgnoreCase(appId, "wxd101a85aa106f53e", "wx570bc396a51b8ff8")) {
                String msgType = wxMessage.getMsgType();
                if (StringUtils.equals(msgType, "text")) {
                    if (StringUtils.equals(wxMessage.getContent(), "TESTCOMPONENT_MSG_TYPE_TEXT")) {
                        result = WxOpenXmlMessage.wxMpOutXmlMessageToEncryptedXml(
                                WxMpXmlOutMessage.TEXT().content("TESTCOMPONENT_MSG_TYPE_TEXT_callback")
                                        .fromUser(wxMessage.getToUser())
                                        .toUser(wxMessage.getFromUser())
                                        .build(),
                                wxOpenService.getWxOpenConfigStorage()
                        );
                    } else if (StringUtils.startsWith(wxMessage.getContent(), "QUERY_AUTH_CODE:")) {
                        String msg = wxMessage.getContent().replace("QUERY_AUTH_CODE:", "") + "_from_api";
                        WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(msg).toUser(wxMessage.getFromUser()).build();
                        wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId).getKefuService().sendKefuMessage(kefuMessage);
                    }
                } else if (StringUtils.equals(wxMessage.getMsgType(), EVENT)) {
                    WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(event + "from_callback").toUser(wxMessage.getFromUser()).build();
                    wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId).getKefuService().sendKefuMessage(kefuMessage);
                }
                return RspHd.data(result);
            }
        } catch (WxErrorException e) {
            log.error("微信第三方平台处理全网发布测试用例失败", e);
            return RspHd.fail("处理全网发布测试用例失败");
        }

        if (Objects.equals(wxMessage.getEvent(), "TEMPLATESENDJOBFINISH")) {
            // 模板消息发送事件，丢弃
            return RspHd.data(SUCCESS_STR);
        }

        log.info("解密消息: {}", JsonLogUtil.toJsonString(wxMessage));

        WechatOauthInfo oauthInfo = TenantHolder.methodIgnoreTenant(() -> wechatOauthInfoService.getActivatedByAppId(appId));
        if (oauthInfo == null) {
            return Rsp.data(SUCCESS_STR);
        }

        WechatMsgEventMessage message = JsonUtil.convertValue(wxMessage, WechatMsgEventMessage.class);
        message.setTenantId(oauthInfo.getTenantId());
        message.setAppId(appId);
        message.setAppName(oauthInfo.getAppName());
        message.setAppLogo(oauthInfo.getLogo());
        if (Objects.equals(wxMessage.getMsgType(), "event") && ENTER_EVENT.contains(wxMessage.getEvent())) {
            // 关注和扫码的单独通道，防止被普通消息堵塞
            WechatEventProducer.sendMpEnterEvent(message);
        } else {
            WechatEventProducer.sendMpMsgEvent(message);
        }

        return Rsp.data(SUCCESS_STR);
    }

}
