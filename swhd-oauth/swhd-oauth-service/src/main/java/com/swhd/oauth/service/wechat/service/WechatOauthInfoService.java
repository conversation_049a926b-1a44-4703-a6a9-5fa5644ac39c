package com.swhd.oauth.service.wechat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.oauth.api.wechat.dto.param.oauth.info.WechatOauthInfoPageParam;
import com.swhd.oauth.service.wechat.entity.WechatOauthInfo;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;

import java.util.Collection;
import java.util.List;

/**
 * 微信授权信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
public interface WechatOauthInfoService extends IBaseHdService<WechatOauthInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<WechatOauthInfo> page(WechatOauthInfoPageParam param);

    /**
     * 根据微信应用appId获取
     *
     * @param appId 微信应用appId
     * @return WechatOauthInfo
     */
    WechatOauthInfo getByAppId(String appId);

    /**
     * 根据微信应用appId获取（启用状态的数据）
     *
     * @param appId 微信应用appId
     * @return WechatOauthInfo
     */
    WechatOauthInfo getActivatedByAppId(String appId);

    /**
     * 根据微信应用原始id获取
     *
     * @param userName 微信应用原始id
     * @return WechatOauthInfo
     */
    WechatOauthInfo getByUserName(String userName);

    /**
     * 根据微信应用原始id获取（启用状态的数据）
     *
     * @param userName 微信应用原始id
     * @return WechatOauthInfo
     */
    WechatOauthInfo getActivatedByUserName(String userName);

    /**
     * 根据微信应用appId列表获取
     *
     * @param appIds 微信应用appId列表
     * @return List
     */
    List<WechatOauthInfo> listByAppIds(Collection<String> appIds);

    /**
     * 根据类型获取
     *
     * @param appType 应用类型
     * @return List
     */
    List<WechatOauthInfo> listByType(Integer appType);

    /**
     * 扫码授权认证
     *
     * @param authorizerInfoResult 授权信息
     */
    void scanAuth(WxOpenAuthorizerInfoResult authorizerInfoResult);

    /**
     * 取消授权
     *
     * @param appId 微信应用appId
     */
    void unAuthorized(String appId);

}
