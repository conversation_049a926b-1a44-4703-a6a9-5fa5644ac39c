package com.swhd.oauth.service.wechat.api.client;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.wechat.dto.result.api.WechatApiMpUser;
import com.swhd.oauth.api.wechat.dto.result.api.WechatApiMpUserList;
import com.swhd.oauth.service.wechat.api.utils.WechatErrorUtil;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import me.chanjar.weixin.open.api.WxOpenMpService;
import me.chanjar.weixin.open.api.WxOpenService;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
@Slf4j
@Component
@AllArgsConstructor
public class WechatMpUserClient {

    private final WxOpenService wxOpenService;

    /**
     * <pre>
     * 获取用户基本信息
     * 详情请见: <a href="http://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140839&token=&lang=zh_CN">获取用户基本信息</a>
     * http请求方式: GET
     * 接口地址：https://api.weixin.qq.com/cgi-bin/user/info?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN
     * </pre>
     *
     * @param appId  微信appId
     * @param openId 用户的唯一标识
     * @return the wx mp user
     */
    public Rsp<WechatApiMpUser> userInfo(String appId, String openId) {
        WxOpenMpService wxOpenMpService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
        try {
            WxMpUser mpUser = wxOpenMpService.getUserService().userInfo(openId);
            return RspHd.data(Func.copy(mpUser, WechatApiMpUser.class));
        } catch (WxErrorException e) {
            log.warn("微信appId[{}]获取用户[{}]基本信息异常", appId, openId, e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), WechatErrorUtil.getErrorMsg(e, "获取用户基本信息失败"));
        }
    }

    /**
     * <pre>
     * 获取用户基本信息列表
     * 开发者可通过该接口来批量获取用户基本信息。最多支持一次拉取100条。
     * 详情请见: <a href="http://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140839&token=&lang=zh_CN">获取用户基本信息列表</a>
     * http请求方式: POST
     * 接口地址：https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=ACCESS_TOKEN
     * </pre>
     *
     * @param appId      微信appId
     * @param openIdList 用户openid列表
     * @return the list
     */
    public Rsp<List<WechatApiMpUser>> userInfoList(String appId, List<String> openIdList) {
        WxOpenMpService wxOpenMpService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
        try {
            List<WxMpUser> userInfoList = wxOpenMpService.getUserService().userInfoList(openIdList);
            return RspHd.data(Func.copy(userInfoList, WechatApiMpUser.class));
        } catch (WxErrorException e) {
            log.warn("微信appId[{}]获取用户[{}]基本信息异常", appId, String.join(",", openIdList), e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), WechatErrorUtil.getErrorMsg(e, "获取用户基本信息失败"));
        }
    }


    /**
     * <pre>
     * 获取用户列表
     * 公众号可通过本接口来获取帐号的关注者列表，
     * 关注者列表由一串OpenID（加密后的微信号，每个用户对每个公众号的OpenID是唯一的）组成。
     * 一次拉取调用最多拉取10000个关注者的OpenID，可以通过多次拉取的方式来满足需求。
     * 详情请见: <a href="http://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140840&token=&lang=zh_CN">获取用户列表</a>
     * http请求方式: GET（请使用https协议）
     * 接口地址：https://api.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&next_openid=NEXT_OPENID
     * </pre>
     *
     * @param appId      微信appId
     * @param nextOpenId 可选，第一个拉取的OPENID，null为从头开始拉取
     * @return the wx mp user list
     */
    public Rsp<WechatApiMpUserList> userList(String appId, String nextOpenId) {
        WxOpenMpService wxOpenMpService = wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId);
        try {
            WxMpUserList userList = wxOpenMpService.getUserService().userList(nextOpenId);
            WechatApiMpUserList apiMpUserList = new WechatApiMpUserList();
            apiMpUserList.setCount(userList.getCount());
            apiMpUserList.setTotal(userList.getTotal());
            apiMpUserList.setOpenIds(userList.getOpenids());
            apiMpUserList.setNextOpenId(userList.getNextOpenid());
            return RspHd.data(apiMpUserList);
        } catch (WxErrorException e) {
            log.warn("微信appId[{}]获取nextOpenId[{}]用户列表异常", appId, nextOpenId, e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), WechatErrorUtil.getErrorMsg(e, "获取用户基本信息失败"));
        }
    }

}
