package com.swhd.oauth.service.oceanengine.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferCreateReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferQueryReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferCreateResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferQueryResp;
import org.springframework.stereotype.Component;

/**
 * 巨量引擎转账相关接口
 */
@Component
public class OceanengineOpenCgTransferApiClient extends BaseOceanengineOpenApiClient {

    /**
     * 发起账户转账
     *
     * @see <a href="https://open.oceanengine.com/labels/34/docs/1789755060558916">文档</a>
     */
    public OceanengineRsp<TransferCreateResp> transferCreate(TransferCreateReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "transferCreate", "发起账户转账", apiProperties.getRateLimiter().getTransferCreate());
    }

    /**
     * 查询账户转账
     *
     * @see <a href="https://open.oceanengine.com/labels/34/docs/1789755120706634">文档</a>
     */
    public OceanengineRsp<TransferQueryResp> transferQuery(TransferQueryReq request) {
        return rateLimiter(() -> execute(request, new TypeToken<>() {
        }), "transferQuery", "查询账户转账", apiProperties.getRateLimiter().getTransferQuery());
    }
}
