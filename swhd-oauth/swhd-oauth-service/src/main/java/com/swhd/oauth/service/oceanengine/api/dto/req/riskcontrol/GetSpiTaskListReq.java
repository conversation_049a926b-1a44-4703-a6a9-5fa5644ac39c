package com.swhd.oauth.service.oceanengine.api.dto.req.riskcontrol;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.GetNewMaterialStatusResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.riskcontrol.GetSpiTaskListResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineRiskControlGetSpiTaskListParam")
public class GetSpiTaskListReq  extends BaseOceanengineReq<GetSpiTaskListResp> {


    @Schema(description = "代理商ID")
    @SerializedName("app_id")
    private Long appId;

    @Schema(description = "订阅任务ID")
    @SerializedName("subscribe_id")
    private Long subscribeId;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_BODY;
    }

    @Override
    public String getUrl() {
        return "https://ad.oceanengine.com/open_api/2/spi_task/get/";
    }
}
