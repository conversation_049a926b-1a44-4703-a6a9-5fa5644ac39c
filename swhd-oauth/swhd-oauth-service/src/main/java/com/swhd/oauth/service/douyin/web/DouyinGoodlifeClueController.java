package com.swhd.oauth.service.douyin.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.douyin.client.DouyinGoodlifeClueClient;
import com.swhd.oauth.api.douyin.dto.param.goodlife.clue.DouyinGoodlifeCluePageParam;
import com.swhd.oauth.api.douyin.dto.result.DouyinGoodlifeClueResult;
import com.swhd.oauth.service.douyin.entity.DouyinGoodlifeClue;
import com.swhd.oauth.service.douyin.service.DouyinGoodlifeClueService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@RestController
@AllArgsConstructor
@RequestMapping(DouyinGoodlifeClueClient.BASE_PATH)
public class DouyinGoodlifeClueController implements DouyinGoodlifeClueClient {

    private final DouyinGoodlifeClueService douyinGoodlifeClueService;

    @Override
    public Rsp<PageResult<DouyinGoodlifeClueResult>> page(DouyinGoodlifeCluePageParam param) {
        IPage<DouyinGoodlifeClue> iPage = douyinGoodlifeClueService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, DouyinGoodlifeClueResult.class));
    }

    @Override
    public Rsp<DouyinGoodlifeClueResult> getById(Long id) {
        DouyinGoodlifeClue entity = douyinGoodlifeClueService.getById(id);
        return RspHd.data(Func.copy(entity, DouyinGoodlifeClueResult.class));
    }

    @Override
    public Rsp<List<DouyinGoodlifeClueResult>> listByIds(Collection<Long> ids) {
        List<DouyinGoodlifeClue> list = douyinGoodlifeClueService.listByIds(ids);
        return RspHd.data(Func.copy(list, DouyinGoodlifeClueResult.class));
    }

}
