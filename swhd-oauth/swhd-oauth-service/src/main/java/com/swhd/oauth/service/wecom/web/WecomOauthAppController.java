package com.swhd.oauth.service.wecom.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.wecom.client.WecomOauthAppClient;
import com.swhd.oauth.api.wecom.dto.param.oauth.app.WecomOauthAppPageParam;
import com.swhd.oauth.api.wecom.dto.param.oauth.app.WecomOauthAppUpdateParam;
import com.swhd.oauth.api.wecom.dto.result.WecomOauthAppResult;
import com.swhd.oauth.service.wecom.entity.WecomOauthApp;
import com.swhd.oauth.service.wecom.service.WecomOauthAppService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-02
 */
@RestController
@AllArgsConstructor
@RequestMapping(WecomOauthAppClient.BASE_PATH)
public class WecomOauthAppController implements WecomOauthAppClient {

    private final WecomOauthAppService wecomOauthAppService;

    @Override
    public Rsp<PageResult<WecomOauthAppResult>> page(WecomOauthAppPageParam param) {
        IPage<WecomOauthApp> iPage = wecomOauthAppService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<WecomOauthAppResult> getById(Long id) {
        WecomOauthApp entity = wecomOauthAppService.getById(id);
        return RspHd.data(Func.copy(entity, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<WecomOauthAppResult> getByCorpId(String corpId) {
        WecomOauthApp entity = wecomOauthAppService.getByCorpId(corpId);
        return RspHd.data(Func.copy(entity, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<List<WecomOauthAppResult>> listByIds(Collection<Long> ids) {
        List<WecomOauthApp> list = wecomOauthAppService.listByIds(ids);
        return RspHd.data(Func.copy(list, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<List<WecomOauthAppResult>> listByCorpIds(Collection<String> corpIds) {
        List<WecomOauthApp> list = wecomOauthAppService.listByCorpIds(corpIds);
        return RspHd.data(Func.copy(list, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<List<WecomOauthAppResult>> listAll() {
        List<WecomOauthApp> list = wecomOauthAppService.list();
        return RspHd.data(Func.copy(list, WecomOauthAppResult.class));
    }

    @Override
    public Rsp<Void> update(WecomOauthAppUpdateParam param) {
        boolean result = wecomOauthAppService.updateById(Func.copy(param, WecomOauthApp.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        wecomOauthAppService.remove(ids);
        return RspHd.success();
    }

}
