package com.swhd.oauth.service.oceanengine.api.dto.resp.advertising;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PromotionUpdateScheduleResp {

    /**
     * 更新成功的广告计划ID列表
     */
    @SerializedName("promotion_ids")
    private List<Long> promotionIds;

    /**
     * 更新失败的广告计划列表
     */
    private List<ErrorItem> errors;

    @Data
    public static class ErrorItem {
        /**
         * 广告计划ID
         */
        @SerializedName("promotion_id")
        private Long promotionId;

        /**
         * 错误信息
         */
        @SerializedName("error_message")
        private String errorMessage;
    }
} 