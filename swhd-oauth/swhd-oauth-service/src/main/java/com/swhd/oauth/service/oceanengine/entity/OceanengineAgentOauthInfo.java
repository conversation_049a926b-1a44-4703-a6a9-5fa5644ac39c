package com.swhd.oauth.service.oceanengine.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 巨量引擎代理商授权信息表实体类
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("toauth_oceanengine_agent_oauth_info")
public class OceanengineAgentOauthInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "代理商公司名称")
    private String companyName;

    @Schema(description = "授权类型：1-巨量广告，2-巨量千川")
    private Integer oauthType;

    @Schema(description = "巨量引擎的授权应用id")
    private Long oauthAppId;

    @Schema(description = "巨量引擎的授权用户id")
    private Long oauthUserId;

    @Schema(description = "巨量引擎的授权用户邮箱（巨量引擎接口返回已经脱敏处理）")
    private String oauthUserEmail;

    @Schema(description = "巨量引擎的授权用户名")
    private String oauthUserName;

    @Schema(description = "刷新令牌")
    private String refreshToken;

    @Schema(description = "刷新令牌过期时间")
    private LocalDateTime refreshExpireTime;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

}
