package com.swhd.oauth.service.wechat.service.impl;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.web.log.utils.GatewayUtil;
import com.swhd.oauth.service.wechat.service.WechatOauthInfoService;
import com.swhd.oauth.service.wechat.service.WechatOpenProviderOauthPreService;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.url.UrlUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizationInfo;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Slf4j
@Service
@AllArgsConstructor
public class WechatOpenProviderOauthPreServiceImpl implements WechatOpenProviderOauthPreService {

    private final WechatOauthInfoService wechatOauthInfoService;

    private final WxOpenService wxOpenService;

    @Override
    public Rsp<String> createUrl(String redirectUri, Integer authType) {
        try {
            redirectUri = String.format("%s/swhd-oauth-web-tenant/wechatOpenProviderOauthPre/scanCallback?tenantId=%s&redirectUri=%s",
                    GatewayUtil.getGatewayHost(), TenantHolder.getRequiredTenantId(), UrlUtil.urlEncode(redirectUri));
            String strAuthType = Optional.ofNullable(authType).map(String::valueOf).orElse("3");
            String preAuthUrl = wxOpenService.getWxOpenComponentService().getPreAuthUrl(redirectUri, strAuthType, null);
            preAuthUrl = String.format("%s/swhd-oauth-web-tenant/wechatOpenProviderOauthPre/toOauth?data=%s",
                    GatewayUtil.getGatewayHost(), UrlUtil.urlEncode(preAuthUrl));
            return Rsp.data(preAuthUrl);
        } catch (WxErrorException e) {
            log.error("获取预授权码失败", e);
            return RspHd.fail("获取预授权码失败");
        }
    }

    @Override
    public Rsp<Void> scanCallback(String authorizationCode) {
        // 先根据授权码获取授权信息
        WxOpenQueryAuthResult authResult;
        try {
            authResult = wxOpenService.getWxOpenComponentService().getQueryAuth(authorizationCode);
        } catch (WxErrorException e) {
            log.error("获取微信第三方平台授权信息失败", e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取授权信息失败：" + e.getMessage());
        }
        WxOpenAuthorizationInfo authorizationInfo;
        if (Objects.isNull(authResult)
                || Objects.isNull(authorizationInfo = authResult.getAuthorizationInfo())
                || Objects.isNull(authorizationInfo.getAuthorizerAppid())) {
            log.error("微信第三方平台回调授权失败[信息不完整]，authorizationCode: {}", authorizationCode);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "微信第三方平台回调授权失败[信息不完整]");
        }

        // 再根据授权appId获取授权者信息
        WxOpenAuthorizerInfoResult authorizerInfo;
        try {
            authorizerInfo = wxOpenService.getWxOpenComponentService().getAuthorizerInfo(authorizationInfo.getAuthorizerAppid());
        } catch (WxErrorException e) {
            log.error("获取微信第三方平台授权者信息失败", e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取授权者信息失败：" + e.getMessage());
        }
        if (Objects.isNull(authorizerInfo) || Objects.isNull(authorizerInfo.getAuthorizationInfo())
                || Objects.isNull(authorizerInfo.getAuthorizerInfo())) {
            log.error("获取授权者信息失败[信息不完整]，authorizerAppid：{}", authorizationInfo.getAuthorizerAppid());
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取授权者信息失败[信息不完整]");
        }
        wechatOauthInfoService.scanAuth(authorizerInfo);
        return RspHd.success();
    }

}
