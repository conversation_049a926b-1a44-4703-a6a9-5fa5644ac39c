package com.swhd.oauth.service.oceanengine.web;

import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.oauth.api.oceanengine.client.OceanengineAgentApiAdvertiserClient;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineApiAdvertiserInfoResult;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineApiAdvertiserPublicInfoResult;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineApiStartInfoResult;
import com.swhd.oauth.service.oceanengine.api.client.OceanengineOpenAdvertiserApiClient;
import com.swhd.oauth.service.oceanengine.api.dto.req.advertiser.AdvertiserInfoReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.advertiser.AdvertiserPublicInfoReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.advertiser.StartInfoReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.advertiser.AdvertiserInfoResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.advertiser.AdvertiserPublicInfoResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.advertiser.StartInfoResp;
import com.swhd.oauth.service.oceanengine.api.utils.AgentTokenUtil;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/11
 */
@RestController
@AllArgsConstructor
@RequestMapping(OceanengineAgentApiAdvertiserClient.BASE_PATH)
public class OceanengineAgentApiAdvertiserController implements OceanengineAgentApiAdvertiserClient {

    private final OceanengineOpenAdvertiserApiClient oceanengineOpenAdvertiserApiClient;

    @Override
    public Rsp<OceanengineApiAdvertiserInfoResult> getByAdvertiserId(Long agentId, Long advertiserId) {
        AdvertiserInfoReq req = new AdvertiserInfoReq();
        req.setAdvertiserIds(List.of(advertiserId));
        OceanengineRsp<List<AdvertiserInfoResp>> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::advertiserInfo);
        return oceanengineRsp.toRsp(list -> {
            if (Func.isEmpty(list)) {
                return null;
            }
            return list.stream()
                    .filter(item -> Objects.equals(item.getId(), advertiserId))
                    .findAny()
                    .map(item -> Func.copy(item, OceanengineApiAdvertiserInfoResult.class))
                    .orElse(null);
        });
    }

    @Override
    public Rsp<List<OceanengineApiAdvertiserInfoResult>> listByAdvertiserIds(Long agentId, List<Long> advertiserIds) {
        AdvertiserInfoReq req = new AdvertiserInfoReq();
        req.setAdvertiserIds(advertiserIds);
        OceanengineRsp<List<AdvertiserInfoResp>> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::advertiserInfo);
        return oceanengineRsp.toRsp(list -> Func.copy(list, OceanengineApiAdvertiserInfoResult.class));
    }

    @Override
    public Rsp<OceanengineApiAdvertiserPublicInfoResult> getPublicByAdvertiserId(Long agentId, Long advertiserId) {
        AdvertiserPublicInfoReq req = new AdvertiserPublicInfoReq();
        req.setAdvertiserIds(List.of(advertiserId));
        OceanengineRsp<List<AdvertiserPublicInfoResp>> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::advertiserPublicInfo);
        return oceanengineRsp.toRsp(list -> {
            if (Func.isEmpty(list)) {
                return null;
            }
            return list.stream()
                    .filter(item -> Objects.equals(item.getId(), advertiserId))
                    .findAny()
                    .map(item -> Func.copy(item, OceanengineApiAdvertiserPublicInfoResult.class))
                    .orElse(null);
        });
    }

    @Override
    public Rsp<List<OceanengineApiAdvertiserPublicInfoResult>> listPublicByAdvertiserIds(Long agentId, List<Long> advertiserIds) {
        AdvertiserPublicInfoReq req = new AdvertiserPublicInfoReq();
        req.setAdvertiserIds(advertiserIds);
        OceanengineRsp<List<AdvertiserPublicInfoResp>> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::advertiserPublicInfo);
        return oceanengineRsp.toRsp(list -> Func.copy(list, OceanengineApiAdvertiserPublicInfoResult.class));
    }

    @Override
    public Rsp<OceanengineApiStartInfoResult> starInfo(Long agentId, Long starId) {
        StartInfoReq req = new StartInfoReq();
        req.setStarIds(List.of(starId));
        OceanengineRsp<StartInfoResp> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::startInfo);
        return oceanengineRsp.toRsp(data -> {
            if (data == null || Func.isEmpty(data.getInfoList())) {
                return null;
            }
            return data.getInfoList().stream()
                    .filter(item -> Objects.equals(item.getStartId(), starId))
                    .findAny()
                    .map(item -> JsonUtil.convertValue(item, OceanengineApiStartInfoResult.class))
                    .orElse(null);
        });
    }

    @Override
    public Rsp<List<OceanengineApiStartInfoResult>> listStarByStarIds(Long agentId, List<Long> starIds) {
        StartInfoReq req = new StartInfoReq();
        req.setStarIds(starIds);
        OceanengineRsp<StartInfoResp> oceanengineRsp = AgentTokenUtil.execute(agentId, req,
                oceanengineOpenAdvertiserApiClient::startInfo);
        return oceanengineRsp.toRsp(data -> {
            if (data == null || Func.isEmpty(data.getInfoList())) {
                return Collections.emptyList();
            }
            return JsonUtil.convertListValue(data.getInfoList(), OceanengineApiStartInfoResult.class);
        });
    }

}
