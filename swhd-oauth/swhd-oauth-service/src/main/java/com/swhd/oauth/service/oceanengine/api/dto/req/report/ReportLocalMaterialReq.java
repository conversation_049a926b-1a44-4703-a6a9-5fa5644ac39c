package com.swhd.oauth.service.oceanengine.api.dto.req.report;

import com.google.gson.annotations.SerializedName;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanenginePageReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.report.ReportLocalMaterialResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 获取线索推广素材数据(本地推)
 *
 * <AUTHOR>
 * @see <a href="https://open.oceanengine.com/labels/37/docs/1804001258474595">文档</a>
 * @since 2024/8/20
 */
@Getter
@Setter
@Accessors(chain = true)
public class ReportLocalMaterialReq extends BaseOceanenginePageReq<ReportLocalMaterialResp> {

    public static final List<String> DEF_METRICS = List.of("stat_cost", "show_cnt", "click_cnt", "ctr",
            "cpc_platform", "cpm_platform", "convert_cnt", "conversion_rate", "conversion_cost", "attribution_convert_cnt",
            "attribution_conversion_rate", "attribution_convert_cost", "form_cnt", "clue_pay_order_cnt", "clue_message_count",
            "phone_confirm_cnt", "phone_connect_cnt", "message_action_cnt", "intention_form_cnt", "intention_phone_cnt",
            "intention_message_clue_cnt", "attribution_form_cnt", "attribution_clue_pay_order_cnt", "attribution_clue_message_cnt",
            "attribution_phone_confirm_cnt", "attribution_phone_connect_cnt", "attribution_message_action_cnt",
            "attribution_intention_form_cnt", "attribution_intention_phone_cnt", "attribution_intention_message_clue_cnt",
            "dy_like", "dy_comment", "dy_share", "dy_collect", "total_play", "play_duration_3s", "play_duration_5s",
            "play_25_feed_break", "play_50_feed_break", "play_75_feed_break", "play_over", "play_duration_5s_show_cnt_rate",
            "play_over_rate", "dy_like_rate", "luban_live_enter_cnt", "live_watch_one_minute_count", "luban_live_comment_cnt",
            "luban_live_share_cnt", "dy_follow");

    /**
     * 本地推广告账户ID
     */
    @SerializedName("local_account_id")
    private Long localAccountId;

    /**
     * 时间粒度，允许值：
     * TIME_GRANULARITY_DAILY 天维度（默认值）
     * TIME_GRANULARITY_HOURLY 小时维度
     * TIME_GRANULARITY_TOTAL 汇总
     */
    @SerializedName("time_granularity")
    private String timeGranularity;

    /**
     * 开始时间，格式 2021-04-05，开始时间不得早于今日-180天
     */
    @SerializedName("start_date")
    private LocalDate startDate;

    /**
     * 查询结束日期，格式：yyyy-mm-dd
     * 当time_granularity = TIME_GRANULARITY_DAILY/TIME_GRANULARITY_TOTAL时，时间跨度不能超过365天
     * 当time_granularity = TIME_GRANULARITY_HOURLY时，时间跨度不能超过7天
     */
    @SerializedName("end_date")
    private LocalDate endDate;

    /**
     * 排序方式，允许值：
     * ASC 升序（默认值）
     * DESC 降序
     */
    @SerializedName("order_type")
    private String orderType;

    /**
     * 排序字段，允许值可参考应答返回数据指标
     */
    @SerializedName("order_field")
    private String orderField;

    /**
     * 指标集，允许值可参考应答返回数据指标
     */
    @LogMask(type = JsonMaskType.OMIT)
    @SerializedName("metrics")
    private List<String> metrics;

    /**
     * 过滤器
     */
    private Filtering filtering;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/local/report/material/get/";
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Filtering {

        /**
         * 素材ID
         */
        @SerializedName("material_ids")
        private List<Long> materialIds;

        /**
         * 广告类型，允许值：
         * GENERAL 通投广告
         * SEARCHING 线索广告
         */
        @SerializedName("campaign_type")
        private String campaignType;

        /**
         * 素材类型，允许值：
         * CASURAL 图文
         * VIDEO 视频
         */
        @SerializedName("material_type")
        private String materialType;

    }

}
