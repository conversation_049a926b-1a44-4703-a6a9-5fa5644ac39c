package com.swhd.oauth.service.xhs.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "toauth_xhs_oauth_info", autoResultMap = true)
public class XhsOauthInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "授权账号的user_id")
    private String userId;

    @Schema(description = "刷新令牌")
    private String refreshToken;

    @Schema(description = "刷新令牌过期时间")
    private LocalDateTime refreshExpireTime;

    @Schema(description = "企业名称")
    private String corporationName;

    @Schema(description = "授权账号类型，4：品牌，601：代理商")
    private Integer approvalRoleType;

    @Schema(description = "应用角色类型，1：品牌开发者，2：代理商开发者，3：服务商开发者，4:技术服务商")
    private Integer roleType;

    @Schema(description = "平台类型，1：聚光，2：蒲公英，3:私信三方，4:乘风")
    private Integer platformType;

    @TableField(typeHandler = JsonTypeHandler.class)
    @Schema(description = "授权接口范围，third_im：三方工具服务，three_im_leads_push：私信留资数据")
    private List<String> scope;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

}
