package com.swhd.oauth.service.oceanengine.api.dto.req.transfer;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferCreateResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 发起账户转账请求
 *
 * @see <a href="https://open.oceanengine.com/labels/34/docs/****************">文档</a>
 */
@Getter
@Setter
@Accessors(chain = true)
public class TransferCreateReq extends BaseOceanengineReq<TransferCreateResp> {

    /**
     * 请求唯一编号，建议用uuid，防止重复转账
     */
    @SerializedName("biz_request_no")
    private String bizRequestNo;

    /**
     * 代理商账户id，用于鉴权
     */
    @SerializedName("agent_id")
    private Long agentId;

    /**
     * 锁定账户id，1:N的1
     */
    @SerializedName("account_id")
    private Long accountId;

    /**
     * 目标账户列表，1:N的N，最多支持100个
     */
    @SerializedName("target_account_detail_list")
    private List<TargetAccountDetail> targetAccountDetailList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 转账方向
     * TRANSFER_IN：转入
     * TRANSFER_OUT：转出
     */
    @SerializedName("transfer_direction")
    private String transferDirection;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.POST;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/cg_transfer/create_transfer/";
    }

    /**
     * 目标账户详情
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class TargetAccountDetail {

        /**
         * 目标账户id
         */
        @SerializedName("account_id")
        private Long accountId;

        /**
         * 锁定账户与目标账户转账资金列表
         */
        @SerializedName("transfer_capital_detail_list")
        private List<TransferCapitalDetail> transferCapitalDetailList;
    }

    /**
     * 转账资金详情
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class TransferCapitalDetail {

        /**
         * 转账资金类型
         * CREDIT_BIDDING：授信竞价
         * CREDIT_BRAND：授信品牌
         * CREDIT_GENERAL：授信通用
         * PREPAY_BIDDING：预付竞价
         * PREPAY_BRAND：预付品牌
         * PREPAY_GENERAL：预付通用
         */
        @SerializedName("capital_type")
        private String capitalType;

        /**
         * 转账金额（单位：分）
         */
        @SerializedName("transfer_amount")
        private Long transferAmount;

    }
}
