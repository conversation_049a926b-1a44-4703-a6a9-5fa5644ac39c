package com.swhd.oauth.service.wecom.service;

import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.oauth.service.wecom.entity.WecomMsgKefuCursor;

/**
 * 企微客服消息指针表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
public interface WecomMsgKefuCursorService extends IBaseHdService<WecomMsgKefuCursor> {

    /**
     * 根据企业ID和客服ID
     *
     * @param corpId 企业ID
     * @param kefuId 客服ID
     * @return WecomMsgKefuCursor
     */
    WecomMsgKefuCursor getOne(String corpId, String kefuId);

    /**
     * 保存
     *
     * @param corpId     企业ID
     * @param kefuId     客服ID
     * @param nextCursor 调用时返回的next_cursor
     */
    void save(String corpId, String kefuId, String nextCursor);

}
