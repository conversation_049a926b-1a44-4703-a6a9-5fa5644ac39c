package com.swhd.oauth.service.oceanengine.api.dto.req.advertising;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanengineCursorReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.advertising.PromotionListResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 获取广告计划列表请求参数
 * 
 * @see <a href="https://api.oceanengine.com/open_api/v3.0/promotion/list/">巨量引擎开放平台文档</a>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PromotionListReq extends BaseOceanengineCursorReq<PromotionListResp> {

    /**
     * 广告主ID
     */
    @SerializedName("advertiser_id")
    private Long advertiserId;

    /**
     * 页面大小，默认值：20，取值范围：1-100
     */
    private Integer count;

    /**
     * 查询字段集合，允许的值：
     * project_id: 项目ID
     * promotion_id: 广告计划ID
     * promotion_name: 广告计划名称
     * status: 广告计划投放状态
     * opt_status: 操作状态
     * promotion_create_time: 广告计划创建时间
     * promotion_modify_time: 广告计划更新时间
     */
    private List<String> fields;

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/v3.0/promotion/list/";
    }

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }
} 