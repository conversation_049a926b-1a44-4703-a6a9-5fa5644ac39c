package com.swhd.oauth.service.wecom.service.impl;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.dto.result.ScrollResult;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.redis.utils.RedisCacheUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.WecomApiKefuMsgScrollParam;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.WecomApiKefuMsgSendParam;
import com.swhd.oauth.api.wecom.dto.result.api.WecomApiKefuExternalContactResult;
import com.swhd.oauth.api.wecom.dto.result.api.WecomApiKefuMsgResult;
import com.swhd.oauth.api.wecom.dto.result.api.WecomApiMediaUploadResult;
import com.swhd.oauth.service.wecom.api.dto.request.ApiWxCpKfMsgSendRequest;
import com.swhd.oauth.service.wecom.api.dto.resp.ApiWxCpKfMsgListResp;
import com.swhd.oauth.service.wecom.api.exception.WrapperException;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpGetService;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpKfService;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpService;
import com.swhd.oauth.service.wecom.service.WecomApiKefuMsgService;
import com.swhd.oauth.service.wecom.service.WecomApiMediaService;
import com.swhd.oauth.service.wecom.service.WecomOauthKefuService;
import com.swhd.oauth.service.wecom.utils.WecomApiUtil;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpKfService;
import me.chanjar.weixin.cp.bean.kf.WxCpKfCustomerBatchGetResp;
import me.chanjar.weixin.cp.bean.kf.WxCpKfMsgSendResp;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Slf4j
@Service
@AllArgsConstructor
public class WecomApiKefuMsgServiceImpl implements WecomApiKefuMsgService {

    private final ApiWxCpGetService apiWxCpGetService;

    private final WecomOauthKefuService wecomOauthKefuService;

    private final WecomApiMediaService wecomApiMediaService;

    @Override
    public Rsp<ScrollResult<WecomApiKefuMsgResult>> scrollMsg(WecomApiKefuMsgScrollParam param) {
        String token = Func.blankOrDefault(param.getToken(), null);
        String cursor = Func.blankOrDefault(param.getScrollId(), null);
        try {
            Long tenantId = TenantHolder.getRequiredTenantId();
            Rsp<String> corpIdRsp = wecomOauthKefuService.getCorpId(param.getCorpId(), param.getKefuId());
            if (RspHd.isFail(corpIdRsp)) {
                return RspHd.fail(corpIdRsp);
            }
            ApiWxCpService wxCpService = apiWxCpGetService.getWxCpService(corpIdRsp.getData());
            ApiWxCpKfService wxCpKfService = (ApiWxCpKfService) wxCpService.getKfService();
            ApiWxCpKfMsgListResp listResp = wxCpKfService.apiSyncMsg(
                    cursor, token, param.getSize(), null, param.getKefuId());
            ScrollResult<WecomApiKefuMsgResult> scrollResult = new ScrollResult<>();
            scrollResult.setSize(param.getSize());
            scrollResult.setHasMore(Objects.equals(listResp.getHasMore(), 1));
            scrollResult.setSearchAfter(listResp.getNextCursor());
            List<WecomApiKefuMsgResult> results = JsonUtil.convertListValue(listResp.getMsgList(), WecomApiKefuMsgResult.class);
            results.forEach(result -> {
                if (Func.isEmpty(result.getOpenKfid())) {
                    if (result.getEvent() != null && Func.isNotEmpty(result.getEvent().getOpenKfid())) {
                        result.setOpenKfid(result.getEvent().getOpenKfid());
                    } else {
                        result.setOpenKfid(param.getKefuId());
                    }
                }
                if (Func.isEmpty(result.getExternalUserId())
                        && result.getEvent() != null
                        && Func.isNotEmpty(result.getEvent().getExternalUserId())) {
                    result.setExternalUserId(result.getEvent().getExternalUserId());
                }
                result.setTenantId(tenantId);
            });
            scrollResult.setRecords(results);
            return Rsp.data(scrollResult);
        } catch (Exception e) {
            if (e instanceof WxErrorException wxError
                    && wxError.getError() != null
                    && Objects.equals(wxError.getError().getErrorCode(), 50100)
                    && cursor != null) {
                // 无效游标
                param.setScrollId(null);
                return scrollMsg(param);
            }
            WecomApiUtil.log(log, "读取客服消息异常", e);
            return WecomApiUtil.exception2rsp(e, "读取客服消息失败");
        }
    }

    @Override
    public Rsp<WecomApiKefuExternalContactResult> getCustomerInfo(String corpId, String externalUserid) {
        try {
            String cacheKey = String.format("oauth:wecom:api:kefu:msg:getCustomerInfo:%s:%s", corpId, externalUserid);
            WecomApiKefuExternalContactResult result = RedisCacheUtil.get(() -> {
                try {
                    ApiWxCpService wxCpService = apiWxCpGetService.getWxCpService(corpId);
                    WxCpKfService wxCpKfService = wxCpService.getKfService();
                    WxCpKfCustomerBatchGetResp resp = wxCpKfService.customerBatchGet(List.of(externalUserid));
                    if (Func.isEmpty(resp.getCustomerList())) {
                        return null;
                    }
                    return Func.copy(resp.getCustomerList().getFirst(), WecomApiKefuExternalContactResult.class);
                } catch (Exception e) {
                    throw new WrapperException(e);
                }
            }, cacheKey, Duration.ofMinutes(30), WecomApiKefuExternalContactResult.class);
            return Rsp.data(result);
        } catch (Exception e) {
            WecomApiUtil.log(log, "获取客服客户基础信息异常", e);
            return WecomApiUtil.exception2rsp(e, "获取客服客户基础信息失败");
        }
    }

    @Override
    public Rsp<String> sendMsg(WecomApiKefuMsgSendParam param) {
        try {
            Rsp<String> corpIdRsp = wecomOauthKefuService.getCorpId(param.getCorpId(), param.getKefuId());
            if (RspHd.isFail(corpIdRsp)) {
                return RspHd.fail(corpIdRsp);
            }
            String corpId = corpIdRsp.getData();
            // 文件url转素材ID
            if (param.getImage() != null && Func.isNotBlank(param.getImage().getMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.IMAGE, param.getImage().getMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getImage().setMediaId(uploadRsp.getData().getMediaId());
            }
            if (param.getVoice() != null && Func.isNotBlank(param.getVoice().getMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.VOICE, param.getVoice().getMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getVoice().setMediaId(uploadRsp.getData().getMediaId());
            }
            if (param.getVideo() != null && Func.isNotBlank(param.getVideo().getMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.VIDEO, param.getVideo().getMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getVideo().setMediaId(uploadRsp.getData().getMediaId());
            }
            if (param.getFile() != null && Func.isNotBlank(param.getFile().getMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.FILE, param.getFile().getMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getFile().setMediaId(uploadRsp.getData().getMediaId());
            }
            if (param.getLink() != null && Func.isNotBlank(param.getLink().getThumbMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.IMAGE, param.getLink().getThumbMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getLink().setThumbMediaId(uploadRsp.getData().getMediaId());
            }
            if (param.getMiniProgram() != null && Func.isNotBlank(param.getMiniProgram().getThumbMediaUrl())) {
                Rsp<WecomApiMediaUploadResult> uploadRsp = wecomApiMediaService.tempUpload(
                        corpId, WxConsts.MediaFileType.IMAGE, param.getMiniProgram().getThumbMediaUrl());
                if (RspHd.isFail(uploadRsp)) {
                    return RspHd.fail(uploadRsp);
                }
                param.getMiniProgram().setThumbMediaId(uploadRsp.getData().getMediaId());
            }
            // 发送消息
            ApiWxCpService wxCpService = apiWxCpGetService.getWxCpService(corpId);
            ApiWxCpKfMsgSendRequest sendRequest = JsonUtil.convertValue(param, ApiWxCpKfMsgSendRequest.class);
            sendRequest.setOpenKfid(param.getKefuId());
            WxCpKfMsgSendResp resp;
            if (Func.isNotEmpty(param.getCode())) {
                resp = wxCpService.getKfService().sendMsgOnEvent(sendRequest);
            } else {
                resp = wxCpService.getKfService().sendMsg(sendRequest);
            }
            return RspHd.data(resp.getMsgId());
        } catch (Exception e) {
            WecomApiUtil.log(log, "获取客服列表异常", e);
            return WecomApiUtil.exception2rsp(e, "获取客服列表失败");
        }
    }

}
