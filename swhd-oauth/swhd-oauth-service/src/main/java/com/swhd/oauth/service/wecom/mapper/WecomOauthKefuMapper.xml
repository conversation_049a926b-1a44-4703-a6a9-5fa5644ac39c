<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.oauth.service.wecom.mapper.WecomOauthKefuMapper">

    <select id="page" resultType="com.swhd.oauth.api.wecom.dto.result.WecomOauthKefuResult">
        select wpi.*
        from toauth_wecom_oauth_kefu wok join toauth_wecom_oauth_app woa
             on wok.corp_id = woa.corp_id and woa.is_delete = 0
        where wok.is_delete = 0
        <if test="param.corpId != null and param.corpId != ''">
            and woa.corp_id = #{param.corpId}
        </if>
        <if test="param.corpName != null and param.corpName != ''">
            and woa.corp_name like concat('%', #{param.corpName}, '%')
        </if>
        <if test="param.kefuId != null and param.kefuId != ''">
            and wok.kefu_id = #{param.kefuId}
        </if>
        <if test="param.kefuName != null and param.kefuName != ''">
            and wok.kefu_name like concat('%', #{param.kefuName}, '%')
        </if>
        <if test="param.state != null">
            and wok.state = #{param.state}
        </if>
        order by wok.create_time desc, wpi.id desc
    </select>

</mapper>
