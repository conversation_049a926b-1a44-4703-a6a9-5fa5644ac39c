package com.swhd.oauth.service.douyin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.oauth.api.douyin.dto.param.oauth.info.DouyinOauthInfoPageParam;
import com.swhd.oauth.service.douyin.entity.DouyinOauthInfo;

import java.util.Collection;
import java.util.List;

/**
 * 抖音授权信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface DouyinOauthInfoService extends IBaseHdService<DouyinOauthInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<DouyinOauthInfo> page(DouyinOauthInfoPageParam param);

    /**
     * 根据抖音openId获取
     *
     * @param openId 抖音openId
     * @return DouyinOauthInfo
     */
    DouyinOauthInfo getByOpenId(String openId);

    /**
     * 根据抖音openId列表获取
     *
     * @param openIds 抖音openId列表
     * @return List
     */
    List<DouyinOauthInfo> listByOpenIds(Collection<String> openIds);

    /**
     * 根据抖音openId禁用授权
     *
     * @param id 主键id
     * @return boolean
     */
    boolean disableById(Long id);

    /**
     * 根据抖音openId禁用授权
     *
     * @param openId 抖音openId
     * @return boolean
     */
    boolean disableByOpenId(String openId);

    /**
     * 保存授权信息
     *
     * @param oauthInfo 授权信息
     */
    void saveOauthInfo(DouyinOauthInfo oauthInfo);

}
