package com.swhd.oauth.service.oceanengine.service.impl;

import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferCreateParam;
import com.swhd.oauth.api.oceanengine.dto.param.transfer.OceanengineTransferQueryParam;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferCreateResult;
import com.swhd.oauth.api.oceanengine.dto.result.transfer.OceanengineTransferQueryResult;
import com.swhd.oauth.service.oceanengine.api.client.OceanengineOpenCgTransferApiClient;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferCreateReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferQueryReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferCreateResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferQueryResp;
import com.swhd.oauth.service.oceanengine.service.OceanengineTransferApiService;
import com.swhd.oauth.service.oceanengine.api.utils.AgentTokenUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.core.util.Func;
import com.swj.magiccube.core.util.RspHd;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 巨量引擎转账API服务实现
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Slf4j
@Service
@AllArgsConstructor
public class OceanengineTransferApiServiceImpl implements OceanengineTransferApiService {

    private final OceanengineOpenCgTransferApiClient oceanengineOpenCgTransferApiClient;

    @Override
    public Rsp<OceanengineTransferCreateResult> transferCreate(OceanengineTransferCreateParam param) {
        try {
            // 构建转账请求
            TransferCreateReq request = buildTransferCreateReq(param);

            // 调用巨量引擎转账接口
            OceanengineRsp<TransferCreateResp> oceanengineRsp = AgentTokenUtil.execute(
                    param.getAgentId(),
                    request,
                    oceanengineOpenCgTransferApiClient::transferCreate
            );

            if (!oceanengineRsp.isSuccess()) {
                return oceanengineRsp.toRspFail();
            }

            // 转换响应结果
            OceanengineTransferCreateResult result = Func.copy(oceanengineRsp.getData(), OceanengineTransferCreateResult.class);
            return RspHd.data(result);

        } catch (Exception e) {
            log.error("发起账户转账异常，参数：{}", param, e);
            return RspHd.fail("发起账户转账失败：" + e.getMessage());
        }
    }

    @Override
    public Rsp<OceanengineTransferQueryResult> transferQuery(OceanengineTransferQueryParam param) {
        try {
            // 构建查询请求
            TransferQueryReq request = buildTransferQueryReq(param);

            // 调用巨量引擎查询接口
            OceanengineRsp<TransferQueryResp> oceanengineRsp = AgentTokenUtil.execute(
                    param.getAgentId(),
                    request,
                    oceanengineOpenCgTransferApiClient::transferQuery
            );

            if (!oceanengineRsp.isSuccess()) {
                return oceanengineRsp.toRspFail();
            }

            // 转换响应结果
            OceanengineTransferQueryResult result = convertTransferQueryResult(oceanengineRsp.getData());
            return RspHd.data(result);

        } catch (Exception e) {
            log.error("查询账户转账异常，参数：{}", param, e);
            return RspHd.fail("查询账户转账失败：" + e.getMessage());
        }
    }

    /**
     * 构建转账创建请求
     */
    private TransferCreateReq buildTransferCreateReq(OceanengineTransferCreateParam param) {
        TransferCreateReq request = new TransferCreateReq()
                .setBizRequestNo(param.getBizRequestNo())
                .setAgentId(param.getAgentId())
                .setAccountId(param.getAccountId())
                .setRemark(param.getRemark())
                .setTransferDirection(param.getTransferDirection());

        // 转换目标账户列表
        List<TransferCreateReq.TargetAccountDetail> targetAccountDetailList = param.getTargetAccountDetailList()
                .stream()
                .map(this::convertTargetAccountDetail)
                .collect(Collectors.toList());

        request.setTargetAccountDetailList(targetAccountDetailList);
        return request;
    }

    /**
     * 转换目标账户详情
     */
    private TransferCreateReq.TargetAccountDetail convertTargetAccountDetail(OceanengineTransferCreateParam.TargetAccountDetail param) {
        TransferCreateReq.TargetAccountDetail detail = new TransferCreateReq.TargetAccountDetail()
                .setAccountId(param.getAccountId());

        // 转换转账资金详情列表
        List<TransferCreateReq.TransferCapitalDetail> transferCapitalDetailList = param.getTransferCapitalDetailList()
                .stream()
                .map(this::convertTransferCapitalDetail)
                .collect(Collectors.toList());

        detail.setTransferCapitalDetailList(transferCapitalDetailList);
        return detail;
    }

    /**
     * 转换转账资金详情
     */
    private TransferCreateReq.TransferCapitalDetail convertTransferCapitalDetail(OceanengineTransferCreateParam.TransferCapitalDetail param) {
        return new TransferCreateReq.TransferCapitalDetail()
                .setCapitalType(param.getCapitalType())
                .setTransferAmount(param.getTransferAmount());
    }

    /**
     * 构建转账查询请求
     */
    private TransferQueryReq buildTransferQueryReq(OceanengineTransferQueryParam param) {
        return new TransferQueryReq()
                .setBizRequestNo(param.getBizRequestNo())
                .setAgentId(param.getAgentId())
                .setTransferBizRequestNo(param.getTransferBizRequestNo())
                .setTransferSerial(param.getTransferSerial());
    }

    /**
     * 转换查询响应结果
     */
    private OceanengineTransferQueryResult convertTransferQueryResult(TransferQueryResp resp) {
        OceanengineTransferQueryResult result = Func.copy(resp, OceanengineTransferQueryResult.class);

        // 转换目标记录列表
        if (resp.getTransferTargetRecordList() != null) {
            List<OceanengineTransferQueryResult.TargetRecord> targetRecordList = resp.getTransferTargetRecordList()
                    .stream()
                    .map(this::convertTargetRecord)
                    .collect(Collectors.toList());
            result.setTransferTargetRecordList(targetRecordList);
        }

        return result;
    }

    /**
     * 转换目标记录
     */
    private OceanengineTransferQueryResult.TargetRecord convertTargetRecord(TransferQueryResp.TargetRecord resp) {
        OceanengineTransferQueryResult.TargetRecord record = Func.copy(resp, OceanengineTransferQueryResult.TargetRecord.class);

        // 转换资金记录列表
        if (resp.getTransferCapitalRecordList() != null) {
            List<OceanengineTransferQueryResult.CapitalRecord> capitalRecordList = resp.getTransferCapitalRecordList()
                    .stream()
                    .map(capitalResp -> Func.copy(capitalResp, OceanengineTransferQueryResult.CapitalRecord.class))
                    .collect(Collectors.toList());
            record.setTransferCapitalRecordList(capitalRecordList);
        }

        return record;
    }
}
