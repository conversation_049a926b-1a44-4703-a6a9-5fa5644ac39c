package com.swhd.oauth.service.xhs.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("toauth_xhs_oauth_account")
public class XhsOauthAccount extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "授权账号的user_id")
    private String userId;
    
    @Schema(description = "KOS的用户id（员工）")
    private String kosUserId;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "帐号类型：1-主帐号，2-子帐号")
    private Integer accountType;

    @Schema(description = "绑定状态：0-未绑定，1-已绑定")
    private Integer state;

}
