package com.swhd.oauth.service.douyin.api.utils;

import com.swhd.magiccube.redis.utils.CacheConfig;
import com.swhd.magiccube.redis.utils.RedisCacheUtil;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.service.douyin.api.client.DouyinOauthApiClient;
import com.swhd.oauth.service.douyin.api.dto.req.BaseDouyinOauthReq;
import com.swhd.oauth.service.douyin.api.dto.req.oauth.DouyinOauthRefreshTokenReq;
import com.swhd.oauth.service.douyin.api.dto.resp.BaseDouyinResp;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRspExtra;
import com.swhd.oauth.service.douyin.api.dto.resp.oauth.DouyinOauthRefreshTokenResp;
import com.swhd.oauth.service.douyin.api.properties.DouyinApiProperties;
import com.swhd.oauth.service.douyin.entity.DouyinOauthInfo;
import com.swhd.oauth.service.douyin.service.DouyinOauthInfoService;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.util.SpringUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/3/25
 */
@Slf4j
@UtilityClass
public class AccessTokenUtil {

    private static final CacheConfig CACHE_CONFIG = CacheConfig.NO_TENANT;

    private static DouyinOauthApiClient CLIENT;

    private static DouyinOauthInfoService SERVICE;

    private static DouyinApiProperties API_PROPERTIES;

    public static DouyinOauthApiClient getClient() {
        if (CLIENT == null) {
            CLIENT = SpringUtil.getBean(DouyinOauthApiClient.class);
        }
        return CLIENT;
    }

    public static DouyinOauthInfoService getService() {
        if (SERVICE == null) {
            SERVICE = SpringUtil.getBean(DouyinOauthInfoService.class);
        }
        return SERVICE;
    }

    public static DouyinApiProperties getApiProperties() {
        if (API_PROPERTIES == null) {
            API_PROPERTIES = SpringUtil.getBean(DouyinApiProperties.class);
        }
        return API_PROPERTIES;
    }

    /**
     * 获取access_token
     *
     * @param openId 抖音openId
     * @return access_token
     */
    public String getAccessToken(String openId) {
        return RedisCacheUtil.get(() -> {
            DouyinOauthInfo oauthInfo = getService().getByOpenId(openId);
            if (oauthInfo == null || !Objects.equals(oauthInfo.getState(), Constant.IntNum.ONE)) {
                return null;
            }
            DouyinOauthRefreshTokenReq refreshTokenReq = new DouyinOauthRefreshTokenReq()
                    .setClientKey(getApiProperties().getClientKey())
                    .setRefreshToken(oauthInfo.getRefreshToken());
            DouyinRsp<DouyinOauthRefreshTokenResp> rsp = getClient().refreshToken(refreshTokenReq);
            if (rsp.isFail()) {
                log.error("【抖音】获取Access Token失败, code:[{}], message:[{}]", rsp.getErrorCode(), rsp.getDescription());
            }
            return Optional.ofNullable(rsp.getData()).map(DouyinOauthRefreshTokenResp::getAccessToken).orElse(null);
        }, getCacheKey(openId), getApiProperties().getClientTokenCacheDuration(), String.class, CACHE_CONFIG);
    }

    /**
     * 租户的代理商access token缓存到redis
     *
     * @param openId         抖音openId
     * @param accessToken    access token
     * @param expireDuration 过期时长
     */
    public void setAccessToken(String openId, String accessToken, Duration expireDuration) {
        RedisUtil.set(RedisCacheUtil.getCacheKey(getCacheKey(openId), CACHE_CONFIG), accessToken, expireDuration);
    }

    /**
     * 执行，返回auth失败会删除token，重新获取token执行方法
     *
     * @param req      请求参数
     * @param supplier 执行方法
     * @param <Req>    请求参数泛型
     * @param <Resp>   返回结果泛型
     * @return OceanengineRsp<T>
     */
    public <Req extends BaseDouyinOauthReq<Req>, Resp extends BaseDouyinResp> DouyinRsp<Resp> execute(
            Req req, Supplier<DouyinRsp<Resp>> supplier) {
        String accessToken = getAccessToken(req.getOpenId());
        if (Func.isEmpty(accessToken)) {
            return tokenEmptyError();
        }
        req.setAccessToken(accessToken);
        DouyinRsp<Resp> rsp = supplier.get();
        if (rsp.isSuccess()) {
            return rsp;
        }
        if (!(Objects.equals(rsp.getErrorCode(), 10008)
                || Objects.equals(rsp.getErrorCode(), 2190002)
                || Objects.equals(rsp.getErrorCode(), 2190008)
                || Objects.equals(rsp.getErrorCode(), 28001003)
                || Objects.equals(rsp.getErrorCode(), 28001008))) {
            return rsp;
        }
        // 删除缓存，旧值相同才删除
        RedisUtil.tryDelete(RedisCacheUtil.getCacheKey(getCacheKey(req.getOpenId()), CACHE_CONFIG),
                Func.emptyOrDefault(accessToken, Constant.Str.EMPTY));
        // 重新获取token
        accessToken = getAccessToken(req.getOpenId());
        if (Func.isEmpty(accessToken)) {
            return tokenEmptyError();
        }
        req.setAccessToken(accessToken);
        return supplier.get();
    }

    private String getCacheKey(String openId) {
        return String.format("oauth:douyin:accessToken:%s:%s", getApiProperties().getClientKey(), openId);
    }

    private <Resp extends BaseDouyinResp> DouyinRsp<Resp> tokenEmptyError() {
        DouyinRsp<Resp> rsp = new DouyinRsp<>();
        DouyinRspExtra rspExtra = new DouyinRspExtra();
        rspExtra.setDescription("无法获取access_token");
        rsp.setExtra(rspExtra);
        return rsp;
    }

}
