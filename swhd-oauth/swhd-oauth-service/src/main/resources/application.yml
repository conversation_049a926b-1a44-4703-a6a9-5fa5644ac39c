server:
  port: 9360

spring:
  application:
    name: swhd-oauth-service
  datasource:
    url: ${magiccube.db.swhd.url}
    username: ${magiccube.db.swhd.username}
    hikari:
      rsa-password: ${magiccube.db.swhd.password}
      rsa-public-key: ${magiccube.db.swhd.public-key}
  cloud:
    nacos:
      # 默认test环境
      server-addr: 10.101.23.45:8848,10.101.23.243:8848,10.101.23.12:8848
    stream:
      swhd-binder: rabbitSwj
      bindings:
        # 生产者：授权新增
        oauthOauthAdd-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-oauth-add
        # 生产者：抖音用户进入私信会话页事件
        oauthDouyinMessagePushEnterMsgEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-douyin-message-push-enter-msg-event
        # 生产者：抖音用户私信消息事件
        oauthDouyinMessagePushUserMsgEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-douyin-message-push-user-msg-event
        # 生产者：小红书用户消息事件
        oauthXhsMessagePushUserMsgEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-xhs-message-push-user-msg-event
        # 生产者：微信公众号进入（关注、扫码）事件
        oauthWechatMpEnterEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wechat-mp-enter-event
        # 生产者：微信公众号消息事件
        oauthWechatMpMsgEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wechat-mp-msg-event
        # 生产者：企微客服进入事件
        oauthWecomKefuEnterEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wecom-kefu-enter-event
        # 生产者：企微客服消息事件
        oauthWecomKefuMsgEvent-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wecom-kefu-msg-event
        # 生产者：企微客服同步消息
        oauthWecomKefuSyncMsg-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wecom-kefu-sync-msg
        # 消费者：企微客服同步消息
        oauthWecomKefuSyncMsg-in-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-wecom-kefu-sync-msg
          group: swhd-oauth-service
          consumer:
            concurrency: 5
        # 生产者：抖音生活服务线索同步
        oauthDouyinGoodlifeClueSync-out-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-douyin-goodlife-clue-sync
        # 消费者：抖音生活服务线索同步
        oauthDouyinGoodlifeClueSync-in-0:
          binder: ${spring.cloud.stream.swhd-binder}
          destination: swhd-oauth-douyin-goodlife-clue-sync
          group: swhd-oauth-service
          consumer:
            concurrency: 5

magiccube:
  job:
    executor:
      appname: ${spring.application.name}
      title: ${spring.application.name}
      port: 1${server.port:8080}
  # 以下两个核心配置请务必根据 https://wiki.3weijia.com/pages/viewpage.action?pageId=33001928 进行调整
  application:
    # 应用主要维护人信息（必须修改）
    owner:
      - 美家平台事业部_钟廷员_13763340378
    # 应用对应业务码（必须修改）
    status-code: 11360
  mybatis:
    tenant:
      enabled: true
      ignore-table-list:
        - toauth_douyin_image
        - toauth_wecom_msg_kefu_cursor

mybatis-plus:
  mapper-locations: classpath:com/swhd/oauth/service/**/mapper/*Mapper.xml
