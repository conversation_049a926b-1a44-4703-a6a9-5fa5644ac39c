package com.swhd.oauth.service.oceanengine.api.client;

import com.swhd.oauth.service.oceanengine.api.constant.TransferCapitalType;
import com.swhd.oauth.service.oceanengine.api.constant.TransferDirection;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferCreateReq;
import com.swhd.oauth.service.oceanengine.api.dto.req.transfer.TransferQueryReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineRsp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferCreateResp;
import com.swhd.oauth.service.oceanengine.api.dto.resp.transfer.TransferQueryResp;
import com.swhd.oauth.service.oceanengine.api.util.TransferRequestBuilder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 转账API客户端测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class OceanengineOpenTransferApiClientTest {

    @Resource
    private OceanengineOpenCgTransferApiClient transferApiClient;

    @Test
    public void testTransferCreate() {
        // 创建转账资金详情
        TransferCreateReq.TransferCapitalDetail capitalDetail = new TransferCreateReq.TransferCapitalDetail()
                .setCapitalType("PREPAY_GENERAL") // 预付通用
                .setTransferAmount(100000L) // 1000元，单位分
                .setTransferDirection("TRANSFER_OUT"); // 转出

        // 创建目标账户详情
        TransferCreateReq.TargetAccountDetail targetAccount = new TransferCreateReq.TargetAccountDetail()
                .setAccountId(789012L) // 目标账户ID
                .setTransferCapitalDetailList(List.of(capitalDetail));

        // 创建转账请求
        TransferCreateReq request = new TransferCreateReq()
                .setBizRequestNo("TEST_" + System.currentTimeMillis()) // 唯一请求编号
                .setAgentId(123456L) // 代理商账户ID
                .setAccountId(654321L) // 锁定账户ID
                .setTargetAccountDetailList(List.of(targetAccount)) // 目标账户列表
                .setRemark("测试转账");

        // 设置访问令牌（实际使用时需要真实的token）
        request.putAccessTokenHeader("test_access_token");

        // 执行转账
        OceanengineRsp<TransferCreateResp> response = transferApiClient.transferCreate(request);

        // 验证响应
        System.out.println("转账创建响应: " + response);
        if (response.isSuccess()) {
            System.out.println("转账单号: " + response.getData().getTransferOrderId());
            System.out.println("转账状态: " + response.getData().getStatus());
        } else {
            System.out.println("转账失败: " + response.getMessage());
        }
    }

    @Test
    public void testTransferQuery() {
        // 创建查询请求
        TransferQueryReq request = new TransferQueryReq()
                .setAdvertiserId(123456L)
                .setTransferOrderId("TEST_ORDER_123456789");

        // 设置访问令牌（实际使用时需要真实的token）
        request.putAccessTokenHeader("test_access_token");

        // 执行查询
        OceanengineRsp<TransferQueryResp> response = transferApiClient.transferQuery(request);

        // 验证响应
        System.out.println("转账查询响应: " + response);
        if (response.isSuccess()) {
            System.out.println("转账单号: " + response.getData().getTransferOrderId());
            System.out.println("转账状态: " + response.getData().getStatus());
            System.out.println("转账金额: " + response.getData().getAmount());
        } else {
            System.out.println("查询失败: " + response.getMessage());
        }
    }

    @Test
    public void testTransferCreateWithBuilder() {
        // 使用构建器创建转账请求
        TransferCreateReq request = TransferRequestBuilder.builder()
                .agentId(123456L)
                .accountId(654321L)
                .addTransfer(789012L, TransferCapitalType.PREPAY_GENERAL, 100000L, TransferDirection.TRANSFER_OUT)
                .addTransfer(789013L, TransferCapitalType.CREDIT_BIDDING, 50000L, TransferDirection.TRANSFER_OUT)
                .remark("使用构建器的测试转账")
                .build();

        // 设置访问令牌
        request.putAccessTokenHeader("test_access_token");

        // 执行转账
        OceanengineRsp<TransferCreateResp> response = transferApiClient.transferCreate(request);

        // 验证响应
        System.out.println("构建器转账响应: " + response);
        if (response.isSuccess()) {
            System.out.println("转账单号: " + response.getData().getTransferOrderId());
            System.out.println("转账状态: " + response.getData().getStatus());
        } else {
            System.out.println("转账失败: " + response.getMessage());
        }
    }

    @Test
    public void testSimpleTransfer() {
        // 使用快速创建方法
        TransferCreateReq request = TransferRequestBuilder.createSimpleTransfer(
                123456L,                           // 代理商账户ID
                654321L,                           // 锁定账户ID
                789012L,                           // 目标账户ID
                TransferCapitalType.PREPAY_GENERAL, // 预付通用
                100000L,                           // 1000元
                TransferDirection.TRANSFER_OUT,     // 转出
                "快速创建的测试转账"
        );

        // 设置访问令牌
        request.putAccessTokenHeader("test_access_token");

        // 执行转账
        OceanengineRsp<TransferCreateResp> response = transferApiClient.transferCreate(request);

        // 验证响应
        System.out.println("快速转账响应: " + response);
        if (response.isSuccess()) {
            System.out.println("转账单号: " + response.getData().getTransferOrderId());
            System.out.println("转账状态: " + response.getData().getStatus());
        } else {
            System.out.println("转账失败: " + response.getMessage());
        }
    }
}
