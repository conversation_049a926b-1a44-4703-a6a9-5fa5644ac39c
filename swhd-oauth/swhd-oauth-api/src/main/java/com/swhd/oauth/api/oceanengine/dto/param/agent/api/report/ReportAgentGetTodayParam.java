package com.swhd.oauth.api.oceanengine.dto.param.agent.api.report;

import com.swhd.magiccube.core.dto.param.ScrollReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ReportAgentGetTodayParam对象")
public class ReportAgentGetTodayParam extends ScrollReq {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    @Schema(description = "筛选条件")
    private ReportAgentGetFiltering filtering;

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringAdvertiserIds(List<Long> advertiserIds) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setAdvertiserIds(advertiserIds);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringCompanyName(String companyName) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setCompanyName(companyName);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringActive(String active) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setActive(active);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringFirstIndustry(String firstIndustry) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setFirstIndustry(firstIndustry);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringSecondIndustry(String secondIndustry) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setSecondIndustry(secondIndustry);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringAccountSource(String accountSource) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setAccountSource(accountSource);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringAccountStatus(String accountStatus) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setAccountStatus(accountStatus);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringStartAuditPassTime(LocalDate startAuditPassTime) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setStartAuditPassTime(startAuditPassTime);
        return this;
    }

    @Schema(hidden = true)
    public ReportAgentGetTodayParam setFilteringEndAuditPassTime(LocalDate endAuditPassTime) {
        if (filtering == null) {
            filtering = new ReportAgentGetFiltering();
        }
        filtering.setEndAuditPassTime(endAuditPassTime);
        return this;
    }

}
