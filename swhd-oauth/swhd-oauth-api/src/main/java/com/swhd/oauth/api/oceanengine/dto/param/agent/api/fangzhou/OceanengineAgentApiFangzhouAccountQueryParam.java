package com.swhd.oauth.api.oceanengine.dto.param.agent.api.fangzhou;

import com.swhd.oauth.api.oceanengine.constant.OceanengineAgentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineAgentApiFangzhouAccountQueryParam对象")
public class OceanengineAgentApiFangzhouAccountQueryParam {

    @Schema(description = "页码")
    private int page = 1;

    @Schema(description = "每页记录数")
    private int size = 10;

    @NotEmpty(message = "公司名称不能为空")
    @Schema(description = "公司名称")
    private String companyName;

    @NotNull(message = "代理商类型不能为空")
    @Schema(description = "代理商类型")
    private OceanengineAgentType agentType;

    @NotNull(message = "注册开始时间不能为空")
    @Schema(description = "注册开始时间")
    private LocalDate registerTimeStart;

    @NotNull(message = "注册结束时间不能为空")
    @Schema(description = "注册结束时间")
    private LocalDate registerTimeEnd;

}
