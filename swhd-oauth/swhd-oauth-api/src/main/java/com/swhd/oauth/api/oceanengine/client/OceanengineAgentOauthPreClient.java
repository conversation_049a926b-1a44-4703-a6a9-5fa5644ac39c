package com.swhd.oauth.api.oceanengine.client;

import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024/5/9
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = OceanengineAgentOauthPreClient.BASE_PATH)
public interface OceanengineAgentOauthPreClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/oceanengine/agent/oauth/pre";

    @GetMapping("/createAdPreAuthUrl")
    @Operation(summary = "创建广告授权URL")
    Rsp<String> createAdPreAuthUrl(@RequestParam("redirectUri") String redirectUri);

    @GetMapping("/createQcPreAuthUrl")
    @Operation(summary = "创建千川授权URL")
    Rsp<String> createQcPreAuthUrl(@RequestParam("redirectUri") String redirectUri);

    @GetMapping("/callback")
    @Operation(summary = "授权回调", description = "data：重定向url")
    Rsp<String> callback(@RequestParam("state") String state,
                         @RequestParam("authCode") String authCode);

}
