package com.swhd.oauth.api.oceanengine.dto.param.agent.api.fangzhou;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

@Data
@Schema(description = "本地推标准广告计划批量修改投放时段参数")
public class OceanengineAgentApiFangzhouLocalPushBatchUpdateScheduleParam {

    @Schema(description = "广告主ID")
    @NotNull(message = "广告主ID不能为空")
    private Long advertiserId;

    @Schema(description = "广告计划投放时段数据列表")
    @NotEmpty(message = "广告计划投放时段数据列表不能为空")
    @Valid
    private List<AdScheduleData> adsData;

    @Schema(description = "广告主附身acSessionId")
    @NotNull(message = "广告主附身acSessionId不能为空")
    private String acSessionId;

    @Data
    @Schema(description = "广告计划投放时段数据")
    public static class AdScheduleData {
        
        @Schema(description = "广告计划ID")
        @NotNull(message = "广告计划ID不能为空")
        private String adId;

        @Schema(description = "投放星期几，1-7代表周一到周日")
        @NotEmpty(message = "投放星期几不能为空")
        private List<Integer> weekDays;

        @Schema(description = "开始时间")
        @NotNull(message = "开始时间不能为空")
        private LocalTime startTime;

        @Schema(description = "结束时间")
        @NotNull(message = "结束时间不能为空")
        private LocalTime endTime;
    }
} 