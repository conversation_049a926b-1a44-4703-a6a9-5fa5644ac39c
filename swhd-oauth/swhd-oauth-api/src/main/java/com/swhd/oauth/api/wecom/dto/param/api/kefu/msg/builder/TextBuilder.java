package com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.builder;

import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.WecomApiKefuMsgSendParam;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.send.WecomApiKefuMsgText;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 文本消息builder
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
public final class TextBuilder extends BaseBuilder<TextBuilder> {

    @Schema(description = "消息内容")
    private String content;

    public TextBuilder() {
        this.msgType = "text";
    }

    public TextBuilder content(String content) {
        this.content = content;
        return this;
    }

    @Override
    public WecomApiKefuMsgSendParam build() {
        WecomApiKefuMsgSendParam param = super.build();
        WecomApiKefuMsgText text = new WecomApiKefuMsgText();
        text.setContent(content);
        param.setText(text);
        return param;
    }

}
