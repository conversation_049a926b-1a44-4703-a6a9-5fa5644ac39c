package com.swhd.oauth.api.oceanengine.dto.result.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询账户转账响应结果
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Getter
@Setter
@Schema(description = "查询账户转账响应结果")
public class OceanengineTransferQueryResult {

    @Schema(description = "转账单号")
    private String transferSerial;

    @Schema(description = "业务请求id")
    private String bizRequestNo;

    @Schema(description = "转账方向（以目标账户视角确定），TRANSFER_IN: 转入，TRANSFER_OUT: 转出")
    private String transferDirection;

    @Schema(description = "转账总金额（单位：分）")
    private Long transferAmount;

    @Schema(description = "转账总状态，NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS")
    private String transferStatus;

    @Schema(description = "转账完成时间")
    private String transferFinishTime;

    @Schema(description = "转账创建时间")
    private String transferCreateTime;

    @Schema(description = "账户信息列表")
    private List<TargetRecord> transferTargetRecordList;

    @Schema(description = "备注")
    private String remark;

    @Getter
    @Setter
    @Schema(description = "目标记录")
    public static class TargetRecord {

        @Schema(description = "错误账号id")
        private Long accountId;

        @Schema(description = "目标账号id")
        private Long targetAccountId;

        @Schema(description = "转账金额（单位：分）")
        private Long transferAmount;

        @Schema(description = "转账状态，NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS")
        private String transferStatus;

        @Schema(description = "转账资金类型列表")
        private List<CapitalRecord> transferCapitalRecordList;
    }

    @Getter
    @Setter
    @Schema(description = "资金记录")
    public static class CapitalRecord {

        @Schema(description = "转账资金类型，CREDIT_BIDDING/CREDIT_BRAND/CREDIT_GENERAL/PREPAY_BIDDING/PREPAY_BRAND/PREPAY_GENERAL")
        private String capitalType;

        @Schema(description = "转账资金金额（单位：分）")
        private Long transferAmount;

        @Schema(description = "转账资金状态，NO_TRANSFER/TRANSFER_FAILED/TRANSFER_ING/TRANSFER_PART/TRANSFER_SUCCESS")
        private String transferStatus;

        @Schema(description = "失败原因")
        private String failReason;
    }
}
