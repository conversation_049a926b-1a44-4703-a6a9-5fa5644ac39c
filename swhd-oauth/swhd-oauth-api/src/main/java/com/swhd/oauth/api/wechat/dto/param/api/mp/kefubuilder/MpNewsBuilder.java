package com.swhd.oauth.api.wechat.dto.param.api.mp.kefubuilder;

import com.swhd.oauth.api.wechat.constant.WechatMpConstant;
import com.swhd.oauth.api.wechat.dto.param.api.mp.WechatApiMpKefuMessageParam;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 图文消息（点击跳转到图文消息页面）builder
 * <pre>
 * 用法:
 * WxMpKefuMessage m = WxMpKefuMessage.NEWS().mediaId("xxxxx").toUser(...).build();
 * </pre>
 *
 * <AUTHOR>
 * @since 2021/8/31
 */
@Schema
public final class MpNewsBuilder extends BaseBuilder<MpNewsBuilder> {

    @Schema(description ="发送的图文消息的媒体ID")
    private String mediaId;

    public MpNewsBuilder() {
        this.msgType = WechatMpConstant.KefuMsgType.MPNEWS;
    }

    public MpNewsBuilder mediaId(String mediaId) {
        this.mediaId = mediaId;
        return this;
    }

    @Override
    public WechatApiMpKefuMessageParam build() {
        WechatApiMpKefuMessageParam m = super.build();
        m.setMpNewsMediaId(this.mediaId);
        return m;
    }

}
