package com.swhd.oauth.api.wechat.dto.param.open.provider;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WechatOpenProviderEventAuthParam对象")
public class WechatOpenProviderEventAuthParam {

    @NotEmpty
    @Schema(description ="随机数")
    private String nonce;

    @NotNull
    @Schema(description ="时间戳，单位秒")
    private String timestamp;

    @Schema(description ="请求签名")
    private String signature;

    @NotEmpty
    @Schema(description ="消息签名")
    private String msgSignature;

    @Schema(description ="消息加密方式")
    private String encryptType;

    @NotEmpty
    @Schema(description ="事件消息体")
    private String postData;

}
