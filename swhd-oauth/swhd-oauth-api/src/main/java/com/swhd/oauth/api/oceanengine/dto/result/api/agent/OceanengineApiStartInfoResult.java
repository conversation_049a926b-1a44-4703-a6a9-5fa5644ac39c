package com.swhd.oauth.api.oceanengine.dto.result.api.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/8/29
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineApiStartInfoResult对象")
public class OceanengineApiStartInfoResult {

    @Schema(description = "星图id")
    private Long startId;

    @Schema(description = "星图名称")
    private String startName;

    @Schema(description = """
            账户状态：
            DELETED 已删除
            ENABLE 有效
            FROZEN 禁用
            NEW_PROTOCOL 待同意新协议
            PUNISH 惩罚
            QUALIFICATION_VERIFICATION 资质验证
            UN_PROTOCOL 未同意协议
            """)
    private String status;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "行业分类id")
    private Long categoryId;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "一级行业")
    private Industry firstInfo;

    @Schema(description = "二级行业")
    private Industry secondInfo;

    @Schema(description = "创建时间（10位）")
    private Long createTime;

    @Getter
    @Setter
    @Schema(name = "OceanengineApiStartInfoResultIndustry")
    public static class Industry {

        @Schema(description = "行业id")
        private Long industryId;

        @Schema(description = "行业名称")
        private String industryName;

    }

}
