package com.swhd.oauth.api.oceanengine.dto.result.api.advertising;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 巨量引擎广告项目列表查询结果
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "巨量引擎广告项目列表查询结果")
public class OceanengineAgentApiAdvertisingAdProjectListResult {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = """
        投放模式:
        MANUAL 手动投放
        PROCEDURAL 自动投放
        """)
    private String deliveryMode;

    @Schema(description = """
        广告投放类型:
        FEED 信息流
        SEARCH 搜索广告
        """)
    private String landingType;

    @Schema(description = """
        营销目标:
        VIDEO_PROM_GOODS 短视频带货
        LIVE_PROM_GOODS 直播带货
        """)
    private String marketingGoal;

    @Schema(description = """
        广告类型:
        ALL 所有广告
        SEARCH 搜索广告
        """)
    private String adType;

    @Schema(description = """
        运营状态:
        ENABLE 启用
        DISABLE 暂停
        """)
    private String optStatus;

    @Schema(description = """
        项目状态:
        ALL 所有包含已删除
        DELETE 已删除
        ADVERTISER_OFFLINE 广告主账号异常
        DISABLED 已暂停
        ENABLE 已启用
        """)
    private String status;

    @Schema(description = "项目创建时间")
    private LocalDateTime projectCreateTime;

    @Schema(description = "项目修改时间")
    private LocalDateTime projectModifyTime;

} 