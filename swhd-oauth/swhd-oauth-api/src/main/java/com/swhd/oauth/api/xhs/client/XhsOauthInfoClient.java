package com.swhd.oauth.api.xhs.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.xhs.dto.param.info.XhsOauthInfoAddParam;
import com.swhd.oauth.api.xhs.dto.param.info.XhsOauthInfoPageParam;
import com.swhd.oauth.api.xhs.dto.param.info.XhsOauthInfoUpdateParam;
import com.swhd.oauth.api.xhs.dto.result.XhsOauthInfoResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = XhsOauthInfoClient.BASE_PATH)
public interface XhsOauthInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/xhs/oauth/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<XhsOauthInfoResult>> page(@RequestBody @Valid XhsOauthInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<XhsOauthInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据userId获取")
    @GetMapping("/getByUserId")
    Rsp<XhsOauthInfoResult> getByUserId(@RequestParam("userId") String userId);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<XhsOauthInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据userId列表获取")
    @PostMapping("/listByUserIds")
    Rsp<List<XhsOauthInfoResult>> listByUserIds(@RequestBody @Valid @NotEmpty Collection<String> userIds);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid XhsOauthInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid XhsOauthInfoUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "列表查询")
    @PostMapping("/listAll")
    Rsp<List<XhsOauthInfoResult>> listAll();
}
