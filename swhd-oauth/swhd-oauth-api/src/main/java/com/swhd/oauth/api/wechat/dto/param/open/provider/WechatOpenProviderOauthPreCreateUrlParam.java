package com.swhd.oauth.api.wechat.dto.param.open.provider;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WechatOpenProviderOauthPreCreateUrlParam对象")
public class WechatOpenProviderOauthPreCreateUrlParam {

    @NotEmpty(message = "授权回调地址不能为空")
    @Schema(description ="授权回调地址")
    private String redirectUri;

    @Schema(description ="授权类型，空默认为3，1：仅展示公众号授权列表；2：仅展示小程序授权列表；3：同时展示公众号和小程序授权列表")
    private Integer authType;

}
