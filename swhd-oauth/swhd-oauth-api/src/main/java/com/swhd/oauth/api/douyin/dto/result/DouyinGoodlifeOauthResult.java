package com.swhd.oauth.api.douyin.dto.result;

import com.swhd.oauth.api.douyin.constant.GoodlifeOauthScope;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "DouyinGoodlifeOauthResult对象")
public class DouyinGoodlifeOauthResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "商家ID")
    private String bizId;

    @Schema(description = "商家名称")
    private String bizName;

    @Schema(description = "授权功能")
    private List<GoodlifeOauthScope> scopes;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
