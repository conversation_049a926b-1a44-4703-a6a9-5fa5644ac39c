package com.swhd.oauth.api.oceanengine.dto.result.api.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/8/20
 */
@Getter
@Setter
@Schema(description = "ReportLocalPromotionResult对象")
public class ReportLocalPromotionResult {

    @Schema(description = "广告id")
    private Long promotionId;

    @Schema(description = "广告名称")
    private String promotionName;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "时间-天")
    private LocalDate statTimeDay;

    @Schema(description = "时间-小时")
    private String statTimeHour;

    @Schema(description = "消耗(元)")
    private BigDecimal statCost;

    @Schema(description = "展示次数")
    private Integer showCount;

    @Schema(description = "点击次数")
    private Integer clickCount;

    @Schema(description = "点击率")
    private BigDecimal ctr;

    @Schema(description = "点击均价(元)")
    private BigDecimal cpcPlatform;

    @Schema(description = "平均千次展示费用(元)")
    private BigDecimal cpmPlatform;

    @Schema(description = "转化数")
    private Integer convertCount;

    @Schema(description = "转化率")
    private BigDecimal conversionRate;

    @Schema(description = "转化成本(元)")
    private BigDecimal conversionCost;

    @Schema(description = "转化数(计费时间)")
    private Integer attributionConvertCount;

    @Schema(description = "转化率(计费时间)")
    private BigDecimal attributionConversionRate;

    @Schema(description = "转化成本(元)(计费时间)")
    private BigDecimal attributionConvertCost;

    @Schema(description = "表单提交数")
    private Integer formCount;

    @Schema(description = "团购线索数")
    private Integer cluePayOrderCount;

    @Schema(description = "私信留资数")
    private Integer clueMessageCount;

    @Schema(description = "电话拨打数")
    private Integer phoneConfirmCount;

    @Schema(description = "电话接通数")
    private Integer phoneConnectCount;

    @Schema(description = "私信咨询数")
    private Integer messageActionCount;

    @Schema(description = "意向表单数")
    private Integer intentionFormCount;

    @Schema(description = "意向话单数")
    private Integer intentionPhoneCount;

    @Schema(description = "意向咨询数")
    private Integer intentionMessageClueCount;

    @Schema(description = "表单提交数(计费时间)")
    private Integer attributionFormCount;

    @Schema(description = "团购线索数(计费时间)")
    private Integer attributionCluePayOrderCount;

    @Schema(description = "私信留资数(计费时间)")
    private Integer attributionClueMessageCount;

    @Schema(description = "电话拨打数(计费时间)")
    private Integer attributionPhoneConfirmCount;

    @Schema(description = "电话接通数(计费时间)")
    private Integer attributionPhoneConnectCount;

    @Schema(description = "私信咨询数(计费时间)")
    private Integer attributionMessageActionCount;

    @Schema(description = "意向表单数(计费时间)")
    private Integer attributionIntentionFormCount;

    @Schema(description = "意向话单数(计费时间)")
    private Integer attributionIntentionPhoneCount;

    @Schema(description = "意向咨询数(计费时间)")
    private Integer attributionIntentionMessageClueCount;

    @Schema(description = "视频点赞次数")
    private Integer dyLike;

    @Schema(description = "视频评论次数")
    private Integer dyComment;

    @Schema(description = "视频分享次数")
    private Integer dyShare;

    @Schema(description = "视频收藏次数")
    private Integer dyCollect;

    @Schema(description = "视频播放次数")
    private Integer totalPlay;

    @Schema(description = "视频3s播放次数")
    private Integer playDuration3s;

    @Schema(description = "视频5s播放次数")
    private Integer playDuration5s;

    @Schema(description = "视频25%进度播放次数")
    private Integer play25FeedBreak;

    @Schema(description = "视频50%进度播放次数")
    private Integer play50FeedBreak;

    @Schema(description = "视频75%进度播放次数")
    private Integer play75FeedBreak;

    @Schema(description = "视频播放完成次数")
    private Integer playOver;

    @Schema(description = "视频5s播放率")
    private BigDecimal playDuration5sShowCntRate;

    @Schema(description = "视频完播率")
    private BigDecimal playOverRate;

    @Schema(description = "视频点赞率")
    private BigDecimal dyLikeRate;

    @Schema(description = "直播间观看次数")
    private Integer lubanLiveEnterCount;

    @Schema(description = "直播间超1分钟停留次数")
    private Integer liveWatchOneMinuteCount;

    @Schema(description = "直播间评论次数")
    private Integer lubanLiveCommentCount;

    @Schema(description = "直播间分享次数")
    private Integer lubanLiveShareCount;

    @Schema(description = "粉丝量")
    private Integer dyFollow;

}
