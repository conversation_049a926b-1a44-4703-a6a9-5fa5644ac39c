package com.swhd.oauth.api.oceanengine.dto.result.api.fangzhou;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineAgentApiFangzhouAccountResult对象")
public class OceanengineAgentApiFangzhouUserInfoResult {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "员工ID")
    private Long employeeId;

    @Schema(description = "员工名称")
    private String employeeName;

    @Schema(description = "员工类型")
    private Integer employeeType;

    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "部门名称")
    private String departmentName;

    private Integer adOperationType;

    private Long realCompanyId;

    private String realCompanyName;

    private String companyId;

    private String companyName;

    private String avatarUrl;

    private String email;

}
