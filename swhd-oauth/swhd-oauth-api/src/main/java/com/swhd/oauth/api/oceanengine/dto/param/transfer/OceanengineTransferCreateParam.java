package com.swhd.oauth.api.oceanengine.dto.param.transfer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * 发起账户转账请求参数
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "发起账户转账请求参数")
public class OceanengineTransferCreateParam {

    @Schema(description = "请求唯一编号，建议用uuid，防止重复转账", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "请求唯一编号不能为空")
    private String bizRequestNo;

    @Schema(description = "代理商账户id，用于鉴权", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "代理商账户id不能为空")
    private Long agentId;

    @Schema(description = "锁定账户id，1:N的1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "锁定账户id不能为空")
    private Long accountId;

    @Schema(description = "目标账户列表，1:N的N，最多支持100个", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "目标账户列表不能为空")
    @Valid
    private List<TargetAccountDetail> targetAccountDetailList;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "转账方向，TRANSFER_IN：转入，TRANSFER_OUT：转出", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "转账方向不能为空")
    private String transferDirection;

    /**
     * 目标账户详情
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(description = "目标账户详情")
    public static class TargetAccountDetail {

        @Schema(description = "目标账户id", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "目标账户id不能为空")
        private Long accountId;

        @Schema(description = "锁定账户与目标账户转账资金列表", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "转账资金列表不能为空")
        @Valid
        private List<TransferCapitalDetail> transferCapitalDetailList;
    }

    /**
     * 转账资金详情
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(description = "转账资金详情")
    public static class TransferCapitalDetail {

        @Schema(description = "转账资金类型，CREDIT_BIDDING：授信竞价，CREDIT_BRAND：授信品牌，CREDIT_GENERAL：授信通用，PREPAY_BIDDING：预付竞价，PREPAY_BRAND：预付品牌，PREPAY_GENERAL：预付通用", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "转账资金类型不能为空")
        private String capitalType;

        @Schema(description = "转账金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "转账金额不能为空")
        private Long transferAmount;
    }
}
