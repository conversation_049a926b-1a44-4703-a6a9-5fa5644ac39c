package com.swhd.oauth.api.oceanengine.dto.result.api.fangzhou;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "本地推标准广告项目批量修改预算结果")
public class OceanengineAgentApiFangzhouProjectBatchUpdateBudgetResult {

    @Schema(description = "广告项目ID")
    private String projectId;

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "错误信息")
    private String message;

    @Schema(description = "原预算")
    private BigDecimal oldBudget;

    @Schema(description = "新预算")
    private BigDecimal newBudget;

    @Schema(description = "原预算类型：0-日预算，1-总预算")
    private Integer oldBudgetMode;

    @Schema(description = "新预算类型：0-日预算，1-总预算")
    private Integer newBudgetMode;
} 