package com.swhd.oauth.api.douyin.dto.param.goodlife.clue;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "DouyinGoodlifeCluePageParam对象")
public class DouyinGoodlifeCluePageParam extends PageReq {

    @Schema(description = "商家ID")
    private String bizId;

}
