package com.swhd.oauth.api.wecom.client;

import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.wecom.dto.param.oauth.app.WecomOauthAppPageParam;
import com.swhd.oauth.api.wecom.dto.param.oauth.app.WecomOauthAppUpdateParam;
import com.swhd.oauth.api.wecom.dto.result.WecomOauthAppResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-02
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = WecomOauthAppClient.BASE_PATH)
public interface WecomOauthAppClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/wecom/oauth/app";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<WecomOauthAppResult>> page(@RequestBody @Valid WecomOauthAppPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<WecomOauthAppResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据企业ID获取")
    @GetMapping("/getByCorpId")
    Rsp<WecomOauthAppResult> getByCorpId(@RequestParam("corpId") String corpId);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<WecomOauthAppResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据企业ID列表获取")
    @PostMapping("/listByCorpIds")
    Rsp<List<WecomOauthAppResult>> listByCorpIds(@RequestBody @Valid @NotEmpty Collection<String> corpIds);

    @Operation(summary = "所有列表")
    @GetMapping("/listAll")
    Rsp<List<WecomOauthAppResult>> listAll();

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid WecomOauthAppUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
