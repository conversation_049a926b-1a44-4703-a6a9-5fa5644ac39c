package com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.builder;

import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.WecomApiKefuMsgSendParam;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.send.WecomApiKefuMsgMenu;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
public final class MsgMenuBuilder extends BaseBuilder<MsgMenuBuilder> {

    @Schema(description = "起始文本 不多于1024字节")
    private String headContent;

    private List<WecomApiKefuMsgMenu.Item> list;

    @Schema(description = "结束文本 不多于1024字节")
    private String tailContent;

    public MsgMenuBuilder() {
        this.msgType = "msgmenu";
    }

    public MsgMenuBuilder headContent(String headContent) {
        this.headContent = headContent;
        return this;
    }

    public MsgMenuBuilder item(WecomApiKefuMsgMenu.Item item) {
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(item);
        return this;
    }

    public MsgMenuBuilder item(WecomApiKefuMsgMenu.Click click) {
        if (list == null) {
            list = new ArrayList<>();
        }
        WecomApiKefuMsgMenu.Item item = new WecomApiKefuMsgMenu.Item();
        item.setType("click");
        item.setClick(click);
        list.add(item);
        return this;
    }

    public MsgMenuBuilder item(WecomApiKefuMsgMenu.View view) {
        if (list == null) {
            list = new ArrayList<>();
        }
        WecomApiKefuMsgMenu.Item item = new WecomApiKefuMsgMenu.Item();
        item.setType("view");
        item.setView(view);
        list.add(item);
        return this;
    }

    public MsgMenuBuilder item(WecomApiKefuMsgMenu.MiniProgram miniProgram) {
        if (list == null) {
            list = new ArrayList<>();
        }
        WecomApiKefuMsgMenu.Item item = new WecomApiKefuMsgMenu.Item();
        item.setType("miniprogram");
        item.setMiniProgram(miniProgram);
        list.add(item);
        return this;
    }

    public MsgMenuBuilder item(WecomApiKefuMsgMenu.Text text) {
        if (list == null) {
            list = new ArrayList<>();
        }
        WecomApiKefuMsgMenu.Item item = new WecomApiKefuMsgMenu.Item();
        item.setType("text");
        item.setText(text);
        list.add(item);
        return this;
    }

    public MsgMenuBuilder items(WecomApiKefuMsgMenu.Item item, WecomApiKefuMsgMenu.Item... items) {
        list = new ArrayList<>();
        list.add(item);
        list.addAll(Arrays.asList(items));
        return this;
    }

    public MsgMenuBuilder items(Collection<WecomApiKefuMsgMenu.Item> items) {
        list = new ArrayList<>(items);
        return this;
    }

    public MsgMenuBuilder tailContent(String tailContent) {
        this.tailContent = tailContent;
        return this;
    }

    @Override
    public WecomApiKefuMsgSendParam build() {
        WecomApiKefuMsgSendParam param = super.build();
        WecomApiKefuMsgMenu msgMenu = new WecomApiKefuMsgMenu();
        msgMenu.setHeadContent(headContent);
        msgMenu.setList(list);
        msgMenu.setTailContent(tailContent);
        param.setMsgMenu(msgMenu);
        return param;
    }

}
