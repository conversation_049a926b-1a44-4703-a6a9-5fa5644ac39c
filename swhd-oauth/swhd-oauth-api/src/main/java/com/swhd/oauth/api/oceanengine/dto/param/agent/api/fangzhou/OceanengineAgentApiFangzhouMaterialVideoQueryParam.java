package com.swhd.oauth.api.oceanengine.dto.param.agent.api.fangzhou;

import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineAgentApiFangzhouMaterialVideoParam对象")
public class OceanengineAgentApiFangzhouMaterialVideoQueryParam extends PageReq {

    @NotEmpty(message = "公司名称不能为空")
    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "素材ID")
    private List<Long> materialIds;

    public Map<String, Object> toFilter() {
        List<Map<String, Object>> filters = new ArrayList<>();
        // 默认参数
        filters.add(Map.of("field", 1003, "type", 2, "valueList", List.of("17")));
        if (Func.isNotEmpty(this.materialIds)) {
            filters.add(Map.of("field", 1002, "type", 2, "valueList", this.materialIds));
        }
        Map<String, Object> filter = new HashMap<>(filters.getLast());
        if (filters.size() > 1) {
            List<Map<String, Object>> conditions = new ArrayList<>();
            for (int i = 0; i < filters.size() - 1; i++) {
                conditions.add(filters.get(i));
            }
            filter.put("conditions", conditions);
        }
        return filter;
    }

}
