package com.swhd.oauth.api.douyin.dto.message;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "抖音事件广告信息")
public class DouyinEventAdInfo {

    @Schema(description = "广告主Id")
    @SerializedName("adv_id")
    private Long advId;

    @Schema(description = "广告主名称")
    @SerializedName("adv_name")
    private String advName;

    @Schema(description = "广告计划Id")
    @SerializedName("ad_id")
    private Long adId;

    @Schema(description = "广告计划名")
    @SerializedName("ad_name")
    private String adName;

    @Schema(description = "创意Id")
    @SerializedName("creative_id")
    private Long creativeId;

    @Schema(description = "广告Id")
    @SerializedName("promotion_id")
    private Long promotionId;

    @Schema(description = "广告素材-标题Id")
    @SerializedName("material_title_id")
    private Long materialTitleId;

    @Schema(description = "广告素材-图片Id")
    @SerializedName("material_image_id")
    private Long materialImageId;

    @Schema(description = "广告素材-视频Id")
    @SerializedName("material_video_id")
    private Long materialVideoId;

}
