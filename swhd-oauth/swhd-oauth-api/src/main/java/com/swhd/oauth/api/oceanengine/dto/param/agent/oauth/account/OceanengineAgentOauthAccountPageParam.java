package com.swhd.oauth.api.oceanengine.dto.param.agent.oauth.account;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineOauthAgentPageParam对象")
public class OceanengineAgentOauthAccountPageParam extends PageReq {

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "代理商类型：1-巨量广告，2-巨量千川，3-巨量星图，4-巨量本地推")
    private Integer agentType;

    @Schema(description = "代理商类型：1-巨量广告，2-巨量千川，3-巨量星图，4-巨量本地推")
    private List<Integer> agentTypeList;

    @Schema(description = "代理商名称")
    private String agentName;

    @Schema(description = "公司名称")
    private String companyName;

}
