package com.swhd.oauth.api.oceanengine.dto.param.riskControl;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineRiskControlGetVideoParam对象")
public class OceanengineRiskControlGetPictureParam {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    /**
     * 广告主id
     */
    private Long advertiserId;

    /**
     * 视频过滤条件
     */
    private Filtering filtering;

    /**
     * 页码，默认值1
     */
    private Long page;

    /**
     * 页面大小，默认值20
     */
    private Long pageSize;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Filtering {

        /**
         * 视频宽度
         */
        private Long width;

        /**
         * 视频高度
         */
        private Long height;

        /**
         * 视频宽高比，示例: [1.7, 2.5]
         */
        private List<Float> ratio;

        /**
         * 视频ids，示例: ["86adb23eaa21229fc04ef932b5089bb8"]
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        private List<String> videoIds;

        /**
         * 素材id列表，可以根据material_ids（素材报表使用的id，一个素材唯一对应一个素材id）进行过滤
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        private List<Long> materialIds;

        /**
         * md5值列表，可以根据素材的md5进行过滤
         * 数量限制：<=100
         * 注意：video_ids、material_ids、signatures只能选择一个进行过滤
         */
        private List<String> signatures;

        /**
         * 根据视频上传时间进行过滤的起始时间，与end_time搭配使用，格式：yyyy-mm-dd
         */
        private LocalDateTime startTime;

        /**
         * 根据视频上传时间进行过滤的截止时间，与start_time搭配使用，格式：yyyy-mm-dd
         */
        private LocalDateTime endTime;

        /**
         * 视频标签
         */
        private List<String> labels;

        /**
         * 素材来源，详见【附录-素材来源】
         * 枚举值大小写敏感，请严格按照定义的名称传参
         */
        private List<String> source;

        /**
         * 星图达人 id 检索，仅当source = STAR 时，支持通过星图达人ID进行筛选，单次最多支持传入20 个id进行检索
         */
        private List<String> starAuthorIds;
    }

}
