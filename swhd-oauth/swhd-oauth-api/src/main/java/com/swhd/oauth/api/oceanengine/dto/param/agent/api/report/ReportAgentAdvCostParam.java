package com.swhd.oauth.api.oceanengine.dto.param.agent.api.report;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ReportAgentAdvCostParam对象")
public class ReportAgentAdvCostParam extends PageReq {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商id")
    private Long agentId;

    @NotNull(message = "开始时间不能为空")
    @Schema(description = "开始时间。格式：YYYY-MM-DD。闭区间,可选日期范围是今天以前（不允许包括今天）")
    private LocalDate startDate;

    @NotNull(message = "结束时间不能为空")
    @Schema(description = "结束时间。格式：YYYY-MM-DD。闭区间,可选日期范围是今天以前；开始时间与结束时间的跨度不能超过365天。")
    private LocalDate endDate;

    @Schema(description = "过滤条件")
    private Filtering filtering;

    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(name = "ReportAgentAdvCostParamFiltering")
    public static class Filtering {

        @Schema(description = "广告主id")
        private List<Long> advertiserIds;

        @Schema(description = "代理商客户id")
        private Long agentCustomerId;

        @Schema(description = "广告主所属公司名称，若选填该字段，限制最小长度为1，最大长度为223。支持模糊查询。")
        private String companyName;

        @Schema(description = "一级行业名称。可从【获取行业列表】接口获取。")
        private String firstIndustry;

        @Schema(description = "二级行业名称。可从【获取行业列表】接口获取。")
        private String secondIndustry;

        @Schema(description = "代理商子账户id")
        private Long secondAdAgentId;

        @Schema(description = "项目编号")
        private String projectSerial;

        @Schema(description = "项目名称，若选填该字段，限制最小长度为1，最大长度为223。支持模糊查询")
        private String projectName;

        /**

         */
        @Schema(description = """
                消耗来源 可选值:
                1 本地广告
                102 懂车帝效果联盟
                12 F项目幸福豆订单
                159 懂车聚光达人服务费
                2 DSP
                3 月结消耗
                31 懂车帝AI（智投通）
                4 非标消耗
                5 项目调差
                """)
        private Integer costSource;

        @Schema(description = """
                投放平台 可选值:
                1 通投智选
                10 懂车帝APP
                11 穿山甲
                12 剪映
                13 鲜时光
                14 FaceU
                15 抖音lite
                16 皮皮虾
                17 头条lite APP
                2 西瓜APP
                3 搜索广告
                4 头条APP
                5 火山APP
                6 番茄小说
                7 抖音APP
                8 站内通投
                9 J项目
                """)
        private Integer appName;

        @Schema(description = """
                广告类型 可选值:
                0 不区分品牌和竞价广告组
                1 品牌广告组
                2 竞价广告组
                """)
        private Integer pricingCategory;

        @Schema(description = """
                推广形式 可选值:
                1 直播计划
                2 非直播计划
                """)
        private Integer promotionType;

        @Schema(description = """
                电商类型 可选值:
                1 闭环电商
                2 非闭环电商
                3 引流电商
                4 平台电商
                5 非电商
                """)
        private Integer ecommerceType;

        @Schema(description = """
                品牌广告类型 可选值:
                BRAND_CONVERSION 品牌转化类
                BRAND_EXPOSURE 品牌曝光类
                BRAND_GRASS 品牌种草类
                CONCENTRATING_RESOURCE 招商聚光资源
                STAR_RESOURCE 招商星图资源
                """)
        private List<String> spuLabelName;

    }

}
