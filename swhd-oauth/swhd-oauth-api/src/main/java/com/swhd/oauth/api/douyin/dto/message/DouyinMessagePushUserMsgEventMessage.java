package com.swhd.oauth.api.douyin.dto.message;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Getter
@Setter
@Schema(description = "DouyinMessagePushUserMsgEventMessage对象")
public class DouyinMessagePushUserMsgEventMessage extends DouyinMessagePushBaseMsgEventMessage {

    @Schema(description = "消息类型：text：文本，image：图片，emoji：表情，video：视频，retain_consult_card：留资卡片，card：卡片，other：其他")
    @SerializedName("message_type")
    private String messageType;

    @Schema(description = "区分发出应用，通过发送私信消息接口发送，会显示具体的 clientkey，通过端上主动发送，该字段默认为空")
    private String source;

    @Schema(description = "文本消息")
    private String text;

    @Schema(description = "资源类型")
    @SerializedName("resource_type")
    private String resourceType;

    @Schema(description = "资源高度")
    @SerializedName("resource_height")
    private String resourceHeight;

    @Schema(description = "资源宽度")
    @SerializedName("resource_width")
    private String resourceWidth;

    @Schema(description = "资源链接")
    @SerializedName("resource_url")
    private String resourceUrl;

    @Schema(description = "加密后的视频ID")
    @SerializedName("item_id")
    private String itemId;

    @Schema(description = "卡片id")
    @SerializedName("card_id")
    private String cardId;

    @Schema(description = "卡片状态：1-空白态，2-完成态")
    @SerializedName("card_status")
    private Integer cardStatus;

    @Schema(description = """
            卡片数据，如：[
              {
                "label": "姓名",
                "value": "Tonality"
              },
              {
                "label": "手机号",
                "value": "12345678901"
              },
              {
                "label": "城市",
                "value": "北京市北京市海淀区"
              }
            ]
            """)
    @SerializedName("card_data")
    private List<DouyinEventCardItem> cardData;

    @Schema(description = "actions")
    private Map<String, DouyinEventCardAction> actions;

    @Schema(description = "卡片组件状态：1-发送，2-提交")
    @SerializedName("component_status")
    private Integer componentStatus;

}
