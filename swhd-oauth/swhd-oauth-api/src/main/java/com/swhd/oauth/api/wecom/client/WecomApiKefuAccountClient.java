package com.swhd.oauth.api.wecom.client;

import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.account.WecomApiKefuAccountGetLinkParam;
import com.swhd.oauth.api.wecom.dto.param.api.kefu.account.WecomApiKefuAccountListParam;
import com.swhd.oauth.api.wecom.dto.result.api.WecomApiKefuAccountResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = WecomApiKefuAccountClient.BASE_PATH)
public interface WecomApiKefuAccountClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/wecom/api/kefu/account";

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    Rsp<List<WecomApiKefuAccountResult>> list(@RequestBody @Valid WecomApiKefuAccountListParam param);

    @Operation(summary = "获取账号链接")
    @PostMapping("/getLink")
    Rsp<String> getLink(@RequestBody @Valid WecomApiKefuAccountGetLinkParam param);

}
