package com.swhd.oauth.api.oceanengine.dto.result.api.advertising;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "巨量引擎更新广告计划投放时段结果")
public class OceanengineAgentApiAdvertisingPromotionUpdateScheduleResult {

    @Schema(description = "广告计划ID")
    private Long promotionId;

    @Schema(description = "是否更新成功")
    private Boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;
} 