package com.swhd.oauth.api.wecom.dto.param.api.kefu.msg.send;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WecomApiKefuMsgMiniProgram对象")
public class WecomApiKefuMsgMiniProgram {

    @Schema(description = "小程序appid")
    private String appId;

    @Schema(description = "小程序消息标题，最多64个字节，超过会自动截断")
    private String title;

    @Schema(description = "小程序消息封面的mediaid，封面图建议尺寸为520*416")
    private String thumbMediaId;

    @Schema(description = "小程序消息封面的url，封面图建议尺寸为520*416")
    private String thumbMediaUrl;

    @Schema(description = "点击消息卡片后进入的小程序页面路径。注意路径要以.html为后缀，否则在微信中打开会提示找不到页面")
    private String pagePath;

}
