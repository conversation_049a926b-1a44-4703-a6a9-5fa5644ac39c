package com.swhd.oauth.api.wechat.dto.param.api.mp.kefubuilder;

import com.swhd.oauth.api.wechat.constant.WechatMpConstant;
import com.swhd.oauth.api.wechat.dto.param.api.mp.WechatApiMpKefuMessageParam;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 语音消息builder
 * <pre>
 * 用法: WxMpKefuMessage m = WxMpKefuMessage.VOICE().mediaId(...).toUser(...).build();
 * </pre>
 *
 * <AUTHOR>
 * @since 2021/8/31
 */
@Schema
public class VoiceBuilder extends BaseBuilder<VoiceBuilder> {

    @Schema(description ="语音的媒体ID")
    private String mediaId;

    @Schema(description ="语音的媒体ID，mediaId和mediaUrl二选一")
    private String mediaUrl;

    public VoiceBuilder() {
        this.msgType = WechatMpConstant.KefuMsgType.VOICE;
    }

    public VoiceBuilder mediaId(String mediaId) {
        this.mediaId = mediaId;
        return this;
    }

    public VoiceBuilder mediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
        return this;
    }

    @Override
    public WechatApiMpKefuMessageParam build() {
        WechatApiMpKefuMessageParam m = super.build();
        m.setMediaId(this.mediaId);
        m.setMediaUrl(this.mediaUrl);
        return m;
    }

}
