package com.swhd.oauth.api.douyin.client;

import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.douyin.dto.param.goodlife.clue.DouyinGoodlifeCluePageParam;
import com.swhd.oauth.api.douyin.dto.result.DouyinGoodlifeClueResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = DouyinGoodlifeClueClient.BASE_PATH)
public interface DouyinGoodlifeClueClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/douyin/goodlife/clue";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<DouyinGoodlifeClueResult>> page(@RequestBody @Valid DouyinGoodlifeCluePageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<DouyinGoodlifeClueResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<DouyinGoodlifeClueResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
