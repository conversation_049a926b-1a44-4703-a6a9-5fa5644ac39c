package com.swhd.oauth.api.oceanengine.dto.param.riskControl;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineRiskControlPauseMaterialParam对象")
public class OceanengineRiskControlPauseMaterialParam {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;


    /**
     * 广告账户ID
     */
    @SerializedName("advertiser_id")
    @NotNull(message = "广告账户ID不能为空")
    private Long advertiserId;

    /**
     * 广告ID
     */
    @SerializedName("promotion_id")
    @NotNull(message = "广告ID不能为空")
    private Long promotionId;


    /**
     * 批量更新广告启用状态，包含广告ID和操作状态，list长度限制1～10
     */
    @SerializedName("date")
    @NotNull(message = "批量更新广告数据不能为空")
    private List<PauseMaterial> data;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PauseMaterial {

        /**
         * 素材ID，最多支持100个
         */
        @SerializedName("material_id")
        private Long materialId;

        /**
         * 允许值：DISABLE 暂停、ENABLE 启用
         */
        @SerializedName("opt_status")
        private String optStatus;
    }

}
