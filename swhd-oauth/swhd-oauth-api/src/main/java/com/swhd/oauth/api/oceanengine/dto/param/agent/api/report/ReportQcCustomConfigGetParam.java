package com.swhd.oauth.api.oceanengine.dto.param.agent.api.report;

import com.swhd.oauth.api.oceanengine.constant.OceanengineReportQcDataTopics;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/15
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ReportAgentGetTodayParam对象")
public class ReportQcCustomConfigGetParam {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    @NotNull(message = "广告主ID不能为空")
    @Schema(description = "广告主ID")
    private Long advertiserId;

    @NotEmpty(message = "数据主题不能为空")
    @Schema(description = "数据主题查询列表")
    private List<OceanengineReportQcDataTopics> dataTopics;

}
