package com.swhd.oauth.api.oceanengine.dto.param.agent.api.file;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FileQcVideoGetParam对象")
public class FileQcVideoGetParam extends PageReq {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    @NotNull(message = "广告主ID不能为空")
    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "过滤条件")
    private Filtering filtering;

    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(name = "OceanengineAgentApiFileVideoGetParamFiltering")
    public static class Filtering {

        @Schema(description = "视频ids，数量限制：<=100。注意：video_ids、material_ids、signatures只能选择一个进行过滤")
        private List<String> videoIds;

        @Schema(description = "素材id列表，数量限制：<=100。注意：video_ids、material_ids、signatures只能选择一个进行过滤")
        private List<Long> materialIds;

        @Schema(description = "md5值列表，数量限制：<=100。注意：video_ids、material_ids、signatures只能选择一个进行过滤")
        private List<String> signatures;

        @Schema(description = "根据视频上传时间进行过滤的起始时间，与end_time搭配使用，格式：yyyy-mm-dd")
        private LocalDate startTime;

        @Schema(description = "根据视频上传时间进行过滤的截止时间，与start_time搭配使用，格式：yyyy-mm-dd")
        private LocalDate endTime;

        @Schema(description = "素材标签")
        private List<String> tags;

        @Schema(description = "素材来源，详见【附录-素材来源】。枚举值大小写敏感，请严格按照定义的名称传参")
        private List<String> source;

    }

}
