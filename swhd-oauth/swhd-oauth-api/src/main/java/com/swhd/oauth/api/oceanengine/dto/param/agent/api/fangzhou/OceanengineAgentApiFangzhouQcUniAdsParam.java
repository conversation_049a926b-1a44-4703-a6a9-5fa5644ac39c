package com.swhd.oauth.api.oceanengine.dto.param.agent.api.fangzhou;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "千川全域广告计划查询参数")
public class OceanengineAgentApiFangzhouQcUniAdsParam {
    
    @NotEmpty(message = "acSessionId不能为空")
    @Schema(description = "acSessionId")
    private String acSessionId;

    @NotNull(message = "广告主ID不能为空")
    @Schema(description = "广告主ID")
    private Long advertiserId;
    
    @Schema(description = "营销目标： 1-推商品,2-直播间推广")
    private Integer marGoal = 2;
    
    @Schema(description = "页码")
    private Integer current = 1;
    
    @Schema(description = "每页条数")
    private Integer size = 10;
} 