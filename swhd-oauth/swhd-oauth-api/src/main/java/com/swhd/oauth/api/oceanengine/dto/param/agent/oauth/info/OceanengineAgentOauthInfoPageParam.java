package com.swhd.oauth.api.oceanengine.dto.param.agent.oauth.info;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-05-09
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineOauthInfoPageParam对象")
public class OceanengineAgentOauthInfoPageParam extends PageReq {

    @Schema(description = "代理商公司名称")
    private String companyName;

    @Schema(description = "授权类型：1-巨量广告，2-巨量千川")
    private Integer oauthType;

    @Schema(description = "巨量引擎的授权应用id")
    private Long oauthAppId;

    @Schema(description = "巨量引擎的授权用户id")
    private Long oauthUserId;

    @Schema(description = "刷新令牌过期时间(大于)")
    private LocalDateTime gtRefreshExpireTime;

    @Schema(description = "状态：0-禁用，1-启用")
    private Integer state;

}
