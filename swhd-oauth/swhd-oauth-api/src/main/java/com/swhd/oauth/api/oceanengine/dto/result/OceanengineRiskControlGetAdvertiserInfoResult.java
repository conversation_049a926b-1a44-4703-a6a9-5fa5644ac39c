package com.swhd.oauth.api.oceanengine.dto.result;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineRiskControlGetPromotionListResult对象")
public class OceanengineRiskControlGetAdvertiserInfoResult {
    /**
     * 请求的日志id，唯一标识一个请求
     */
    @SerializedName("requestId")
    private String requestId;
    /**
     * 广告主ID
     */
    @SerializedName("id")
    private Long id;

    /**
     * 账户名
     */
    @SerializedName("name")
    private String name;

    /**
     *角色
     */
    @SerializedName("role")
    private String role;

    /**
     *状态
     */
    @SerializedName("status")
    private String status;

    /**
     *备注
     */
    @SerializedName("note")
    private String note;

    /**
     * 地址
     */
    @SerializedName("address")
    private String address;

    /**
     *执照预览地址(链接默认1小时内有效)
     */
    @SerializedName("license_url")
    private String licenseUrl;

    /**
     *执照编号
     */
    @SerializedName("license_no")
    private String licenseNo;

    /**
     * 执照省份
     */
    @SerializedName("license_province")
    private String licenseProvince;

    /**
     * 执照城市
     */
    @SerializedName("license_city")
    private String licenseCity;

    /**
     *公司名
     */
    @SerializedName("company")
    private String company;

    /**
     *经营类别
     */
    @SerializedName("brand")
    private String brand;

    /**
     *运营区域
     */
    @SerializedName("promotion_area")
    private String promotionArea;

    /**
     *运营省份
     */
    @SerializedName("promotion_center_province")
    private String promotionCenterProvince;

    /**
     *运营城市
     */
    @SerializedName("promotion_center_city")
    private String promotionCenterCity;

    /**
     *一级行业名称（新版）
     */
    @SerializedName("first_industry_name")
    private String firstIndustryName;

    /**
     *二级行业名称（新版）
     */
    @SerializedName("second_industry_name")
    private String secondIndustryName;

    /**
     *审核拒绝原因
     */
    @SerializedName("reason")
    private String reason;

    /**
     *创建时间
     */
    @SerializedName("create_time")
    private String createTime;
}
