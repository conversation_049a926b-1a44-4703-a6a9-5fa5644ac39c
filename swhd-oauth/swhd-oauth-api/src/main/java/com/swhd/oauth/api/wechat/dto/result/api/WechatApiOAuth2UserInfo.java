package com.swhd.oauth.api.wechat.dto.result.api;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WechatApiOAuth2UserInfo对象")
public class WechatApiOAuth2UserInfo {

    @Schema(description = "用户的唯一标识")
    private String openid;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户的性别，值为1时是男性，值为2时是女性，值为0时是未知")
    private Integer sex;

    @Schema(description = "普通用户个人资料填写的城市")
    private String city;

    @Schema(description = "用户个人资料填写的省份")
    private String province;

    @Schema(description = "国家，如中国为CN")
    private String country;

    @Schema(description = "用户头像")
    private String headImgUrl;

    @Schema(description = "只有在用户将公众号绑定到微信开放平台账号后，才会出现该字段。")
    private String unionId;

    @Schema(description = "用户特权信息")
    private String[] privileges;

}
