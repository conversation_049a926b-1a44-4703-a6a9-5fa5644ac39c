package com.swhd.oauth.api.oceanengine.dto.param.agent.api.report;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/8/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "ReportRebateMaterialDownloadFileParam对象")
public class ReportRebateMaterialDownloadFileParam {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    @NotNull(message = "任务ID不能为空")
    @Schema(description = "任务ID")
    private String taskId;

}
