package com.swhd.oauth.api.oceanengine.dto.param.agent.api.advertising;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 巨量引擎广告项目列表查询参数
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "巨量引擎广告项目列表查询参数")
public class OceanengineAgentApiAdvertisingAdProjectListParam extends PageReq {

    @NotNull(message = "代理商ID不能为空")
    @Schema(description = "代理商ID")
    private Long agentId;

    @NotNull(message = "广告主ID不能为空")
    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "过滤条件")
    private Filtering filtering;

    @Schema(description = "查询字段列表")
    private List<String> fields;

    @Getter
    @Setter
    @Accessors(chain = true)
    @Schema(name = "OceanengineAgentApiAdvertisingAdProjectListParamFiltering")
    public static class Filtering {

        @Schema(description = "项目ID列表")
        private List<Long> projectIds;

        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = """
            投放模式，允许值：
            MANUAL 手动投放
            PROCEDURAL 自动投放
            """)
        private String deliveryMode;

        @Schema(description = """
            广告投放类型，允许值：
            FEED 信息流
            SEARCH 搜索广告
            """)
        private String landingType;

        @Schema(description = """
            营销目标，允许值：
            VIDEO_PROM_GOODS 短视频带货
            LIVE_PROM_GOODS 直播带货
            """)
        private String marketingGoal;

        @Schema(description = """
            广告类型，允许值：
            ALL 所有广告
            SEARCH 搜索广告
            """)
        private String adType;

        @Schema(description = """
            运营状态，允许值：
            ENABLE 启用
            DISABLE 暂停
            """)
        private String optStatus;

        @Schema(description = """
            项目状态，允许值：
            ALL 所有包含已删除
            DELETE 已删除
            ADVERTISER_OFFLINE 广告主账号异常
            DISABLED 已暂停
            ENABLE 已启用
            """)
        private String status;
    }
} 