package com.swhd.oauth.api.xhs.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.oauth.api.common.constant.ApiConstant;
import com.swhd.oauth.api.xhs.dto.param.account.XhsOauthAccountAddParam;
import com.swhd.oauth.api.xhs.dto.param.account.XhsOauthAccountPageParam;
import com.swhd.oauth.api.xhs.dto.param.account.XhsOauthAccountUpdateParam;
import com.swhd.oauth.api.xhs.dto.result.XhsOauthAccountResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-10
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = XhsOauthAccountClient.BASE_PATH)
public interface XhsOauthAccountClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/xhs/oauth/account";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<XhsOauthAccountResult>> page(@RequestBody @Valid XhsOauthAccountPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<XhsOauthAccountResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<XhsOauthAccountResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid XhsOauthAccountAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid XhsOauthAccountUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据kosUserId获取")
    @GetMapping("/getByKosUserId")
    Rsp<XhsOauthAccountResult> getByKosUserId(@RequestParam("kosUserId") String kosUserId);

    @Operation(summary = "根据kosUserId列表获取")
    @PostMapping("/listByKosUserIds")
    Rsp<List<XhsOauthAccountResult>> listByKosUserIds(@RequestBody @Valid @NotEmpty Collection<String> kosUserIds);

}
