package com.swhd.oauth.api.wecom.dto.param.oauth.kefu;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-01-02
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WecomOauthKefuAddParam对象")
public class WecomOauthKefuAddParam {

    @NotEmpty(message = "企业ID不能为空")
    @Schema(description = "企业ID")
    private String corpId;

    @NotEmpty(message = "客服ID不能为空")
    @Schema(description = "客服ID")
    private String kefuId;

    @NotEmpty(message = "客服名称不能为空")
    @Schema(description = "客服名称")
    private String kefuName;

    @NotEmpty(message = "客服头像不能为空")
    @Schema(description = "客服头像")
    private String kefuAvatar;

}
