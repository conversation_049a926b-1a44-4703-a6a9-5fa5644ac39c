package com.swhd.oauth.web.tenant.oceanengine.publisher;

import com.swhd.oauth.web.tenant.oceanengine.vo.result.OceanengineSpiCallbackEventResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Observable;

@Component
@AllArgsConstructor
@Slf4j
public class Publisher extends Observable {
    public void setMessage(OceanengineSpiCallbackEventResult message) {
        setChanged();
        notifyObservers(message);
    }
}
