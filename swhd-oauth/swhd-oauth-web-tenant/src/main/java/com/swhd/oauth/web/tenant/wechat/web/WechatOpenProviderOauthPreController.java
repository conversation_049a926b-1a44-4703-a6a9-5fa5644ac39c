package com.swhd.oauth.web.tenant.wechat.web;

import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.launcher.validation.RedirectUrl;
import com.swhd.oauth.api.wechat.client.WechatOpenProviderOauthPreClient;
import com.swhd.oauth.api.wechat.constant.WechatAppType;
import com.swhd.oauth.api.wechat.dto.param.open.provider.WechatOpenProviderOauthPreCreateUrlParam;
import com.swhd.oauth.api.wechat.dto.param.open.provider.WechatOpenProviderOauthPreScanCallbackParam;
import com.swhd.oauth.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/wechatOpenProviderOauthPre")
public class WechatOpenProviderOauthPreController {

    private final WechatOpenProviderOauthPreClient wechatOpenProviderOauthPreClient;

    @Operation(summary = "创建授权URL")
    @GetMapping(value = "/createUrl/{type}")
    public Rsp<String> createUrl(@PathVariable("type") WechatAppType type,
                                 @RequestParam @Valid @RedirectUrl String redirectUri) {
        WechatOpenProviderOauthPreCreateUrlParam param = new WechatOpenProviderOauthPreCreateUrlParam()
                .setRedirectUri(redirectUri)
                .setAuthType(type.getAuthType());
        return wechatOpenProviderOauthPreClient.createUrl(param);
    }

    @Operation(summary = "去授权")
    @GetMapping("/toOauth")
    @Auth(way = Auth.Way.ANONYMOUS)
    public String toOauth(@RequestParam String data) {
        // 微信授权会校验源referer域名
        return MessageFormat.format("<!DOCTYPE html><html><script>window.location.href=''{0}''</script></html>", data);
    }

    @Operation(summary = "授权后认证回调")
    @GetMapping("/scanCallback")
    @Auth(way = Auth.Way.ANONYMOUS)
    public ModelAndView scanCallback(@RequestParam("auth_code") String authorizationCode,
                                     @RequestParam Long tenantId,
                                     @RequestParam @Valid @RedirectUrl String redirectUri) {
        return TenantHolder.methodTenant(tenantId, () -> {
            WechatOpenProviderOauthPreScanCallbackParam param = new WechatOpenProviderOauthPreScanCallbackParam();
            param.setAuthorizationCode(authorizationCode);
            Rsp<?> res = wechatOpenProviderOauthPreClient.scanCallback(param);
            if (res.isSuccess()) {
                ModelAndView mv = new ModelAndView();
                mv.setView(new RedirectView(redirectUri));
                return mv;
            }
            log.error("open userAuthCallback fail: {}", res.getMsg());
            throw new ServiceException("第三方平台授权认证处理失败");
        });
    }

}
