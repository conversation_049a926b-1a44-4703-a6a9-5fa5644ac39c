package com.swhd.oauth.web.tenant.oceanengine.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.oauth.api.oceanengine.dto.param.riskControl.*;
import com.swhd.oauth.api.oceanengine.utils.OceanengineSpiAuthTokenUtil;
import com.swhd.oauth.web.tenant.common.constant.WebConstant;
import com.swhd.oauth.web.tenant.oceanengine.publisher.Publisher;
import com.swhd.oauth.web.tenant.oceanengine.publisher.*;
import com.swhd.oauth.web.tenant.oceanengine.vo.result.OceanengineSpiCallbackEventResult;
import com.swhd.oauth.web.tenant.oceanengine.vo.result.OceanengineSpiCallbackResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/5/10
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/oceanengineSpiCallback")
public class OceanengineSpiCallbackController implements InitializingBean {

    private final Publisher publisher;

    private final AdInfoPushSubscriber adInfoPushSubscriber;


    @Operation(summary = "巨量引擎SPI回调接口")
    @RequestMapping(value = "/callback", method = {RequestMethod.GET, RequestMethod.POST})
    @Auth(way = Auth.Way.ANONYMOUS)
    public OceanengineSpiCallbackResponse callback(HttpServletRequest request,
                                                   @RequestParam(value = "challenge", defaultValue = "0") int challenge,
                                                   @RequestParam(value = "event", defaultValue = "") String event) throws IOException {
        // 处理 verify 事件
        if (Objects.nonNull(event) && "verify_webhook".equals(event)) {
            OceanengineSpiCallbackResponse oceanengineSpiCallbackResult = new OceanengineSpiCallbackResponse(OceanengineSpiBaseResponse.ok(), challenge, "");
            log.info(oceanengineSpiCallbackResult.toString());
            return oceanengineSpiCallbackResult;
        }

//        String secretKey = riskControlProperties.getSpiSecretKey();
//        String secretKey="c77c35b98b174032893a19374a1e37b0";
        //9.27版本改为配置
        String secretKey = "f1d4aa06c5114e4faeb796bfb3040114";
        log.info("sercretKey:{}", secretKey);
        // 数据接收，验证消息
        OceanengineSpiAuthTokenUtil.InputStreamCacher cacher = new OceanengineSpiAuthTokenUtil.InputStreamCacher(request.getInputStream());
        boolean isValidToken = OceanengineSpiAuthTokenUtil.isValidToken(secretKey, cacher, request.getHeader("X-Open-Signature"));
        if (!isValidToken) {
            return new OceanengineSpiCallbackResponse(new OceanengineSpiBaseResponse(400, "invalid token"), 0, "");
        }
        String content = new String(OceanengineSpiAuthTokenUtil.readAsBytes(cacher));
        try {
            OceanengineSpiCallbackEventResult oceanengineSpiCallbackEventResult = new OceanengineSpiCallbackEventResult();
            JsonNode parse = JsonUtil.parse(content);
            JsonNode data = JsonUtil.parse(parse.get("data").textValue());
            oceanengineSpiCallbackEventResult.setData(data);
            oceanengineSpiCallbackEventResult.setMessageId(parse.get("message_id").textValue());
            oceanengineSpiCallbackEventResult.setSubscribeTaskId(parse.get("subscribe_task_id").textValue());
            oceanengineSpiCallbackEventResult.setServiceLabel(parse.get("service_label").textValue());
            //发布事件
            publisher.setMessage(oceanengineSpiCallbackEventResult);
        } catch (Exception e) {
            log.warn("巨量引擎SPI回调数据保存失败:{}", e.getMessage());
        } finally {
            log.info("巨量引擎SPI回调数据：{}", content);
        }
        return new OceanengineSpiCallbackResponse(OceanengineSpiBaseResponse.ok(), 0, content);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //订阅违规广告信息推送事件
        publisher.addObserver(adInfoPushSubscriber);
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Collaborator {
        @Schema(description = "优化师协助者id")
        private Long collaboratorId;
        @Schema(description = "优化师协助者姓名")
        private String collaboratorName;
    }
}
