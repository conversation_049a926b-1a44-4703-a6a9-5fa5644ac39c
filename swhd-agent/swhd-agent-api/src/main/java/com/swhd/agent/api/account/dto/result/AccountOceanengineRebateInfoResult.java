package com.swhd.agent.api.account.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineRebateResult对象")
public class AccountOceanengineRebateInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "分组id")
    private Long groupId;

    @Schema(description = "返点类型：1-行业，2-广告主公司，3-广告主账户")
    private Integer rebateType;

    @Schema(description = "返点标识：行业id/广告主账户id/广告主公司id")
    private Long rebateIdentity;

    @Schema(description = "返点生效日期")
    private LocalDate effectiveDate;

    @Schema(description = "返点比例")
    private BigDecimal rebateRatio;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
