package com.swhd.agent.api.account.dto.param.task;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-09-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineOptLogTaskUpdateParam对象")
public class AccountOceanengineOptLogTaskUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "计划ID")
    private Long promotionId;

    @Schema(description = "最后拉取时间")
    private LocalDateTime lastFetchTime;

    @Schema(description = "最后数据时间")
    private LocalDateTime lastDataTime;

    @Schema(description = "失败原因")
    private String failureReason;

}
