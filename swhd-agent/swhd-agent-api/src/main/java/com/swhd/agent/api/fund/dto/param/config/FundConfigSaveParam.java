package com.swhd.agent.api.fund.dto.param.config;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-12-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundConfigAddParam对象")
public class FundConfigSaveParam {

    @Schema(description = "渠道返点比例")
    private BigDecimal channelRebateRatio;

    @Schema(description = "服务订单编号前缀")
    private String orderNoPre;

}
