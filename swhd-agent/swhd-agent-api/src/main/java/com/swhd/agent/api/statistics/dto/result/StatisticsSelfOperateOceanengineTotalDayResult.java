package com.swhd.agent.api.statistics.dto.result;

import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-02-02
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsSelfOperateOceanengineTotalDayResult对象")
public class StatisticsSelfOperateOceanengineTotalDayResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "自运营消耗(单位元)")
    private BigDecimal selfOperateConsume;

    @Schema(description = "自运营消耗占比")
    private BigDecimal selfOperateConsumeRatio;

    @Schema(description = "自运营账户数")
    private Integer selfOperateAccountNum;

    @Schema(description = "自运营账户数占比")
    private BigDecimal selfOperateAccountNumRatio;

    @Schema(description = "总消耗(单位元)")
    private BigDecimal totalConsume;

    @Schema(description = "总账户数")
    private Integer totalAccountNum;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
