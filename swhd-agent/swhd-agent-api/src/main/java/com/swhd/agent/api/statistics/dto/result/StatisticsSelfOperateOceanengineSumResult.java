package com.swhd.agent.api.statistics.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/2/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsSelfOperateOceanengineCompanyMonthResult对象")
public class StatisticsSelfOperateOceanengineSumResult {

    @Schema(description = "自运营消耗(单位元)")
    private BigDecimal selfOperateConsume;

    @Schema(description = "自运营消耗占比")
    private BigDecimal selfOperateConsumeRatio;

    @Schema(description = "总消耗(单位元)")
    private BigDecimal totalConsume;

    public static StatisticsSelfOperateOceanengineSumResult zero() {
        StatisticsSelfOperateOceanengineSumResult sumResult = new StatisticsSelfOperateOceanengineSumResult();
        sumResult.setSelfOperateConsume(BigDecimal.ZERO);
        sumResult.setSelfOperateConsumeRatio(BigDecimal.ZERO);
        sumResult.setTotalConsume(BigDecimal.ZERO);
        return sumResult;
    }

}
