package com.swhd.agent.api.account.dto.param.record;

import com.swj.magiccube.api.SortableReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerCreditChangeRecordListParam对象")
public class AccountOceanengineCustomerCreditChangeRecordListParam extends SortableReq implements Serializable {

    @Schema(description = "授信ID")
    private Long selfRechargeCreditId;

    @Schema(description = "变更类型 CLOSE-关闭 OPEN-开启 INCREASE-提额 CONSUME-客户消耗 CONSUME_ROLLBACK-客户消耗回滚")
    private List<String> changeTypes;

    @Schema(description = "充值明细ID")
    private Long rechargeDetailId;

    @Schema(description = "操作时间")
    private List<LocalDate> createTimeBetween;

    @Schema(description = "操作人")
    private Long creatorId;

}
