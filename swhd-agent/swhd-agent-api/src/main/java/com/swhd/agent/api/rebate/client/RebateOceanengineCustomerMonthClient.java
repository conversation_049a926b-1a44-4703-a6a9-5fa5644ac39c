package com.swhd.agent.api.rebate.client;

import com.swhd.agent.api.common.constant.ApiConstant;
import com.swhd.agent.api.rebate.dto.param.oceanengine.customer.RebateOceanengineCustomerMonthPageParam;
import com.swhd.agent.api.rebate.dto.param.oceanengine.customer.RebateOceanengineCustomerSumPageParam;
import com.swhd.agent.api.rebate.dto.result.RebateOceanengineCustomerMonthResult;
import com.swhd.agent.api.rebate.dto.result.RebateOceanengineCustomerSumResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-16
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = RebateOceanengineCustomerMonthClient.BASE_PATH)
public interface RebateOceanengineCustomerMonthClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/rebate/oceanengine/customer/month";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<RebateOceanengineCustomerMonthResult>> page(@RequestBody @Valid RebateOceanengineCustomerMonthPageParam param);

    @Operation(summary = "客户统计分页查询")
    @PostMapping("/customerSumPage")
    Rsp<PageResult<RebateOceanengineCustomerSumResult>> customerSumPage(@RequestBody @Valid RebateOceanengineCustomerSumPageParam param);

    @Operation(summary = "客户统计列表查询")
    @PostMapping("/customerSumList")
    Rsp<List<RebateOceanengineCustomerSumResult>> customerSumList(@RequestBody @Valid RebateOceanengineCustomerSumPageParam param);

}
