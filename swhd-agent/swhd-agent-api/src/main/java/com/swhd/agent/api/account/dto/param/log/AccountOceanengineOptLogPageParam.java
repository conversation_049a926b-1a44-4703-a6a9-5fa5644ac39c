package com.swhd.agent.api.account.dto.param.log;

import com.swj.magiccube.api.PageReq;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-09-25
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineOptLogPageParam对象")
public class AccountOceanengineOptLogPageParam extends PageReq {

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "计划ID")
    private Long promotionId;

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "日志ID")
    private String logId;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "操作时间")
    private LocalDateTime opTime;

    @Schema(description = "操作IP")
    private String opIp;

    @Schema(description = "操作内容标题")
    private String contentTitle;

    @Schema(description = "操作对象类型")
    private String objectType;

    @Schema(description = "操作对象id")
    private Long objectId;

    @Schema(description = "操作对象名称")
    private String objectName;

    @Schema(description = "是否代理商IP：0-否，1-是")
    private Boolean agentIp;

}
