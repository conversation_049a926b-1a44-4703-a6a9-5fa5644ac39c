package com.swhd.agent.api.account.dto.result;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自助充值配置结果对象
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeCreditConfigResult对象")
public class AccountOceanengineCustomerRechargeCreditConfigResult {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "客户ID")
    private Long customId;

    @Schema(description = "客户名称")
    private String customName;

    @Schema(description = "是否开启自助充值")
    private Boolean allowSelfRecharge;

    @Schema(description = "广告账号数")
    private Integer accountCount;

    @Schema(description = "授信额度")
    private BigDecimal creditLimit;

    @Schema(description = "已用授信额度")
    private BigDecimal usedAmount;

    @Schema(description = "剩余授信额度")
    private BigDecimal remainingAmount;

}
