package com.swhd.agent.api.fund.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-01-08
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomOrderConfirmResult对象")
public class FundOceanengineCustomOrderConfirmResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "确认文件oss key")
    private String confirmFileOssKey;

    @Schema(description = "确认的手机号")
    private String confirmMobile;

    @Schema(description = "确认的ip")
    private String confirmIp;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
