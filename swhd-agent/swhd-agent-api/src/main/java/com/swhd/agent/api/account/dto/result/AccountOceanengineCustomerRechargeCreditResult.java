package com.swhd.agent.api.account.dto.result;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeCreditResult对象")
public class AccountOceanengineCustomerRechargeCreditResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "客户ID")
    private Long customId;

    @Schema(description = "是否允许自助充值 0-不允许 1-允许")
    private Integer allowSelfRecharge;

    @Schema(description = "授信额度")
    private BigDecimal creditLimit;

    @Schema(description = "已使用额度")
    private BigDecimal usedAmount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
