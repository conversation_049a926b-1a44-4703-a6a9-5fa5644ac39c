package com.swhd.agent.api.fund.dto.param.recharge;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomRechargeAddTransferRecord对象")
public class FundOceanengineCustomRechargeAddTransferRecord {

    @NotEmpty(message = "转账编号不能为空")
    @Schema(description = "巨量引擎转账编号")
    private String oceanengineTransactionSeq;

    @NotNull(message = "代理商返点比例不能为空")
    @DecimalMin(value = "0", message = "代理商返点比例不能小于0")
    @Schema(description = "代理商返点比例")
    private BigDecimal agentRebateRatio;

    @NotNull(message = "代理商总收款返点比例不能为空")
    @DecimalMin(value = "0", message = "代理商总收款返点比例不能小于0")
    @Schema(description = "代理商总收款返点比例")
    private BigDecimal agentTotalRebateRatio;

    @DecimalMin(value = "0", message = "渠道返点比例不能小于0")
    @Schema(description = "渠道返点比例")
    private BigDecimal rechargeChannelRebateRatio;

}
