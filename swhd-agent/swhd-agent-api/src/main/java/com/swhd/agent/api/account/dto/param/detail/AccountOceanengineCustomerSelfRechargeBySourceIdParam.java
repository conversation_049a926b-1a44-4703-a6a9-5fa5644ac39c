package com.swhd.agent.api.account.dto.param.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 根据sourceId自助充值参数类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "根据sourceId自助充值参数")
public class AccountOceanengineCustomerSelfRechargeBySourceIdParam {

    @Schema(description = "充值源ID")
    @NotNull(message = "充值源ID不能为空")
    private Long sourceId;

    @Schema(description = "广告主账号ID")
    @NotNull(message = "广告主账号ID不能为空")
    private Long advertiserId;

    @Schema(description = "充值账目金额")
    @NotNull(message = "充值账目金额不能为空")
    @DecimalMin(value = "0.01", message = "充值账目金额不能小于0.01")
    private BigDecimal rechargeAccountAmount;

    @Schema(description = "录入方式 WECHAT_BOT-微信机器人 MANUAL-手动录入 H5-h5页面")
    @NotBlank(message = "录入方式不能为空")
    private String inputMethod;

    @Schema(description = "转账类型 RECHARGE_IN-充值转入 REFUND_OUT-退款转出")
    @NotBlank(message = "转账类型不能为空")
    private String capitalType;

}
