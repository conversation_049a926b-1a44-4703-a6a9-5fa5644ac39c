package com.swhd.agent.api.statistics.dto.result;

import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdvertiserOceanengineCityDayResult对象")
public class StatisticsAdvertiserOceanengineCityDayResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "统计日期")
    private LocalDate statisticDate;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "总消耗")
    private BigDecimal statCost;

    @Schema(description = "展示次数")
    private Integer showCnt;

    @Schema(description = "点击次数")
    private Integer clickCnt;

    @Schema(description = "转化次数")
    private Integer convertCnt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
