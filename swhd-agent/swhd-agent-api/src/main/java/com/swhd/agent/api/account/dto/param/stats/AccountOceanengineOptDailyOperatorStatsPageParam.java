package com.swhd.agent.api.account.dto.param.stats;

import com.swj.magiccube.api.PageReq;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineOptDailyOperatorStatsPageParam对象")
public class AccountOceanengineOptDailyOperatorStatsPageParam extends PageReq {

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "操作日期")
    private LocalDate statDate;

    @Schema(description = "统计表ID")
    private Long dailyStatsId;

    @Schema(description = "操作数")
    private Integer operationCount;

    @Schema(description = "操作人")
    private String operator;

}
