package com.swhd.agent.api.account.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeSourceResult对象")
public class AccountOceanengineCustomerRechargeSourceResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "充值源标题")
    private String title;

    @Schema(description = "客户ID列表")
    private List<Long> customerIds;

    @Schema(description = "状态 0-关闭 1-开启")
    private Integer state;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
