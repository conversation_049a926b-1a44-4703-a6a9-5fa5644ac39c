package com.swhd.agent.api.account.dto.param.company;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023-12-08
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCompanyAddParam对象")
@EqualsAndHashCode(callSuper = false, onlyExplicitlyIncluded = true)
public class AccountOceanengineCompanyAddParam {

    @EqualsAndHashCode.Include
    @NotNull(message = "公司id不能为空")
    @Schema(description = "公司id")
    private Long companyId;

    @NotEmpty(message = "公司名不能为空")
    @Schema(description = "公司名")
    private String companyName;

}
