package com.swhd.agent.api.account.dto.param.source;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeSourceUpdateParam对象")
public class AccountOceanengineCustomerRechargeSourceUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "充值源标题")
    private String title;

    @Schema(description = "客户ID列表")
    private List<Long> customerIds;

    @Schema(description = "状态 0-关闭 1-开启")
    private Integer state;

}
