package com.swhd.agent.api.fund.dto.result;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-01-16
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomOrderChannelResult对象")
public class FundOceanengineCustomOrderChannelResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "订单id")
    private Long orderId;

    @Schema(description = "客户渠道id：tagent_custom_info#id")
    private Long customChannelId;

    @Schema(description = "渠道佣金金额(单位元)")
    private BigDecimal channelCommissionAmount;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
