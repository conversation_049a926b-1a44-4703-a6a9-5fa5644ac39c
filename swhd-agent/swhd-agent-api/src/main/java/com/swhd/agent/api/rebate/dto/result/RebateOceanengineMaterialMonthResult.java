package com.swhd.agent.api.rebate.dto.result;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RebateOceanengineMaterialMonthResult对象")
public class RebateOceanengineMaterialMonthResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate statisticDate;

    @Schema(description = "素材id")
    private Long materialId;

    @Schema(description = "是否有效优质")
    private Boolean isValidHighQuality;

    @Schema(description = "是否有效首发")
    private Boolean isValidFirstEffective;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
