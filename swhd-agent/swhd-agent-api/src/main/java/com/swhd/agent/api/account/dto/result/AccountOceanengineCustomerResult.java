package com.swhd.agent.api.account.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-17
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerResult对象")
public class AccountOceanengineCustomerResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "客户id")
    private Long customerId;

    @Schema(description = "客户名")
    private String customerName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
