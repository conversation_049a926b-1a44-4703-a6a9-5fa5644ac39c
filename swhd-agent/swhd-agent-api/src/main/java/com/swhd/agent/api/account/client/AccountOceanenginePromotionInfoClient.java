package com.swhd.agent.api.account.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoAddParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoPageParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanenginePromotionInfoResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-07
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanenginePromotionInfoClient.BASE_PATH)
public interface AccountOceanenginePromotionInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/promotion/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanenginePromotionInfoResult>> page(@RequestBody @Valid AccountOceanenginePromotionInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanenginePromotionInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanenginePromotionInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid AccountOceanenginePromotionInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid AccountOceanenginePromotionInfoUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
