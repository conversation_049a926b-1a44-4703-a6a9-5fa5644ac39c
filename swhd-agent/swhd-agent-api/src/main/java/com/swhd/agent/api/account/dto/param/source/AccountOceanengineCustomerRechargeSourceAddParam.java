package com.swhd.agent.api.account.dto.param.source;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeSourceAddParam对象")
public class AccountOceanengineCustomerRechargeSourceAddParam {

    @Schema(description = "充值源标题")
    private String title;

    @Schema(description = "客户ID列表")
    private List<Long> customerIds;

    @Schema(description = "状态 0-关闭 1-开启")
    private Integer state;

}
