package com.swhd.agent.api.fund.dto.param.order;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomOrderPageParam对象")
public class FundOceanengineCustomOrderPageParam extends PageReq {

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customId;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "是否千川品牌：0-否，1-是")
    private Boolean qcBrand;

    @Schema(description = "确认状态：0-未确认，1-已确认")
    private Integer confirmState;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private List<LocalDate> createTimeBetween;

}
