package com.swhd.agent.api.account.wrapper;

import cn.hutool.core.util.ObjectUtil;
import com.swhd.agent.api.account.client.AccountOceanengineInfoClient;
import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.magiccube.core.wrapper.ApiWrapperUtil;
import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/3
 */
public class AccountOceanengineInfoWrapper extends BaseLongApiWrapper<AccountOceanengineInfoResult, AccountOceanengineInfoResult> {

    @Getter
    private static final AccountOceanengineInfoWrapper instance = new AccountOceanengineInfoWrapper();

    private final AccountOceanengineInfoClient accountOceanengineInfoClient;

    private AccountOceanengineInfoWrapper() {
        this.accountOceanengineInfoClient = SpringUtil.getBean(AccountOceanengineInfoClient.class);
    }

    @Override
    protected Rsp<AccountOceanengineInfoResult> getRspById(Long id) {
        return accountOceanengineInfoClient.getById(id);
    }

    @Override
    protected Rsp<List<AccountOceanengineInfoResult>> getRspByIds(Collection<Long> ids) {
        return accountOceanengineInfoClient.listByIds(ids);
    }

    @Override
    protected Long getListId(AccountOceanengineInfoResult accountOceanengineInfoResult) {
        return accountOceanengineInfoResult.getId();
    }

    /**
     * 列表设置信息(advertiserId)
     *
     * @param list     list
     * @param function 获取advertiserId的function
     * @param consumer 处理consumer
     * @param <T>      Rsp对象的泛型
     * @return List<T>
     */
    public <T> List<T> setListByAdvertiserId(List<T> list, Function<T, Long> function, BiConsumer<T, AccountOceanengineInfoResult> consumer) {
        if (ObjectUtil.isEmpty(list)) {
            return list;
        }
        List<Long> advertiserIds = list.stream().map(function).filter(ObjectUtil::isNotEmpty).distinct().collect(Collectors.toList());
        if (ObjectUtil.isEmpty(advertiserIds)) {
            return list;
        }
        List<AccountOceanengineInfoResult> accountList = ApiWrapperUtil.executeBatch(advertiserIds,
                accountOceanengineInfoClient::listByAdvertiserIds);
        if (ObjectUtil.isNotEmpty(accountList)) {
            Map<Long, AccountOceanengineInfoResult> resultMap = accountList.stream()
                    .collect(Collectors.toMap(AccountOceanengineInfoResult::getAdvertiserId, s -> s));
            list.stream()
                    .filter(t -> resultMap.containsKey(function.apply(t)))
                    .forEach(t -> consumer.accept(t, resultMap.get(function.apply(t))));
        }
        return list;
    }


}
