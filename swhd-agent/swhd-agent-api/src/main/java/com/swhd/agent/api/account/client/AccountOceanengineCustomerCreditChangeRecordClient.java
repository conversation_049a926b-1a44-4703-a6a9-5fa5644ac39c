package com.swhd.agent.api.account.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordAddParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordListParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerCreditChangeRecordResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanengineCustomerCreditChangeRecordClient.BASE_PATH)
public interface AccountOceanengineCustomerCreditChangeRecordClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/customer/credit/change/record";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResult>> page(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordPageParam param);

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    Rsp<List<AccountOceanengineCustomerCreditChangeRecordResult>> list(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordListParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanengineCustomerCreditChangeRecordResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanengineCustomerCreditChangeRecordResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
