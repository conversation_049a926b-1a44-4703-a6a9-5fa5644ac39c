package com.swhd.agent.api.account.dto.param.detail;

import com.swj.magiccube.api.SortableReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeDetailListParam对象")
public class AccountOceanengineCustomerRechargeDetailListParam extends SortableReq implements Serializable {

    @Schema(description = "广告主账号ID")
    private Long advertiserId;

    @Schema(description = "客户ID")
    private Long customId;

    @Schema(description = "操作时间")
    private List<LocalDate> executeTimeBetween;

    @Schema(description = "录入方式 WECHAT_BOT-微信机器人 MANUAL-手动录入 H5-h5页面")
    private List<String> inputMethods;

    @Schema(description = "转账类型 RECHARGE_IN-充值转入 REFUND_OUT-退款转出")
    private List<String> capitalTypes;

    @Schema(description = "执行状态  PENDING-待处理 PROCESSING-处理中 FAILED-处理失败 SUCCESS-处理成功")
    private List<String> executeStatuses;

    @Schema(description = "转账单号")
    private String transferSerial;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "来源ID")
    private Long sourceId;

}
