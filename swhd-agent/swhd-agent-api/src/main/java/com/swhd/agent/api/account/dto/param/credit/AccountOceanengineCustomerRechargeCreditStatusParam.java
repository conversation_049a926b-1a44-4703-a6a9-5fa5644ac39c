package com.swhd.agent.api.account.dto.param.credit;

import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自助充值状态设置参数对象
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerRechargeCreditStatusParam对象")
public class AccountOceanengineCustomerRechargeCreditStatusParam {

    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customId;

    @Schema(description = "是否允许自助充值")
    @NotNull(message = "是否允许自助充值不能为空")
    private Boolean allowSelfRecharge;

    @Schema(description = "授信额度")
    private BigDecimal creditLimit;

}
