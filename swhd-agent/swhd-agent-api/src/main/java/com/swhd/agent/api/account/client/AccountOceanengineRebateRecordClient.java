package com.swhd.agent.api.account.client;

import com.swhd.agent.api.account.dto.param.rebate.record.AccountOceanengineRebateRecordPageParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineRebateRecordResult;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-08
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanengineRebateRecordClient.BASE_PATH)
public interface AccountOceanengineRebateRecordClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/rebate/record";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanengineRebateRecordResult>> page(@RequestBody @Valid AccountOceanengineRebateRecordPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanengineRebateRecordResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanengineRebateRecordResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据广告主账户id列表获取最新返点")
    @PostMapping("/listLatestRebateByAdvertiserIds")
    Rsp<List<AccountOceanengineRebateRecordResult>> listLatestRebateByAdvertiserIds(
            @RequestBody @Valid @NotEmpty Collection<Long> advertiserIds);

}
