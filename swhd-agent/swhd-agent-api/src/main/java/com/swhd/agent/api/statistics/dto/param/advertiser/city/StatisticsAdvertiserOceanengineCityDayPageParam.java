package com.swhd.agent.api.statistics.dto.param.advertiser.city;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdvertiserOceanengineCityDayPageParam对象")
public class StatisticsAdvertiserOceanengineCityDayPageParam extends PageReq {

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "统计日期")
    private List<LocalDate> statisticDateBetween;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

}
