package com.swhd.agent.api.statistics.dto.param.custom.account;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-31
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsCustomAccountOceanengineBatchAddParam对象")
public class StatisticsCustomAccountOceanengineBatchAddParam {

    @NotNull(message = "分组ID不能为空")
    @Schema(description = "分组ID")
    private Long groupId;

    @NotEmpty(message = "广告主ID不能为空")
    @Schema(description = "广告主ID")
    private List<Long> advertiserIds;

}
