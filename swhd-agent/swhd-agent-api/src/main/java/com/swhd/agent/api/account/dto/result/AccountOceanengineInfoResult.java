package com.swhd.agent.api.account.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineInfoResult对象")
public class AccountOceanengineInfoResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private String tenantId;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "广告主账户id")
    private Long advertiserId;

    @Schema(description = "广告主账户账户名")
    private String advertiserName;

    @Schema(description = "广告主公司id")
    private Long advertiserCompanyId;

    @Schema(description = "广告主公司名")
    private String advertiserCompanyName;

    @Schema(description = "广告主客户id")
    private Long advertiserCustomerId;

    @Schema(description = "一级行业id")
    private Long firstIndustryId;

    @Schema(description = "二级行业id")
    private Long secondIndustryId;

    @Schema(description = "账户报备类型：DECREASE_QUANTITY-走量，EMPTY-无标签，INCREASE_QUANTITY 收量，SELF_OPERATION 自运营")
    private String selfOperationTag;

    @Schema(description = "账户类型id：tagent_account_type#id")
    private Long accountTypeId;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customId;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customChannelId;

    @Schema(description = "优化师")
    private Long optimizationEngineer;

    @Schema(description = "巨量引擎账户的创建时间")
    private LocalDateTime oceanengineCreateTime;

    @Schema(description = "是否代运营")
    private Boolean managedByAgent;

    @Schema(description = "是否使用VPN")
    private Boolean usingVpn;

    @Schema(description = "商务人员列表")
    private List<Long> businessPersonnelList;

    @Schema(description = "渠道返点")
    private BigDecimal channelRebate;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
