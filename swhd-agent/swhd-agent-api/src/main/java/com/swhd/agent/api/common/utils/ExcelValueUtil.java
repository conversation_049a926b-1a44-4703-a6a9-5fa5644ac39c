package com.swhd.agent.api.common.utils;

import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.tool.DateTimeUtil;
import com.swhd.magiccube.tool.Func;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@UtilityClass
public class ExcelValueUtil {

    public static Long parseLong(int index, String name, String value) {
        if (Func.isEmpty(value)) {
            throw new ServiceException(String.format("第%s行数据，%s为空", index, name));
        }
        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            throw new ServiceException(String.format("第%s行数据，%s格式错误", index, name));
        }
    }

    public static BigDecimal parseBigDecimal(int index, String name, String value) {
        if (Func.isEmpty(value)) {
            throw new ServiceException(String.format("第%s行数据，%s为空", index, name));
        }
        try {
            return new BigDecimal(value);
        } catch (Exception e) {
            throw new ServiceException(String.format("第%s行数据，%s格式错误", index, name));
        }
    }

    public static String notEmptyValue(int index, String name, String value) {
        if (Func.isEmpty(value)) {
            throw new ServiceException(String.format("第%s行数据，%s为空", index, name));
        }
        return value;
    }

    public static LocalDateTime parseLocalDateTime(int index, String name, String value) {
        return parseLocalDateTime(index, name, value, DateTimeUtil.DATETIME_FORMAT);
    }

    public static LocalDateTime parseLocalDateTime(int index, String name, String value, DateTimeFormatter formatter) {
        if (Func.isEmpty(value)) {
            throw new ServiceException(String.format("第%s行数据，%s为空", index, name));
        }
        try {
            return DateTimeUtil.parseDateTime(value, formatter);
        } catch (Exception e) {
            throw new ServiceException(String.format("第%s行数据，%s格式错误", index, name));
        }
    }

    public static LocalDate parseLocalDate(int index, String name, String value) {
        return parseLocalDate(index, name, value, DateTimeUtil.DATE_FORMAT);
    }

    public static LocalDate parseLocalDate(int index, String name, String value, DateTimeFormatter formatter) {
        if (Func.isEmpty(value)) {
            throw new ServiceException(String.format("第%s行数据，%s为空", index, name));
        }
        try {
            return DateTimeUtil.parseDate(value, formatter);
        } catch (Exception e) {
            throw new ServiceException(String.format("第%s行数据，%s格式错误", index, name));
        }
    }

}
