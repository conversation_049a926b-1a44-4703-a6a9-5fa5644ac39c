package com.swhd.agent.api.statistics.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-02-02
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsSelfOperateOceanengineCompanyMonthResult对象")
public class StatisticsSelfOperateOceanengineCompanyMonthResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "自运营消耗(单位元)")
    private BigDecimal selfOperateConsume;

    @Schema(description = "自运营消耗占比")
    private BigDecimal selfOperateConsumeRatio;

    @Schema(description = "总消耗(单位元)")
    private BigDecimal totalConsume;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
