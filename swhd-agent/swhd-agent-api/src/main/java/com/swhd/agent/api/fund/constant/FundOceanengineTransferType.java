package com.swhd.agent.api.fund.constant;

import com.swhd.magiccube.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/12/8
 */
@Getter
@AllArgsConstructor
public enum FundOceanengineTransferType {

    /**
     * 加款
     */
    ADD(0, "加款"),
    /**
     * 退款
     */
    REFUND(1, "退款"),
    ;

    private final int type;

    private final String name;

    public static Optional<FundOceanengineTransferType> ofOptional(Integer type) {
        return Arrays.stream(values()).filter(v -> Objects.equals(type, v.getType())).findAny();
    }

    public static FundOceanengineTransferType of(Integer type) {
        return ofOptional(type).orElseThrow(() -> new ServiceException("未知的转账类型"));
    }

}
