package com.swhd.agent.api.account.dto.param.rebate.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/1/3
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineRebateAddItem对象")
public class AccountOceanengineRebateInfoEditItem {

    @NotNull(message = "返点生效日期不能为空")
    @Schema(description = "返点生效日期")
    private LocalDate effectiveDate;

    @NotNull(message = "返点比例不能为空")
    @Schema(description = "返点比例")
    private BigDecimal rebateRatio;

}
