package com.swhd.agent.api.statistics.dto.param.advertiser.province;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-16
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdvertiserOceanengineProvinceSumPageParam对象")
public class StatisticsAdvertiserOceanengineProvinceSumPageParam extends PageReq {

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "广告主ID")
    private List<Long> advertiserIds;

    @NotEmpty(message = "统计日期不能为空")
    @Size(min = 2, max = 2, message = "统计日期长度不符")
    @Schema(description = "统计日期")
    private List<LocalDate> statisticDateBetween;

}
