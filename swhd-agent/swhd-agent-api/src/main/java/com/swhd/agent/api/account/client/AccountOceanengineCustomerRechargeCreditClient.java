package com.swhd.agent.api.account.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.agent.api.common.constant.ApiConstant;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditAddParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditPageParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditStatusParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditConfigResult;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AccountOceanengineCustomerRechargeCreditClient.BASE_PATH)
public interface AccountOceanengineCustomerRechargeCreditClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/account/oceanengine/customer/recharge/credit";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<AccountOceanengineCustomerRechargeCreditResult>> page(@RequestBody @Valid AccountOceanengineCustomerRechargeCreditPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<AccountOceanengineCustomerRechargeCreditResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<AccountOceanengineCustomerRechargeCreditResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid AccountOceanengineCustomerRechargeCreditAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid AccountOceanengineCustomerRechargeCreditUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "根据客户ID获取自助充值配置")
    @GetMapping("/getByCustomId")
    Rsp<AccountOceanengineCustomerRechargeCreditConfigResult> getByCustomId(@RequestParam("customId") Long customId);

    @Operation(summary = "根据客户ID关闭或开启设置额度")
    @PostMapping("/updateCreditStatus")
    Rsp<Void> updateCreditStatus(@RequestBody @Valid AccountOceanengineCustomerRechargeCreditStatusParam param);

}
