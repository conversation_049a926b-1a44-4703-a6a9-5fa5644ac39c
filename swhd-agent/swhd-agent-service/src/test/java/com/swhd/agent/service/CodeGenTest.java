package com.swhd.agent.service;

import com.swhd.magiccube.test.codegen.CodeGenerator;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
public class CodeGenTest {

    @Test
    public void genTest() {
        new CodeGenerator()
                .dataSourceConfig("**************************************************************************************************************************************", "test_mj_all", "test_mj_all321")
                .parentPackage("com.swhd.agent")
                .columnPrefix("tagent_")
                .moduleName("account")
                .authorByGitUserEmail()
                .mapperXml(false)
                .gen("tagent_account_oceanengine_customer_recharge_source");
    }

}
