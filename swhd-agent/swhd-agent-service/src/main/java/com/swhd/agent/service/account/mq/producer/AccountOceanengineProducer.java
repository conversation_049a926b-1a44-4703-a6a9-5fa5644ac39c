package com.swhd.agent.service.account.mq.producer;

import com.swhd.agent.api.account.dto.message.AccountOceanengineCustomerSyncMessage;
import com.swj.magiccube.stream.messaging.MagiccubeMessageBuilder;
import com.swj.magiccube.stream.utils.StreamUtil;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @since 2025/4/17
 */
@UtilityClass
public class AccountOceanengineProducer {

    /**
     * 同步巨量引擎账户客户
     *
     * @param message 消息
     */
    public void customerSync(AccountOceanengineCustomerSyncMessage message) {
        StreamUtil.send("agentAccountOceanengineCustomerSync-out-0", MagiccubeMessageBuilder
                .withPayload(message)
                .build());
    }

}
