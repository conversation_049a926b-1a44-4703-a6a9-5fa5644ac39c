package com.swhd.agent.service.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditPageParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditStatusParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditConfigResult;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeCredit;
import com.swj.magiccube.api.Rsp;

/**
 * 自助充值授信表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface AccountOceanengineCustomerRechargeCreditService extends IBaseHdService<AccountOceanengineCustomerRechargeCredit> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<AccountOceanengineCustomerRechargeCredit> page(AccountOceanengineCustomerRechargeCreditPageParam param);

	/**
	 * 根据客户ID获取自助充值配置
	 *
	 * @param customId 客户ID
	 * @return AccountOceanengineCustomerRechargeCreditConfigResult
	 */
	AccountOceanengineCustomerRechargeCreditConfigResult getByCustomId(Long customId);

	/**
	 * 根据客户ID关闭或开启设置额度
	 *
	 * @param param 参数
	 * @return Rsp
	 */
	Rsp<Void> updateCreditStatus(AccountOceanengineCustomerRechargeCreditStatusParam param);

}
