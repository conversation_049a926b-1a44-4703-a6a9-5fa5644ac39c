package com.swhd.agent.service.fund.web;

import com.swhd.agent.api.fund.client.FundOceanengineCustomOrderTransferClient;
import com.swhd.agent.api.fund.dto.param.order.FundOceanengineCustomOrderAutoGetTransferRecordParam;
import com.swhd.agent.api.fund.dto.result.FundOceanengineCustomOrderTransferResult;
import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordResult;
import com.swhd.agent.service.fund.entity.FundOceanengineCustomOrderTransfer;
import com.swhd.agent.service.fund.entity.FundOceanengineTransferRecord;
import com.swhd.agent.service.fund.service.FundOceanengineCustomOrderTransferService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-12-05
 */
@RestController
@AllArgsConstructor
@RequestMapping(FundOceanengineCustomOrderTransferClient.BASE_PATH)
public class FundOceanengineCustomOrderTransferController implements FundOceanengineCustomOrderTransferClient {

    private final FundOceanengineCustomOrderTransferService fundOceanengineCustomOrderTransferService;

    @Override
    public Rsp<List<FundOceanengineCustomOrderTransferResult>> listByOrderId(Long customOrderId) {
        List<FundOceanengineCustomOrderTransfer> list = fundOceanengineCustomOrderTransferService.listByOrderId(customOrderId);
        return RspHd.data(Func.copy(list, FundOceanengineCustomOrderTransferResult.class));
    }

    @Override
    public Rsp<List<FundOceanengineCustomOrderTransferResult>> listByOrderIds(Collection<Long> customOrderIds) {
        List<FundOceanengineCustomOrderTransfer> list = fundOceanengineCustomOrderTransferService.listByOrderIds(customOrderIds);
        return RspHd.data(Func.copy(list, FundOceanengineCustomOrderTransferResult.class));
    }

    @Override
    public Rsp<List<FundOceanengineCustomOrderTransferResult>> listByTransactionSeq(String oceanengineTransactionSeq) {
        List<FundOceanengineCustomOrderTransfer> list = fundOceanengineCustomOrderTransferService
                .listByTransactionSeq(oceanengineTransactionSeq);
        return RspHd.data(Func.copy(list, FundOceanengineCustomOrderTransferResult.class));
    }

    @Override
    public Rsp<List<FundOceanengineCustomOrderTransferResult>> listByTransactionSeqList(Collection<String> oceanengineTransactionSeqList) {
        List<FundOceanengineCustomOrderTransfer> list = fundOceanengineCustomOrderTransferService
                .listByTransactionSeqList(oceanengineTransactionSeqList);
        return RspHd.data(Func.copy(list, FundOceanengineCustomOrderTransferResult.class));
    }

    @Override
    public Rsp<List<FundOceanengineTransferRecordResult>> autoGetTransferRecord(FundOceanengineCustomOrderAutoGetTransferRecordParam param) {
        List<FundOceanengineTransferRecord> list = fundOceanengineCustomOrderTransferService.autoGetTransferRecord(param);
        return RspHd.data(Func.copy(list, FundOceanengineTransferRecordResult.class));
    }

}
