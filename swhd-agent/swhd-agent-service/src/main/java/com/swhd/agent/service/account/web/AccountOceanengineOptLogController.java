package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.dto.param.log.AccountOceanengineOptLogPageStatisticsParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineOptLogStatisticsResult;
import com.swhd.agent.service.account.properties.AccountProperties;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.log.AccountOceanengineOptLogAddParam;
import com.swhd.agent.api.account.dto.param.log.AccountOceanengineOptLogPageParam;
import com.swhd.agent.api.account.dto.param.log.AccountOceanengineOptLogUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineOptLogResult;
import com.swhd.agent.api.account.client.AccountOceanengineOptLogClient;
import com.swhd.agent.service.account.entity.AccountOceanengineOptLog;
import com.swhd.agent.service.account.service.AccountOceanengineOptLogService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-09-25
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineOptLogClient.BASE_PATH)
public class AccountOceanengineOptLogController implements AccountOceanengineOptLogClient {

    private final AccountOceanengineOptLogService accountOceanengineOptLogService;
    private final AccountProperties accountProperties;

    @Override
    public Rsp<PageResult<AccountOceanengineOptLogResult>> page(AccountOceanengineOptLogPageParam param) {
        IPage<AccountOceanengineOptLog> iPage = accountOceanengineOptLogService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineOptLogResult.class));
    }

    @Override
    public Rsp<AccountOceanengineOptLogResult> getById(Long id) {
        AccountOceanengineOptLog entity = accountOceanengineOptLogService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineOptLogResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineOptLogResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineOptLog> list = accountOceanengineOptLogService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineOptLogResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineOptLogAddParam param) {
        Long id = accountOceanengineOptLogService.add(param);
        return RspHd.status(Objects.nonNull(id));
    }

    @Override
    public Rsp<Void> update(AccountOceanengineOptLogUpdateParam param) {
        boolean result = accountOceanengineOptLogService.updateById(Func.copy(param, AccountOceanengineOptLog.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanengineOptLogService.removeByIds(ids);
        return RspHd.status(result);
    }

    @Override
    public Rsp<PageResult<AccountOceanengineOptLogStatisticsResult>> pageStatistics(AccountOceanengineOptLogPageStatisticsParam param) {
        // 根据配置决定使用原始查询还是优化查询
        if (accountProperties.getOptLogConfig().isEnableOptimizedStatistics()) {
            return RspHd.data(PageUtil.convertFromMyBatis(accountOceanengineOptLogService.pageStatisticsOptimized(param)));
        } else {
            return RspHd.data(PageUtil.convertFromMyBatis(accountOceanengineOptLogService.pageStatistics(param)));
        }
    }

}
