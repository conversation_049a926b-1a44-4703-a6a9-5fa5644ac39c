package com.swhd.agent.service.statistics.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.statistics.client.StatisticsAdvertiserOceanengineCityDayClient;
import com.swhd.agent.api.statistics.dto.param.advertiser.city.StatisticsAdvertiserOceanengineCityDayPageParam;
import com.swhd.agent.api.statistics.dto.param.advertiser.city.StatisticsAdvertiserOceanengineCitySumPageParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineCityDayResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineCityGroupSumResult;
import com.swhd.agent.service.statistics.entity.StatisticsAdvertiserOceanengineCityDay;
import com.swhd.agent.service.statistics.service.StatisticsAdvertiserOceanengineCityDayService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-23
 */
@RestController
@AllArgsConstructor
@RequestMapping(StatisticsAdvertiserOceanengineCityDayClient.BASE_PATH)
public class StatisticsAdvertiserOceanengineCityDayController implements StatisticsAdvertiserOceanengineCityDayClient {

    private final StatisticsAdvertiserOceanengineCityDayService statisticsAdvertiserOceanengineCityDayService;

    @Override
    public Rsp<PageResult<StatisticsAdvertiserOceanengineCityDayResult>> page(StatisticsAdvertiserOceanengineCityDayPageParam param) {
        IPage<StatisticsAdvertiserOceanengineCityDay> iPage = statisticsAdvertiserOceanengineCityDayService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, StatisticsAdvertiserOceanengineCityDayResult.class));
    }

    @Override
    public Rsp<PageResult<StatisticsAdvertiserOceanengineCityGroupSumResult>> sumPage(StatisticsAdvertiserOceanengineCitySumPageParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineCityDayService.sumPage(param));
    }

    @Override
    public Rsp<List<StatisticsAdvertiserOceanengineCityGroupSumResult>> sumList(StatisticsAdvertiserOceanengineCitySumPageParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineCityDayService.sumList(param));
    }

}
