package com.swhd.agent.service.fund.service.impl;

import com.swhd.agent.api.fund.constant.FundOceanengineTransferType;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordAddParam;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordBatchAddParam;
import com.swhd.agent.service.fund.service.FundOceanengineTransferRecordService;
import com.swhd.agent.service.fund.service.FundOceanengineTransferRecordSyncService;
import com.swhd.agent.service.fund.service.FundOceanengineTransferSeqService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineAgentApiAgentTransactionRecordResult;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.swhd.agent.service.fund.service.impl.FundOceanengineTransferRecordServiceImpl.ADD_LOCK_PRE;

/**
 * <AUTHOR>
 * @since 2023/12/27
 */
@Service
@AllArgsConstructor
public class FundOceanengineTransferRecordSyncServiceImpl implements FundOceanengineTransferRecordSyncService {

    private final FundOceanengineTransferRecordService fundOceanengineTransferRecordService;

    private final FundOceanengineTransferSeqService fundOceanengineTransferSeqService;

    @Override
    @Lockable(prefixKey = ADD_LOCK_PRE, waitTime = 30000)
    public Rsp<List<String>> syncFormApi(List<OceanengineAgentApiAgentTransactionRecordResult> list) {
        list = list.stream()
                .filter(param -> Func.isNotBlank(param.getTransferOrderSerial()))
                .filter(param -> param.getRemitter() != null)
                .filter(param -> param.getPayee() != null)
                .filter(param -> param.getAmount() != null)
                .filter(param -> param.getModifyTime() != null)
                // operatorId=0是巨量纵横
                .filter(param -> param.getOperatorId() > 0)
                .filter(param -> Objects.equals(param.getTransferType(), "ADD_MONEY")
                        || Objects.equals(param.getTransferType(), "REFUND_MONEY")
                        || Objects.equals(param.getTransferType(), "MUTUAL_TRANSFER"))
                .toList();
        if (Func.isEmpty(list)) {
            return RspHd.data(Collections.emptyList());
        }
        // 转换转账编号：批量转账，巨量方舟的转账编号相同，需要增加序号进行区分
        fundOceanengineTransferSeqService.convertTransferSeq(list);
        List<FundOceanengineTransferRecordAddParam> addParamList = list.stream()
                .flatMap(param -> {
                    List<FundOceanengineTransferRecordAddParam> addList = new ArrayList<>();
                    if (Objects.equals(param.getTransferType(), "ADD_MONEY")) {
                        FundOceanengineTransferRecordAddParam addParam = newAddParam(param);
                        addParam.setTransferType(FundOceanengineTransferType.ADD);
                        addParam.setAgentId(param.getRemitter());
                        addParam.setAdvertiserId(param.getPayee());
                        addList.add(addParam);
                    } else if (Objects.equals(param.getTransferType(), "REFUND_MONEY")) {
                        FundOceanengineTransferRecordAddParam addParam = newAddParam(param);
                        addParam.setTransferType(FundOceanengineTransferType.REFUND);
                        addParam.setAgentId(param.getPayee());
                        addParam.setAdvertiserId(param.getRemitter());
                        addList.add(addParam);
                    } else {
                        // 同级账户转账
                        // 1.模拟退款到代理商户
                        FundOceanengineTransferRecordAddParam addParam1 = newAddParam(param);
                        addParam1.setOceanengineTransactionSeq(addParam1.getOceanengineTransactionSeq() + "-1");
                        addParam1.setOceanengineAmount(BigDecimal.ZERO.subtract(addParam1.getOceanengineAmount()));
                        addParam1.setTransferType(FundOceanengineTransferType.REFUND);
                        addParam1.setAgentId(param.getRemitterFirstAdAgentId());
                        addParam1.setAdvertiserId(param.getRemitter());
                        addList.add(addParam1);
                        // 2.模拟代理商户转到广告主
                        FundOceanengineTransferRecordAddParam addParam2 = newAddParam(param);
                        addParam2.setOceanengineTransactionSeq(addParam2.getOceanengineTransactionSeq() + "-2");
                        addParam2.setTransferType(FundOceanengineTransferType.ADD);
                        addParam2.setAgentId(param.getPayeeFirstAdAgentId());
                        addParam2.setAdvertiserId(param.getPayee());
                        addList.add(addParam2);
                    }
                    return addList.stream();
                })
                .toList();
        FundOceanengineTransferRecordBatchAddParam batchAddParam = new FundOceanengineTransferRecordBatchAddParam();
        batchAddParam.setList(addParamList);
        return fundOceanengineTransferRecordService.batchAdd(batchAddParam);
    }

    private FundOceanengineTransferRecordAddParam newAddParam(OceanengineAgentApiAgentTransactionRecordResult transferRecord) {
        FundOceanengineTransferRecordAddParam addParam = new FundOceanengineTransferRecordAddParam();
        addParam.setOceanengineTransactionSeq(transferRecord.getTransferOrderSerial());
        addParam.setOceanengineAmount(transferRecord.getAmount());
        addParam.setOceanengineTransferTime(transferRecord.getModifyTime());
        return addParam;
    }

}
