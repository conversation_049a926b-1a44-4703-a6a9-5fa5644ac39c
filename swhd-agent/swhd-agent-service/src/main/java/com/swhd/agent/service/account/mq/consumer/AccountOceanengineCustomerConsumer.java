package com.swhd.agent.service.account.mq.consumer;

import com.swhd.agent.api.account.dto.message.AccountOceanengineCustomerSyncMessage;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerService;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2025/4/17
 */
@Slf4j
@Component
@AllArgsConstructor
public class AccountOceanengineCustomerConsumer {

    private final AccountOceanengineCustomerService accountOceanengineCustomerService;

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    /**
     * 同步巨量引擎账户客户
     */
    @Bean
    public Consumer<AccountOceanengineCustomerSyncMessage> agentAccountOceanengineCustomerSync() {
        return message -> TenantHolder.methodTenantVoid(message.getTenantId(), () -> {
            log.info("消费同步巨量引擎账户客户数据: {}", JsonLogUtil.toJsonString(message));
            if (message.getCustomerId() == null || message.getCustomerId() <= 0) {
                log.warn("客户ID为空");
                return;
            }
            if (Func.isNotBlank(message.getCustomerName())) {
                accountOceanengineCustomerService.saveOrUpdate(message.getCustomerId(), message.getCustomerName());
            }
            if (message.getAdvertiserId() != null && message.getAdvertiserId() > 0) {
                AccountOceanengineInfo accountInfo = accountOceanengineInfoService.getByAdvertiserId(message.getAdvertiserId());
                if (accountInfo != null && Objects.equals(accountInfo.getAdvertiserCustomerId(), message.getCustomerId())) {
                    AccountOceanengineInfo update = new AccountOceanengineInfo();
                    update.setId(accountInfo.getId());
                    update.setAdvertiserCustomerId(message.getCustomerId());
                    accountOceanengineInfoService.updateById(update);
                }
            }
        });
    }

}
