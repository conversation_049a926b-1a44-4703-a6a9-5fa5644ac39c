package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditPageParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditStatusParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditConfigResult;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeCredit;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerCreditChangeRecord;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeCreditMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeCreditService;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerCreditChangeRecordService;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.crm.api.custom.client.CustomInfoClient;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 自助充值授信表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
@Slf4j
public class AccountOceanengineCustomerRechargeCreditServiceImpl extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeCreditMapper, AccountOceanengineCustomerRechargeCredit> implements AccountOceanengineCustomerRechargeCreditService {

    private final AccountOceanengineCustomerCreditChangeRecordService accountOceanengineCustomerCreditChangeRecordService;
    private final AccountOceanengineInfoService accountOceanengineInfoService;
    private final CustomInfoClient customInfoClient;

    @Override
    public IPage<AccountOceanengineCustomerRechargeCredit> page(AccountOceanengineCustomerRechargeCreditPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getCustomId()), AccountOceanengineCustomerRechargeCredit::getCustomId, param.getCustomId())
                .eq(Func.isNotEmpty(param.getAllowSelfRecharge()), AccountOceanengineCustomerRechargeCredit::getAllowSelfRecharge, param.getAllowSelfRecharge())
                .eq(Func.isNotEmpty(param.getCreditLimit()), AccountOceanengineCustomerRechargeCredit::getCreditLimit, param.getCreditLimit())
                .eq(Func.isNotEmpty(param.getUsedAmount()), AccountOceanengineCustomerRechargeCredit::getUsedAmount, param.getUsedAmount())
                .orderByDesc(AccountOceanengineCustomerRechargeCredit::getCreateTime)
                .orderByDesc(AccountOceanengineCustomerRechargeCredit::getId)
                .page(convertToPage(param));
    }

    @Override
    public AccountOceanengineCustomerRechargeCreditConfigResult getByCustomId(Long customId) {
        // 查询客户信息
        CustomInfoResult customInfo;
        Rsp<CustomInfoResult> customInfoRsp = customInfoClient.getById(customId);
        if (RspHd.isSuccess(customInfoRsp) && customInfoRsp.getData() != null) {
            customInfo = customInfoRsp.getData();
        }else {
            log.error("获取客户信息失败：{}", customInfoRsp.getMsg());
            throw new ServiceException("获取客户信息失败");
        }

        // 查询授信记录
        AccountOceanengineCustomerRechargeCredit credit = lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeCredit::getCustomId, customId)
                .limitOne();

        // 查询广告账号数量
        long accountCount =  accountOceanengineInfoService.lambdaQuery()
                .eq(AccountOceanengineInfo::getCustomId, customId)
                .count();

        // 构建返回结果
        AccountOceanengineCustomerRechargeCreditConfigResult result = new AccountOceanengineCustomerRechargeCreditConfigResult();
        result.setCustomId(customId);
        result.setCustomName(customInfo.getName());
        result.setAccountCount((int)accountCount);

        if (credit != null) {
            result.setId(credit.getId());
            result.setAllowSelfRecharge(Objects.equals(credit.getAllowSelfRecharge(), 1));
            result.setCreditLimit(credit.getCreditLimit() != null ? credit.getCreditLimit() : BigDecimal.ZERO);
            result.setUsedAmount(credit.getUsedAmount() != null ? credit.getUsedAmount() : BigDecimal.ZERO);
            result.setRemainingAmount(result.getCreditLimit().subtract(result.getUsedAmount()));
        } else {
            result.setAllowSelfRecharge(false);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "agent:account:oceanengine:customer:recharge:credit:update", key = "#param.customId", waitTime = 6000)
    public Rsp<Void> updateCreditStatus(AccountOceanengineCustomerRechargeCreditStatusParam param) {
        Long customId = param.getCustomId();
        Boolean allowSelfRecharge = param.getAllowSelfRecharge();
        BigDecimal newCreditLimit = param.getCreditLimit();

        // 查询现有记录
        AccountOceanengineCustomerRechargeCredit existingCredit = lambdaQuery()
                .eq(AccountOceanengineCustomerRechargeCredit::getCustomId, customId)
                .limitOne();

        if (!allowSelfRecharge) {
            // 关闭自助充值
            return handleCloseCredit(existingCredit, customId);
        } else {
            // 开启自助充值并设置额度
            return handleOpenCredit(existingCredit, customId, newCreditLimit);
        }
    }

    /**
     * 处理关闭自助充值
     */
    private Rsp<Void> handleCloseCredit(AccountOceanengineCustomerRechargeCredit existingCredit, Long customId) {
        if (existingCredit == null || !Objects.equals(existingCredit.getAllowSelfRecharge(), 1)) {
            // 如果已关闭或不存在，直接返回成功
            return RspHd.success();
        }

        // 记录变更前的状态
        BigDecimal beforeCreditLimit = existingCredit.getCreditLimit() != null ? existingCredit.getCreditLimit() : BigDecimal.ZERO;

        // 更新记录：关闭自助充值，清空额度和已使用额度
        existingCredit.setAllowSelfRecharge(0);
        existingCredit.setCreditLimit(BigDecimal.ZERO);
        existingCredit.setUsedAmount(BigDecimal.ZERO);
        updateById(existingCredit);

        // 记录额度变更记录
        if (beforeCreditLimit.compareTo(BigDecimal.ZERO) > 0) {
            AccountOceanengineCustomerCreditChangeRecord changeRecord = new AccountOceanengineCustomerCreditChangeRecord();
            changeRecord.setSelfRechargeCreditId(existingCredit.getId());
            changeRecord.setChangeType("CLOSE");
            changeRecord.setBeforeAmount(beforeCreditLimit);
            changeRecord.setAfterAmount(BigDecimal.ZERO);
            changeRecord.setChangeAmount(beforeCreditLimit.negate());
            accountOceanengineCustomerCreditChangeRecordService.save(changeRecord);
        }

        return RspHd.success();
    }

    /**
     * 处理开启自助充值
     */
    private Rsp<Void> handleOpenCredit(AccountOceanengineCustomerRechargeCredit existingCredit, Long customId, BigDecimal newCreditLimit) {

        if (newCreditLimit == null || newCreditLimit.compareTo(BigDecimal.ZERO) < 0) {
            return RspHd.fail("授信额度不能为空且不能小于0");
        }

        //新的授信额度 不能小于当前的
        if (existingCredit != null) {
            BigDecimal currentCreditLimit = existingCredit.getCreditLimit() != null ? existingCredit.getCreditLimit() : BigDecimal.ZERO;
            if (newCreditLimit.compareTo(currentCreditLimit) < 0) {
                return RspHd.fail("授信额度不能小于当前授信额度：" + currentCreditLimit);
            }
        }

        if (existingCredit == null) {
            // 创建新记录
            AccountOceanengineCustomerRechargeCredit newCredit = new AccountOceanengineCustomerRechargeCredit();
            newCredit.setCustomId(customId);
            newCredit.setAllowSelfRecharge(1);
            newCredit.setCreditLimit(newCreditLimit);
            newCredit.setUsedAmount(BigDecimal.ZERO);
            save(newCredit);

            // 记录开启记录
            if (newCreditLimit.compareTo(BigDecimal.ZERO) > 0) {
                AccountOceanengineCustomerCreditChangeRecord changeRecord = new AccountOceanengineCustomerCreditChangeRecord();
                changeRecord.setSelfRechargeCreditId(newCredit.getId());
                changeRecord.setChangeType("OPEN");
                changeRecord.setBeforeAmount(BigDecimal.ZERO);
                changeRecord.setAfterAmount(newCreditLimit);
                changeRecord.setChangeAmount(newCreditLimit);
                accountOceanengineCustomerCreditChangeRecordService.save(changeRecord);
            }

            return RspHd.success();
        } else {
            // 更新现有记录
            BigDecimal currentCreditLimit = existingCredit.getCreditLimit() != null ? existingCredit.getCreditLimit() : BigDecimal.ZERO;
            boolean wasEnabled = Objects.equals(existingCredit.getAllowSelfRecharge(), 1);

            // 如果已经开启，校验额度不能小于当前授信额度
            if (wasEnabled) {
                // 如果额度相等，直接返回成功
                if (newCreditLimit.compareTo(currentCreditLimit) == 0) {
                    return RspHd.success();
                }
            }

            // 更新记录
            existingCredit.setAllowSelfRecharge(1);
            existingCredit.setCreditLimit(newCreditLimit);
            updateById(existingCredit);
            // 记录额度变更
            if (newCreditLimit.compareTo(currentCreditLimit) != 0) {
                // 判断变更类型：如果之前是开启状态则为提额，否则为开启
                String changeType = wasEnabled ? "INCREASE" : "OPEN";
                AccountOceanengineCustomerCreditChangeRecord changeRecord = new AccountOceanengineCustomerCreditChangeRecord();
                changeRecord.setSelfRechargeCreditId(existingCredit.getId());
                changeRecord.setChangeType(changeType);
                changeRecord.setBeforeAmount(currentCreditLimit);
                changeRecord.setAfterAmount(newCreditLimit);
                changeRecord.setChangeAmount(newCreditLimit.subtract(currentCreditLimit));
                accountOceanengineCustomerCreditChangeRecordService.save(changeRecord);
            }

            return RspHd.success();
        }
    }

}
