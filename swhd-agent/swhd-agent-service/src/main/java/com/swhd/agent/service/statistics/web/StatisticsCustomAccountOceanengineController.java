package com.swhd.agent.service.statistics.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.statistics.client.StatisticsCustomAccountOceanengineClient;
import com.swhd.agent.api.statistics.dto.param.custom.account.StatisticsCustomAccountOceanengineAddParam;
import com.swhd.agent.api.statistics.dto.param.custom.account.StatisticsCustomAccountOceanengineBatchAddParam;
import com.swhd.agent.api.statistics.dto.param.custom.account.StatisticsCustomAccountOceanenginePageParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsCustomAccountOceanengineResult;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.agent.service.statistics.entity.StatisticsCustomAccountOceanengine;
import com.swhd.agent.service.statistics.service.StatisticsCustomAccountOceanengineService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.client.OceanengineAgentOauthAccountClient;
import com.swhd.oauth.api.oceanengine.dto.result.OceanengineAgentOauthAccountResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-08-31
 */
@RestController
@AllArgsConstructor
@RequestMapping(StatisticsCustomAccountOceanengineClient.BASE_PATH)
public class StatisticsCustomAccountOceanengineController implements StatisticsCustomAccountOceanengineClient {

    private final StatisticsCustomAccountOceanengineService statisticsCustomAccountOceanengineService;

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    private final OceanengineAgentOauthAccountClient oceanengineAgentOauthAccountClient;

    @Override
    public Rsp<PageResult<StatisticsCustomAccountOceanengineResult>> page(StatisticsCustomAccountOceanenginePageParam param) {
        IPage<StatisticsCustomAccountOceanengine> iPage = statisticsCustomAccountOceanengineService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, StatisticsCustomAccountOceanengineResult.class));
    }

    @Override
    public Rsp<StatisticsCustomAccountOceanengineResult> getById(Long id) {
        StatisticsCustomAccountOceanengine entity = statisticsCustomAccountOceanengineService.getById(id);
        return RspHd.data(Func.copy(entity, StatisticsCustomAccountOceanengineResult.class));
    }

    @Override
    public Rsp<List<StatisticsCustomAccountOceanengineResult>> listByIds(Collection<Long> ids) {
        List<StatisticsCustomAccountOceanengine> list = statisticsCustomAccountOceanengineService.listByIds(ids);
        return RspHd.data(Func.copy(list, StatisticsCustomAccountOceanengineResult.class));
    }

    @Override
    public Rsp<List<StatisticsCustomAccountOceanengineResult>> listByGroupId(Long groupId) {
        List<StatisticsCustomAccountOceanengine> list = statisticsCustomAccountOceanengineService.listByGroupId(groupId);
        return RspHd.data(Func.copy(list, StatisticsCustomAccountOceanengineResult.class));
    }

    @Override
    public Rsp<Void> add(StatisticsCustomAccountOceanengineAddParam param) {
        AccountOceanengineInfo accountInfo = accountOceanengineInfoService.getByAdvertiserId(param.getAdvertiserId());
        if (accountInfo == null) {
            return RspHd.fail("广告主信息不存在");
        }
        Rsp<OceanengineAgentOauthAccountResult> agentRsp = oceanengineAgentOauthAccountClient
                .getByAgentId(accountInfo.getAgentId());
        if (RspHd.isFail(agentRsp)) {
            return RspHd.fail(agentRsp);
        }
        if (agentRsp.getData() == null) {
            return RspHd.fail("代理商信息为空");
        }
        StatisticsCustomAccountOceanengine accountOceanengine = new StatisticsCustomAccountOceanengine();
        accountOceanengine.setGroupId(param.getGroupId());
        accountOceanengine.setAdvertiserId(param.getAdvertiserId());
        accountOceanengine.setAgentId(accountInfo.getAgentId());
        accountOceanengine.setAgentType(agentRsp.getData().getAgentType());
        return statisticsCustomAccountOceanengineService.add(accountOceanengine);
    }

    @Override
    public Rsp<Void> batchAdd(StatisticsCustomAccountOceanengineBatchAddParam param) {
        List<AccountOceanengineInfo> accountList = accountOceanengineInfoService.listByAdvertiserIds(param.getAdvertiserIds());
        if (Func.isEmpty(accountList)) {
            return RspHd.fail("广告主信息为空");
        }
        Map<Long, AccountOceanengineInfo> advertiserMap = accountList.stream()
                .collect(Collectors.toMap(AccountOceanengineInfo::getAdvertiserId, i -> i));
        List<Long> notExistsAdvertiserIdList = param.getAdvertiserIds().stream()
                .filter(advertiserId -> !advertiserMap.containsKey(advertiserId))
                .toList();
        if (Func.isNotEmpty(notExistsAdvertiserIdList)) {
            return RspHd.fail(String.format("广告主%s信息为空", notExistsAdvertiserIdList));
        }
        List<Long> agentIds = accountList.stream().map(AccountOceanengineInfo::getAgentId).distinct().toList();
        Rsp<List<OceanengineAgentOauthAccountResult>> agentListRsp = oceanengineAgentOauthAccountClient.listByAgentIds(agentIds);
        if (RspHd.isFail(agentListRsp)) {
            return RspHd.fail(agentListRsp);
        }
        if (Func.isEmpty(agentListRsp.getData())) {
            return RspHd.fail("代理商信息为空");
        }
        Map<Long, OceanengineAgentOauthAccountResult> agentMap = agentListRsp.getData().stream()
                .collect(Collectors.toMap(OceanengineAgentOauthAccountResult::getAgentId, i -> i));
        List<Long> notExistsAgentIdList = agentIds.stream()
                .filter(agentId -> !agentMap.containsKey(agentId))
                .toList();
        if (Func.isNotEmpty(notExistsAgentIdList)) {
            return RspHd.fail(String.format("代理商%s信息为空", notExistsAgentIdList));
        }
        List<StatisticsCustomAccountOceanengine> addList = param.getAdvertiserIds().stream()
                .distinct()
                .map(advertiserId -> {
                    AccountOceanengineInfo accountInfo = advertiserMap.get(advertiserId);
                    OceanengineAgentOauthAccountResult agentInfo = agentMap.get(accountInfo.getAgentId());
                    StatisticsCustomAccountOceanengine accountOceanengine = new StatisticsCustomAccountOceanengine();
                    accountOceanengine.setGroupId(param.getGroupId());
                    accountOceanengine.setAdvertiserId(accountInfo.getAdvertiserId());
                    accountOceanengine.setAgentId(accountInfo.getAgentId());
                    accountOceanengine.setAgentType(agentInfo.getAgentType());
                    return accountOceanengine;
                })
                .toList();
        return statisticsCustomAccountOceanengineService.batchAdd(param.getGroupId(), addList);
    }

    @Override
    public Rsp<Void> removeById(Long id) {
        StatisticsCustomAccountOceanengine entity = statisticsCustomAccountOceanengineService.getById(id);
        if (entity == null) {
            return RspHd.success();
        }
        return statisticsCustomAccountOceanengineService.remove(entity.getGroupId(), entity.getId());
    }

}
