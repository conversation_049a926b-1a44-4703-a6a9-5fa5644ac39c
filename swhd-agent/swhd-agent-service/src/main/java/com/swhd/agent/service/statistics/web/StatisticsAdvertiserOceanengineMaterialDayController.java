package com.swhd.agent.service.statistics.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.statistics.client.StatisticsAdvertiserOceanengineMaterialDayClient;
import com.swhd.agent.api.statistics.dto.param.advertiser.material.StatisticsAdvertiserOceanengineMaterialDayPageParam;
import com.swhd.agent.api.statistics.dto.param.advertiser.material.StatisticsAdvertiserOceanengineMaterialGroupSumParam;
import com.swhd.agent.api.statistics.dto.param.advertiser.material.StatisticsAdvertiserOceanengineMaterialSumParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineMaterialDayResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineMaterialGroupSumResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineMaterialSumResult;
import com.swhd.agent.service.statistics.entity.StatisticsAdvertiserOceanengineMaterialDay;
import com.swhd.agent.service.statistics.service.StatisticsAdvertiserOceanengineMaterialDayService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-16
 */
@RestController
@AllArgsConstructor
@RequestMapping(StatisticsAdvertiserOceanengineMaterialDayClient.BASE_PATH)
public class StatisticsAdvertiserOceanengineMaterialDayController implements StatisticsAdvertiserOceanengineMaterialDayClient {

    private final StatisticsAdvertiserOceanengineMaterialDayService statisticsAdvertiserOceanengineMaterialDayService;

    @Override
    public Rsp<PageResult<StatisticsAdvertiserOceanengineMaterialDayResult>> page(StatisticsAdvertiserOceanengineMaterialDayPageParam param) {
        IPage<StatisticsAdvertiserOceanengineMaterialDay> iPage = statisticsAdvertiserOceanengineMaterialDayService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, StatisticsAdvertiserOceanengineMaterialDayResult.class));
    }

    @Override
    public Rsp<StatisticsAdvertiserOceanengineMaterialSumResult> sum(StatisticsAdvertiserOceanengineMaterialSumParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineMaterialDayService.sum(param));
    }

    @Override
    public Rsp<List<StatisticsAdvertiserOceanengineMaterialGroupSumResult>> materialGroupSum(
            StatisticsAdvertiserOceanengineMaterialGroupSumParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineMaterialDayService.materialGroupSum(param));
    }

    @Override
    public Rsp<StatisticsAdvertiserOceanengineMaterialDayResult> getById(Long id) {
        StatisticsAdvertiserOceanengineMaterialDay entity = statisticsAdvertiserOceanengineMaterialDayService.getById(id);
        return RspHd.data(Func.copy(entity, StatisticsAdvertiserOceanengineMaterialDayResult.class));
    }

    @Override
    public Rsp<List<StatisticsAdvertiserOceanengineMaterialDayResult>> listByIds(Collection<Long> ids) {
        List<StatisticsAdvertiserOceanengineMaterialDay> list = statisticsAdvertiserOceanengineMaterialDayService.listByIds(ids);
        return RspHd.data(Func.copy(list, StatisticsAdvertiserOceanengineMaterialDayResult.class));
    }

}
