package com.swhd.agent.service.statistics.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 自运营统计汇总月表实体类
 *
 * <AUTHOR>
 * @since 2024-02-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_statistics_self_operate_oceanengine_total_month")
public class StatisticsSelfOperateOceanengineTotalMonth extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "自运营消耗(单位元)")
    private BigDecimal selfOperateConsume;

    @Schema(description = "自运营消耗占比")
    private BigDecimal selfOperateConsumeRatio;

    @Schema(description = "总消耗(单位元)")
    private BigDecimal totalConsume;

}
