package com.swhd.agent.service.statistics.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.statistics.client.StatisticsAdvertiserOceanengineAccountDayClient;
import com.swhd.agent.api.statistics.dto.param.advertiser.account.StatisticsAdvertiserOceanengineAccountDayPageParam;
import com.swhd.agent.api.statistics.dto.param.advertiser.account.StatisticsAdvertiserOceanengineAccountSumPageParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineAccountDayResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineAccountGroupSumResult;
import com.swhd.agent.service.statistics.entity.StatisticsAdvertiserOceanengineAccountDay;
import com.swhd.agent.service.statistics.service.StatisticsAdvertiserOceanengineAccountDayService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-16
 */
@RestController
@AllArgsConstructor
@RequestMapping(StatisticsAdvertiserOceanengineAccountDayClient.BASE_PATH)
public class StatisticsAdvertiserOceanengineAccountDayController implements StatisticsAdvertiserOceanengineAccountDayClient {

    private final StatisticsAdvertiserOceanengineAccountDayService statisticsAdvertiserOceanengineAccountDayService;

    @Override
    public Rsp<PageResult<StatisticsAdvertiserOceanengineAccountDayResult>> page(StatisticsAdvertiserOceanengineAccountDayPageParam param) {
        IPage<StatisticsAdvertiserOceanengineAccountDay> iPage = statisticsAdvertiserOceanengineAccountDayService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, StatisticsAdvertiserOceanengineAccountDayResult.class));
    }

    @Override
    public Rsp<PageResult<StatisticsAdvertiserOceanengineAccountGroupSumResult>> sumPage(
            StatisticsAdvertiserOceanengineAccountSumPageParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineAccountDayService.sumPage(param));
    }

    @Override
    public Rsp<List<StatisticsAdvertiserOceanengineAccountGroupSumResult>> sumList(
            StatisticsAdvertiserOceanengineAccountSumPageParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineAccountDayService.sumList(param));
    }

}
