package com.swhd.agent.service.account.web;

import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.agent.service.account.job.*;
import com.swhd.agent.service.statistics.job.StatisticsAdvertiserOceanengineAccountJob;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/9/27 16:39
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/test")
public class TestController {

    private AccountOceanengineOptLogJob accountOceanengineOptLogJob;

    private AcountOceanenginePromotionJob acountOceanenginePromotionJob;

    private final AccountOceanenginePromotionOptLogJob accountOceanenginePromotionOptLogJob;

    private final AccountOceanengineProjectJob accountOceanengineProjectJob;

    private final AccountOceanengineAdvertisingAutoOptJob accountOceanengineAdvertisingAutoOptJob;

    private final StatisticsAdvertiserOceanengineAccountJob statisticsAdvertiserOceanengineAccountJob;

    @Operation(summary = "syncToday")
    @PostMapping("/syncToday")
    public Rsp<Void> syncToday() {
        try {
            //statisticsAdvertiserOceanengineAccountJob.syncToday();
            return Rsp.success();
        } catch (Exception e) {
            return Rsp.fail(500, "同步失败：" + e.getMessage());
        }
    }


    @Operation(summary = "testSyncAdProject")
    @PostMapping("/testSyncAdProject")
    public Rsp<Void> testSyncAdProject() {
        try {
            accountOceanengineProjectJob.syncAdProject();
            return Rsp.success();
        } catch (Exception e) {
            return Rsp.fail(500, "同步失败：" + e.getMessage());
        }
    }

    @Operation(summary = "checkAndCreateAccountTask")
    @GetMapping("/checkAndCreateAccountTask")
    public Rsp<Void> checkAndCreateAccountTask(){
        accountOceanengineOptLogJob.checkAndCreateAccountTask();
        return Rsp.success();
    }

    @Operation(summary = "检测并新建巨量引擎广告同步计划的操作日志任务")
    @GetMapping("/checkAndCreatePromotionTask")
    public Rsp<Void> checkAndCreatePromotionTask(){
        try {
            accountOceanenginePromotionOptLogJob.checkAndCreateTask();
            return Rsp.success();
        } catch (Exception e) {
            return RspHd.fail("执行任务异常");
        }
    }

    @Operation(summary = "syncLog")
    @GetMapping("/syncLog")
    public Rsp<Void> syncLog(){
        accountOceanengineOptLogJob.syncLog();
        return Rsp.success();
    }

    @Operation(summary = "sendOptLogInvalidData")
    @GetMapping("/sendOptLogInvalidData")
    public Rsp<Void> sendOptLogInvalidData(){
        accountOceanengineOptLogJob.sendOptLogInvalidData();
        return Rsp.success();
    }

    @Operation(summary = "testJsonsso")
    @GetMapping("/testJsonsso")
    public Rsp<AccountOceanengineInfoResult> testJsonsso(){
        AccountOceanengineInfoResult d= new AccountOceanengineInfoResult();
        d.setAdvertiserName("test");
        return Rsp.data(d);
    }

    @Operation(summary = "syncPromotion")
    @GetMapping("/syncPromotion")
    public Rsp<Void> syncPromotion(){
        acountOceanenginePromotionJob.syncSwjPromotion();
        acountOceanenginePromotionJob.syncSwhdPromotion();
        return Rsp.success();
    }

    @Operation(summary = "巨量引擎AD计划同步测试")
    @PostMapping("/testSyncAdPromotion")
    public Rsp<Void> testSyncAdPromotion() {
        try {
            acountOceanenginePromotionJob.syncAdPromotion();
            return Rsp.success();
        } catch (Exception e) {
            log.error("巨量引擎AD计划同步测试失败", e);
            return Rsp.fail(500, "执行失败：" + e.getMessage());
        }
    }

    @Operation(summary = "巨量引擎千川广告计划操作日志同步")
    @GetMapping("/syncQcLog")
    public Rsp<Void> syncQcLog(){
        accountOceanenginePromotionOptLogJob.syncSwjPromotionLog();
        return Rsp.success();
    }

    @Operation(summary = "巨量引擎广告自动操作测试")
    @PostMapping("/testAdvertisingAutoOpt")
    public Rsp<Void> testAdvertisingAutoOpt() {
        try {
            accountOceanengineAdvertisingAutoOptJob.optAdProject();
            return Rsp.success();
        } catch (Exception e) {
            log.error("巨量引擎广告自动操作测试失败", e);
            return Rsp.fail(500, "执行失败：" + e.getMessage());
        }
    }

    @Operation(summary = "巨量引擎广告计划自动操作测试")
    @PostMapping("/testAdvertisingPromotionAutoOpt")
    public Rsp<Void> testAdvertisingPromotionAutoOpt() {
        try {
            accountOceanengineAdvertisingAutoOptJob.optAdPromotion();
            return Rsp.success();
        } catch (Exception e) {
            log.error("巨量引擎广告计划自动操作测试失败", e);
            return Rsp.fail(500, "执行失败：" + e.getMessage());
        }
    }


}
