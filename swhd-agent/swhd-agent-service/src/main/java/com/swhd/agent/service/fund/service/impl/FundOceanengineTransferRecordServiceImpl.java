package com.swhd.agent.service.fund.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.agent.api.account.dto.param.rebate.info.AccountOceanengineRebateInfoGetParam;
import com.swhd.agent.api.fund.constant.FundConfirmAmountState;
import com.swhd.agent.api.fund.constant.FundConstant;
import com.swhd.agent.api.fund.constant.FundOceanengineTransferType;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordAddParam;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordBatchAddParam;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordPageParam;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordSplitParam;
import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordRebateResult;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.entity.AccountOceanengineRebateInfo;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.agent.service.account.service.AccountOceanengineRebateInfoService;
import com.swhd.agent.service.fund.entity.FundOceanengineTransferRecord;
import com.swhd.agent.service.fund.mapper.FundOceanengineTransferRecordMapper;
import com.swhd.agent.service.fund.properties.FundProperties;
import com.swhd.agent.service.fund.service.FundOceanengineTransferRecordService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.idgenerator.IdGenerator;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 巨量引擎转账记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-05
 */
@Service
@AllArgsConstructor
public class FundOceanengineTransferRecordServiceImpl
        extends BaseHdServiceImpl<FundOceanengineTransferRecordMapper, FundOceanengineTransferRecord>
        implements FundOceanengineTransferRecordService {

    public static final String ADD_LOCK_PRE = "agent:fund:oceanengine:transfer:record:add";

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    private final AccountOceanengineRebateInfoService accountOceanengineRebateInfoService;

    private final IdGenerator<Long> idGenerator;

    private final FundProperties fundProperties;

    @Override
    public IPage<FundOceanengineTransferRecord> page(FundOceanengineTransferRecordPageParam param) {
        return baseMapper.page(param, convertToPage(param));
    }

    @Override
    public List<FundOceanengineTransferRecord> list(FundOceanengineTransferRecordPageParam param) {
        Page<FundOceanengineTransferRecord> page = new Page<>();
        page.setCurrent(1);
        page.setSize(fundProperties.getListMaxQuerySize());
        page.setSearchCount(false);
        IPage<FundOceanengineTransferRecord> iPage = baseMapper.page(param, page);
        return iPage.getRecords();
    }

    @Override
    public boolean existsByTransactionSeq(String oceanengineTransactionSeq) {
        return lambdaQuery().eq(FundOceanengineTransferRecord::getOceanengineTransactionSeq, oceanengineTransactionSeq).exists();
    }

    @Override
    public FundOceanengineTransferRecord getByTransactionSeq(String oceanengineTransactionSeq) {
        return lambdaQuery().eq(FundOceanengineTransferRecord::getOceanengineTransactionSeq, oceanengineTransactionSeq).limitOne();
    }

    @Override
    public List<String> existsByTransactionSeqList(List<String> oceanengineTransactionSeqList) {
        if (Func.isEmpty(oceanengineTransactionSeqList)) {
            return Collections.emptyList();
        }
        List<String> existsByTransactionSeqList = new ArrayList<>();
        Func.executeBatch(oceanengineTransactionSeqList, 1000, subOceanengineTransactionSeqList -> {
            List<FundOceanengineTransferRecord> list = lambdaQuery()
                    .select(List.of(FundOceanengineTransferRecord::getOceanengineTransactionSeq))
                    .in(FundOceanengineTransferRecord::getOceanengineTransactionSeq, subOceanengineTransactionSeqList)
                    .list();
            existsByTransactionSeqList.addAll(list.stream().map(FundOceanengineTransferRecord::getOceanengineTransactionSeq).distinct().toList());
        });
        return existsByTransactionSeqList;
    }

    @Override
    public List<FundOceanengineTransferRecord> listByTransactionSeqList(Collection<String> oceanengineTransactionSeqList) {
        if (Func.isEmpty(oceanengineTransactionSeqList)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(FundOceanengineTransferRecord::getOceanengineTransactionSeq, oceanengineTransactionSeqList)
                .list();
    }

    @Override
    public Rsp<List<FundOceanengineTransferRecord>> listAssociatedRechargeByTransactionSeqList(Collection<String> oceanengineTransactionSeqList) {
        if (Func.isEmpty(oceanengineTransactionSeqList)) {
            return Rsp.data(Collections.emptyList());
        }
        // 检查流水号是否都存在
        List<FundOceanengineTransferRecord> transferRecordList = listByTransactionSeqList(oceanengineTransactionSeqList);
        List<String> existsTransactionSeqList = transferRecordList.stream()
                .map(FundOceanengineTransferRecord::getOceanengineTransactionSeq)
                .toList();
        List<String> notExistsTransactionSeqList = oceanengineTransactionSeqList.stream()
                .filter(transactionSeq -> !existsTransactionSeqList.contains(transactionSeq))
                .map(Object::toString)
                .toList();
        if (Func.isNotEmpty(notExistsTransactionSeqList)) {
            return RspHd.fail(String.format("转账编号[%s]不存在", String.join(",", notExistsTransactionSeqList)));
        }

        // 检查流水是否被关联充值信息
        List<FundOceanengineTransferRecord> notAssociatedRechargeList = transferRecordList.stream()
                .filter(transferRecord -> Objects.equals(transferRecord.getRechargeId(), 0L))
                .toList();
        if (Func.isNotEmpty(notAssociatedRechargeList)) {
            String notAssociatedTransactionSeq = notAssociatedRechargeList.stream()
                    .map(FundOceanengineTransferRecord::getOceanengineTransactionSeq)
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            return RspHd.fail(String.format("转账编号[%s]未被关联充值，请关联后操作", notAssociatedTransactionSeq));
        }

        return RspHd.data(transferRecordList);
    }

    @Override
    public List<FundOceanengineTransferRecord> listByRechargeId(Long rechargeId) {
        if (Objects.equals(rechargeId, Constant.LongNum.ZERO)) {
            // 未关联时，充值id是0，关联数据过大，返回空列表
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(FundOceanengineTransferRecord::getRechargeId, rechargeId)
                .list();
    }

    @Override
    public List<FundOceanengineTransferRecord> listByRechargeIds(Collection<Long> rechargeIds) {
        rechargeIds = Optional.ofNullable(rechargeIds).orElse(Collections.emptyList()).stream()
                // 未关联时，充值id是0，不查询该值的数据
                .filter(customRechargeId -> !Objects.equals(customRechargeId, 0L))
                .toList();
        if (Func.isEmpty(rechargeIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(FundOceanengineTransferRecord::getRechargeId, rechargeIds)
                .list();
    }

    @Override
    public FundOceanengineTransferRecordRebateResult getRebateById(Long id) {
        return getRebateByEntity(getById(id));
    }

    @Override
    public FundOceanengineTransferRecordRebateResult getRebateByEntity(FundOceanengineTransferRecord entity) {
        if (entity == null) {
            return null;
        }
        AccountOceanengineInfo account = accountOceanengineInfoService.getByAdvertiserId(entity.getAdvertiserId());
        AccountOceanengineRebateInfoGetParam rebateGetParam = new AccountOceanengineRebateInfoGetParam();
        if (account != null) {
            rebateGetParam.setAdvertiserId(account.getAdvertiserId());
            rebateGetParam.setAdvertiserCompanyId(account.getAdvertiserCompanyId());
            rebateGetParam.setFirstIndustryId(account.getFirstIndustryId());
            rebateGetParam.setSecondIndustryId(account.getSecondIndustryId());
            rebateGetParam.setCustomId(account.getCustomId());
            rebateGetParam.setAccountTypeId(account.getAccountTypeId());
        }
        AccountOceanengineRebateInfo rebate = accountOceanengineRebateInfoService.getRebate(rebateGetParam);
        FundOceanengineTransferRecordRebateResult transferRecordRebate = new FundOceanengineTransferRecordRebateResult();
        transferRecordRebate.setId(entity.getId());
        if (rebate != null) {
            transferRecordRebate.setRebateRatio(rebate.getRebateRatio());
        }
        return transferRecordRebate;
    }

    @Override
    public List<FundOceanengineTransferRecordRebateResult> listRebateByIds(Collection<Long> ids) {
        if (Func.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return listRebateByEntityList(listByIds(ids));
    }

    @Override
    public List<FundOceanengineTransferRecordRebateResult> listRebateByEntityList(List<FundOceanengineTransferRecord> list) {
        if (Func.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<FundOceanengineTransferRecordRebateResult> rebateList = new ArrayList<>();
        Func.executeBatch(list, 1000, subList -> {
            // 获取广告主账户信息
            List<Long> advertiserIds = subList.stream().map(FundOceanengineTransferRecord::getAdvertiserId).distinct().toList();
            List<AccountOceanengineInfo> accountList = accountOceanengineInfoService.listByAdvertiserIds(advertiserIds);
            // 获取返点信息
            Map<Long, AccountOceanengineInfo> accountMap = accountList.stream()
                    .collect(Collectors.toMap(AccountOceanengineInfo::getAdvertiserId, a -> a));
            Map<AccountOceanengineRebateInfoGetParam, List<FundOceanengineTransferRecord>> rebateParamMap = subList.stream()
                    .collect(Collectors.groupingBy(transferRecord -> {
                        AccountOceanengineInfo account = accountMap.get(transferRecord.getAdvertiserId());
                        AccountOceanengineRebateInfoGetParam rebateGetParam = new AccountOceanengineRebateInfoGetParam();
                        if (account != null) {
                            rebateGetParam.setAdvertiserId(account.getAdvertiserId());
                            rebateGetParam.setAdvertiserCompanyId(account.getAdvertiserCompanyId());
                            rebateGetParam.setFirstIndustryId(account.getFirstIndustryId());
                            rebateGetParam.setSecondIndustryId(account.getSecondIndustryId());
                            rebateGetParam.setCustomId(account.getCustomId());
                            rebateGetParam.setAccountTypeId(account.getAccountTypeId());
                        }
                        rebateGetParam.setDate(transferRecord.getOceanengineTransferTime().toLocalDate());
                        return rebateGetParam;
                    }));
            List<FundOceanengineTransferRecordRebateResult> subRebateList = rebateParamMap.entrySet().stream()
                    .flatMap(entry -> {
                        AccountOceanengineRebateInfo rebate = accountOceanengineRebateInfoService.getRebate(entry.getKey());
                        return entry.getValue().stream().map(transferRecord -> {
                            FundOceanengineTransferRecordRebateResult transferRecordRebate = new FundOceanengineTransferRecordRebateResult();
                            transferRecordRebate.setId(transferRecord.getId());
                            if (rebate != null) {
                                transferRecordRebate.setRebateRatio(rebate.getRebateRatio());
                            }
                            return transferRecordRebate;
                        });
                    })
                    .toList();
            rebateList.addAll(subRebateList);
        });
        return rebateList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = ADD_LOCK_PRE, waitTime = 6000)
    public Rsp<List<String>> batchAdd(FundOceanengineTransferRecordBatchAddParam param) {
        List<String> oceanengineTransactionSeqList = param.getList().stream()
                .map(FundOceanengineTransferRecordAddParam::getOceanengineTransactionSeq)
                .toList();
        List<String> existsOceanengineTransactionSeqList = existsByTransactionSeqList(oceanengineTransactionSeqList);
        List<FundOceanengineTransferRecord> saveList = param.getList().stream()
                .filter(transferRecord -> !existsOceanengineTransactionSeqList.contains(transferRecord.getOceanengineTransactionSeq()))
                .filter(addParam -> checkTransferType(addParam).isSuccess())
                .map(addParam -> {
                    FundOceanengineTransferRecord transferRecord = Func.copy(addParam, FundOceanengineTransferRecord.class);
                    transferRecord.setId(idGenerator.generateId());
                    transferRecord.setTransferType(addParam.getTransferType().getType());
                    transferRecord.setAgentRebateRatio(BigDecimal.ZERO);
                    transferRecord.setAgentAmount(transferRecord.getOceanengineAmount());
                    transferRecord.setAgentTotalRebateRatio(BigDecimal.ZERO);
                    transferRecord.setAgentTotalAmount(transferRecord.getOceanengineAmount());
                    transferRecord.setChannelCommissionAmount(BigDecimal.ZERO);
                    return transferRecord;
                })
                .toList();
        if (Func.isEmpty(saveList)) {
            return RspHd.data(Collections.emptyList());
        }
        // 同步账户里的客户和渠道
        List<Long> advertiserIds = saveList.stream().map(FundOceanengineTransferRecord::getAdvertiserId).distinct().toList();
        List<AccountOceanengineInfo> oceanengineInfoList = accountOceanengineInfoService.listByAdvertiserIds(advertiserIds);
        Map<Long, AccountOceanengineInfo> oceanengineInfoMap = oceanengineInfoList.stream()
                .collect(Collectors.toMap(AccountOceanengineInfo::getAdvertiserId, i -> i));
        saveList.stream()
                .filter(transferRecord -> oceanengineInfoMap.containsKey(transferRecord.getAdvertiserId()))
                .forEach(transferRecord -> {
                    AccountOceanengineInfo oceanengineInfo = oceanengineInfoMap.get(transferRecord.getAdvertiserId());
                    transferRecord.setCustomId(oceanengineInfo.getCustomId());
                    transferRecord.setCustomChannelId(oceanengineInfo.getCustomChannelId());
                });
        saveBatch(saveList);
        return RspHd.data(saveList.stream().map(FundOceanengineTransferRecord::getOceanengineTransactionSeq).toList());
    }

    private Rsp<Void> checkTransferType(FundOceanengineTransferRecordAddParam param) {
        if (param.getTransferType() == FundOceanengineTransferType.ADD
                && param.getOceanengineAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return RspHd.fail("加款类型的金额不能小于等于0");
        }
        if (param.getTransferType() == FundOceanengineTransferType.REFUND
                && param.getOceanengineAmount().compareTo(BigDecimal.ZERO) >= 0) {
            return RspHd.fail("退款类型的金额不能大于等于0");
        }
        return RspHd.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = FundConstant.TRANSFER_OPERATE_LOCK_KEY, waitTime = 6000)
    public Rsp<Void> split(FundOceanengineTransferRecordSplitParam param) {
        Rsp<FundOceanengineTransferRecord> checkRsp = checkSplitParam(param);
        if (RspHd.isFail(checkRsp)) {
            return RspHd.fail(checkRsp);
        }
        FundOceanengineTransferRecord transferRecord = checkRsp.getData();
        BigDecimal firstAmount = param.getOceanengineAmountList().getFirst();
        FundOceanengineTransferRecord updateTransferRecord = new FundOceanengineTransferRecord()
                .setOceanengineAmount(firstAmount)
                .setAgentRebateRatio(BigDecimal.ZERO)
                .setAgentAmount(firstAmount)
                .setAgentTotalRebateRatio(BigDecimal.ZERO)
                .setAgentTotalAmount(firstAmount)
                .setChannelCommissionAmount(BigDecimal.ZERO)
                .setId(transferRecord.getId());
        List<FundOceanengineTransferRecord> addTransferRecordList = new ArrayList<>();
        for (int i = 1; i < param.getOceanengineAmountList().size(); i++) {
            BigDecimal amount = param.getOceanengineAmountList().get(i);
            FundOceanengineTransferRecord add = Func.copy(transferRecord, FundOceanengineTransferRecord.class)
                    .setOceanengineTransactionSeq(String.format("%s-%s", transferRecord.getOceanengineTransactionSeq(), i))
                    .setOceanengineAmount(amount)
                    .setAgentRebateRatio(BigDecimal.ZERO)
                    .setAgentAmount(amount)
                    .setAgentTotalRebateRatio(BigDecimal.ZERO)
                    .setAgentTotalAmount(amount)
                    .setChannelCommissionAmount(BigDecimal.ZERO)
                    .setId(null);
            addTransferRecordList.add(add);
        }
        // 检查是否被拆分过（新增的转账编号是否存在）
        List<String> oceanengineTransactionSeqList = addTransferRecordList.stream()
                .map(FundOceanengineTransferRecord::getOceanengineTransactionSeq)
                .toList();
        if (Func.isNotEmpty(existsByTransactionSeqList(oceanengineTransactionSeqList))) {
            return RspHd.fail("转账记录已被拆分，不能继续拆分");
        }
        updateById(updateTransferRecord);
        saveBatch(addTransferRecordList);
        return RspHd.success();
    }

    private Rsp<FundOceanengineTransferRecord> checkSplitParam(FundOceanengineTransferRecordSplitParam param) {
        if (param.getOceanengineAmountList().stream().anyMatch(amount -> amount == null || amount.compareTo(BigDecimal.ZERO) == 0)) {
            return RspHd.fail("拆分金额异常：金额不能为0");
        }
        FundOceanengineTransferRecord transferRecord = getById(param.getId());
        if (transferRecord == null) {
            return RspHd.fail("转账记录不存在");
        }
        // abs防止正负金额混入
        BigDecimal sum = param.getOceanengineAmountList().stream()
                .map(BigDecimal::abs)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (sum.compareTo(transferRecord.getOceanengineAmount().abs()) != 0) {
            return RspHd.fail("拆分金额异常：总金额不等");
        }
        if (transferRecord.getRechargeId() > 0) {
            return RspHd.fail("转账记录已关联充值，不允许拆分");
        }
        if (!Objects.equals(transferRecord.getOrderConfirmState(), FundConfirmAmountState.UN_CONFIRM.getState())) {
            return RspHd.fail("转账记录已关联服务订单，不允许拆分");
        }
        if (!Objects.equals(transferRecord.getPaymentConfirmState(), FundConfirmAmountState.UN_CONFIRM.getState())) {
            return RspHd.fail("转账记录已关联收款，不允许拆分");
        }
        return RspHd.data(transferRecord);
    }

}
