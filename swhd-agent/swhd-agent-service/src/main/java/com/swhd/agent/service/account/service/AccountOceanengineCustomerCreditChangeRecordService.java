package com.swhd.agent.service.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordListParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerCreditChangeRecord;

import java.util.List;

/**
 * 自助充值额度变更记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface AccountOceanengineCustomerCreditChangeRecordService extends IBaseHdService<AccountOceanengineCustomerCreditChangeRecord> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<AccountOceanengineCustomerCreditChangeRecord> page(AccountOceanengineCustomerCreditChangeRecordPageParam param);

	/**
	 * 列表查询
	 *
	 * @param param 查询参数
	 * @return List
	 */
	List<AccountOceanengineCustomerCreditChangeRecord> list(AccountOceanengineCustomerCreditChangeRecordListParam param);

}
