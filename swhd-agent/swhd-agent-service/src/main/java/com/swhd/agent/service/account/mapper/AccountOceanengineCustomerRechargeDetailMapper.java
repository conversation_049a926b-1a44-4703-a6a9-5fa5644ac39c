package com.swhd.agent.service.account.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.magiccube.mybatis.base.BaseHdMapper;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自助充值明细表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface AccountOceanengineCustomerRechargeDetailMapper extends BaseHdMapper<AccountOceanengineCustomerRechargeDetail> {

    /**
     * 分页查询充值明细（关联代理商和账户信息）
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<AccountOceanengineCustomerRechargeDetailResult> pageWithAgent(Page<AccountOceanengineCustomerRechargeDetail> page, @Param("param") AccountOceanengineCustomerRechargeDetailListParam param);

    /**
     * 查询充值明细列表（关联代理商和账户信息）
     *
     * @param param 查询参数
     * @return 充值明细列表
     */
    List<AccountOceanengineCustomerRechargeDetailResult> listWithAgent(@Param("param") AccountOceanengineCustomerRechargeDetailListParam param);
}
