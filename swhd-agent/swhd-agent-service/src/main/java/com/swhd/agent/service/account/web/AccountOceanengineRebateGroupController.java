package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.client.AccountOceanengineRebateGroupClient;
import com.swhd.agent.api.account.dto.param.rebate.group.AccountOceanengineRebateGroupAddParam;
import com.swhd.agent.api.account.dto.param.rebate.group.AccountOceanengineRebateGroupPageParam;
import com.swhd.agent.api.account.dto.param.rebate.group.AccountOceanengineRebateGroupUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineRebateGroupResult;
import com.swhd.agent.service.account.entity.AccountOceanengineRebateGroup;
import com.swhd.agent.service.account.service.AccountOceanengineRebateGroupService;
import com.swhd.agent.service.account.service.AccountOceanengineRebateInfoService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-02
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineRebateGroupClient.BASE_PATH)
public class AccountOceanengineRebateGroupController implements AccountOceanengineRebateGroupClient {

    private final AccountOceanengineRebateGroupService accountOceanengineRebateGroupService;

    private final AccountOceanengineRebateInfoService accountOceanengineRebateInfoService;

    @Override
    public Rsp<PageResult<AccountOceanengineRebateGroupResult>> page(AccountOceanengineRebateGroupPageParam param) {
        IPage<AccountOceanengineRebateGroup> iPage = accountOceanengineRebateGroupService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineRebateGroupResult.class));
    }

    @Override
    public Rsp<AccountOceanengineRebateGroupResult> getById(Long id) {
        AccountOceanengineRebateGroup entity = accountOceanengineRebateGroupService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineRebateGroupResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineRebateGroupResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineRebateGroup> list = accountOceanengineRebateGroupService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineRebateGroupResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineRebateGroupAddParam param) {
        AccountOceanengineRebateGroup group = Func.copy(param, AccountOceanengineRebateGroup.class);
        boolean result = accountOceanengineRebateGroupService.save(group);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineRebateGroupUpdateParam param) {
        AccountOceanengineRebateGroup group = Func.copy(param, AccountOceanengineRebateGroup.class);
        boolean result = accountOceanengineRebateGroupService.updateById(group);
        return RspHd.status(result);
    }

    @Override
    @Lockable(prefixKey = AccountOceanengineRebateInfoService.LOCK_KEY_PRE, waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Void> removeById(Long id) {
        boolean result = accountOceanengineRebateGroupService.removeById(id);
        if (result) {
            accountOceanengineRebateInfoService.removeByGroupId(id);
        }
        return RspHd.status(result);
    }

}
