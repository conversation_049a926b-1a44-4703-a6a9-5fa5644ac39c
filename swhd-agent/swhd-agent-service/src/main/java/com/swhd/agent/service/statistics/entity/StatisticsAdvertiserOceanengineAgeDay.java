package com.swhd.agent.service.statistics.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 广告主性别日统计表实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_statistics_advertiser_oceanengine_age_day")
@EqualsAndHashCode(callSuper = false, onlyExplicitlyIncluded = true)
public class StatisticsAdvertiserOceanengineAgeDay extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @EqualsAndHashCode.Include
    @Schema(description = "代理商id")
    private Long agentId;

    @EqualsAndHashCode.Include
    @Schema(description = "广告主ID")
    private Long advertiserId;

    @EqualsAndHashCode.Include
    @Schema(description = "统计日期")
    private LocalDate statisticDate;

    @EqualsAndHashCode.Include
    @Schema(description = "年龄")
    private String age;

    @Schema(description = "总消耗")
    private BigDecimal statCost;

    @Schema(description = "展示次数")
    private Integer showCnt;

    @Schema(description = "点击次数")
    private Integer clickCnt;

    @Schema(description = "转化次数")
    private Integer convertCnt;

}
