package com.swhd.agent.service.statistics.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自运营统计汇总日表实体类
 *
 * <AUTHOR>
 * @since 2024-02-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_statistics_self_operate_oceanengine_total_day")
public class StatisticsSelfOperateOceanengineTotalDay extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate date;

    @Schema(description = "自运营消耗(单位元)")
    private BigDecimal selfOperateConsume;

    @Schema(description = "自运营消耗占比")
    private BigDecimal selfOperateConsumeRatio;

    @Schema(description = "自运营账户数")
    private Integer selfOperateAccountNum;

    @Schema(description = "自运营账户数占比")
    private BigDecimal selfOperateAccountNumRatio;

    @Schema(description = "总消耗(单位元)")
    private BigDecimal totalConsume;

    @Schema(description = "总账户数")
    private Integer totalAccountNum;

}
