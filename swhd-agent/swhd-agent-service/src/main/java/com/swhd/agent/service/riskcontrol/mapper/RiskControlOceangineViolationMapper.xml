<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.agent.service.riskcontrol.mapper.RiskControlOceanengineViolationMapper">

    <select id="adClientAddressAuth" resultType="java.lang.Integer">
        select count(distinct trcov.company_id) from tcreative_risk_control_oceanengine_violation trcov
        where trcov.is_delete = 0
        <if test="param.illegalMaterialId != null">
            and trcov.illegal_material_id = #{param.illegalMaterialId}
        </if>
        <if test="param.promotionStatus != null and param.promotionStatus != ''">
            and trcov.promotion_status = #{param.promotionStatus}
        </if>
        <if test="param.finalOperatorTag != null and param.finalOperatorTag != ''">
            and trcov.final_operator_tag = #{param.finalOperatorTag}
        </if>
        <if test="param.advertiserId != null">
            and trcov.advertiser_id = #{param.advertiserId}
        </if>
          <if test="param.level != null and param.level != ''">
            and trcov.level = #{param.level}
        </if>
        <if test="param.category != null and param.category != ''">
            and trcov.category = #{param.category}
        </if>
        <if test="param.materialStatus != null and param.materialStatus != ''">
            and trcov.material_status = #{param.materialStatus}
        </if>
        <if test="param.excuteStatus != null">
            and trcov.excute_status = #{param.excuteStatus}
        </if>
        <if test="param.companyId != null">
            and trcov.company_id = #{param.companyId}
        </if>
        <if test="param.optimizerId != null">
            and trcov.optimizer_id = #{param.optimizerId}
        </if>
        <if test="param.promotionName != null and param.promotionName != ''">
            and trcov.promotion_name like concat('%', #{param.promotionName}, '%')
        </if>
        <if test="param.advertiserName != null and param.advertiserName != ''">
            and trcov.advertiser_name like concat('%', #{param.advertiserName}, '%')
        </if>
        <if test="param.companyName != null and param.companyName != ''">
            and trcov.company_name like concat('%', #{param.companyName}, '%')
        </if>
        <if test="param.optimizerName != null and param.optimizerName != ''">
            and trcov.optimizer_name like concat('%', #{param.optimizerName}, '%')
        </if>
    </select>

    <select id="materialStatistics" resultType="com.swhd.agent.api.riskcontrol.dto.result.RiskContorlOceanengineMaterialStatisticsResult">
        SELECT COUNT(*) AS allMaterialNum,
            COUNT(CASE WHEN trcov.excute_status = 1 THEN 1 END) AS excuteMaterialNum,
            AVG(CASE WHEN trcov.excute_status = 1 THEN TIMESTAMPDIFF(SECOND, trcov.send_date, trcov.excute_date) ELSE 0 END) AS excuteAvTime
            from tcreative_risk_control_oceanengine_violation trcov
            where trcov.is_delete = 0
            <if test="param.startTime != null">
                <![CDATA[
                AND DATE(trcov.send_date) >=  #{param.startTime}
            ]]>
            </if>
            <if test="param.endTime != null">
                <![CDATA[
                AND DATE(trcov.send_date) <=  #{param.endTime}
            ]]>
            </if>
    </select>


    <select id="pagematerialStatistics" resultType="com.swhd.agent.api.riskcontrol.dto.result.RiskContorlOceanengineMaterialStatisticsSearchResult">
        SELECT trcov.company_name,trcov.company_id,trcov.advertiser_name,trcov.advertiser_id,trcov.level,trcov.category,COUNT(*) AS all_material_num,
               COUNT(CASE WHEN trcov.excute_status = 1 THEN 1 END) AS excute_material_num,
                COUNT(CASE WHEN trcov.excute_status = 0 THEN 1 END) AS un_excute_material_num,
                ROUND(COUNT(CASE WHEN trcov.excute_status = 1 THEN 1 END) / NULLIF(COUNT(*), 0)*100, 2) AS excute_rate,
               AVG(CASE WHEN trcov.excute_status = 1 THEN TIMESTAMPDIFF(SECOND, trcov.send_date, trcov.excute_date) ELSE 0 END) AS excute_av_time
        from tcreative_risk_control_oceanengine_violation trcov
        where trcov.is_delete = 0 and trcov.company_id!=0
        <if test="param.startTime != null">
            <![CDATA[
            AND DATE(trcov.send_date) >=  #{param.startTime}
        ]]>
        </if>
        <if test="param.endTime != null">
            <![CDATA[
            AND DATE(trcov.send_date) <=  #{param.endTime}
        ]]>
        </if>
        <if test="param.dimension == 0">
            group by trcov.company_id
        </if>
        <if test="param.dimension == 1">
            group by trcov.advertiser_id
        </if>
        <if test="param.dimension == 2">
            group by trcov.level
        </if>
        <if test="param.dimension == 3">
            group by trcov.category
        </if>;
    </select>
</mapper>