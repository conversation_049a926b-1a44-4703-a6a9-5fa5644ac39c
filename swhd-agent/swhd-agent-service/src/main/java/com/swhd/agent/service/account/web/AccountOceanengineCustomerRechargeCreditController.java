package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditAddParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditPageParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditStatusParam;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditConfigResult;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditResult;
import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeCreditClient;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeCredit;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeCreditService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineCustomerRechargeCreditClient.BASE_PATH)
public class AccountOceanengineCustomerRechargeCreditController implements AccountOceanengineCustomerRechargeCreditClient {

    private final AccountOceanengineCustomerRechargeCreditService accountOceanengineCustomerRechargeCreditService;

    @Override
    public Rsp<PageResult<AccountOceanengineCustomerRechargeCreditResult>> page(AccountOceanengineCustomerRechargeCreditPageParam param) {
        IPage<AccountOceanengineCustomerRechargeCredit> iPage = accountOceanengineCustomerRechargeCreditService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineCustomerRechargeCreditResult.class));
    }

    @Override
    public Rsp<AccountOceanengineCustomerRechargeCreditResult> getById(Long id) {
        AccountOceanengineCustomerRechargeCredit entity = accountOceanengineCustomerRechargeCreditService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineCustomerRechargeCreditResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerRechargeCreditResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineCustomerRechargeCredit> list = accountOceanengineCustomerRechargeCreditService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineCustomerRechargeCreditResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineCustomerRechargeCreditAddParam param) {
        boolean result = accountOceanengineCustomerRechargeCreditService.save(Func.copy(param, AccountOceanengineCustomerRechargeCredit.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineCustomerRechargeCreditUpdateParam param) {
        boolean result = accountOceanengineCustomerRechargeCreditService.updateById(Func.copy(param, AccountOceanengineCustomerRechargeCredit.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanengineCustomerRechargeCreditService.removeByIds(ids);
        return RspHd.status(result);
    }

    @Override
    public Rsp<AccountOceanengineCustomerRechargeCreditConfigResult> getByCustomId(Long customId) {
        AccountOceanengineCustomerRechargeCreditConfigResult result = accountOceanengineCustomerRechargeCreditService.getByCustomId(customId);
        return RspHd.data(result);
    }

    @Override
    public Rsp<Void> updateCreditStatus(AccountOceanengineCustomerRechargeCreditStatusParam param) {
        return accountOceanengineCustomerRechargeCreditService.updateCreditStatus(param);
    }

}
