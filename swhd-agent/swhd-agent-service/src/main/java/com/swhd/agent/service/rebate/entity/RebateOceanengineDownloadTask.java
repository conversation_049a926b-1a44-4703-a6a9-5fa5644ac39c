package com.swhd.agent.service.rebate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 巨量引擎明点下载任务表实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tagent_rebate_oceanengine_download_task", autoResultMap = true)
public class RebateOceanengineDownloadTask extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "统计日期")
    private LocalDate statisticDate;

    @Schema(description = "统计结束日期")
    private LocalDate statisticEndDate;

    @Schema(description = "下载任务对应的查询ID")
    private String queryId;

    @Schema(description = "任务ID列表")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<String> taskList;

    @Schema(description = "查询状态：1-初始化，2-运行中，3-成功，4-失败")
    private Integer queryStatus;

    @Schema(description = "下载状态：0-未处理，1-处理中，2-已处理")
    private Integer downloadState;

}
