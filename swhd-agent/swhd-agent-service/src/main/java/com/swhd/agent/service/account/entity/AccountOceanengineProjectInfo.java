package com.swhd.agent.service.account.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 广告计划信息表实体类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_account_oceanengine_project_info")
public class AccountOceanengineProjectInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目状态")
    private String projectStatus;

    @Schema(description = "项目状态名称")
    private String projectStatusName;

    @Schema(description = "计划创建时间")
    private LocalDateTime projectCreateTime;

    @Schema(description = "计划修改时间")
    private LocalDateTime projectModifyTime;

    @Schema(description = "投放模式：MANUAL-手动投放;PROCEDURAL-自动投放")
    private String deliveryMode;

    @Schema(description = "广告类型：1-巨量广告，2-巨量千川，3-巨量星图，4-巨量本地推")
    private Integer adsType;

    @Schema(description = "营销目标：1-图文商品、2-直播间")
    private Integer marGoal;

    @Schema(description = "推广类型：1-通投、2-搜索、1001-商城")
    private Integer campaignType;

    @Schema(description = "投放类型：1-标准、2-全域")
    private Integer deliveryGoal;

}
