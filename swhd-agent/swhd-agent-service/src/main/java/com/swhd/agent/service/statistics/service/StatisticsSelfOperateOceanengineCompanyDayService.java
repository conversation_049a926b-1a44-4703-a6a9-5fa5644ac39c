package com.swhd.agent.service.statistics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.statistics.dto.param.selfoperate.StatisticsSelfOperateOceanengineDateCompanyParam;
import com.swhd.agent.api.statistics.dto.param.selfoperate.company.StatisticsSelfOperateOceanengineCompanyDayPageParam;
import com.swhd.agent.service.statistics.entity.StatisticsSelfOperateOceanengineCompanyDay;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

import java.time.LocalDate;
import java.util.List;

/**
 * 自运营统计公司日表 服务类
 *
 * <AUTHOR>
 * @since 2024-02-02
 */
public interface StatisticsSelfOperateOceanengineCompanyDayService extends IBaseHdService<StatisticsSelfOperateOceanengineCompanyDay> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<StatisticsSelfOperateOceanengineCompanyDay> page(StatisticsSelfOperateOceanengineCompanyDayPageParam param);

    /**
     * 根据日期获取
     *
     * @param date 日期
     * @return List
     */
    List<StatisticsSelfOperateOceanengineCompanyDay> listByDate(LocalDate date);

    /**
     * 根据日期和公司获取列表
     *
     * @param paramList 参数列表
     * @return List
     */
    List<StatisticsSelfOperateOceanengineCompanyDay> listByDateAndCompany(List<StatisticsSelfOperateOceanengineDateCompanyParam> paramList);

    /**
     * 批量保存并汇总统计
     *
     * @param date 统计日期
     * @param list 列表数据
     */
    void saveBatchAndSum(LocalDate date, List<StatisticsSelfOperateOceanengineCompanyDay> list);

}
