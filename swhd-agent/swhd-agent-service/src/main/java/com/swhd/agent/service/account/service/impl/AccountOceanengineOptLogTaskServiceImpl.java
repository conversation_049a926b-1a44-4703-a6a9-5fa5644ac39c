package com.swhd.agent.service.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.dto.param.task.AccountOceanengineOptLogNeedFetchTaskPageParam;
import com.swhd.agent.service.account.dto.AccountOceanengineOptLogTaskPromotionDTO;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.entity.AccountOceanenginePromotionInfo;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.task.AccountOceanengineOptLogTaskPageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineOptLogTask;
import com.swhd.agent.service.account.mapper.AccountOceanengineOptLogTaskMapper;
import com.swhd.agent.service.account.service.AccountOceanengineOptLogTaskService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 广告主巨量操作日志采集任务表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineOptLogTaskServiceImpl extends BaseHdServiceImpl<AccountOceanengineOptLogTaskMapper, AccountOceanengineOptLogTask> implements AccountOceanengineOptLogTaskService {

    @Override
    public IPage<AccountOceanengineOptLogTask> page(AccountOceanengineOptLogTaskPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getAdvertiserId()), AccountOceanengineOptLogTask::getAdvertiserId, param.getAdvertiserId())
                .eq(Func.isNotEmpty(param.getLastFetchTime()), AccountOceanengineOptLogTask::getLastFetchTime, param.getLastFetchTime())
                .eq(Func.isNotEmpty(param.getLastDataTime()), AccountOceanengineOptLogTask::getLastDataTime, param.getLastDataTime())
                .eq(Func.isNotEmpty(param.getFailureReason()), AccountOceanengineOptLogTask::getFailureReason, param.getFailureReason())
                .orderByDesc(AccountOceanengineOptLogTask::getCreateTime)
                .orderByDesc(AccountOceanengineOptLogTask::getId)
                .page(convertToPage(param));
    }

    @Override
    public IPage<AccountOceanengineOptLogTask> pageNeedFetchTask(AccountOceanengineOptLogNeedFetchTaskPageParam param){
        return baseMapper.pageNeedFetchTask(param,convertToPage(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createManagedByAgentAccountTask(Long agentId){
        List<AccountOceanengineInfo> accountOceanengineInfos = baseMapper.listNotTaskManagedByAgentAccount(agentId);
        List<AccountOceanengineOptLogTask> addTasks = accountOceanengineInfos.stream().map(account -> {
            AccountOceanengineOptLogTask task = new AccountOceanengineOptLogTask();
            task.setTenantId(Long.parseLong(account.getTenantId()))
                    .setLastFetchTime(LocalDateTime.now().minusDays(1))
                    .setAdvertiserId(account.getAdvertiserId());
            return task;
        }).toList();
        this.saveBatch(addTasks);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPromotionManagedByAgentAccountTask(Long agentId) {
        List<AccountOceanenginePromotionInfo> promotionInfos = baseMapper.listNotTaskPromotionManagedByAgentAccount(agentId);
        if(CollectionUtil.isEmpty(promotionInfos)){
            return;
        }

        // 按广告主ID分组
        Map<Long, List<AccountOceanenginePromotionInfo>> advertiserPromotionsMap = promotionInfos.stream()
                .collect(Collectors.groupingBy(AccountOceanenginePromotionInfo::getAdvertiserId));

        List<AccountOceanengineOptLogTask> addTasks = new ArrayList<>();
        
        // 遍历每个广告主
        for (Map.Entry<Long, List<AccountOceanenginePromotionInfo>> entry : advertiserPromotionsMap.entrySet()) {
            Long advertiserId = entry.getKey();
            List<AccountOceanenginePromotionInfo> advertiserPromotions = entry.getValue();
            
            // 查询该广告主是否有已存在的任务
            LocalDateTime lastFetchTime;
            AccountOceanengineOptLogTask existingTask = lambdaQuery()
                    .eq(AccountOceanengineOptLogTask::getAdvertiserId, advertiserId)
                    .orderByDesc(AccountOceanengineOptLogTask::getLastFetchTime)
                    .last("limit 1")
                    .one();
            
            if (existingTask != null && existingTask.getLastFetchTime() != null) {
                // 如果存在任务，使用该任务的LastFetchTime
                lastFetchTime = existingTask.getLastFetchTime();
            } else {
                // 如果不存在任务，使用当前时间的整十分钟
                LocalDateTime now = LocalDateTime.now();
                lastFetchTime = now.withMinute((now.getMinute() / 10) * 10).withSecond(0).withNano(0);
            }
            
            // 为该广告主的所有计划创建任务
            for (AccountOceanenginePromotionInfo promotion : advertiserPromotions) {
                AccountOceanengineOptLogTask task = new AccountOceanengineOptLogTask();
                task.setTenantId(promotion.getTenantId())
                        .setLastFetchTime(lastFetchTime)
                        .setPromotionId(promotion.getPromotionId())
                        .setAdvertiserId(promotion.getAdvertiserId());
                addTasks.add(task);
            }
        }
        
        if (!addTasks.isEmpty()) {
            this.saveBatch(addTasks);
        }
    }

    @Override
    public List<AccountOceanengineOptLogTaskPromotionDTO> listNeedFetchPromotionTask(Long advertiserId, Integer intervalSecond) {
        return baseMapper.listNeedFetchPromotionTask(advertiserId, intervalSecond);
    }

}
