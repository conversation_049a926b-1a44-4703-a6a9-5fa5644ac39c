package com.swhd.agent.service.account.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * 巨量引擎的账户返点分组表实体类
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tagent_account_oceanengine_rebate_group", autoResultMap = true)
public class AccountOceanengineRebateGroup extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "返点类型：1-行业，2-广告主公司，3-广告主账户")
    private Integer rebateType;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @TableField(typeHandler = JsonTypeHandler.class)
    @Schema(description = "客户id列表，0代表所有客户")
    private List<Long> customIds;

    @TableField(typeHandler = JsonTypeHandler.class)
    @Schema(description = "账户类型id列表，0代表所有账户类型")
    private List<Long> accountTypeIds;

}
