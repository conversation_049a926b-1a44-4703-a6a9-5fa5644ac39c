package com.swhd.agent.service.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;

import java.util.List;

/**
 * 自助充值明细表 服务类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface AccountOceanengineCustomerRechargeDetailService extends IBaseHdService<AccountOceanengineCustomerRechargeDetail> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<AccountOceanengineCustomerRechargeDetailResult> page(AccountOceanengineCustomerRechargeDetailPageParam param);

	/**
	 * 列表查询
	 *
	 * @param param 查询参数
	 * @return List
	 */
	List<AccountOceanengineCustomerRechargeDetailResult> list(AccountOceanengineCustomerRechargeDetailListParam param);


}
