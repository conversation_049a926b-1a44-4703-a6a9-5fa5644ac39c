package com.swhd.agent.service.fund.service.impl;

import com.swhd.agent.api.fund.dto.param.config.FundConfigSaveParam;
import com.swhd.agent.service.fund.entity.FundConfig;
import com.swhd.agent.service.fund.mapper.FundConfigMapper;
import com.swhd.agent.service.fund.service.FundConfigService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 代理商配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Service
@AllArgsConstructor
public class FundConfigServiceImpl extends BaseHdServiceImpl<FundConfigMapper, FundConfig> implements FundConfigService {

    @Override
    public FundConfig getOne() {
        TenantHolder.requiredTenant();
        return lambdaQuery().limitOne();
    }

    @Override
    public FundConfig getOneNotNull() {
        return Optional.ofNullable(getOne())
                .orElseGet(() -> {
                    FundConfig config = new FundConfig();
                    config.setChannelRebateRatio(BigDecimal.ZERO);
                    config.setOrderNoPre(Constant.Str.EMPTY);
                    return config;
                });
    }

    @Override
    @Lockable(prefixKey = "agent:fund:config:save", waitTime = 6000)
    public Rsp<Void> save(FundConfigSaveParam param) {
        FundConfig config = getOne();
        FundConfig save = Func.copy(param, FundConfig.class);
        if (Func.isNotEmpty(save.getOrderNoPre())) {
            save.setOrderNoPre(save.getOrderNoPre().trim());
        }
        if (config == null) {
            return RspHd.status(save(save));
        }
        save.setId(config.getId());
        return RspHd.status(updateById(save));
    }

}
