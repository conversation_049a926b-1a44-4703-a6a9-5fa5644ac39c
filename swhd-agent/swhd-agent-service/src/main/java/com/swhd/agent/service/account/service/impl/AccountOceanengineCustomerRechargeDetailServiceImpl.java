package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;
import com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeDetailMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeDetailService;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.mp.util.SortOrderUtil;
import com.swj.magiccube.tool.bean.BeanUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自助充值明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeDetailServiceImpl
        extends BaseHdServiceImpl<AccountOceanengineCustomerRechargeDetailMapper, AccountOceanengineCustomerRechargeDetail>
        implements AccountOceanengineCustomerRechargeDetailService {

    @Override
    public IPage<AccountOceanengineCustomerRechargeDetailResult> page(AccountOceanengineCustomerRechargeDetailPageParam param) {
        AccountOceanengineCustomerRechargeDetailListParam listParam = BeanUtil.copy(param, AccountOceanengineCustomerRechargeDetailListParam.class);
        Page<AccountOceanengineCustomerRechargeDetail> page = convertToPage(param);
        if (Func.isNotEmpty(param.getSort())) {
            List<OrderItem> orders = SortOrderUtil.normalize(param.getSort()).stream()
                    .map((item) -> (new OrderItem()).setColumn(item.getProperty()).setAsc(item.getAscending()))
                    .toList();
            page.setOrders(orders);
        }else{
            page.setOrders(List.of(new OrderItem().setColumn("execute_time").setAsc(false),
                    new OrderItem().setColumn("id").setAsc(false)));
        }
        return baseMapper.pageWithAgent(page, listParam);
    }

    @Override
    public List<AccountOceanengineCustomerRechargeDetailResult> list(AccountOceanengineCustomerRechargeDetailListParam param) {
        return baseMapper.listWithAgent(param);
    }

}
