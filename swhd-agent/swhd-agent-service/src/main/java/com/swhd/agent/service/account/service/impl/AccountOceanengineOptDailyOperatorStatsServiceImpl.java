package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.agent.api.account.dto.param.stats.AccountOceanengineOptDailyOperatorStatsPageParam;
import com.swhd.agent.service.account.entity.AccountOceanengineOptDailyOperatorStats;
import com.swhd.agent.service.account.mapper.AccountOceanengineOptDailyOperatorStatsMapper;
import com.swhd.agent.service.account.service.AccountOceanengineOptDailyOperatorStatsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 广告主账户每日操作人操作统计表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Service
@AllArgsConstructor
public class AccountOceanengineOptDailyOperatorStatsServiceImpl extends BaseHdServiceImpl<AccountOceanengineOptDailyOperatorStatsMapper, AccountOceanengineOptDailyOperatorStats> implements AccountOceanengineOptDailyOperatorStatsService {

    @Override
    public IPage<AccountOceanengineOptDailyOperatorStats> page(AccountOceanengineOptDailyOperatorStatsPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getAdvertiserId()), AccountOceanengineOptDailyOperatorStats::getAdvertiserId, param.getAdvertiserId())
                .eq(Func.isNotEmpty(param.getStatDate()), AccountOceanengineOptDailyOperatorStats::getStatDate, param.getStatDate())
                .eq(Func.isNotEmpty(param.getDailyStatsId()), AccountOceanengineOptDailyOperatorStats::getDailyStatsId, param.getDailyStatsId())
                .eq(Func.isNotEmpty(param.getOperationCount()), AccountOceanengineOptDailyOperatorStats::getOperationCount, param.getOperationCount())
                .eq(Func.isNotEmpty(param.getOperator()), AccountOceanengineOptDailyOperatorStats::getOperator, param.getOperator())
                .orderByDesc(AccountOceanengineOptDailyOperatorStats::getCreateTime)
                .orderByDesc(AccountOceanengineOptDailyOperatorStats::getId)
                .page(convertToPage(param));
    }

}
