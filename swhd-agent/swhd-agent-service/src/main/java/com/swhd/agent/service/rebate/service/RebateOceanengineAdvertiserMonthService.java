package com.swhd.agent.service.rebate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.rebate.dto.param.oceanengine.advertiser.RebateOceanengineAdvertiserMonthPageParam;
import com.swhd.agent.api.rebate.dto.param.oceanengine.advertiser.RebateOceanengineAdvertiserSumPageParam;
import com.swhd.agent.api.rebate.dto.result.RebateOceanengineAdvertiserSumResult;
import com.swhd.agent.service.rebate.entity.RebateOceanengineAdvertiserMonth;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

import java.time.LocalDate;
import java.util.List;

/**
 * 巨量引擎明点广告主月度表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface RebateOceanengineAdvertiserMonthService extends IBaseHdService<RebateOceanengineAdvertiserMonth> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<RebateOceanengineAdvertiserMonth> page(RebateOceanengineAdvertiserMonthPageParam param);

    /**
     * 广告主统计分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<RebateOceanengineAdvertiserSumResult> advertiserSumPage(RebateOceanengineAdvertiserSumPageParam param);

    /**
     * 广告主统计列表查询
     *
     * @param param 查询参数
     * @return List
     */
    List<RebateOceanengineAdvertiserSumResult> advertiserSumList(RebateOceanengineAdvertiserSumPageParam param);

    /**
     * 批量保存
     *
     * @param statisticDate 统计日期
     * @param agentId       代理商id
     * @param saveList      保存列表
     */
    void batchSave(LocalDate statisticDate, Long agentId, List<RebateOceanengineAdvertiserMonth> saveList);

}
