package com.swhd.agent.service.fund.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 收款记录表实体类
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_fund_payment_record")
public class FundPaymentRecord extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "业务类型id：tagent_type_info#id")
    private Long bizTypeId;

    @Schema(description = "收款类型id：tagent_type_info#id")
    private Long paymentTypeId;

    @Schema(description = "收款负责人")
    private String paymentManager;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customId;

    @Schema(description = "付款人")
    private String payer;

    @Schema(description = "收款总金额(单位元)")
    private BigDecimal amount;

    @Schema(description = "预存金额(单位元)")
    private BigDecimal preStoreAmount;

    @Schema(description = "服务费金额(单位元)")
    private BigDecimal serviceChargeAmount;

    @Schema(description = "关联转账的巨量方舟转账金额(单位元)")
    private BigDecimal transferOceanengineAmount;

    @Schema(description = "关联转账的代理商总收款金额(单位元)")
    private BigDecimal transferAgentTotalAmount;

    @Schema(description = "银行水单图片oss key")
    private String bankReceiptOssKey;

    @Schema(description = "收款日期")
    private LocalDate paymentDate;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "代理商账户充值状态：0-未充值，1-已充值")
    private Integer agentAccountAddState;

}
