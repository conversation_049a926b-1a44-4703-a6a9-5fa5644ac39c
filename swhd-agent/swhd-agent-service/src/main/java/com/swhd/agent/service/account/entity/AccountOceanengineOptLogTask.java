package com.swhd.agent.service.account.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 广告主巨量操作日志采集任务表实体类
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_account_oceanengine_opt_log_task")
public class AccountOceanengineOptLogTask extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "广告主ID")
    private Long advertiserId;

    @Schema(description = "最后拉取时间")
    private LocalDateTime lastFetchTime;

    @Schema(description = "计划ID")
    private Long promotionId;

    @Schema(description = "最后数据时间")
    private LocalDateTime lastDataTime;

    @Schema(description = "失败原因")
    private String failureReason;

}
