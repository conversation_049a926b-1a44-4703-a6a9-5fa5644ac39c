package com.swhd.agent.service.riskcontrol.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.riskcontrol.dto.param.RiskContorlOceanengineMaterialStatisticsParam;
import com.swhd.agent.api.riskcontrol.dto.param.RiskControlOceanengineViolationListParam;
import com.swhd.agent.api.riskcontrol.dto.param.RiskControlOceanengineViolationPageParam;
import com.swhd.agent.api.riskcontrol.dto.param.RiskControlOceanengineViolationUpdateParam;
import com.swhd.agent.api.riskcontrol.dto.result.RiskContorlOceanengineMaterialStatisticsResult;
import com.swhd.agent.api.riskcontrol.dto.result.RiskContorlOceanengineMaterialStatisticsSearchResult;
import com.swhd.agent.service.riskcontrol.entity.RiskControlOceanengineViolation;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.PageResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 风控违规素材信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
public interface RiskControlOceanengineViolationService extends IBaseHdService<RiskControlOceanengineViolation> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<RiskControlOceanengineViolation> page(RiskControlOceanengineViolationPageParam param);

	int adClientAddressAuth(RiskControlOceanengineViolationPageParam param);

	void updateBatchByPromotionIdAndMaterialId(List<RiskControlOceanengineViolationUpdateParam> param);

	RiskContorlOceanengineMaterialStatisticsResult materialStatistics(RiskContorlOceanengineMaterialStatisticsParam param);

	PageResult<RiskContorlOceanengineMaterialStatisticsSearchResult> materialStatisticsSearch(RiskContorlOceanengineMaterialStatisticsParam param);

	List<RiskControlOceanengineViolation> list(RiskControlOceanengineViolationListParam param);
}
