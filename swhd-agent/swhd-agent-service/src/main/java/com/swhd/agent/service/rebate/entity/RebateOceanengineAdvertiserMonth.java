package com.swhd.agent.service.rebate.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 巨量引擎明点广告主月度表实体类
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tagent_rebate_oceanengine_advertiser_month")
public class RebateOceanengineAdvertiserMonth extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "统计日期")
    private LocalDate statisticDate;

    @Schema(description = "代理商id")
    private Long agentId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "广告主id")
    private Long advertiserId;

    @Schema(description = "运营标签")
    private String operatorTag;

    @Schema(description = "竞价分类")
    private String rebateCalcExternalIndustryCategory;

    @Schema(description = "总消耗（现金消耗+赠款消耗）")
    private BigDecimal cost;

    @Schema(description = "业绩消耗（现金消耗）")
    private BigDecimal performanceCost;

    @Schema(description = "优质返点金额")
    private BigDecimal highQualityRebateAmount;

    @Schema(description = "首发返点金额")
    private BigDecimal firstEffectiveRebateAmount;

    @Schema(description = "直播返点金额")
    private BigDecimal liveRebateAmount;

}
