<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.agent.service.account.mapper.AccountOceanengineCustomerRechargeDetailMapper">

    <!-- 基础查询条件 -->
    <sql id="baseQueryWhere">
        <where>
            detail.is_delete = 0
            <if test="param.advertiserId != null">
                AND detail.advertiser_id = #{param.advertiserId}
            </if>
            <if test="param.customId != null">
                AND detail.custom_id = #{param.customId}
            </if>
            <if test="param.sourceId != null">
                AND detail.source_id = #{param.sourceId}
            </if>
            <if test="param.executeTimeBetween != null and param.executeTimeBetween.size() == 2">
                AND DATE(detail.execute_time) BETWEEN #{param.executeTimeBetween[0]} AND #{param.executeTimeBetween[1]}
            </if>
            <if test="param.inputMethods != null and param.inputMethods.size() > 0">
                AND detail.input_method IN
                <foreach collection="param.inputMethods" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.capitalTypes != null and param.capitalTypes.size() > 0">
                AND detail.capital_type IN
                <foreach collection="param.capitalTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.executeStatuses != null and param.executeStatuses.size() > 0">
                AND detail.execute_status IN
                <foreach collection="param.executeStatuses" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.transferSerial != null and param.transferSerial != ''">
                AND detail.transfer_serial = #{param.transferSerial}
            </if>
            <if test="param.agentId != null">
                AND agent.agent_id = #{param.agentId}
            </if>
        </where>
    </sql>

    <!-- 分页查询充值明细（关联代理商和账户信息） -->
    <select id="pageWithAgent" resultType="com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult">
        SELECT
            detail.*,
            info.advertiser_name AS advertiserName,
            info.agent_id AS agentId,
            agent.agent_name AS agentName
        FROM tagent_account_oceanengine_customer_recharge_detail detail
        INNER JOIN tagent_account_oceanengine_info info
            ON detail.advertiser_id = info.advertiser_id
            AND info.is_delete = 0
        INNER JOIN toauth_oceanengine_agent_oauth_account agent
            ON info.agent_id = agent.agent_id
            AND agent.is_delete = 0
        <include refid="baseQueryWhere"/>
    </select>

    <!-- 查询充值明细列表（关联代理商和账户信息） -->
    <select id="listWithAgent" resultType="com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult">
        SELECT
            detail.*,
            info.advertiser_name AS advertiserName,
            info.agent_id AS agentId,
            agent.agent_name AS agentName
        FROM tagent_account_oceanengine_customer_recharge_detail detail
        INNER JOIN tagent_account_oceanengine_info info
            ON detail.advertiser_id = info.advertiser_id
            AND info.is_delete = 0
        INNER JOIN toauth_oceanengine_agent_oauth_account agent
            ON info.agent_id = agent.agent_id
            AND agent.is_delete = 0
        <include refid="baseQueryWhere"/>
        ORDER BY detail.execute_time DESC, detail.id DESC
    </select>

</mapper>
