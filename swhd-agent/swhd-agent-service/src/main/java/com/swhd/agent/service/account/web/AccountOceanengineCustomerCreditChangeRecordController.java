package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordAddParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordListParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerCreditChangeRecordResult;
import com.swhd.agent.api.account.client.AccountOceanengineCustomerCreditChangeRecordClient;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerCreditChangeRecord;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerCreditChangeRecordService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineCustomerCreditChangeRecordClient.BASE_PATH)
public class AccountOceanengineCustomerCreditChangeRecordController implements AccountOceanengineCustomerCreditChangeRecordClient {

    private final AccountOceanengineCustomerCreditChangeRecordService accountOceanengineCustomerCreditChangeRecordService;

    @Override
    public Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResult>> page(AccountOceanengineCustomerCreditChangeRecordPageParam param) {
        IPage<AccountOceanengineCustomerCreditChangeRecord> iPage = accountOceanengineCustomerCreditChangeRecordService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineCustomerCreditChangeRecordResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerCreditChangeRecordResult>> list(AccountOceanengineCustomerCreditChangeRecordListParam param) {
        List<AccountOceanengineCustomerCreditChangeRecord> list = accountOceanengineCustomerCreditChangeRecordService.list(param);
        return RspHd.data(Func.copy(list, AccountOceanengineCustomerCreditChangeRecordResult.class));
    }

    @Override
    public Rsp<AccountOceanengineCustomerCreditChangeRecordResult> getById(Long id) {
        AccountOceanengineCustomerCreditChangeRecord entity = accountOceanengineCustomerCreditChangeRecordService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineCustomerCreditChangeRecordResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerCreditChangeRecordResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineCustomerCreditChangeRecord> list = accountOceanengineCustomerCreditChangeRecordService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineCustomerCreditChangeRecordResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineCustomerCreditChangeRecordAddParam param) {
        boolean result = accountOceanengineCustomerCreditChangeRecordService.save(Func.copy(param, AccountOceanengineCustomerCreditChangeRecord.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineCustomerCreditChangeRecordUpdateParam param) {
        boolean result = accountOceanengineCustomerCreditChangeRecordService.updateById(Func.copy(param, AccountOceanengineCustomerCreditChangeRecord.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanengineCustomerCreditChangeRecordService.removeByIds(ids);
        return RspHd.status(result);
    }

}
