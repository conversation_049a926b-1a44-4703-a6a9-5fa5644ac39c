package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailAddParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailListParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailPageParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerRechargeDetailUpdateParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeBySourceIdParam;
import com.swhd.agent.api.account.dto.param.detail.AccountOceanengineCustomerSelfRechargeByTitleParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeDetailResult;
import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeDetailClient;
import com.swhd.agent.service.account.entity.AccountOceanengineCustomerRechargeDetail;
import com.swhd.agent.service.account.service.AccountOceanengineCustomerRechargeDetailService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanengineCustomerRechargeDetailClient.BASE_PATH)
public class AccountOceanengineCustomerRechargeDetailController implements AccountOceanengineCustomerRechargeDetailClient {

    private final AccountOceanengineCustomerRechargeDetailService accountOceanengineCustomerRechargeDetailService;

    @Override
    public Rsp<PageResult<AccountOceanengineCustomerRechargeDetailResult>> page(AccountOceanengineCustomerRechargeDetailPageParam param) {
        IPage<AccountOceanengineCustomerRechargeDetailResult> iPage = accountOceanengineCustomerRechargeDetailService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineCustomerRechargeDetailResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerRechargeDetailResult>> list(AccountOceanengineCustomerRechargeDetailListParam param) {
        List<AccountOceanengineCustomerRechargeDetailResult> list = accountOceanengineCustomerRechargeDetailService.list(param);
        return RspHd.data(list);
    }

    @Override
    public Rsp<AccountOceanengineCustomerRechargeDetailResult> getById(Long id) {
        AccountOceanengineCustomerRechargeDetail entity = accountOceanengineCustomerRechargeDetailService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineCustomerRechargeDetailResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineCustomerRechargeDetailResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineCustomerRechargeDetail> list = accountOceanengineCustomerRechargeDetailService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineCustomerRechargeDetailResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanengineCustomerRechargeDetailAddParam param) {
        boolean result = accountOceanengineCustomerRechargeDetailService.save(Func.copy(param, AccountOceanengineCustomerRechargeDetail.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanengineCustomerRechargeDetailUpdateParam param) {
        boolean result = accountOceanengineCustomerRechargeDetailService.updateById(Func.copy(param, AccountOceanengineCustomerRechargeDetail.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanengineCustomerRechargeDetailService.removeByIds(ids);
        return RspHd.status(result);
    }

}
