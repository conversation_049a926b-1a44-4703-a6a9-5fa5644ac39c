package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoAddParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoPageParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanenginePromotionInfoUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanenginePromotionInfoResult;
import com.swhd.agent.api.account.client.AccountOceanenginePromotionInfoClient;
import com.swhd.agent.service.account.entity.AccountOceanenginePromotionInfo;
import com.swhd.agent.service.account.service.AccountOceanenginePromotionInfoService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-07
 */
@RestController
@AllArgsConstructor
@RequestMapping(AccountOceanenginePromotionInfoClient.BASE_PATH)
public class AccountOceanenginePromotionInfoController implements AccountOceanenginePromotionInfoClient {

    private final AccountOceanenginePromotionInfoService accountOceanenginePromotionInfoService;

    @Override
    public Rsp<PageResult<AccountOceanenginePromotionInfoResult>> page(AccountOceanenginePromotionInfoPageParam param) {
        IPage<AccountOceanenginePromotionInfo> iPage = accountOceanenginePromotionInfoService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanenginePromotionInfoResult.class));
    }

    @Override
    public Rsp<AccountOceanenginePromotionInfoResult> getById(Long id) {
        AccountOceanenginePromotionInfo entity = accountOceanenginePromotionInfoService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanenginePromotionInfoResult.class));
    }

    @Override
    public Rsp<List<AccountOceanenginePromotionInfoResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanenginePromotionInfo> list = accountOceanenginePromotionInfoService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanenginePromotionInfoResult.class));
    }

    @Override
    public Rsp<Void> add(AccountOceanenginePromotionInfoAddParam param) {
        boolean result = accountOceanenginePromotionInfoService.save(Func.copy(param, AccountOceanenginePromotionInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(AccountOceanenginePromotionInfoUpdateParam param) {
        boolean result = accountOceanenginePromotionInfoService.updateById(Func.copy(param, AccountOceanenginePromotionInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = accountOceanenginePromotionInfoService.removeByIds(ids);
        return RspHd.status(result);
    }

}
