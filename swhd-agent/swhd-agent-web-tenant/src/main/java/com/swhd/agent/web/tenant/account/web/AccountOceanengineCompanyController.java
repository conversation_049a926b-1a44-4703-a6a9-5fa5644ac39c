package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCompanyClient;
import com.swhd.agent.api.account.dto.param.company.AccountOceanengineCompanyPageParam;
import com.swhd.agent.api.account.dto.param.company.AccountOceanengineCompanyUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCompanyResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2023/12/8
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCompany")
public class AccountOceanengineCompanyController {

    private final AccountOceanengineCompanyClient accountOceanengineCompanyClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCompanyResult>> page(@RequestBody @Valid AccountOceanengineCompanyPageParam param) {
        return accountOceanengineCompanyClient.page(param);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<AccountOceanengineCompanyResult> getById(@RequestParam("id") Long id) {
        return accountOceanengineCompanyClient.getById(id);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid AccountOceanengineCompanyUpdateParam param) {
        return accountOceanengineCompanyClient.update(param);
    }

}
