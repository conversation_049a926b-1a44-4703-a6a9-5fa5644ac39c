package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.web.tenant.account.vo.param.WorkToolCallbackParam;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.magiccube.tool.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/5/28 16:20
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountCustomerRechargeWorkTool")
public class AccountCustomerRechargeWorkToolController {

    // 文档参考 https://worktool.apifox.cn/doc-861677 和 https://worktool.apifox.cn/api-********
    @PostMapping("/callback")
    public String callback(@RequestBody WorkToolCallbackParam param) {
        System.out.println("接收到的消息：" + JsonUtil.toJson(param));
        try {
            // todo 业务处理

        } catch (Exception e) {
            System.out.println("发生异常：");
            e.printStackTrace(System.out);
        }

        return "{\"code\":0,\"message\":\"参数接收成功\"}";
    }






}
