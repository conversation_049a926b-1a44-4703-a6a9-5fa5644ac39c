package com.swhd.agent.web.tenant.riskcontrol.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/9
 */
@Getter
@Setter
@Component
@ConfigurationProperties(RiskControlProperties.PREFIX)
public class RiskControlProperties {

    public static final String PREFIX = "agent.riskcontrol";

    /**
     * 服务订单确认h5地址
     */
    private String orderConfirmH5Url;

    private String oceanengineViolationExcelTemplate = "classpath:easypoi/risk_control_oceanengine_violation_template.xlsx";
}

