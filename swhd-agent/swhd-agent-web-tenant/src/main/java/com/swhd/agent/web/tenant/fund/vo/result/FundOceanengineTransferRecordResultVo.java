package com.swhd.agent.web.tenant.fund.vo.result;

import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordResult;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoResultVo;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoSet;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/12/7
 */
@Getter
@Setter
public class FundOceanengineTransferRecordResultVo extends FundOceanengineTransferRecordResult
        implements AccountOceanengineInfoSet {

    @Schema(description = "客户信息")
    private CustomInfoResult customInfo;

    @Schema(description = "客户渠道信息")
    private CustomInfoResult customChannelInfo;

    @Schema(description = "广告主账户")
    private AccountOceanengineInfoResultVo account;

}
