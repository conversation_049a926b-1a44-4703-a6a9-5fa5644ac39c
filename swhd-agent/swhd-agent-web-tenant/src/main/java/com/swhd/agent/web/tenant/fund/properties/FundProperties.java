package com.swhd.agent.web.tenant.fund.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2023/12/9
 */
@Getter
@Setter
@Component
@ConfigurationProperties(FundProperties.PREFIX)
public class FundProperties {

    public static final String PREFIX = "agent.fund";

    private String transferRecordExcelTemplate = "classpath:easypoi/fund_oceanengine_transfer_record_template.xlsx";

    private String rechargeExcelTemplate = "classpath:easypoi/fund_oceanengine_custom_recharge_template.xlsx";

    private String orderConfirmTemplate = "classpath:easypoi/fund_oceanengine_custom_order_confirm_template.docx";

    private String orderExcelTemplate = "classpath:easypoi/fund_oceanengine_custom_order_template.xlsx";

    private String paymentRecordExcelTemplate = "classpath:easypoi/fund_payment_record_template.xlsx";

    /**
     * 服务订单确认缓存时间
     */
    private Duration orderConfirmCacheDuration = Duration.ofDays(30);

    /**
     * 服务订单确认h5地址
     */
    private String orderConfirmH5Url;

    /**
     * 服务订单确认短信验证码业务code
     */
    private String orderConfirmSmsCaptchaBusinessCode = "agentFundOrderConfigCaptcha";

    /**
     * 服务订单确认短信缓存时间
     */
    private Duration orderConfirmSmsCacheTime = Duration.ofMinutes(2);

}
