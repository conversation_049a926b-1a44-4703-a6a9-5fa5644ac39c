package com.swhd.agent.web.tenant.fund.vo.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/1/8
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomOrderUpdateParamVo对象")
public class FundOceanengineCustomOrderUpdateParamVo {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "代理商服务费金额(单位元)")
    private BigDecimal agentServiceChargeAmount;

    @Schema(description = "确认状态：0-未确认，1-已确认")
    private Integer confirmState;

    @Schema(description = "确认文件oss key")
    private String confirmFileOssKey;

    @Schema(description = "备注")
    private String remark;

}
