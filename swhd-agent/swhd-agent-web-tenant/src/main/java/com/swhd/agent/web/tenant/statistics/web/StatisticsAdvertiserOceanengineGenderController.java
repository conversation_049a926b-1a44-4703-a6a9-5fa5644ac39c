package com.swhd.agent.web.tenant.statistics.web;

import com.swhd.agent.api.statistics.dto.param.advertiser.gender.StatisticsAdvertiserOceanengineGenderSumPageParam;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.agent.web.tenant.statistics.service.StatisticsAdvertiserOceanengineGenderService;
import com.swhd.agent.web.tenant.statistics.vo.result.StatisticsAdvertiserOceanengineGenderGroupSumResultVo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/8/31
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/statisticsAdvertiserOceanengineGender")
public class StatisticsAdvertiserOceanengineGenderController {

    private final StatisticsAdvertiserOceanengineGenderService statisticsAdvertiserOceanengineGenderService;

    @Operation(summary = "统计分页查询")
    @PostMapping("/sumPage")
    public Rsp<PageResult<StatisticsAdvertiserOceanengineGenderGroupSumResultVo>> sumPage(
            @RequestBody @Valid StatisticsAdvertiserOceanengineGenderSumPageParam param) {
        return RspHd.data(statisticsAdvertiserOceanengineGenderService.sumPage(param));
    }

}
