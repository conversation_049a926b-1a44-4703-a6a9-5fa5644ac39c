package com.swhd.agent.web.tenant.account.service;

import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeDetailResultVo;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeDetailService {

    /**
     * 填充客户自助充值明细信息
     *
     * @param list 列表
     */
    public void fillDetailInfo(List<AccountOceanengineCustomerRechargeDetailResultVo> list) {
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(list, 
                AccountOceanengineCustomerRechargeDetailResultVo::getCustomId,
                AccountOceanengineCustomerRechargeDetailResultVo::setCustomInfo);
    }

}
