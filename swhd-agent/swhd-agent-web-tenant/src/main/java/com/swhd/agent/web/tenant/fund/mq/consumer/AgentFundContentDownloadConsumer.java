package com.swhd.agent.web.tenant.fund.mq.consumer;

import com.swhd.agent.api.fund.client.FundOceanengineCustomOrderClient;
import com.swhd.agent.api.fund.client.FundOceanengineCustomRechargeClient;
import com.swhd.agent.api.fund.client.FundOceanengineTransferRecordClient;
import com.swhd.agent.api.fund.client.FundPaymentRecordClient;
import com.swhd.agent.api.fund.dto.param.order.FundOceanengineCustomOrderPageParam;
import com.swhd.agent.api.fund.dto.param.payment.FundPaymentRecordPageParam;
import com.swhd.agent.api.fund.dto.param.recharge.FundOceanengineCustomRechargePageParam;
import com.swhd.agent.api.fund.dto.param.transfer.FundOceanengineTransferRecordPageParam;
import com.swhd.agent.api.fund.dto.result.FundOceanengineCustomOrderResult;
import com.swhd.agent.api.fund.dto.result.FundOceanengineCustomRechargeResult;
import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordResult;
import com.swhd.agent.api.fund.dto.result.FundPaymentRecordResult;
import com.swhd.agent.web.tenant.fund.properties.FundProperties;
import com.swhd.agent.web.tenant.fund.service.FundOceanengineTransferRecordService;
import com.swhd.agent.web.tenant.fund.service.FundPaymentRecordService;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineCustomRechargeResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundOceanengineCustomOrderResultPoiExcelVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundOceanengineCustomRechargeResultPoiExcelVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundOceanengineTransferRecordResultPoiExcelVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundPaymentRecordResultPoiExcelVo;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.constant.DownloadExportState;
import com.swhd.content.api.download.dto.message.DownloadRecordAddMessage;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordUpdateParam;
import com.swhd.content.api.download.dto.result.DownloadExportRecordResult;
import com.swhd.content.api.download.util.DownloadExportRecordUtil;
import com.swhd.content.api.download.util.DownloadPoiExcelUtil;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.function.ConsumerException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.easypoi.entity.EasyExcelData;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Slf4j
@Component
@AllArgsConstructor
public class AgentFundContentDownloadConsumer {

    public static final String OCEANENGINE_TRANSFER_RECORD_EXPORT_TYPE = "agent_oceanengine_transfer_record";
    public static final String OCEANENGINE_CUSTOM_ORDER_EXPORT_TYPE = "agent_oceanengine_custom_order";
    public static final String OCEANENGINE_CUSTOM_RECHARGE_EXPORT_TYPE = "agent_oceanengine_custom_recharge";
    public static final String PAYMENT_RECORD_EXPORT_TYPE = "agent_payment_record";

    private final FundOceanengineTransferRecordClient fundOceanengineTransferRecordClient;

    private final FundOceanengineTransferRecordService fundOceanengineTransferRecordService;

    private final FundOceanengineCustomOrderClient fundOceanengineCustomOrderClient;

    private final FundOceanengineCustomRechargeClient fundOceanengineCustomRechargeClient;

    private final FundPaymentRecordClient fundPaymentRecordClient;

    private final FundPaymentRecordService fundPaymentRecordService;

    private final FundProperties fundProperties;

    @Bean
    public Consumer<DownloadRecordAddMessage> agentContentDownloadRecordAddFund() {
        return message -> TenantHolder.methodTenantVoid(message.getTenantId(), () -> {
            try {
                switch (message.getExportType()) {
                    case OCEANENGINE_TRANSFER_RECORD_EXPORT_TYPE -> {
                        log.info("消费巨量引擎转账记录下载消息：{}", JsonLogUtil.toJsonString(message));
                        DownloadExportRecordUtil.exportRecord(message.getId(), this::oceanengineTransferRecord);
                    }
                    case OCEANENGINE_CUSTOM_ORDER_EXPORT_TYPE -> {
                        log.info("消费巨量引擎服务订单下载消息：{}", JsonLogUtil.toJsonString(message));
                        DownloadExportRecordUtil.exportRecord(message.getId(), this::oceanengineCustomOrder);
                    }
                    case OCEANENGINE_CUSTOM_RECHARGE_EXPORT_TYPE -> {
                        log.info("消费巨量引擎客户充值下载消息：{}", JsonLogUtil.toJsonString(message));
                        DownloadExportRecordUtil.exportRecord(message.getId(), this::oceanengineCustomRecharge);
                    }
                    case PAYMENT_RECORD_EXPORT_TYPE -> {
                        log.info("消费付款记录下载消息：{}", JsonLogUtil.toJsonString(message));
                        DownloadExportRecordUtil.exportRecord(message.getId(), this::paymentRecord);
                    }
                }
            } catch (Throwable e) {
                log.error("下载异常", e);
                DownloadExportRecordUtil.fail(message.getId());
            }
        });
    }

    /**
     * 巨量引擎转账记录下载
     */
    private void oceanengineTransferRecord(DownloadExportRecordResult exportRecord) throws IOException {
        FundOceanengineTransferRecordPageParam pageParam = JsonUtil.convertValue(exportRecord.getExportParams(),
                FundOceanengineTransferRecordPageParam.class);
        Rsp<List<FundOceanengineTransferRecordResult>> rsp = fundOceanengineTransferRecordClient.list(pageParam);
        RspHd.failThrowException(rsp);
        List<FundOceanengineTransferRecordResultPoiExcelVo> voList = Func.copy(rsp.getData(),
                FundOceanengineTransferRecordResultPoiExcelVo.class);
        fundOceanengineTransferRecordService.fillVoInfo(voList);
        EasyExcelData data = new EasyExcelData();
        data.addFillIndexList(voList);
        DownloadPoiExcelUtil.uploadOss(exportRecord, fundProperties.getTransferRecordExcelTemplate(), EasyExcelData.indexList(voList));
    }

    /**
     * 巨量引擎服务订单下载
     */
    private void oceanengineCustomOrder(DownloadExportRecordResult exportRecord) throws IOException {
        FundOceanengineCustomOrderPageParam pageParam = JsonUtil.convertValue(exportRecord.getExportParams(),
                FundOceanengineCustomOrderPageParam.class);
        Rsp<List<FundOceanengineCustomOrderResult>> rsp = fundOceanengineCustomOrderClient.list(pageParam);
        RspHd.failThrowException(rsp);
        List<FundOceanengineCustomOrderResultPoiExcelVo> voList = Func.copy(rsp.getData(), FundOceanengineCustomOrderResultPoiExcelVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(voList, FundOceanengineCustomOrderResultPoiExcelVo::getCustomId,
                FundOceanengineCustomOrderResultPoiExcelVo::setCustomInfo);
        EasyExcelData data = new EasyExcelData();
        data.addFillIndexList(voList);
        DownloadPoiExcelUtil.uploadOss(exportRecord, fundProperties.getOrderExcelTemplate(), EasyExcelData.indexList(voList));
    }

    /**
     * 巨量引擎客户充值下载
     */
    private void oceanengineCustomRecharge(DownloadExportRecordResult exportRecord) throws IOException {
        FundOceanengineCustomRechargePageParam pageParam = JsonUtil.convertValue(exportRecord.getExportParams(),
                FundOceanengineCustomRechargePageParam.class);
        Rsp<List<FundOceanengineCustomRechargeResult>> rsp = fundOceanengineCustomRechargeClient.list(pageParam);
        RspHd.failThrowException(rsp);
        List<FundOceanengineCustomRechargeResultPoiExcelVo> voList = Func.copy(rsp.getData(), FundOceanengineCustomRechargeResultPoiExcelVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(voList, FundOceanengineCustomRechargeResultVo::getCustomId,
                FundOceanengineCustomRechargeResultVo::setCustomInfo);
        // 设置客户渠道信息
        CustomInfoWrapper.getInstance().setList(voList, FundOceanengineCustomRechargeResultVo::getCustomChannelId,
                FundOceanengineCustomRechargeResultVo::setCustomChannelInfo);
        DownloadPoiExcelUtil.uploadOss(exportRecord, fundProperties.getRechargeExcelTemplate(), EasyExcelData.indexList(voList));
    }

    /**
     * 付款记录下载
     */
    private void paymentRecord(DownloadExportRecordResult exportRecord) throws IOException {
        FundPaymentRecordPageParam pageParam = JsonUtil.convertValue(exportRecord.getExportParams(),
                FundPaymentRecordPageParam.class);
        Rsp<List<FundPaymentRecordResult>> rsp = fundPaymentRecordClient.list(pageParam);
        RspHd.failThrowException(rsp);
        List<FundPaymentRecordResultPoiExcelVo> voList = Func.copy(rsp.getData(), FundPaymentRecordResultPoiExcelVo.class);
        fundPaymentRecordService.fillPaymentInfo(voList);
        DownloadPoiExcelUtil.uploadOss(exportRecord, fundProperties.getPaymentRecordExcelTemplate(), EasyExcelData.indexList(voList));
    }

}
