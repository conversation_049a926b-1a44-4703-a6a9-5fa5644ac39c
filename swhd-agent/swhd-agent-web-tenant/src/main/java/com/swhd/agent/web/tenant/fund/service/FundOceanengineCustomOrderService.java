package com.swhd.agent.web.tenant.fund.service;

import com.swhd.agent.api.fund.client.*;
import com.swhd.agent.api.fund.dto.result.*;
import com.swhd.agent.web.tenant.fund.properties.FundProperties;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineCustomOrderChannelResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineCustomOrderResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineCustomOrderTransferResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineTransferRecordResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundOceanengineCustomOrderResultPoiWordVo;
import com.swhd.agent.web.tenant.fund.vo.result.poi.FundOceanengineCustomOrderTransferResultPoiWordVo;
import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.crm.api.custom.constant.CustomContractType;
import com.swhd.crm.api.custom.wrapper.CustomContractWrapper;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.constant.SwitchConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.easypoi.utils.PoiWordUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.wrapper.OceanengineAgentOauthAccountWrapper;
import com.swhd.user.api.tenant.client.TenantInfoClient;
import com.swhd.user.api.tenant.dto.result.TenantInfoResult;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/12
 */
@Service
@AllArgsConstructor
public class FundOceanengineCustomOrderService {

    private final FundOceanengineCustomOrderClient fundOceanengineCustomOrderClient;

    private final FundOceanengineCustomOrderTransferClient fundOceanengineCustomOrderTransferClient;

    private final FundOceanengineCustomOrderConfirmClient fundOceanengineCustomOrderConfirmClient;

    private final FundOceanengineCustomOrderChannelClient fundOceanengineCustomOrderChannelClient;

    private final FundOceanengineTransferRecordClient fundOceanengineTransferRecordClient;

    private final TenantInfoClient tenantInfoClient;

    private final FundOceanengineTransferRecordService fundOceanengineTransferRecordService;

    private final FundProperties fundProperties;

    /**
     * 详情数据
     *
     * @param id 主键id
     * @return FundOceanengineCustomOrderResultVo
     */
    public FundOceanengineCustomOrderResultVo detail(Long id) {
        Rsp<FundOceanengineCustomOrderResult> rsp = fundOceanengineCustomOrderClient.getById(id);
        RspHd.failThrowException(rsp);
        if (rsp.getData() == null) {
            return null;
        }
        return toVo(rsp.getData());
    }

    public FundOceanengineCustomOrderResultVo toVo(FundOceanengineCustomOrderResult result) {
        FundOceanengineCustomOrderResultVo vo = Func.copy(result, FundOceanengineCustomOrderResultVo.class);
        // 设置代理商信息
        OceanengineAgentOauthAccountWrapper.getInstance().setListByAgentId(List.of(vo),
                FundOceanengineCustomOrderResultVo::getAgentId, FundOceanengineCustomOrderResultVo::setAgent);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setInfo(vo, FundOceanengineCustomOrderResultVo::getCustomId,
                FundOceanengineCustomOrderResultVo::setCustomInfo);
        // 设置服务订单转账关联列表
        vo.setOrderTransferList(listOrderTransferByOrderId(vo.getId()));
        // 设置服务订单转账关联列表
        vo.setOrderChannelList(listOrderChannelByOrderId(vo.getId()));
        // 设置订单确认信息
        setConfirmInfo(vo);
        return vo;
    }

    /**
     * 设置订单确认信息
     */
    public void setConfirmInfo(FundOceanengineCustomOrderResultVo vo) {
        if (!Objects.equals(vo.getConfirmState(), SwitchConstant.YES)) {
            return;
        }
        Rsp<FundOceanengineCustomOrderConfirmResult> confirmRsp = fundOceanengineCustomOrderConfirmClient.getByOrderId(vo.getId());
        RspHd.failThrowException(confirmRsp);
        FundOceanengineCustomOrderConfirmResult orderConfirm = confirmRsp.getData();
        if (orderConfirm == null) {
            return;
        }
        vo.setOrderConfirm(orderConfirm);
        // oss key签名
        OssPrivateUtil.preSignedUrl(orderConfirm, FundOceanengineCustomOrderConfirmResult::getConfirmFileOssKey,
                FundOceanengineCustomOrderConfirmResult::setConfirmFileOssKey);
    }

    /**
     * 设置订单确认信息
     */
    public void setConfirmInfo(List<FundOceanengineCustomOrderResultVo> voList) {
        if (Func.isEmpty(voList)) {
            return;
        }
        voList = voList.stream().filter(vo -> Objects.equals(vo.getConfirmState(), SwitchConstant.YES)).toList();
        if (Func.isEmpty(voList)) {
            return;
        }
        Map<Long, FundOceanengineCustomOrderResultVo> orderMap = voList.stream()
                .collect(Collectors.toMap(FundOceanengineCustomOrderResultVo::getId, o -> o));
        Rsp<List<FundOceanengineCustomOrderConfirmResult>> confirmListRsp = fundOceanengineCustomOrderConfirmClient
                .listByOrderIds(orderMap.keySet());
        RspHd.failThrowException(confirmListRsp);
        List<FundOceanengineCustomOrderConfirmResult> orderConfirmList = confirmListRsp.getData();
        if (Func.isEmpty(orderConfirmList)) {
            return;
        }
        // oss key签名
        OssPrivateUtil.preSignedUrlList(orderConfirmList, FundOceanengineCustomOrderConfirmResult::getConfirmFileOssKey,
                FundOceanengineCustomOrderConfirmResult::setConfirmFileOssKey);
        orderConfirmList.stream()
                .filter(orderConfirm -> orderMap.containsKey(orderConfirm.getOrderId()))
                .forEach(orderConfirm -> orderMap.get(orderConfirm.getOrderId()).setOrderConfirm(orderConfirm));
    }

    /**
     * 根据服务订单id获取服务订单转账关联列表
     */
    public List<FundOceanengineCustomOrderTransferResultVo> listOrderTransferByOrderId(Long orderId) {
        // 服务订单转账关联列表
        Rsp<List<FundOceanengineCustomOrderTransferResult>> listRsp = fundOceanengineCustomOrderTransferClient.listByOrderId(orderId);
        RspHd.failThrowException(listRsp);
        List<FundOceanengineCustomOrderTransferResultVo> listVo = Func.copy(listRsp.getData(), FundOceanengineCustomOrderTransferResultVo.class);
        setOrderTransferInfo(listVo);
        return listVo;
    }

    /**
     * 根据服务订单id获取服务订单转账关联列表
     */
    public List<FundOceanengineCustomOrderChannelResultVo> listOrderChannelByOrderId(Long orderId) {
        // 服务订单转账关联列表
        Rsp<List<FundOceanengineCustomOrderChannelResult>> listRsp = fundOceanengineCustomOrderChannelClient.listByOrderId(orderId);
        RspHd.failThrowException(listRsp);
        List<FundOceanengineCustomOrderChannelResultVo> listVo = Func.copy(listRsp.getData(), FundOceanengineCustomOrderChannelResultVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(listVo, FundOceanengineCustomOrderChannelResultVo::getCustomChannelId,
                FundOceanengineCustomOrderChannelResultVo::setCustomChannelInfo);
        return listVo;
    }

    /**
     * 根据服务订单id列表获取服务订单转账关联列表
     */
    public List<FundOceanengineCustomOrderTransferResultVo> listOrderTransferByOrderIds(Collection<Long> orderIds) {
        // 服务订单转账关联列表
        Rsp<List<FundOceanengineCustomOrderTransferResult>> listRsp = fundOceanengineCustomOrderTransferClient.listByOrderIds(orderIds);
        RspHd.failThrowException(listRsp);
        List<FundOceanengineCustomOrderTransferResultVo> listVo = Func.copy(listRsp.getData(), FundOceanengineCustomOrderTransferResultVo.class);
        setOrderTransferInfo(listVo);
        return listVo;
    }

    private void setOrderTransferInfo(List<FundOceanengineCustomOrderTransferResultVo> listVo) {
        if (Func.isEmpty(listVo)) {
            return;
        }
        // 转账记录
        Map<String, List<FundOceanengineCustomOrderTransferResultVo>> transactionSeqMap = listVo.stream()
                .collect(Collectors.groupingBy(FundOceanengineCustomOrderTransferResultVo::getOceanengineTransactionSeq));
        Rsp<List<FundOceanengineTransferRecordResult>> transferRecordListRsp = fundOceanengineTransferRecordClient
                .listByTransactionSeqList(transactionSeqMap.keySet());
        RspHd.failThrowException(transferRecordListRsp);
        List<FundOceanengineTransferRecordResultVo> transferRecordList = Func.copy(transferRecordListRsp.getData(),
                FundOceanengineTransferRecordResultVo.class);
        fundOceanengineTransferRecordService.fillVoInfo(transferRecordList);
        transferRecordList.forEach(transferRecord -> transactionSeqMap.get(transferRecord.getOceanengineTransactionSeq())
                .forEach(orderTransferVo -> orderTransferVo.setTransferRecord(transferRecord)));
    }

    /**
     * 下载服务订单文档
     */
    public void downloadConfirmDoc(Long id) throws Exception {
        Long tenantId = TenantHolder.getRequiredTenantId();
        Rsp<TenantInfoResult> tenantRsp = tenantInfoClient.getById(tenantId);
        if (RspHd.isFail(tenantRsp)) {
            throw new ServiceException(tenantRsp.getCode(), tenantRsp.getMsg());
        }
        TenantInfoResult tenantInfo = tenantRsp.getData();
        if (tenantInfo == null) {
            throw new ServiceException(String.format("租户[%s]信息异常", tenantId));
        }
        FundOceanengineCustomOrderResultVo result = detail(id);
        if (result == null) {
            throw new ServiceException(String.format("服务订单[%s]信息不存在", id));
        }
        FundOceanengineCustomOrderResultPoiWordVo vo = Func.copy(result, FundOceanengineCustomOrderResultPoiWordVo.class);
        Map<String, Object> params = new HashMap<>();
        params.put("tenant", tenantInfo);
        params.put("order", vo);
        params.put("agentType", vo.getAgentTypeName());
        params.put("custom", vo.getCustomInfo());
        CustomContractWrapper.getInstance().setInfoLatestByCustomId(vo, CustomContractType.AD,
                FundOceanengineCustomOrderResultPoiWordVo::getCustomId, (_vo, latestContract) -> params.put("customContract", latestContract));
        params.put("orderTransferList", Func.copy(vo.getOrderTransferList(), FundOceanengineCustomOrderTransferResultPoiWordVo.class));
        params.put("now", LocalDate.now());
        PoiWordUtil.exportWord07AndDownload(fundProperties.getOrderConfirmTemplate(), params, "服务订单.docx");
    }

}
