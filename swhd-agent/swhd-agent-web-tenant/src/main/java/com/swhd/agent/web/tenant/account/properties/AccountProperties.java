package com.swhd.agent.web.tenant.account.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/9
 */
@Getter
@Setter
@Component
@ConfigurationProperties(AccountProperties.PREFIX)
public class AccountProperties {

    public static final String PREFIX = "agent.account";

    private String opLogStatisticsExcelTemplate = "classpath:easypoi/account_oceanengine_opt_log_statistics.xlsx";

    private String AccountInfoExcelTemplate = "classpath:easypoi/account_oceanengine_info.xlsx";

    private String customerRechargeDetailExcelTemplate = "classpath:easypoi/account_oceanengine_customer_recharge_detail.xlsx";

}
