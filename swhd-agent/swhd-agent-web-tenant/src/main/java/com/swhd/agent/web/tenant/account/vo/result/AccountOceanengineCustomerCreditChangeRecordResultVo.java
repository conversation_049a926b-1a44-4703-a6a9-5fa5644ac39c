package com.swhd.agent.web.tenant.account.vo.result;

import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerCreditChangeRecordResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自助充值额度变更记录表响应VO
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineCustomerCreditChangeRecordResultVo对象")
public class AccountOceanengineCustomerCreditChangeRecordResultVo extends AccountOceanengineCustomerCreditChangeRecordResult {

    @Schema(description = "操作人")
    private String operator = "系统";

}
