package com.swhd.agent.web.tenant.statistics.vo.result;

import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsAdvertiserOceanengineGenderGroupSumResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/2
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdvertiserOceanengineGenderGroupSumResultVo对象")
public class StatisticsAdvertiserOceanengineGenderGroupSumResultVo extends StatisticsAdvertiserOceanengineGenderGroupSumResult {

    @Schema(description = "账户信息")
    private AccountOceanengineInfoResult accountInfo;

}
