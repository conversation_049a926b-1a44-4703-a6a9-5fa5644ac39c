package com.swhd.agent.web.tenant.statistics.web;

import com.swhd.agent.api.statistics.client.StatisticsSelfOperateOceanengineTotalMonthClient;
import com.swhd.agent.api.statistics.dto.param.selfoperate.StatisticsSelfOperateOceanengineSumParam;
import com.swhd.agent.api.statistics.dto.param.selfoperate.total.StatisticsSelfOperateOceanengineTotalMonthPageParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsSelfOperateOceanengineSumResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsSelfOperateOceanengineTotalMonthResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.agent.web.tenant.statistics.vo.result.StatisticsSelfOperateOceanengineTotalMonthResultVo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/2/3
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/statisticsSelfOperateOceanengineTotalMonth")
public class StatisticsSelfOperateOceanengineTotalMonthController {

    private final StatisticsSelfOperateOceanengineTotalMonthClient statisticsSelfOperateOceanengineTotalMonthClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<StatisticsSelfOperateOceanengineTotalMonthResultVo>> page(
            @RequestBody @Valid StatisticsSelfOperateOceanengineTotalMonthPageParam param) {
        Rsp<PageResult<StatisticsSelfOperateOceanengineTotalMonthResult>> rsp = statisticsSelfOperateOceanengineTotalMonthClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        PageResult<StatisticsSelfOperateOceanengineTotalMonthResultVo> pageVoResult = PageUtil.convert(rsp.getData(),
                StatisticsSelfOperateOceanengineTotalMonthResultVo.class);
        fill(pageVoResult.getRecords());
        return RspHd.data(pageVoResult);
    }

    @Operation(summary = "求和统计")
    @PostMapping("/sum")
    public Rsp<StatisticsSelfOperateOceanengineSumResult> sum(@RequestBody @Valid StatisticsSelfOperateOceanengineSumParam param) {
        return statisticsSelfOperateOceanengineTotalMonthClient.sum(param);
    }

    private void fill(List<StatisticsSelfOperateOceanengineTotalMonthResultVo> voList) {
        if (Func.isEmpty(voList)) {
            return;
        }
        // 填充上一天的数据
        List<LocalDate> dateList = voList.stream()
                .map(vo -> vo.getDate().minusMonths(1))
                .toList();
        Rsp<List<StatisticsSelfOperateOceanengineTotalMonthResult>> rsp = statisticsSelfOperateOceanengineTotalMonthClient
                .listByDateList(dateList);
        RspHd.failThrowException(rsp);
        voList.forEach(vo -> {
            LocalDate date = vo.getDate().minusMonths(1);
            Optional<StatisticsSelfOperateOceanengineTotalMonthResult> optional = rsp.getData().stream()
                    .filter(previous -> Objects.equals(date, previous.getDate()))
                    .findAny();
            if (optional.isPresent()) {
                vo.setPrevious(Func.copy(optional.get(), StatisticsSelfOperateOceanengineTotalMonthResultVo.class));
                return;
            }
            StatisticsSelfOperateOceanengineTotalMonthResultVo previous = new StatisticsSelfOperateOceanengineTotalMonthResultVo();
            previous.setDate(date);
            previous.setSelfOperateConsume(BigDecimal.ZERO);
            previous.setSelfOperateConsumeRatio(BigDecimal.ZERO);
            previous.setTotalConsume(BigDecimal.ZERO);
            vo.setPrevious(previous);
        });
    }

}
