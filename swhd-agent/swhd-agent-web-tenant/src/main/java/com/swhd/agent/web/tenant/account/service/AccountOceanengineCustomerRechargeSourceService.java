package com.swhd.agent.web.tenant.account.service;

import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeSourceResultVo;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 自助充值源表 Web服务类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@AllArgsConstructor
public class AccountOceanengineCustomerRechargeSourceService {

    /**
     * 填充充值源信息
     *
     * @param list 列表
     */
    public void fillSourceInfo(List<AccountOceanengineCustomerRechargeSourceResultVo> list) {
        // 收集所有客户ID
        List<Long> allCustomerIds = list.stream()
                .filter(item -> item.getCustomerIds() != null)
                .flatMap(item -> item.getCustomerIds().stream())
                .distinct()
                .collect(Collectors.toList());

        if (allCustomerIds.isEmpty()) {
            return;
        }

        // 批量获取客户信息
        var customerInfoMap = CustomInfoWrapper.getInstance().getMapByIds(allCustomerIds);

        // 为每个充值源设置客户信息列表
        for (AccountOceanengineCustomerRechargeSourceResultVo item : list) {
            if (item.getCustomerIds() != null && !item.getCustomerIds().isEmpty()) {
                var customInfoList = item.getCustomerIds().stream()
                        .map(customerInfoMap::get)
                        .filter(customInfo -> customInfo != null)
                        .collect(Collectors.toList());
                item.setCustomInfoList(customInfoList);
            }
        }
    }

    /**
     * 填充单个充值源信息
     *
     * @param item 充值源对象
     */
    public void fillSourceInfo(AccountOceanengineCustomerRechargeSourceResultVo item) {
        if (item.getCustomerIds() != null && !item.getCustomerIds().isEmpty()) {
            var customInfoList = CustomInfoWrapper.getInstance().getListByIds(item.getCustomerIds());
            item.setCustomInfoList(customInfoList);
        }
    }

}
