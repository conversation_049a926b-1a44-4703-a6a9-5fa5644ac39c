package com.swhd.agent.web.tenant.fund.vo.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/1/12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "FundOceanengineCustomOrderConfirmTransferVo对象")
public class FundOceanengineCustomOrderConfirmTransferVo {

    @Schema(description = "广告主账户id")
    private Long advertiserId;

    @Schema(description = "广告主账户账户名")
    private String advertiserName;

    @Schema(description = "广告主公司id")
    private Long advertiserCompanyId;

    @Schema(description = "广告主公司名")
    private String advertiserCompanyName;

    @Schema(description = "巨量引擎转账编号")
    private String oceanengineTransactionSeq;

    @Schema(description = "转账类型：0-加款，1-退款")
    private Integer transferType;

    @Schema(description = "巨量方舟转账金额(单位元)")
    private BigDecimal oceanengineAmount;

    @Schema(description = "代理商总收款金额(单位元)")
    private BigDecimal agentTotalAmount;

    @Schema(description = "巨量方舟转账时间")
    private LocalDateTime oceanengineTransferTime;

}
