package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeCreditClient;
import com.swhd.agent.api.account.dto.param.credit.AccountOceanengineCustomerRechargeCreditStatusParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeCreditConfigResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 自助充值授信表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerRechargeCredit")
public class AccountOceanengineCustomerRechargeCreditController {

    private final AccountOceanengineCustomerRechargeCreditClient accountOceanengineCustomerRechargeCreditClient;

    @Operation(summary = "根据客户ID获取自助充值配置")
    @GetMapping("/getByCustomId")
    public Rsp<AccountOceanengineCustomerRechargeCreditConfigResult> getByCustomId(@RequestParam("customId") Long customId) {
        return accountOceanengineCustomerRechargeCreditClient.getByCustomId(customId);
    }

    @Operation(summary = "根据客户ID关闭或开启设置额度")
    @PostMapping("/updateCreditStatus")
    public Rsp<Void> updateCreditStatus(@RequestBody @Valid AccountOceanengineCustomerRechargeCreditStatusParam param) {
        return accountOceanengineCustomerRechargeCreditClient.updateCreditStatus(param);
    }

}
