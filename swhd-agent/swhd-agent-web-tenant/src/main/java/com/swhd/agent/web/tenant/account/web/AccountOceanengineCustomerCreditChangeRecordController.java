package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCustomerCreditChangeRecordClient;
import com.swhd.agent.api.account.dto.param.record.AccountOceanengineCustomerCreditChangeRecordPageParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerCreditChangeRecordResult;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerCreditChangeRecordResultVo;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.user.api.tenant.wrapper.TenantUserInfoWrapper;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 自助充值额度变更记录表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerCreditChangeRecord")
public class AccountOceanengineCustomerCreditChangeRecordController {

    private final AccountOceanengineCustomerCreditChangeRecordClient accountOceanengineCustomerCreditChangeRecordClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResultVo>> page(@RequestBody @Valid AccountOceanengineCustomerCreditChangeRecordPageParam param) {
        // 调用原始的分页查询
        Rsp<PageResult<AccountOceanengineCustomerCreditChangeRecordResult>> rsp = accountOceanengineCustomerCreditChangeRecordClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }

        // 转换为VO
        PageResult<AccountOceanengineCustomerCreditChangeRecordResultVo> pageResult = PageUtil.convert(rsp.getData(), AccountOceanengineCustomerCreditChangeRecordResultVo.class);

        // 设置操作人信息
        TenantUserInfoWrapper.getInstance().setList(pageResult.getRecords(),
                vo -> {
                    // 将creatorId字符串转换为Long类型
                    String creatorId = vo.getCreatorId();
                    if (creatorId != null && !creatorId.trim().isEmpty()) {
                        try {
                            return Long.valueOf(creatorId.trim());
                        } catch (NumberFormatException e) {
                            log.warn("无效的创建人ID格式: {}", creatorId);
                        }
                    }
                    return null;
                },
                (vo, userInfo) -> vo.setOperator(userInfo.getNickname()));

        return RspHd.data(pageResult);
    }
}
