package com.swhd.agent.web.tenant.fund.vo.result;

import com.swhd.magiccube.tool.jwt.JwtBasePayload;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/1/16
 */
@Getter
@Setter
@NoArgsConstructor
public class FundOceanengineCustomOrderConfirmJwtVo extends JwtBasePayload {

    private Long orderId;

    public FundOceanengineCustomOrderConfirmJwtVo(LocalDateTime exp, Long orderId) {
        super(exp);
        this.orderId = orderId;
    }

}
