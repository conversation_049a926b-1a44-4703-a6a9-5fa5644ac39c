package com.swhd.agent.web.tenant.account.vo.poi;

import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeDetailResultVo;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import com.swhd.magiccube.easypoi.interfaces.IPoiIndex;
import com.swhd.magiccube.tool.DateTimeUtil;
import com.swj.magiccube.api.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineCustomerRechargeDetailResultPoiExcelVo对象")
public class AccountOceanengineCustomerRechargeDetailResultPoiExcelVo extends AccountOceanengineCustomerRechargeDetailResultVo implements IPoiIndex {

    private Integer index;

    /**
     * 客户名称
     */
    public String getCustomName() {
        CustomInfoResult customInfo = getCustomInfo();
        if (customInfo == null) {
            return null;
        }
        if (customInfo.getCustomType() == null) {
            return customInfo.getName();
        }
        return String.format("%s(%s)", customInfo.getName(), customInfo.getCustomType().getTitle());
    }

    /**
     * 渠道返点比例名称
     */
    public String getChannelRebateName() {
        if (getChannelRebate() == null || getChannelRebate().compareTo(BigDecimal.ZERO) <= 0) {
            return Constant.Str.EMPTY;
        }
        return getChannelRebate() + "%";
    }


    /**
     * 执行时间名称
     */
    public String getExecuteTimeName() {
        return DateTimeUtil.formatDateTime(getExecuteTime());
    }

    /**
     * 创建时间名称
     */
    public String getCreateTimeName() {
        return DateTimeUtil.formatDateTime(getCreateTime());
    }

    /**
     * 录入方式名称
     */
    public String getInputMethodName() {
        if (getInputMethod() == null) {
            return null;
        }
        return switch (getInputMethod()) {
            case "WECHAT_BOT" -> "微信机器人";
            case "MANUAL" -> "手动录入";
            case "H5" -> "H5页面";
            default -> getInputMethod();
        };
    }

    @Schema(description = "转账类型 RECHARGE_IN-充值转入 REFUND_OUT-退款转出")
    public String    getCapitalTypeName() {
        if(getCapitalType() == null){
            return null;
        }
        return switch (getCapitalType()) {
            case "RECHARGE_IN" -> "充值转入";
            case "REFUND_OUT" -> "退款转出";
            default -> getCapitalType();
        };
    }

    /**
     * 执行状态名称
     */
    public String getExecuteStatusName() {
        if (getExecuteStatus() == null) {
            return null;
        }
        return switch (getExecuteStatus()) {
            case "PENDING" -> "待处理";
            case "PROCESSING" -> "处理中";
            case "FAILED" -> "处理失败";
            case "SUCCESS" -> "处理成功";
            default -> getExecuteStatus();
        };
    }

}
