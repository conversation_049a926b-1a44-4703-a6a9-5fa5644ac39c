package com.swhd.agent.web.tenant.fund.vo.result;

import com.swhd.agent.api.fund.dto.result.FundOceanengineCustomOrderChannelResult;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/1/16
 */
@Getter
@Setter
@Schema(description = "FundOceanengineCustomOrderChannelResultVo对象")
public class FundOceanengineCustomOrderChannelResultVo extends FundOceanengineCustomOrderChannelResult {

    @Schema(description = "客户渠道信息")
    private CustomInfoResult customChannelInfo;

}
