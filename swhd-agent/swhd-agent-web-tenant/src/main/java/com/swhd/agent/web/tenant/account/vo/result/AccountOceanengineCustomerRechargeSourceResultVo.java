package com.swhd.agent.web.tenant.account.vo.result;

import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeSourceResult;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 自助充值源表响应VO
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineCustomerRechargeSourceResultVo对象")
public class AccountOceanengineCustomerRechargeSourceResultVo extends AccountOceanengineCustomerRechargeSourceResult {

    @Schema(description = "客户信息列表")
    private List<CustomInfoResult> customInfoList;

}
