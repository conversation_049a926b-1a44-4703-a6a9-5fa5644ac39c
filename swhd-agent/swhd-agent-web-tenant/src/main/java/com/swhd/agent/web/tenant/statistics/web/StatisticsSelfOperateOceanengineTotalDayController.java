package com.swhd.agent.web.tenant.statistics.web;

import com.swhd.agent.api.statistics.client.StatisticsSelfOperateOceanengineTotalDayClient;
import com.swhd.agent.api.statistics.dto.param.selfoperate.StatisticsSelfOperateOceanengineSumParam;
import com.swhd.agent.api.statistics.dto.param.selfoperate.total.StatisticsSelfOperateOceanengineTotalDayPageParam;
import com.swhd.agent.api.statistics.dto.result.StatisticsSelfOperateOceanengineSumResult;
import com.swhd.agent.api.statistics.dto.result.StatisticsSelfOperateOceanengineTotalDayResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.agent.web.tenant.statistics.vo.result.StatisticsSelfOperateOceanengineTotalDayResultVo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/2/3
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/statisticsSelfOperateOceanengineTotalDay")
public class StatisticsSelfOperateOceanengineTotalDayController {

    private final StatisticsSelfOperateOceanengineTotalDayClient statisticsSelfOperateOceanengineTotalDayClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<StatisticsSelfOperateOceanengineTotalDayResultVo>> page(
            @RequestBody @Valid StatisticsSelfOperateOceanengineTotalDayPageParam param) {
        Rsp<PageResult<StatisticsSelfOperateOceanengineTotalDayResult>> rsp = statisticsSelfOperateOceanengineTotalDayClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        PageResult<StatisticsSelfOperateOceanengineTotalDayResultVo> pageVoResult = PageUtil.convert(rsp.getData(),
                StatisticsSelfOperateOceanengineTotalDayResultVo.class);
        fill(pageVoResult.getRecords());
        return RspHd.data(pageVoResult);
    }

    @Operation(summary = "求和统计")
    @PostMapping("/sum")
    public Rsp<StatisticsSelfOperateOceanengineSumResult> sum(@RequestBody @Valid StatisticsSelfOperateOceanengineSumParam param) {
        return statisticsSelfOperateOceanengineTotalDayClient.sum(param);
    }

    private void fill(List<StatisticsSelfOperateOceanengineTotalDayResultVo> voList) {
        if (Func.isEmpty(voList)) {
            return;
        }
        // 填充上一天的数据
        List<LocalDate> dateList = voList.stream()
                .map(vo -> vo.getDate().minusDays(1))
                .toList();
        Rsp<List<StatisticsSelfOperateOceanengineTotalDayResult>> rsp = statisticsSelfOperateOceanengineTotalDayClient
                .listByDateList(dateList);
        RspHd.failThrowException(rsp);
        voList.forEach(vo -> {
            LocalDate date = vo.getDate().minusDays(1);
            Optional<StatisticsSelfOperateOceanengineTotalDayResult> optional = rsp.getData().stream()
                    .filter(previous -> Objects.equals(date, previous.getDate()))
                    .findAny();
            if (optional.isPresent()) {
                vo.setPrevious(Func.copy(optional.get(), StatisticsSelfOperateOceanengineTotalDayResultVo.class));
                return;
            }
            StatisticsSelfOperateOceanengineTotalDayResult previous = new StatisticsSelfOperateOceanengineTotalDayResult();
            previous.setDate(date);
            previous.setSelfOperateConsume(BigDecimal.ZERO);
            previous.setSelfOperateAccountNum(Constant.IntNum.ZERO);
            previous.setSelfOperateConsumeRatio(BigDecimal.ZERO);
            previous.setSelfOperateAccountNumRatio(BigDecimal.ZERO);
            previous.setTotalConsume(BigDecimal.ZERO);
            previous.setTotalAccountNum(Constant.IntNum.ZERO);
            vo.setPrevious(previous);
        });
    }

}
