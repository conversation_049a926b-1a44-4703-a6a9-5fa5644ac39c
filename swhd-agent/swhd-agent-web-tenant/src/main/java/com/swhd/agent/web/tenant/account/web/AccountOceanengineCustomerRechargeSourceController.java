package com.swhd.agent.web.tenant.account.web;

import com.swhd.agent.api.account.client.AccountOceanengineCustomerRechargeSourceClient;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceAddParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourcePageParam;
import com.swhd.agent.api.account.dto.param.source.AccountOceanengineCustomerRechargeSourceUpdateParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineCustomerRechargeSourceResult;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineCustomerRechargeSourceResultVo;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * 自助充值源表 Web控制器
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineCustomerRechargeSource")
public class AccountOceanengineCustomerRechargeSourceController {

    private final AccountOceanengineCustomerRechargeSourceClient accountOceanengineCustomerRechargeSourceClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineCustomerRechargeSourceResultVo>> page(@RequestBody @Valid AccountOceanengineCustomerRechargeSourcePageParam param) {
        Rsp<PageResult<AccountOceanengineCustomerRechargeSourceResult>> pageRsp =
                accountOceanengineCustomerRechargeSourceClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<AccountOceanengineCustomerRechargeSourceResultVo> voPageResult =
                PageUtil.convert(pageRsp.getData(), AccountOceanengineCustomerRechargeSourceResultVo.class);
        if (voPageResult.getRecords() != null && !voPageResult.getRecords().isEmpty()) {
            // 设置客户信息
            CustomInfoWrapper.getInstance().setListOfIds(voPageResult.getRecords(),
                    AccountOceanengineCustomerRechargeSourceResultVo::getCustomerIds,
                    AccountOceanengineCustomerRechargeSourceResultVo::setCustomInfoList);
        }
        return RspHd.data(voPageResult);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<AccountOceanengineCustomerRechargeSourceResultVo> getById(@RequestParam("id") Long id) {
        Rsp<AccountOceanengineCustomerRechargeSourceResult> rsp =
                accountOceanengineCustomerRechargeSourceClient.getById(id);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        AccountOceanengineCustomerRechargeSourceResultVo vo =
                Func.copy(rsp.getData(), AccountOceanengineCustomerRechargeSourceResultVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setInfoOfIds(vo,
                AccountOceanengineCustomerRechargeSourceResultVo::getCustomerIds,
                AccountOceanengineCustomerRechargeSourceResultVo::setCustomInfoList);
        return RspHd.data(vo);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceAddParam param) {
        return accountOceanengineCustomerRechargeSourceClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid AccountOceanengineCustomerRechargeSourceUpdateParam param) {
        return accountOceanengineCustomerRechargeSourceClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return accountOceanengineCustomerRechargeSourceClient.removeByIds(ids);
    }

}
