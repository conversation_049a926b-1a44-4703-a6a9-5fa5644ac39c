package com.swhd.agent.web.tenant.fund.service;

import com.swhd.agent.api.fund.client.FundOceanengineTransferRecordClient;
import com.swhd.agent.api.fund.client.FundPaymentOceanengineTransferClient;
import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordResult;
import com.swhd.agent.api.fund.dto.result.FundPaymentOceanengineTransferResult;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineTransferRecordResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundPaymentOceanengineTransferResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundPaymentRecordResultVo;
import com.swhd.content.api.oss.utils.OssPrivateUtil;
import com.swhd.content.api.type.wrapper.TypeInfoWrapper;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Service
@AllArgsConstructor
public class FundPaymentRecordService {

    private final FundPaymentOceanengineTransferClient fundPaymentOceanengineTransferClient;

    private final FundOceanengineTransferRecordClient fundOceanengineTransferRecordClient;

    private final FundOceanengineTransferRecordService fundOceanengineTransferRecordService;

    /**
     * 服务订单转账关联列表
     */
    public void fillPaymentInfo(List<? extends FundPaymentRecordResultVo> listVo) {
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(listVo, FundPaymentRecordResultVo::getCustomId,
                FundPaymentRecordResultVo::setCustomInfo);
        // 设置业务类型
        TypeInfoWrapper.getInstance().setList(listVo,
                FundPaymentRecordResultVo::getBizTypeId, FundPaymentRecordResultVo::setBizType);
        // 设置收款类型
        TypeInfoWrapper.getInstance().setList(listVo,
                FundPaymentRecordResultVo::getPaymentTypeId, FundPaymentRecordResultVo::setPaymentType);
        // oss key签名
        OssPrivateUtil.preSignedUrlList(listVo, FundPaymentRecordResultVo::getBankReceiptOssKey,
                FundPaymentRecordResultVo::setBankReceiptOssKey);
    }

    /**
     * 收款和转账关联列表
     */
    public void setPaymentTransferList(FundPaymentRecordResultVo vo) {
        if (vo == null) {
            return;
        }
        // 服务订单转账关联列表
        Rsp<List<FundPaymentOceanengineTransferResult>> listRsp = fundPaymentOceanengineTransferClient.listByPaymentRecordId(vo.getId());
        RspHd.failThrowException(listRsp);
        List<FundPaymentOceanengineTransferResultVo> listVo = Func.copy(listRsp.getData(), FundPaymentOceanengineTransferResultVo.class);
        vo.setPaymentTransferList(listVo);
        setPaymentTransferInfo(listVo);
    }

    private void setPaymentTransferInfo(List<FundPaymentOceanengineTransferResultVo> listVo) {
        if (Func.isEmpty(listVo)) {
            return;
        }
        // 转账记录
        Map<String, List<FundPaymentOceanengineTransferResultVo>> transactionSeqMap = listVo.stream()
                .collect(Collectors.groupingBy(FundPaymentOceanengineTransferResultVo::getOceanengineTransactionSeq));
        Rsp<List<FundOceanengineTransferRecordResult>> transferRecordListRsp = fundOceanengineTransferRecordClient
                .listByTransactionSeqList(transactionSeqMap.keySet());
        RspHd.failThrowException(transferRecordListRsp);
        List<FundOceanengineTransferRecordResultVo> transferRecordList = Func.copy(transferRecordListRsp.getData(),
                FundOceanengineTransferRecordResultVo.class);
        fundOceanengineTransferRecordService.fillVoInfo(transferRecordList);
        transferRecordList.forEach(transferRecord -> transactionSeqMap.get(transferRecord.getOceanengineTransactionSeq())
                .forEach(orderTransferVo -> orderTransferVo.setTransferRecord(transferRecord)));
    }

}
