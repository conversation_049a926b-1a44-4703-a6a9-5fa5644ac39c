package com.swhd.ai.kefu.web.platform.robot.vo.result;

import com.swhd.ai.kefu.api.industry.dto.result.IndustryInfoResult;
import com.swhd.ai.kefu.api.robot.dto.result.RobotInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RobotInfoResultVo对象")
public class RobotInfoResultVo extends RobotInfoResult {

    @Schema(description = "行业")
    private IndustryInfoResult industry;

}
