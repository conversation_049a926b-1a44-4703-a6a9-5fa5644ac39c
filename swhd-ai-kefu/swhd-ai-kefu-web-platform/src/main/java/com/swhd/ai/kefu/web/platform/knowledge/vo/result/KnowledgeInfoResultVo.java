package com.swhd.ai.kefu.web.platform.knowledge.vo.result;

import com.swhd.ai.kefu.api.industry.dto.result.IndustryInfoResult;
import com.swhd.ai.kefu.api.knowledge.dto.result.KnowledgeInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "KnowledgeInfoResult对象")
public class KnowledgeInfoResultVo extends KnowledgeInfoResult {

    @Schema(description = "行业信息")
    private IndustryInfoResult industry;

}
