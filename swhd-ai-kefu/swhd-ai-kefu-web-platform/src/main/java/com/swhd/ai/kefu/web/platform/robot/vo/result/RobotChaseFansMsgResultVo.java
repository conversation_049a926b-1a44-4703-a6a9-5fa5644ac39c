package com.swhd.ai.kefu.web.platform.robot.vo.result;

import com.swhd.ai.kefu.api.robot.dto.result.RobotAskSceneResult;
import com.swhd.ai.kefu.api.robot.dto.result.RobotChaseFansMsgResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RobotChaseFansMsgResultVo对象")
public class RobotChaseFansMsgResultVo extends RobotChaseFansMsgResult {

    @Schema(description = "询问场景")
    private RobotAskSceneResult askScene;

}
