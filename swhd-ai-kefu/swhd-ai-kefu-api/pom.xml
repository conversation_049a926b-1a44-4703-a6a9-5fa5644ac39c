<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.swhd</groupId>
        <artifactId>swhd-ai-kefu</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>swhd-ai-kefu-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-launcher</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-magiccube-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dromara.easy-es</groupId>
            <artifactId>easy-es-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swhd-oauth-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swhd</groupId>
            <artifactId>swj-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 源码插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
