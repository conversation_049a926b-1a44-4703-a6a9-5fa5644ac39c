package com.swhd.ai.kefu.api.push.dto.param.record;

import com.swj.magiccube.api.PageReq;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-07-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PushRecordPageParam对象")
public class PushRecordPageParam extends PageReq {

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "机器人平台：1-抖音私信，2-微信公众号")
    private Integer robotPlatform;

    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "用户openId")
    private String userOpenId;

    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "推送脚本id")
    private Long scriptId;

    @Schema(description = "推送状态：0-准备推送，1-正在推送，2-推送完成，3-推送失败")
    private Integer pushState;

    @Schema(description = "推送等待时间")
    private List<LocalDate> pushWaitTimeBetween;

    @Schema(description = "推送完成时间")
    private List<LocalDate> pushSuccessTimeBetween;

}
