package com.swhd.ai.kefu.api.platform.client;

import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.platform.dto.param.info.PlatformInfoAddParam;
import com.swhd.ai.kefu.api.platform.dto.param.info.PlatformInfoBingIndustryParam;
import com.swhd.ai.kefu.api.platform.dto.param.info.PlatformInfoPageParam;
import com.swhd.ai.kefu.api.platform.dto.param.info.PlatformInfoUpdateParam;
import com.swhd.ai.kefu.api.platform.dto.result.PlatformInfoResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = PlatformInfoClient.BASE_PATH)
public interface PlatformInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/platform/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<PlatformInfoResult>> page(@RequestBody @Valid PlatformInfoPageParam param);

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    Rsp<List<PlatformInfoResult>> list(@RequestBody PlatformInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<PlatformInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据oauthOpenId获取")
    @GetMapping("/getByOauthOpenId")
    Rsp<PlatformInfoResult> getByOauthOpenId(@RequestParam("oauthOpenId") String oauthOpenId);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<PlatformInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid PlatformInfoAddParam param);

    @Deprecated
    @Operation(summary = "绑定行业")
    @PostMapping("/bingIndustry")
    Rsp<Void> bingIndustry(@RequestBody @Valid PlatformInfoBingIndustryParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid PlatformInfoUpdateParam param);

    @Operation(summary = "根据oauthOpenIds获取")
    @PostMapping("/listByOauthOpenIds")
    Rsp<List<PlatformInfoResult>> listByOauthOpenIds(@RequestBody @Valid @NotEmpty Collection<String> oauthOpenIds);

    @Operation(summary = "根据ID删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
