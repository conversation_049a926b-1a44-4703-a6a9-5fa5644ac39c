package com.swhd.ai.kefu.api.employee.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AiEmployeeKnowledgeLibraryResult对象")
public class AiEmployeeKnowledgeLibraryResult {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "主键id")
    private Long employeeId;

    @Schema(description = "渠道ID")
    private Long libraryId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
