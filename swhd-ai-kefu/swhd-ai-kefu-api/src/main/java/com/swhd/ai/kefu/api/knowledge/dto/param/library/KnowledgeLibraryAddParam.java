package com.swhd.ai.kefu.api.knowledge.dto.param.library;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "KnowledgeLibraryAddParam对象")
public class KnowledgeLibraryAddParam {

    @Schema(description = "类型：1-我的知识库，2-模版知识库", hidden = true)
    private Integer type;

    @Schema(description = "行业id")
    private Long industryId;

    @Schema(description = "知识库分类ID")
    private Long knowledgeCategoryId;

    @Schema(description = "名称")
    private String name;

}
