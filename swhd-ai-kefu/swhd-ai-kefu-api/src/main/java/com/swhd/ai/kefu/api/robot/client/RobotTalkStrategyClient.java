package com.swhd.ai.kefu.api.robot.client;

import com.swhd.ai.kefu.api.robot.dto.result.RobotTalkStrategyResult;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.robot.dto.param.talk.strategy.RobotTalkStrategyPageParam;
import com.swhd.ai.kefu.api.robot.dto.param.talk.strategy.RobotTalkStrategySaveParam;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = RobotTalkStrategyClient.BASE_PATH)
public interface RobotTalkStrategyClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/robot/talk/strategy";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<RobotTalkStrategyResult>> page(@RequestBody @Valid RobotTalkStrategyPageParam param);

    @Operation(summary = "根据robotId获取")
    @GetMapping("/getByRobotId")
    Rsp<RobotTalkStrategyResult> getByRobotId(@RequestParam("robotId") Long robotId);

    @Operation(summary = "保存")
    @PostMapping("/save")
    Rsp<Void> save(@RequestBody @Valid RobotTalkStrategySaveParam param);

}
