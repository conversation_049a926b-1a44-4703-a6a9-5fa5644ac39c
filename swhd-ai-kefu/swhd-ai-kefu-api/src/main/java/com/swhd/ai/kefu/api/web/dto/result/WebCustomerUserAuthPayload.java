package com.swhd.ai.kefu.api.web.dto.result;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.jwt.JwtBasePayload;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/26
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class WebCustomerUserAuthPayload extends JwtBasePayload {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "用户openId")
    private String userOpenId;

    @Schema(description = "用户昵称")
    private String userNickname;

    public WebCustomerUserAuthPayload(LocalDateTime exp) {
        super(exp);
    }

    public Rsp<Void> check() {
        if (tenantId == null || Func.isBlank(appId) || Func.isBlank(userOpenId) || Func.isBlank(userNickname)) {
            return RspHd.fail("信息缺失");
        }
        return RspHd.success();
    }

}
