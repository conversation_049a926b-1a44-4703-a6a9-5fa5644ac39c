package com.swhd.ai.kefu.api.push.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/11/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PushScriptConfigDataConvert对象")
public class PushScriptConfigDataConvert {

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "字段值")
    private String fieldValue;

    @Schema(description = "新字段名称")
    private String newFieldName;

    @Schema(description = "新字段值")
    private String newFieldValue;

}
