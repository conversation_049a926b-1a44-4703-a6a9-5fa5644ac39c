package com.swhd.ai.kefu.api.user.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserInfoUpdateParam对象")
public class UserInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "用户头像")
    private String userAvatar;

    @Schema(description = "用户的聊天时间")
    private LocalDateTime userChatTime;

    @Schema(description = "最后聊天时间")
    private LocalDateTime lastChatTime;

    @Schema(description = "广告回传时间")
    private LocalDateTime adFeedbackTime;

    @Schema(description = "ai聊天：0-关闭，1-开启")
    private Integer aiChat;

    @Schema(description = "消息最后读取时间")
    private LocalDateTime msgLastReadTime;

    @Schema(description = "是否有人工回复：0-否，1-是")
    private Integer hasHumanReply;

}
