package com.swhd.ai.kefu.api.robot.dto.result.node;

import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import com.swhd.magiccube.tool.Func;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "节点选项")
public class NodeDataSelect {

    @Schema(description = "是否开启选项")
    private boolean open;

    @Schema(description = "是否自动生成编号")
    private boolean autoNumbers;

    @Schema(description = "是否匹配知识库")
    private boolean matchKnowledge;

    @Schema(description = "选项数据")
    private List<String> options;

    @Schema(description = "结尾话术")
    private ResponseContent endContent;

    /**
     * 完整的选项数据（填充自动编号）
     */
    public List<String> completeOptions() {
        if (Func.isEmpty(options)) {
            return Collections.emptyList();
        }
        if (!autoNumbers) {
            return options;
        }
        List<String> newOptions = new ArrayList<>();
        for (int i = 0; i < options.size(); i++) {
            String option = options.get(i);
            newOptions.add(String.format("%s、%s", i + 1, option));
        }
        return newOptions;
    }

    /**
     * 完整的选项数据（填充自动编号）
     */
    public String completeOptionsText() {
        return String.join("\n", completeOptions());
    }

}
