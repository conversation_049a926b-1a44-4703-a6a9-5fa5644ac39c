package com.swhd.ai.kefu.api.user.dto.param.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.swhd.ai.kefu.api.user.dto.param.attr.UserAttrBatchSaveItem;
import com.swhd.ai.kefu.api.user.dto.param.info.UserInfoImportParam;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.ValidatorUtil;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

import static com.swhd.ai.kefu.api.user.constant.UserAttrConstant.*;

/**
 * <AUTHOR>
 * @since 2024/12/3
 */
@Getter
@Setter
@Schema(description = "UserInfoImportItemVo对象")
public class UserInfoImportExcelItem {

    @ExcelProperty("用户ID")
    @Schema(description = "用户openId")
    private String userOpenId;

    @ExcelProperty("用户昵称")
    @Schema(description = "用户昵称")
    private String userNickname;

    @ExcelProperty("用户头像")
    @Schema(description = "用户头像")
    private String userAvatar;

    @ExcelProperty("姓名")
    @Schema(description = "姓名")
    private String name;

    @ExcelProperty("性别")
    @Schema(description = "性别")
    private String sex;

    @ExcelProperty("手机号")
    @Schema(description = "手机号")
    private String mobile;

    @ExcelProperty("省份")
    @Schema(description = "省份")
    private String province;

    @ExcelProperty("城市")
    @Schema(description = "城市")
    private String city;

    @ExcelProperty("区/县")
    @Schema(description = "区/县")
    private String district;

    @ExcelProperty("小区")
    @Schema(description = "小区")
    private String community;

    @ExcelProperty("详细地址")
    @Schema(description = "详细地址")
    private String address;

    @ExcelProperty("需求类型")
    @Schema(description = "需求类型")
    private String requirementType;

    @ExcelProperty("备注")
    @Schema(description = "备注")
    private String remark;

    public static Rsp<List<UserInfoImportParam.UserItem>> getUserItemList(List<UserInfoImportExcelItem> userImportList) {
        if (Func.isEmpty(userImportList)) {
            return RspHd.fail("无数据导入");
        }

        List<UserInfoImportParam.UserItem> userItemList = new ArrayList<>();

        List<String> errors = new ArrayList<>();
        int index = 1;
        for (UserInfoImportExcelItem importItem : userImportList) {
            boolean error = false;
            if (Func.isBlank(importItem.getUserOpenId())) {
                error = true;
                errors.add("用户ID为空");
            }
            if (Func.isBlank(importItem.getName())) {
                error = true;
                errors.add("姓名为空");
            }
            if (Func.isBlank(importItem.getSex())) {
                error = true;
                errors.add("性别为空");
            }
            if (!"女".equals(importItem.getSex()) && !"男".equals(importItem.getSex())) {
                error = true;
                errors.add("性别选项错误");
            }
            if (Func.isBlank(importItem.getMobile())) {
                error = true;
                errors.add("手机号为空");
            }
            if (!ValidatorUtil.isMobile(importItem.getMobile())) {
                error = true;
                errors.add("手机号格式错误");
            }
            if (error) {
                return RspHd.fail(String.format("第%s行校验失败：%s", index, String.join("，", errors)));
            }

            UserInfoImportParam.UserItem userItem = new UserInfoImportParam.UserItem();
            userItemList.add(userItem);
            userItem.setUserOpenId(importItem.getUserOpenId());
            userItem.setUserNickname(importItem.getUserNickname());
            userItem.setUserAvatar(importItem.getUserAvatar());
            List<UserAttrBatchSaveItem> attrs = new ArrayList<>();
            userItem.setAttrs(attrs);
            attrs.add(new UserAttrBatchSaveItem(NAME_CODE, importItem.getName()));
            attrs.add(new UserAttrBatchSaveItem(SEX_CODE, importItem.getSex()));
            attrs.add(new UserAttrBatchSaveItem(MOBILE_CODE, importItem.getMobile()));
            if (Func.isNotBlank(importItem.getProvince())) {
                attrs.add(new UserAttrBatchSaveItem(PROVINCE_CODE, importItem.getProvince()));
            }
            if (Func.isNotBlank(importItem.getCity())) {
                attrs.add(new UserAttrBatchSaveItem(CITY_CODE, importItem.getCity()));
            }
            if (Func.isNotBlank(importItem.getDistrict())) {
                attrs.add(new UserAttrBatchSaveItem(DISTRICT_CODE, importItem.getDistrict()));
            }
            if (Func.isNotBlank(importItem.getCommunity())) {
                attrs.add(new UserAttrBatchSaveItem(COMMUNITY_CODE, importItem.getCommunity()));
            }
            if (Func.isNotBlank(importItem.getAddress())) {
                attrs.add(new UserAttrBatchSaveItem(ADDRESS_CODE, importItem.getAddress()));
            }
            if (Func.isNotBlank(importItem.getRequirementType())) {
                attrs.add(new UserAttrBatchSaveItem(REQUIREMENT_TYPE_CODE, importItem.getRequirementType()));
            }
            if (Func.isNotBlank(importItem.getRemark())) {
                attrs.add(new UserAttrBatchSaveItem(REMARK_CODE, importItem.getRemark()));
            }

            index++;
        }

        return RspHd.data(userItemList);
    }

}
