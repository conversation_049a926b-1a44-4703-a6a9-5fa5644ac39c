package com.swhd.ai.kefu.api.user.dto.param.importtaobao.plan;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-31
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "UserImportTaobaoPlanPageParam对象")
public class UserImportTaobaoPlanPageParam extends PageReq {

    @Schema(description = "子租户id")
    private Long subTenantId;

    @Schema(description = "计划id")
    private String planId;

    @Schema(description = "计划名称")
    private String planName;

}
