package com.swhd.ai.kefu.api.material.dto.param.space;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-06-17
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MaterialSpaceUpdateParam对象")
public class MaterialSpaceUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "ai平台的素材空间id")
    private String aiMaterialSpaceId;

    @Schema(description = "标题")
    private String title;

}
