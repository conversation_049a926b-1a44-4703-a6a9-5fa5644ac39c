package com.swhd.ai.kefu.api.push.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "PushRecordEvalPushMessage对象")
public class PushRecordEvalPushMessage {

    @Schema(description = "推送记录id")
    private Long pushRecordId;

}
