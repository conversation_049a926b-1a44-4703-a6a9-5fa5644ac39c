package com.swhd.ai.kefu.api.msg.dto.param.record;

import com.swhd.magiccube.tool.jwt.JwtBasePayload;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MsgRecordChatShareParam对象")
@NoArgsConstructor
public class MsgRecordChatShareParam extends JwtBasePayload {

    @Schema(description = "用户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private Long userId;

    public MsgRecordChatShareParam(LocalDateTime exp, Long tenantId, Long userId) {
        super(exp);
        this.tenantId = tenantId;
        this.userId = userId;
    }

}
