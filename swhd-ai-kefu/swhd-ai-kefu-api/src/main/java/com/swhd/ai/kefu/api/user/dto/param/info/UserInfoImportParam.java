package com.swhd.ai.kefu.api.user.dto.param.info;

import com.swhd.ai.kefu.api.user.dto.param.attr.UserAttrBatchSaveItem;
import com.swhd.ai.kefu.api.user.dto.result.UserBizExtendResult;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoSourceAdInfo;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/21
 */
@Getter
@Setter
@Schema(description = "UserInfoImportParam对象")
public class UserInfoImportParam {

    @NotNull
    @Schema(description = "机器人平台")
    private Integer robotPlatform;

    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "授权平台授权的昵称")
    private String oauthNickname;

    @Valid
    @NotEmpty(message = "用户列表不能为空")
    @LogMask(type = JsonMaskType.OMIT)
    private List<UserItem> userList;

    @Getter
    @Setter
    @Schema(name = "UserInfoImportParamUserItem")
    public static class UserItem {

        @NotBlank(message = "用户ID不能为空")
        @Schema(description = "用户openId")
        private String userOpenId;

        @Schema(description = "用户昵称")
        private String userNickname;

        @Schema(description = "用户头像")
        private String userAvatar;

        @NotEmpty(message = "用户属性为空")
        @Schema(description = "用户属性")
        private List<UserAttrBatchSaveItem> attrs;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "业务扩展")
        private List<UserBizExtendResult> bizExtend;

        @Schema(description = "线索来源广告json数据")
        private UserInfoSourceAdInfo sourceAdInfo;

    }

}
