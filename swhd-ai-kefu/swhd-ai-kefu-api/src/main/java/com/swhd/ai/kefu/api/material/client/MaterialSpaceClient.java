package com.swhd.ai.kefu.api.material.client;

import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.material.dto.param.space.MaterialSpaceAddParam;
import com.swhd.ai.kefu.api.material.dto.param.space.MaterialSpacePageParam;
import com.swhd.ai.kefu.api.material.dto.param.space.MaterialSpaceUpdateParam;
import com.swhd.ai.kefu.api.material.dto.result.MaterialSpaceResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-17
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MaterialSpaceClient.BASE_PATH)
public interface MaterialSpaceClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/material/space";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MaterialSpaceResult>> page(@RequestBody @Valid MaterialSpacePageParam param);

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    Rsp<List<MaterialSpaceResult>> list(@RequestBody @Valid MaterialSpacePageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MaterialSpaceResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MaterialSpaceResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MaterialSpaceAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid MaterialSpaceUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
