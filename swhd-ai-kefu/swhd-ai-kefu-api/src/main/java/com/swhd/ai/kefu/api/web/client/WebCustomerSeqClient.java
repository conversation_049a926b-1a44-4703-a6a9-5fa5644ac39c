package com.swhd.ai.kefu.api.web.client;

import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 * @since 2024-10-26
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = WebCustomerSeqClient.BASE_PATH)
public interface WebCustomerSeqClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/web/customer/seq";

    @Operation(summary = "获取序列号")
    @GetMapping("/getSeq")
    Rsp<Long> getSeq();

}
