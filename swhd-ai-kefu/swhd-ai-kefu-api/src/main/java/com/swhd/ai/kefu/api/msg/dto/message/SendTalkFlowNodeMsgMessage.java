package com.swhd.ai.kefu.api.msg.dto.message;

import com.swhd.ai.kefu.api.robot.dto.result.node.RobotTalkFlowNode;
import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.LogMask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/6/27
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SendTalkFlowNodeMsgMessage对象")
public class SendTalkFlowNodeMsgMessage {

    @Schema(description = "租户id")
    private Long tenantId;

    @LogMask(type = JsonMaskType.OMIT)
    @Schema(description = "对话流节点")
    private RobotTalkFlowNode talkFlowNode;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "机器人id")
    private Long robotId;

    @Schema(description = "用户消息时间")
    private LocalDateTime userMsgTime;

}
