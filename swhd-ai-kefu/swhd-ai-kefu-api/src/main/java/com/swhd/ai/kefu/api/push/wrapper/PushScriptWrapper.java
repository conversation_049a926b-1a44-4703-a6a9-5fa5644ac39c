package com.swhd.ai.kefu.api.push.wrapper;

import com.swhd.ai.kefu.api.push.client.PushScriptClient;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptListResult;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptResult;
import com.swhd.magiccube.core.wrapper.BaseLongApiWrapper;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.util.SpringUtil;
import lombok.Getter;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/14
 */
public class PushScriptWrapper extends BaseLongApiWrapper<PushScriptResult, PushScriptListResult> {

    @Getter
    private static final PushScriptWrapper instance = new PushScriptWrapper();

    private final PushScriptClient pushScriptClient;

    private PushScriptWrapper() {
        this.pushScriptClient = SpringUtil.getBean(PushScriptClient.class);
    }

    @Override
    protected Rsp<PushScriptResult> getRspById(Long id) {
        return pushScriptClient.getById(id);
    }

    @Override
    protected Rsp<List<PushScriptListResult>> getRspByIds(Collection<Long> ids) {
        return pushScriptClient.listByIds(ids);
    }

    @Override
    protected Long getListId(PushScriptListResult pushScriptResult) {
        return pushScriptResult.getId();
    }

}
