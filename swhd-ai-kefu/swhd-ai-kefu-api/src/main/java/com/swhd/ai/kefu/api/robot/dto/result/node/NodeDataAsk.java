package com.swhd.ai.kefu.api.robot.dto.result.node;

import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "追问")
public class NodeDataAsk {

    @Schema(description = "追问话术")
    private ResponseContent content;

    @Schema(description = "追问时间间隔（单位：秒）")
    private int timeInterval;

}
