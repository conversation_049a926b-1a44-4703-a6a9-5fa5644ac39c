package com.swhd.ai.kefu.api.msg.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.msg.dto.param.card.component.MsgCardComponentAddParam;
import com.swhd.ai.kefu.api.msg.dto.param.card.component.MsgCardComponentPageParam;
import com.swhd.ai.kefu.api.msg.dto.param.card.component.MsgCardComponentUpdateParam;
import com.swhd.ai.kefu.api.msg.dto.result.MsgCardComponentResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = MsgCardComponentClient.BASE_PATH)
public interface MsgCardComponentClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/msg/card/component";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<MsgCardComponentResult>> page(@RequestBody @Valid MsgCardComponentPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<MsgCardComponentResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<MsgCardComponentResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid MsgCardComponentAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid MsgCardComponentUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
