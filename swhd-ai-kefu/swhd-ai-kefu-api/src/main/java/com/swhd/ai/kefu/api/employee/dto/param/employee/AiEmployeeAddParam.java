package com.swhd.ai.kefu.api.employee.dto.param.employee;

import com.swhd.ai.kefu.api.push.constant.PushType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AiEmployeeAddParam对象")
public class AiEmployeeAddParam {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "员工类型：1-留资获客，2-客资推送")
    private Integer type;

    @Schema(description = "行业id")
    private Long industryId;

    @Schema(description = "服务商家名称")
    private String merchantName;

    @Schema(description = "渠道id列表")
    private List<Long> platformIds;

    @Schema(description = "知识库ID列表")
    private List<Long> libraryIds;

    /**
     * 推送类型{@link PushType}
     */
    @Schema(description = "推送类型：1-JS脚本，2-API(配置脚本)，3-ES CRM，4-短信")
    private Integer pushType;

}
