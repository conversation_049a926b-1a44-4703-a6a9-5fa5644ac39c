package com.swhd.ai.kefu.api.robot.dto.param.chasefans.time;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RobotChaseFansTimePageParam对象")
public class RobotChaseFansTimePageParam extends PageReq {

    @Schema(description = "机器人id")
    private Long robotId;

}
