package com.swhd.ai.kefu.api.robot.client;

import com.swhd.ai.kefu.api.robot.dto.result.RobotChaseFansMsgResult;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgAddParam;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgPageParam;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgUpdateParam;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = RobotChaseFansMsgClient.BASE_PATH)
public interface RobotChaseFansMsgClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/robot/chase/fans/msg";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<RobotChaseFansMsgResult>> page(@RequestBody @Valid RobotChaseFansMsgPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<RobotChaseFansMsgResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<RobotChaseFansMsgResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid RobotChaseFansMsgAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid RobotChaseFansMsgUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
