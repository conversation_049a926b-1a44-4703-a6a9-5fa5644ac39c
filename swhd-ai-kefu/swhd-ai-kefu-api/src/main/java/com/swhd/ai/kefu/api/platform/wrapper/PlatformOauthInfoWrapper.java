package com.swhd.ai.kefu.api.platform.wrapper;

import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.web.dto.result.WebPlatformInfoResult;
import com.swhd.ai.kefu.api.web.wrapper.WebPlatformInfoWrapper;
import com.swhd.magiccube.core.constant.SwitchConstant;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.common.dto.result.OauthInfoResult;
import com.swhd.oauth.api.douyin.dto.result.DouyinOauthInfoResult;
import com.swhd.oauth.api.douyin.wrapper.DouyinOauthInfoWrapper;
import com.swhd.oauth.api.wechat.dto.result.WechatOauthInfoResult;
import com.swhd.oauth.api.wechat.wrapper.WechatOauthInfoWrapper;
import com.swhd.oauth.api.wecom.dto.result.WecomOauthKefuResult;
import com.swhd.oauth.api.wecom.wrapper.WecomOauthKefuWrapper;
import com.swhd.oauth.api.xhs.dto.result.XhsOauthInfoResult;
import com.swhd.oauth.api.xhs.wrapper.XhsOauthInfoWrapper;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;

import static com.swj.magiccube.api.Constant.Str.EMPTY;

/**
 * <AUTHOR>
 * @since 2024/4/16
 */
public class PlatformOauthInfoWrapper {

    @Getter
    private static final PlatformOauthInfoWrapper instance = new PlatformOauthInfoWrapper();

    private PlatformOauthInfoWrapper() {
    }

    /**
     * 设置授权信息
     *
     * @param data             data
     * @param getOauthOpenId   获取授权的openId/appId
     * @param getRobotPlatform 获取机器人平台
     * @param consumer         设置授权信息
     * @param <T>              类泛型
     */
    public <T> void setOauthInfo(T data, Function<T, String> getOauthOpenId, Function<T, Integer> getRobotPlatform,
                                 BiConsumer<T, OauthInfoResult> consumer) {
        if (data == null) {
            return;
        }
        Integer robotPlatform = getRobotPlatform.apply(data);
        if (Objects.equals(robotPlatform, RobotPlatform.DOUYIN_CHAT.getType())) {
            // 抖音私信
            DouyinOauthInfoWrapper.getInstance().setInfoByOpenId(data, getOauthOpenId,
                    (item, douyin) -> consumer.accept(item, DouyinOauthInfoResult.newOauthInfoResult(douyin)));
        } else if (Objects.equals(robotPlatform, RobotPlatform.WECHAT_MP.getType())) {
            // 微信公众号
            WechatOauthInfoWrapper.getInstance().setInfoByAppId(data, getOauthOpenId,
                    (item, wechat) -> consumer.accept(item, WechatOauthInfoResult.newOauthInfoResult(wechat)));
        } else if (Objects.equals(robotPlatform, RobotPlatform.WEB.getType())) {
            // 网页
            WebPlatformInfoWrapper.getInstance().setInfoByAppId(data, getOauthOpenId,
                    (item, web) -> consumer.accept(item, WebPlatformInfoResult.newOauthInfoResult(web)));
        } else if (Objects.equals(robotPlatform, RobotPlatform.WECOM_KEFU.getType())) {
            // 企微客服
            WecomOauthKefuWrapper.getInstance().setInfoByKefuId(data, getOauthOpenId,
                    (item, wecomKefu) -> consumer.accept(item, WecomOauthKefuResult.newOauthInfoResult(wecomKefu)));
        } else if (Objects.equals(robotPlatform, RobotPlatform.XHS.getType())) {
            // 小红书
            XhsOauthInfoWrapper.getInstance().setInfoByUserId(data, getOauthOpenId,
                    (item, oauthAccount) -> consumer.accept(item, XhsOauthInfoResult.newOauthInfoResult(oauthAccount)));
        } else if (robotPlatform != null && robotPlatform <= 0) {
            // 平台导流(小于等于0)
            RobotPlatform.ofOptional(robotPlatform)
                    .ifPresent(type -> consumer.accept(data, new OauthInfoResult(getOauthOpenId.apply(data),
                            type.getName(),
                            EMPTY,
                            SwitchConstant.YES,
                            Integer.MAX_VALUE,
                            LocalDateTime.now(),
                            null,
                            null)));
        }
    }

    /**
     * 设置授权信息
     *
     * @param list             列表
     * @param getOauthOpenId   获取授权的openId/appId
     * @param getRobotPlatform 获取机器人平台
     * @param consumer         设置授权信息
     * @param <T>              类泛型
     */
    public <T> void setOauthList(List<T> list, Function<T, String> getOauthOpenId, Function<T, Integer> getRobotPlatform,
                                 BiConsumer<T, OauthInfoResult> consumer) {
        if (Func.isEmpty(list)) {
            return;
        }
        // 抖音私信
        List<T> douyinList = list.stream()
                .filter(item -> Objects.equals(getRobotPlatform.apply(item), RobotPlatform.DOUYIN_CHAT.getType()))
                .toList();
        if (Func.isNotEmpty(douyinList)) {
            DouyinOauthInfoWrapper.getInstance().setListByOpenId(douyinList, getOauthOpenId,
                    (item, douyin) -> consumer.accept(item, DouyinOauthInfoResult.newOauthInfoResult(douyin)));
        }
        // 微信公众号
        List<T> wechatList = list.stream()
                .filter(item -> Objects.equals(getRobotPlatform.apply(item), RobotPlatform.WECHAT_MP.getType()))
                .toList();
        if (Func.isNotEmpty(wechatList)) {
            WechatOauthInfoWrapper.getInstance().setListByAppId(wechatList, getOauthOpenId,
                    (item, wechat) -> consumer.accept(item, WechatOauthInfoResult.newOauthInfoResult(wechat)));
        }
        // 网页
        List<T> webList = list.stream()
                .filter(item -> Objects.equals(getRobotPlatform.apply(item), RobotPlatform.WEB.getType()))
                .toList();
        if (Func.isNotEmpty(webList)) {
            WebPlatformInfoWrapper.getInstance().setListByAppId(webList, getOauthOpenId,
                    (item, web) -> consumer.accept(item, WebPlatformInfoResult.newOauthInfoResult(web)));
        }
        // 企微客服
        List<T> wecomKefuList = list.stream()
                .filter(item -> Objects.equals(getRobotPlatform.apply(item), RobotPlatform.WECOM_KEFU.getType()))
                .toList();
        if (Func.isNotEmpty(wecomKefuList)) {
            WecomOauthKefuWrapper.getInstance().setListByKefuId(wecomKefuList, getOauthOpenId,
                    (item, wecomKefu) -> consumer.accept(item, WecomOauthKefuResult.newOauthInfoResult(wecomKefu)));
        }
        // 小红书
        List<T> xhsList = list.stream()
                .filter(item -> Objects.equals(getRobotPlatform.apply(item), RobotPlatform.XHS.getType()))
                .toList();
        if (Func.isNotEmpty(xhsList)) {
            XhsOauthInfoWrapper.getInstance().setListByUserId(xhsList, getOauthOpenId,
                    (item, oauthAccount) -> consumer.accept(item, XhsOauthInfoResult.newOauthInfoResult(oauthAccount)));
        }
        // 平台导流(小于等于0)
        List<T> diversionList = list.stream()
                .filter(item -> getRobotPlatform.apply(item) != null && getRobotPlatform.apply(item) <= 0)
                .toList();
        if (Func.isNotEmpty(diversionList)) {
            diversionList.forEach(item -> RobotPlatform.ofOptional(getRobotPlatform.apply(item))
                    .ifPresent(robotPlatform -> consumer.accept(item, new OauthInfoResult(getOauthOpenId.apply(item),
                            robotPlatform.getName(),
                            EMPTY,
                            SwitchConstant.YES,
                            Integer.MAX_VALUE,
                            LocalDateTime.now(),
                            null,
                            null))));
        }
    }

}
