package com.swhd.ai.kefu.api.employee.client;

import com.swhd.ai.kefu.api.employee.dto.result.AiEmployeeKnowledgeLibraryResult;
import com.swhd.ai.kefu.api.knowledge.dto.result.KnowledgeLibraryResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = AiEmployeeKnowledgeLibraryClient.BASE_PATH)
public interface AiEmployeeKnowledgeLibraryClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/ai/employee/knowledge/library";

    @Operation(summary = "根据AI员工id获取知识库")
    @GetMapping("/listByEmployeeId")
    Rsp<List<AiEmployeeKnowledgeLibraryResult>> listByEmployeeId(@RequestParam("employeeId") Long employeeId);


}
