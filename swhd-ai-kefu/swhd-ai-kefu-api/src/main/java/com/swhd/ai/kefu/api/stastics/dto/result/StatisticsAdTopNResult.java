package com.swhd.ai.kefu.api.stastics.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdTopN 出参")
public class StatisticsAdTopNResult {

    @Schema(description = "广告计划id")
    private String adId;

    @Schema(description = "广告计划名")
    private String adName;

    @Schema(description = "广告主id")
    private String advertiserId;

    @Schema(description = "广告主名称")
    private String advertiserName;

    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "机器人平台：1-抖音私信，2-微信公众号")
    private Integer robotPlatform;

    @Schema(description = "私信人数（信息流）")
    private Integer adDirectMessageCount;

    @Schema(description = "当天私信人数（信息流）")
    private Integer adDirectMessageTodayCount;

    @Schema(description = "开口人数（信息流）")
    private Integer adSpeakUpCount;

    @Schema(description = "新增开口人数（信息流）")
    private Integer adSpeakUpAddCount;

    @Schema(description = "当天开口人数（信息流）")
    private Integer adSpeakUpTodayCount;

    @Schema(description = "留资人数（信息流）")
    private Integer adCustomerCapitalCount;

    @Schema(description = "当天留资人数（信息流）")
    private Integer adCustomerCapitalTodayCount;

    @Schema(description = "开口率（信息流） = adSpeakUpCount / adDirectMessageCount")
    private BigDecimal adSpeakUpRate;

    @Schema(description = "当天开口率（信息流） = adSpeakUpTodayCount / adDirectMessageCount")
    private BigDecimal adSpeakUpTodayRate;

    @Schema(description = "留资率（信息流） = adCustomerCapitalCount / adDirectMessageCount")
    private BigDecimal adCapitalRate;

    @Schema(description = "当天留资率（信息流） = adCustomerCapitalTodayCount / adDirectMessageCount")
    private BigDecimal adCapitalTodayRate;

    @Schema(description = "开口留资率（信息流） = adCustomerCapitalCount / adSpeakUpCount")
    private BigDecimal adSpeakUpCapitalRate;

}
