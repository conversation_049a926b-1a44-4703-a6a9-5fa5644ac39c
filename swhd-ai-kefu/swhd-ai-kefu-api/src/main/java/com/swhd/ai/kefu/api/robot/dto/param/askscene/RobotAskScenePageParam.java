package com.swhd.ai.kefu.api.robot.dto.param.askscene;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "RobotAskScenePageParam对象")
public class RobotAskScenePageParam extends PageReq {

    @Schema(description = "行业id，0为全行业")
    private Long industryId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "备注")
    private String remark;

}
