package com.swhd.ai.kefu.api.msg.dto.result;

import com.swhd.ai.kefu.api.msg.constant.MsgContentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/19
 */
@Data
@Schema(description = "用户消息内容")
public class UserMsgRecordContent {

    @Schema(description = "消息ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "消息来源：1-用户消息，2-事件消息，3-机器人回复消息，4-人工回复消息")
    private Integer origin;

    @Schema(description = "消息展示类型")
    private MsgContentType showType;

    @Schema(description = "消息展示内容")
    private String showContent;

}
