package com.swhd.ai.kefu.api.push.dto.param.record;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PushRecordAddParam对象")
public class PushRecordAddParam {

    @NotNull(message = "用户id不能为空")
    @Schema(description = "用户id")
    private Long userId;

    @NotNull(message = "机器人平台不能为空")
    @Schema(description = "机器人平台：1-抖音私信，2-微信公众号")
    private Integer robotPlatform;

    @NotEmpty(message = "授权平台openId不能为空")
    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @NotEmpty(message = "用户openId不能为空")
    @Schema(description = "用户openId")
    private String userOpenId;

    @Schema(description = "用户昵称")
    private String userNickname;

    @NotNull(message = "推送脚本id不能为空")
    @Schema(description = "推送脚本id")
    private Long scriptId;

    @NotNull(message = "推送等待时间不能为空")
    @Schema(description = "推送等待时间")
    private LocalDateTime pushWaitTime;

}
