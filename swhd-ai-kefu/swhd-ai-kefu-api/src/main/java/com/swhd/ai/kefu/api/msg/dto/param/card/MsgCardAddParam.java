package com.swhd.ai.kefu.api.msg.dto.param.card;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MsgCardAddParam对象")
public class MsgCardAddParam {

    @NotBlank(message = "名称不能为空")
    @Schema(description = "名称")
    private String name;

}
