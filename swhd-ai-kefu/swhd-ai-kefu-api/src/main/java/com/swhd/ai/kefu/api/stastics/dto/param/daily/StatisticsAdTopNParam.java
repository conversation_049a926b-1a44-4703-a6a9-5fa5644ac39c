package com.swhd.ai.kefu.api.stastics.dto.param.daily;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/23
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "StatisticsAdTopN入参")
public class StatisticsAdTopNParam {

    @NotNull(message = "统计起始日期不能为空")
    @Schema(description = "统计日期-起始")
    private LocalDate statisticsDateBegin;

    @NotNull(message = "统计结束日期不能为空")
    @Schema(description = "统计日期-结束")
    private LocalDate statisticsDateEnd;

    @Schema(description = "返回topN条数据")
    private Integer topN;

}
