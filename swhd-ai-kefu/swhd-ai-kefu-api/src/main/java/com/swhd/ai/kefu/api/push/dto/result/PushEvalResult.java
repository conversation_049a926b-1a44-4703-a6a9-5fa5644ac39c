package com.swhd.ai.kefu.api.push.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/9/18
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PushEvalResult对象")
@NoArgsConstructor
@AllArgsConstructor
public class PushEvalResult {

    @Schema(description = "推送参数")
    private String pushParam;

    @Schema(description = "推送结果")
    private String pushResult;

}
