package com.swhd.ai.kefu.api.kefu.dto.param.consume;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-20
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "KefuRightsConsumeAddParam对象")
public class KefuRightsConsumeAddParam {

    @Schema(description = "AI客户用户ID")
    private Long userId;

    @Schema(description = "消耗量")
    private BigDecimal consumeQuantity;

    @Schema(description = "消耗时间")
    private LocalDateTime consumeTime;

    @Schema(description = "消耗状态：0-待扣费 1-已扣费 2-待回滚 3-已回滚")
    private Integer consumeStatus;

    @Schema(description = "消耗扣费流水号")
    private String consumeTransactionId;

}
