package com.swhd.ai.kefu.api.web.client;

import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserEnterParam;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserSendMsgParam;
import com.swhd.ai.kefu.api.web.dto.result.WebPlatformInfoResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = WebUserMsgClient.BASE_PATH)
public interface WebUserMsgClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/web/user/msg";

    @Operation(summary = "用户进入事件")
    @PostMapping("/enterEvent")
    Rsp<WebPlatformInfoResult> enterEvent(@RequestBody @Valid WebUserEnterParam param);

    @Operation(summary = "用户发送消息")
    @PostMapping("/sendMsg")
    Rsp<Long> sendMsg(@RequestBody @Valid WebUserSendMsgParam param);

}
