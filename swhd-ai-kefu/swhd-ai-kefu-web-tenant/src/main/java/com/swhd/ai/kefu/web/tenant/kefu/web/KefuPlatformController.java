package com.swhd.ai.kefu.web.tenant.kefu.web;

import com.swhd.ai.kefu.api.kefu.client.KefuPlatformClient;
import com.swhd.ai.kefu.api.kefu.dto.param.platform.KefuPlatformAddParam;
import com.swhd.ai.kefu.api.kefu.dto.param.platform.KefuPlatformPageParam;
import com.swhd.ai.kefu.api.kefu.dto.result.KefuPlatformResult;
import com.swhd.ai.kefu.api.platform.client.PlatformInfoClient;
import com.swhd.ai.kefu.api.platform.dto.result.PlatformInfoResult;
import com.swhd.ai.kefu.api.platform.wrapper.PlatformOauthInfoWrapper;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.kefu.vo.param.KefuPlatformAddParamVo;
import com.swhd.ai.kefu.web.tenant.kefu.vo.result.KefuPlatformResultVo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/10/16
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/kefuPlatform")
public class KefuPlatformController {

    private final KefuPlatformClient kefuPlatformClient;

    private final PlatformInfoClient platformInfoClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<KefuPlatformResultVo>> page(@RequestBody @Valid KefuPlatformPageParam param) {
        Rsp<PageResult<KefuPlatformResult>> rsp = kefuPlatformClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        PageResult<KefuPlatformResultVo> pageVoResult = PageUtil.convert(rsp.getData(), KefuPlatformResultVo.class);
        // 设置授权信息
        PlatformOauthInfoWrapper.getInstance().setOauthList(pageVoResult.getRecords(),
                KefuPlatformResultVo::getOauthOpenId,
                KefuPlatformResultVo::getRobotPlatform,
                KefuPlatformResultVo::setOauthInfo);
        return RspHd.data(pageVoResult);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<KefuPlatformResult> getById(@RequestParam("id") Long id) {
        return kefuPlatformClient.getById(id);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid KefuPlatformAddParamVo paramVo) {
        Rsp<PlatformInfoResult> platformRsp = platformInfoClient.getByOauthOpenId(paramVo.getOauthOpenId());
        if (RspHd.isFail(platformRsp)) {
            return RspHd.fail(platformRsp);
        }
        if (platformRsp.getData() == null) {
            return RspHd.fail("渠道信息不能为空");
        }
        KefuPlatformAddParam param = Func.copy(paramVo, KefuPlatformAddParam.class);
        param.setRobotPlatform(platformRsp.getData().getRobotPlatform());
        param.setOauthOpenId(platformRsp.getData().getOauthOpenId());
        return kefuPlatformClient.add(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return kefuPlatformClient.removeByIds(ids);
    }

}
