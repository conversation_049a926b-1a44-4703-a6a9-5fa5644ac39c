package com.swhd.ai.kefu.web.tenant.web.web;

import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.msg.client.MsgRecordClient;
import com.swhd.ai.kefu.api.msg.constant.MsgOrigin;
import com.swhd.ai.kefu.api.msg.dto.param.record.MsgRecordScrollParam;
import com.swhd.ai.kefu.api.msg.dto.result.MsgRecordResult;
import com.swhd.ai.kefu.api.user.client.UserInfoClient;
import com.swhd.ai.kefu.api.user.constant.UserAttrConstant;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoResult;
import com.swhd.ai.kefu.api.web.client.WebCustomerCapitalClient;
import com.swhd.ai.kefu.api.web.client.WebCustomerSeqClient;
import com.swhd.ai.kefu.api.web.client.WebPlatformInfoClient;
import com.swhd.ai.kefu.api.web.client.WebUserMsgClient;
import com.swhd.ai.kefu.api.web.dto.param.capital.WebCustomerCapitalAddParam;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserEnterParam;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserSendMsgParam;
import com.swhd.ai.kefu.api.web.dto.result.WebCustomerCapitalData;
import com.swhd.ai.kefu.api.web.dto.result.WebCustomerUserAuthPayload;
import com.swhd.ai.kefu.api.web.dto.result.WebPlatformInfoResult;
import com.swhd.ai.kefu.api.web.util.AiKefuWebOpenUserAuthUtil;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.web.properties.WebUserProperties;
import com.swhd.ai.kefu.web.tenant.web.vo.param.WebOpenUserCustomerCapitalAddParamVo;
import com.swhd.ai.kefu.web.tenant.web.vo.param.WebOpenUserSendMsgParamVo;
import com.swhd.ai.kefu.web.tenant.web.vo.result.WebOpenUserPlatformInfoResultVo;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.dto.param.ScrollReq;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.dto.result.ScrollResult;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.StringUtil;
import com.swhd.magiccube.web.log.utils.WebSecureUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.tool.uuid.UUIDUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/10/26
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/webOpenUser")
@Auth(way = Auth.Way.ANONYMOUS)
public class WebOpenUserController {

    private final WebPlatformInfoClient webPlatformInfoClient;

    private final WebCustomerSeqClient webCustomerSeqClient;
    private final WebCustomerCapitalClient webCustomerCapitalClient;
    private final WebUserMsgClient webUserMsgClient;
    private final UserInfoClient userInfoClient;
    private final MsgRecordClient msgRecordClient;

    private final WebUserProperties webUserProperties;

    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1\\d{10}$");

    @Operation(summary = "平台信息")
    @GetMapping("/platformInfo")
    public Rsp<WebOpenUserPlatformInfoResultVo> platformInfo(@RequestParam("appId") String appId) {
        Rsp<WebPlatformInfoResult> webPlatformInfoRsp = TenantHolder.methodIgnoreTenant(() ->
                webPlatformInfoClient.getByAppId(appId));
        if (RspHd.isFail(webPlatformInfoRsp)) {
            return RspHd.fail(webPlatformInfoRsp);
        }
        if (webPlatformInfoRsp.getData() == null) {
            return RspHd.fail("渠道不存在");
        }
        WebOpenUserPlatformInfoResultVo vo = Func.copy(webPlatformInfoRsp.getData(), WebOpenUserPlatformInfoResultVo.class);
//        Integer state = Optional.ofNullable(webPlatformInfoRsp.getData().getPlatformInfo())
//                .map(PlatformInfoResult::getState)
//                .orElse(SwitchConstant.NO);
//        vo.setState(state);
        return RspHd.data(vo);
    }

    @Operation(summary = "用户认证")
    @GetMapping("/userAuth")
    public Rsp<String> userAuth(@RequestParam("appId") String appId) {
        Rsp<WebPlatformInfoResult> webPlatformInfoRsp = TenantHolder.methodIgnoreTenant(() ->
                webPlatformInfoClient.getByAppId(appId));
        if (RspHd.isFail(webPlatformInfoRsp)) {
            return RspHd.fail(webPlatformInfoRsp);
        }
        WebPlatformInfoResult webPlatformInfo = webPlatformInfoRsp.getData();
        if (webPlatformInfo == null) {
            return RspHd.fail("渠道不存在");
        }

        // 获取旧的用户凭证
        Rsp<WebCustomerUserAuthPayload> userAuthRsp = AiKefuWebOpenUserAuthUtil.getUserAuth();
        if (RspHd.isSuccess(userAuthRsp)
                && userAuthRsp.getData() != null
                && Objects.equals(userAuthRsp.getData().getTenantId(), webPlatformInfo.getTenantId())
                && Objects.equals(userAuthRsp.getData().getAppId(), webPlatformInfo.getAppId())) {
            return RspHd.data(AiKefuWebOpenUserAuthUtil.getAuthHeader());
        }

        // 重新新用户凭证
        Rsp<Long> seqRsp = TenantHolder.methodTenant(webPlatformInfo.getTenantId(), webCustomerSeqClient::getSeq);
        if (RspHd.isFail(seqRsp)) {
            return RspHd.fail(seqRsp);
        }
        LocalDateTime exp = LocalDateTime.now().plus(webUserProperties.getUserExpDuration());
        WebCustomerUserAuthPayload payload = new WebCustomerUserAuthPayload(exp);
        payload.setTenantId(webPlatformInfo.getTenantId());
        payload.setAppId(webPlatformInfo.getAppId());
        payload.setUserOpenId(UUIDUtil.uuid32Bit());
        payload.setUserNickname(webUserProperties.getUserNicknamePre() + seqRsp.getData());
        String auth = WebSecureUtil.jwtEncryptParam(payload);
        return RspHd.data(auth);
    }

    @Operation(summary = "用户进入事件")
    @PostMapping("/enterEvent")
    public Rsp<WebPlatformInfoResult> enterEvent() {
        Rsp<WebCustomerUserAuthPayload> userAuthRsp = AiKefuWebOpenUserAuthUtil.getUserAuth();
        if (RspHd.isFail(userAuthRsp)) {
            return RspHd.fail(userAuthRsp);
        }
        WebCustomerUserAuthPayload payload = userAuthRsp.getData();
        WebUserEnterParam param = new WebUserEnterParam();
        param.setAppId(payload.getAppId());
        param.setUserOpenId(payload.getUserOpenId());
        param.setUserNickname(payload.getUserNickname());
        return webUserMsgClient.enterEvent(param);
    }

    @Operation(summary = "用户发送消息")
    @PostMapping("/sendMsg")
    public Rsp<Long> sendMsg(@RequestBody @Valid WebOpenUserSendMsgParamVo paramVo) {
        Rsp<WebCustomerUserAuthPayload> userAuthRsp = AiKefuWebOpenUserAuthUtil.getUserAuth();
        if (RspHd.isFail(userAuthRsp)) {
            return RspHd.fail(userAuthRsp);
        }
        WebCustomerUserAuthPayload payload = userAuthRsp.getData();
        WebUserSendMsgParam param = new WebUserSendMsgParam();
        param.setAppId(payload.getAppId());
        param.setUserOpenId(payload.getUserOpenId());
        param.setUserNickname(payload.getUserNickname());
        param.setType(paramVo.getType());
        param.setContent(paramVo.getContent());
        return webUserMsgClient.sendMsg(param);
    }

    @Operation(summary = "客资表单新增")
    @PostMapping("/customerCapitalAdd")
    public Rsp<Void> customerCapitalAdd(@RequestBody @Valid WebOpenUserCustomerCapitalAddParamVo paramVo) {
        Rsp<WebCustomerUserAuthPayload> userAuthRsp = AiKefuWebOpenUserAuthUtil.getUserAuth();
        if (RspHd.isFail(userAuthRsp)) {
            return RspHd.fail(userAuthRsp);
        }

        // 手机格式校验
        Optional<WebCustomerCapitalData.Item> mobileOpt = paramVo.getFormData().getItems().stream()
                .filter(item -> Objects.equals(item.getAttrCode(), UserAttrConstant.MOBILE_CODE) && StringUtil.isNotBlank(item.getValue()))
                .findAny();
        if (mobileOpt.isPresent()) {
            String mobile = mobileOpt.get().getValue();
            if (!MOBILE_PATTERN.matcher(mobile).matches()) {
                return RspHd.fail("手机号格式不正确");
            }
        }

        WebCustomerUserAuthPayload payload = userAuthRsp.getData();
        return TenantHolder.methodTenant(payload.getTenantId(), () -> {
            WebCustomerCapitalAddParam param = new WebCustomerCapitalAddParam();
            param.setAppId(payload.getAppId());
            param.setUserOpenId(payload.getUserOpenId());
            param.setUserNickname(payload.getUserNickname());
            param.setFormData(paramVo.getFormData());
            return webCustomerCapitalClient.add(param);
        });
    }

    @Operation(summary = "滚动分页查询消息")
    @PostMapping("/msgRecord/scroll")
    public Rsp<ScrollResult<MsgRecordResult>> scroll(@RequestBody @Valid ScrollReq req) {
        Rsp<WebCustomerUserAuthPayload> userAuthRsp = AiKefuWebOpenUserAuthUtil.getUserAuth();
        if (RspHd.isFail(userAuthRsp)) {
            return RspHd.fail(userAuthRsp);
        }
        WebCustomerUserAuthPayload payload = userAuthRsp.getData();
        Rsp<UserInfoResult> userInfoRsp = TenantHolder.methodTenant(payload.getTenantId(), () ->
                userInfoClient.getOne(RobotPlatform.WEB, payload.getAppId(), payload.getUserOpenId()));
        if (RspHd.failOrDataIsNull(userInfoRsp)) {
            return RspHd.data(ScrollResult.empty(req));
        }

        MsgRecordScrollParam scrollParam = BeanUtil.copy(req, MsgRecordScrollParam.class);
        scrollParam.setUserId(userInfoRsp.getData().getId());
        scrollParam.setNotInOrigins(List.of(MsgOrigin.EVENT.getType()));

        return TenantHolder.methodTenant(payload.getTenantId(), () -> msgRecordClient.scroll(scrollParam));
    }

}
