package com.swhd.ai.kefu.web.tenant.knowledge.web;

import com.swhd.ai.kefu.api.knowledge.client.KnowledgeLibraryClient;
import com.swhd.ai.kefu.api.knowledge.dto.param.library.KnowledgeLibraryUpdateParam;
import com.swhd.ai.kefu.api.knowledge.dto.result.KnowledgeLibraryResult;
import com.swhd.ai.kefu.web.tenant.FileSizeUtil;
import com.swhd.ai.kefu.api.common.constant.SegmentTypeEnum;
import com.swhd.ai.kefu.web.tenant.knowledge.properties.KnowledgeProperties;
import com.swhd.ai.kefu.web.tenant.knowledge.vo.param.KnowledgeDocImportParamVo;
import com.swhd.ai.kefu.web.tenant.knowledge.vo.param.KnowledgeDocPageParamVo;
import com.swhd.ai.kefu.web.tenant.knowledge.vo.result.KnowledgeDocResultVo;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.FileUtil;
import com.swhd.magiccube.tool.Func;
import com.swj.knowledgeserver.api.KnowledgeCategoryClient;
import com.swj.knowledgeserver.api.KnowledgeServerClient;
import com.swj.knowledgeserver.dto.req.AddKnowledgeCategoryReq;
import com.swj.knowledgeserver.dto.req.KnowledgePageReq;
import com.swj.knowledgeserver.dto.req.KnowledgeSaveReq;
import com.swj.knowledgeserver.dto.resp.KnowledgeResult;
import com.swj.magiccube.api.IdReq;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/knowledge/doc")
@AllArgsConstructor
public class KnowledgeDocController {

    private final KnowledgeLibraryClient knowledgeLibraryClient;
    private final KnowledgeServerClient knowledgeServerClient;
    private final KnowledgeCategoryClient knowledgeCategoryClient;

    private final KnowledgeProperties knowledgeProperties;

    /**
     * 分页查询知识库文档列表
     */
    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<KnowledgeDocResultVo>> page(@RequestBody @Valid KnowledgeDocPageParamVo paramVo) {
        KnowledgeLibraryResult libraryResult = this.getLibraryById(paramVo.getLibraryId());

        if (Objects.isNull(libraryResult)) {
            return Rsp.data(PageResult.empty(paramVo));
        }

        // 如果knowledge_category_id为空或为0，返回空分页列表
        if (Objects.isNull(libraryResult.getKnowledgeCategoryId()) || libraryResult.getKnowledgeCategoryId() == 0) {
            return Rsp.data(PageResult.empty(paramVo));
        }

        // 根据分类ID查询分页列表
        KnowledgePageReq pageReq = BeanUtil.copy(paramVo, KnowledgePageReq.class);
        pageReq.setCategoryId(libraryResult.getKnowledgeCategoryId());
        Rsp<PageResult<KnowledgeResult>> pageResultRsp = knowledgeServerClient.page(pageReq);
        Rsp.assertSuccessAndNotNull(pageResultRsp);

        PageResult<KnowledgeResult> pageResult = pageResultRsp.getData();
        PageResult<KnowledgeDocResultVo> pageResultVo = PageUtil.convert(pageResult, KnowledgeDocResultVo.class);
        if (Func.isEmpty(pageResultVo.getRecords())) {
            return Rsp.data(pageResultVo);
        }
        // 回填文件大小
        this.fillFileSize(pageResultVo.getRecords());

        return Rsp.data(pageResultVo);
    }

    /**
     * 并发获取文件大小
     * @param records
     */
    private void fillFileSize(List<KnowledgeDocResultVo> records) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        records.forEach(record -> {
            if (StringUtils.isBlank(record.getDataUrl())) {
                return;
            }
            // 用OkHttpUtil获取文件大小
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                Integer size = FileSizeUtil.getSize(record.getDataUrl());
                record.setSize(size);
            });
            futureList.add(future);
        });
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 根据知识库ID获取知识库
     */
    private KnowledgeLibraryResult getLibraryById(Long libraryId) {
        if (Objects.isNull(libraryId)) {
            return null;
        }
        Rsp<KnowledgeLibraryResult> libraryResultRsp = knowledgeLibraryClient.getById(libraryId);
        Rsp.assertSuccess(libraryResultRsp);
        return libraryResultRsp.getData();
    }

    /**
     * 导入知识库文档
     */
    @Operation(summary = "导入")
    @PostMapping("/import")
    public Rsp<Void> importDoc(@RequestBody @Valid KnowledgeDocImportParamVo paramVo) {
        Long tenantId = TenantHolder.getTenantId();
        Long userId = CurrentUserHolder.currentUserId();
        KnowledgeLibraryResult libraryResult = this.getLibraryById(paramVo.getLibraryId());
        if (Objects.isNull(libraryResult)) {
            return Rsp.status(false, "知识库不存在");
        }

        // 如果knowledge_category_id为空或为0，新增并保存
        Long knowledgeCategoryId = libraryResult.getKnowledgeCategoryId();
        if (Objects.isNull(knowledgeCategoryId) || knowledgeCategoryId == 0) {
            this.createCategory(libraryResult);
        }

        // 校验文件类型
        String fileExtension = FileUtil.getFileExtension(paramVo.getFileUrl());
        this.validateFileType(fileExtension);

        // 保存知识库文档
        SegmentTypeEnum chunkType = this.getChunkType(fileExtension);
        KnowledgeSaveReq knowledgeSaveReq = new KnowledgeSaveReq();
        knowledgeSaveReq.setCategoryId(knowledgeCategoryId);
        knowledgeSaveReq.setChunkType(chunkType.getCode());
        knowledgeSaveReq.setFileName(paramVo.getFileName());
        knowledgeSaveReq.setFileUrl(paramVo.getFileUrl());
        knowledgeSaveReq.setTenantId("SWHD" + tenantId);
        knowledgeSaveReq.setCreatorId("SWHD" + userId);
        Rsp<List<KnowledgeResult>> saveRsp = knowledgeServerClient.save(knowledgeSaveReq);
        Rsp.assertSuccessAndNotNull(saveRsp);
        if (Func.isEmpty(saveRsp.getData())) {
            return Rsp.status(false, "上传失败");
        }

        return Rsp.success();
    }

    /**
     * 校验文件类型
     */
    private void validateFileType(String fileExtension) {
        if (StringUtils.isBlank(fileExtension)) {
            throw new ServiceException("文件类型错误");
        }
        if (knowledgeProperties.getSupportFileTypes().stream().noneMatch(fileExtension::equalsIgnoreCase)) {
            throw new ServiceException("不支持的文件类型");
        }
    }

    /**
     * 根据文件类型获取分段类型
     */
    private SegmentTypeEnum getChunkType(String fileExtension) {
        return switch (fileExtension) {
            case "txt" -> SegmentTypeEnum.NEWLINE;
            case "doc", "docx" -> SegmentTypeEnum.PAGE;
            case "xls", "xlsx" -> SegmentTypeEnum.EXCEL_ROW;
            default -> throw new ServiceException("不支持的文件类型");
        };
    }

    /**
     * 创建分类并关联到知识库
     */
    private void createCategory(KnowledgeLibraryResult libraryResult) {
        AddKnowledgeCategoryReq addReq = BeanUtil.copy(libraryResult, AddKnowledgeCategoryReq.class);
        addReq.setCategoryType(2);
        addReq.setCategoryName(libraryResult.getName());
        Rsp<Long> addRsp = knowledgeCategoryClient.addKnowledgeCategory(addReq);
        Rsp.assertSuccessAndNotNull(addRsp);

        // 更新知识库的分类ID
        libraryResult.setKnowledgeCategoryId(addRsp.getData());
        KnowledgeLibraryUpdateParam updateParam = new KnowledgeLibraryUpdateParam();
        updateParam.setId(libraryResult.getId());
        updateParam.setKnowledgeCategoryId(addRsp.getData());
        Rsp<Void> updateRsp = knowledgeLibraryClient.update(updateParam);
        Rsp.assertSuccess(updateRsp);

    }

    /**
     * 删除知识库文档
     */
    @Operation(summary = "删除")
    @PostMapping("/deleteById")
    public Rsp<Boolean> deleteById(@RequestBody @Valid IdReq idReq) {
        return knowledgeServerClient.deleteById(BeanUtil.copy(idReq, com.swj.knowledgeserver.dto.req.IdReq.class));
    }

}
