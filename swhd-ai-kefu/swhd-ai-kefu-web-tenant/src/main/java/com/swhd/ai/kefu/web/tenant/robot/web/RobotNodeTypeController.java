package com.swhd.ai.kefu.web.tenant.robot.web;

import com.swhd.ai.kefu.api.robot.client.RobotNodeTypeClient;
import com.swhd.ai.kefu.api.robot.dto.result.RobotNodeTypeResult;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/robotNodeType")
public class RobotNodeTypeController {

    private final RobotNodeTypeClient robotNodeTypeClient;

    @Operation(summary = "根据行业id获取", description = "包括industryId=0的数据")
    @GetMapping("/selectByIndustryId")
    public Rsp<List<AntdSelectResult>> selectByIndustryId(@RequestParam("industryId") Long industryId) {
        Rsp<List<RobotNodeTypeResult>> rsp = robotNodeTypeClient.listByIndustryId(industryId);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        List<AntdSelectResult> list = rsp.getData().stream()
                .map(result -> new AntdSelectResult(result.getId(), result.getTitle()))
                .toList();
        return RspHd.data(list);
    }

}
