package com.swhd.ai.kefu.web.tenant.user.vo.result;

import com.swhd.ai.kefu.api.user.dto.result.UserInfoResult;
import com.swhd.oauth.api.oceanengine.dto.result.api.file.FileBaseVideoGetResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/12/11
 */
@Getter
@Setter
public class UserInfoResultVo extends UserInfoResult {

    @Schema(description = "广告视频信息")
    private FileBaseVideoGetResult adVideoInfo;

}
