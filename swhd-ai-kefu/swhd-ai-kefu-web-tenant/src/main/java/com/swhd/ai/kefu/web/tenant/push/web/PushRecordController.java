package com.swhd.ai.kefu.web.tenant.push.web;

import com.swhd.ai.kefu.api.platform.wrapper.PlatformOauthInfoWrapper;
import com.swhd.ai.kefu.api.push.client.PushRecordClient;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordPageParam;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordRePushParam;
import com.swhd.ai.kefu.api.push.dto.result.PushRecordResult;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptListResult;
import com.swhd.ai.kefu.api.push.wrapper.PushScriptWrapper;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.push.vo.result.PushRecordResultVo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/pushRecord")
public class PushRecordController {

    private final PushRecordClient pushRecordClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<PushRecordResultVo>> page(@RequestBody @Valid PushRecordPageParam param) {
        Rsp<PageResult<PushRecordResult>> rsp = pushRecordClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        PageResult<PushRecordResultVo> pageVoResult = PageUtil.convert(rsp.getData(), PushRecordResultVo.class);
        // 设置授权信息
        PlatformOauthInfoWrapper.getInstance().setOauthList(pageVoResult.getRecords(),
                PushRecordResultVo::getOauthOpenId,
                PushRecordResultVo::getRobotPlatform,
                PushRecordResultVo::setOauthInfo);
        // 推送脚本
        PushScriptWrapper.getInstance().setList(pageVoResult.getRecords(),
                PushRecordResultVo::getScriptId, PushRecordResultVo::setScript);
        return RspHd.data(pageVoResult);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<PushRecordResultVo> getById(@RequestParam("id") Long id) {
        Rsp<PushRecordResult> rsp = pushRecordClient.getById(id);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        if (rsp.getData() == null) {
            return RspHd.data(null);
        }
        PushRecordResultVo vo = Func.copy(rsp.getData(), PushRecordResultVo.class);
        // 设置授权信息
        PlatformOauthInfoWrapper.getInstance().setOauthInfo(vo,
                PushRecordResultVo::getOauthOpenId,
                PushRecordResultVo::getRobotPlatform,
                PushRecordResultVo::setOauthInfo);
        // 推送脚本
        PushScriptWrapper.getInstance().setInfo(vo, PushRecordResultVo::getScriptId,
                (_vo, script) -> _vo.setScript(Func.copy(script, PushScriptListResult.class)));
        return RspHd.data(vo);
    }

    @Operation(summary = "重新推送")
    @PostMapping("/rePush")
    Rsp<Void> rePush(@RequestBody @Valid PushRecordRePushParam param) {
        return pushRecordClient.rePush(param);
    }

}
