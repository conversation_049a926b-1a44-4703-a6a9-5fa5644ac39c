package com.swhd.ai.kefu.web.tenant.robot.web;

import com.swhd.ai.kefu.api.robot.client.RobotTalkFlowClient;
import com.swhd.ai.kefu.api.robot.dto.param.talk.flow.RobotTalkFlowSaveParam;
import com.swhd.ai.kefu.api.robot.dto.result.RobotTalkFlowResult;
import com.swhd.ai.kefu.api.robot.dto.result.node.NodeData;
import com.swhd.ai.kefu.api.robot.wrapper.RobotNodeTypeWrapper;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/robotTalkFlow")
public class RobotTalkFlowController {

    private final RobotTalkFlowClient robotTalkFlowClient;

    @Operation(summary = "根据robotId获取")
    @GetMapping("/getByRobotId")
    public Rsp<RobotTalkFlowResult> getByRobotId(@RequestParam("robotId") Long robotId) {
        Rsp<RobotTalkFlowResult> rsp = robotTalkFlowClient.getByRobotId(robotId);
        if (RspHd.isFail(rsp) || rsp.getData() == null) {
            return rsp;
        }
        RobotNodeTypeWrapper.getInstance().setList(rsp.getData().getNodes(),
                node -> Optional.ofNullable(node.getData()).map(NodeData::getNodeTypeId).orElse(null),
                (node, nodeType) -> node.getData().setNodeTypeName(nodeType.getTitle()));
        return rsp;
    }

    @Operation(summary = "保存")
    @PostMapping("/save")
    public Rsp<Void> save(@RequestBody @Valid RobotTalkFlowSaveParam param) {
        return robotTalkFlowClient.save(param);
    }

}
