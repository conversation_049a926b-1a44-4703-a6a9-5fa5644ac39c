package com.swhd.ai.kefu.web.tenant.web.vo.result;

import com.swhd.ai.kefu.api.web.dto.result.WebPlatformTopButtonConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/10/26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WebOpenUserPlatformInfoResultVo对象")
public class WebOpenUserPlatformInfoResultVo {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用id（全局唯一）")
    private String appId;

    @Schema(description = "客服名称")
    private String kefuName;

    @Schema(description = "客服副名称")
    private String kefuSubName;

    @Schema(description = "客服头像")
    private String kefuAvatar;

    @Schema(description = "主题颜色")
    private String themeColor;

    @Schema(description = "开启顶部按钮：0-关闭，1-开启")
    private Boolean openTopButton;

    @Schema(description = "顶部按钮配置")
    private WebPlatformTopButtonConfig topButtonConfig;

//    @Schema(description = "状态：0-禁用，1-启用")
//    private Integer state;

}
