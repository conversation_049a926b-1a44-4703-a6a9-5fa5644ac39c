package com.swhd.ai.kefu.web.tenant.push.web;

import com.swhd.ai.kefu.api.push.client.PushScriptClient;
import com.swhd.ai.kefu.api.push.constant.PushDataNameConstant;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptAddConfigParam;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptPageParam;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptTestPushParam;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptUpdateParam;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptListResult;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptResult;
import com.swhd.ai.kefu.api.push.dto.result.PushScriptTestPushResult;
import com.swhd.ai.kefu.api.user.client.UserAttrConfigClient;
import com.swhd.ai.kefu.api.user.dto.param.config.UserAttrConfigPageParam;
import com.swhd.ai.kefu.api.user.dto.result.UserAttrConfigResult;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.push.vo.param.PushScriptUpdateParamVo;
import com.swhd.ai.kefu.web.tenant.push.vo.result.PushDataFieldMapResultVo;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/14
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/pushScript")
public class PushScriptController {

    private final PushScriptClient pushScriptClient;

    private final UserAttrConfigClient userAttrConfigClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<PushScriptListResult>> page(@RequestBody @Valid PushScriptPageParam param) {
        return pushScriptClient.page(param);
    }

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    public Rsp<List<AntdSelectResult>> selectAll() {
        return pushScriptClient.selectAll();
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<PushScriptResult> getById(@RequestParam("id") Long id) {
        return pushScriptClient.getById(id);
    }

    @Operation(summary = "数据字段map")
    @GetMapping("/dataFieldMap")
    public Rsp<List<PushDataFieldMapResultVo>> dataFieldMap() {
        Rsp<List<UserAttrConfigResult>> attrConfigRsp = userAttrConfigClient.list(new UserAttrConfigPageParam());
        if (RspHd.isFail(attrConfigRsp)) {
            return RspHd.fail(attrConfigRsp);
        }
        List<PushDataFieldMapResultVo> list = new ArrayList<>();
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.USER_ID, "用户ID"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.USER_NICKNAME, "用户昵称"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.USER_AVATAR, "用户头像"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.CUSTOMER_CAPITAL_TIME, "客资时间"));
        attrConfigRsp.getData().forEach(attrConfig -> {
            PushDataFieldMapResultVo vo = new PushDataFieldMapResultVo(
                    attrConfig.getCode(), attrConfig.getTitle(), attrConfig.getPushDefaultShow());
            list.add(vo);
        });
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_ADV_ID, "广告主Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_ADV_NAME, "广告主名称"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_AD_ID, "广告计划Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_AD_NAME, "广告计划名"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_CREATIVE_ID, "广告创意Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_PROMOTION_ID, "广告Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_MATERIAL_TITLE_ID, "广告素材标题Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_MATERIAL_IMAGE_ID, "广告素材图片Id"));
        list.add(new PushDataFieldMapResultVo(PushDataNameConstant.AD_MATERIAL_VIDEO_ID, "广告素材视频Id"));
        return RspHd.data(list);
    }

    @Operation(summary = "新增配置脚本")
    @PostMapping("/addConfig")
    public Rsp<Void> addConfig(@RequestBody @Valid PushScriptAddConfigParam param) {
        return pushScriptClient.addConfig(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid PushScriptUpdateParamVo param) {
        return pushScriptClient.update(Func.copy(param, PushScriptUpdateParam.class));
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return pushScriptClient.removeByIds(ids);
    }

    @Operation(summary = "测试推送")
    @PostMapping("/testPush")
    public Rsp<PushScriptTestPushResult> testPush(@RequestBody @Valid PushScriptTestPushParam param) {
        return pushScriptClient.testPush(param);
    }

}
