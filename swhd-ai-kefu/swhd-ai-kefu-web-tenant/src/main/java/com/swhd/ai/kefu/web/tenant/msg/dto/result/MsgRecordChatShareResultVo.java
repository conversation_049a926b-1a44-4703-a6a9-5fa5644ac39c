package com.swhd.ai.kefu.web.tenant.msg.dto.result;

import com.swhd.ai.kefu.api.msg.dto.result.MsgRecordResult;
import com.swhd.ai.kefu.web.tenant.user.vo.result.UserInfoResultVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */
@Getter
@Setter
@Schema(description = "MsgRecordChatShareResultVo对象")
@NoArgsConstructor
@AllArgsConstructor
public class MsgRecordChatShareResultVo {

    private UserInfoResultVo userInfo;

    private List<MsgRecordResult> msgList;

}
