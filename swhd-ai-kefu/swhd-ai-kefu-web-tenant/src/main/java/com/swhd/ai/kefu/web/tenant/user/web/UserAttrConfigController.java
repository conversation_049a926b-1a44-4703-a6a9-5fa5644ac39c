package com.swhd.ai.kefu.web.tenant.user.web;

import com.swhd.ai.kefu.api.user.client.UserAttrConfigClient;
import com.swhd.ai.kefu.api.user.dto.param.config.UserAttrConfigPageParam;
import com.swhd.ai.kefu.api.user.dto.result.UserAttrConfigResult;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/28
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/userAttrConfig")
public class UserAttrConfigController {

    private final UserAttrConfigClient userAttrConfigClient;

    @Operation(summary = "根据行业id获取", description = "包括industryId=0的数据")
    @GetMapping("/listByIndustryId")
    public Rsp<List<UserAttrConfigResult>> listByIndustryId(@RequestParam(value = "industryId", required = false) Long industryId) {
        return userAttrConfigClient.listByIndustryId(industryId);
    }

    @Operation(summary = "所有列表")
    @GetMapping("/listAll")
    public Rsp<List<UserAttrConfigResult>> listAll() {
        return userAttrConfigClient.list(new UserAttrConfigPageParam());
    }

}
