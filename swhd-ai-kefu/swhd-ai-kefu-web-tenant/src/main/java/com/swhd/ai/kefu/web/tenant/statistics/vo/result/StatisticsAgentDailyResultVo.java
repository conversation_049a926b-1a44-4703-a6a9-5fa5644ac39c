package com.swhd.ai.kefu.web.tenant.statistics.vo.result;

import com.swhd.ai.kefu.api.stastics.dto.result.StatisticsAgentDailyResult;
import com.swhd.oauth.api.common.dto.result.OauthInfoResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024-09-20
 */
@Getter
@Setter
@Schema(description = "StatisticsAgentDailyResult对象")
public class StatisticsAgentDailyResultVo extends StatisticsAgentDailyResult {

    @Schema(description = "授权信息")
    private OauthInfoResult oauthInfo;

}
