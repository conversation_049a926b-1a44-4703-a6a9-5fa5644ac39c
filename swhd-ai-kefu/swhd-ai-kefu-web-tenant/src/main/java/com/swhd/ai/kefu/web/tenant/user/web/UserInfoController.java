package com.swhd.ai.kefu.web.tenant.user.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.user.client.EsUserInfoClient;
import com.swhd.ai.kefu.api.user.client.UserAttrClient;
import com.swhd.ai.kefu.api.user.client.UserInfoClient;
import com.swhd.ai.kefu.api.user.constant.UserAttrConstant;
import com.swhd.ai.kefu.api.user.dto.param.attr.UserAttrBatchSaveItem;
import com.swhd.ai.kefu.api.user.dto.param.attr.UserAttrBatchSaveParam;
import com.swhd.ai.kefu.api.user.dto.param.info.EsUserCustomerCapitalQueryParam;
import com.swhd.ai.kefu.api.user.dto.param.info.EsUserInfoQueryParam;
import com.swhd.ai.kefu.api.user.dto.param.info.EsUserInfoScrollParam;
import com.swhd.ai.kefu.api.user.dto.param.info.UserInfoUpdateParam;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoResult;
import com.swhd.ai.kefu.api.user.wrapper.UserAttrWrapper;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.user.mq.consumer.UserInfoConsumer;
import com.swhd.ai.kefu.web.tenant.user.service.UserInfoService;
import com.swhd.ai.kefu.web.tenant.user.vo.param.info.*;
import com.swhd.ai.kefu.web.tenant.user.vo.result.ChatUserInfoResultVo;
import com.swhd.ai.kefu.web.tenant.user.vo.result.UserInfoDetailsVoV1;
import com.swhd.ai.kefu.web.tenant.user.vo.result.UserInfoResultVo;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.dto.result.ScrollResult;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.IdReq;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping(WebConstant.BASE_PATH + "/userInfo")
public class UserInfoController {

    private final UserInfoClient userInfoClient;
    private final EsUserInfoClient esUserInfoClient;
    private final UserInfoService userInfoService;
    private final DownloadExportRecordClient downloadExportRecordClient;
    private final UserAttrClient userAttrClient;

    @Operation(summary = "所有用户分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<UserInfoResult>> page(@RequestBody @Valid EsUserInfoQueryParam param) {
        Rsp<PageResult<UserInfoResult>> rsp = esUserInfoClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        List<UserInfoResult> records = rsp.getData().getRecords();
        UserAttrWrapper.getInstance().updateUserAttrs(records,
                UserInfoResult::getAttrList,
                UserInfoResult::setAttrList);
        userInfoService.maskOwingUserAttrValue(records);
        return rsp;
    }

    @Operation(summary = "客资分页查询")
    @PostMapping("/customerCapitalPage")
    public Rsp<PageResult<UserInfoResult>> customerCapitalPage(@RequestBody @Valid EsUserCustomerCapitalQueryParam param) {
        Rsp<PageResult<UserInfoResult>> rsp = esUserInfoClient.customerCapitalPage(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        List<UserInfoResult> records = rsp.getData().getRecords();
        UserAttrWrapper.getInstance().updateUserAttrs(records,
                UserInfoResult::getAttrList,
                UserInfoResult::setAttrList);
        userInfoService.maskOwingUserAttrValue(records);
        return rsp;
    }

    @Operation(summary = "客资详情查询")
    @PostMapping("/customerCapitalDetails")
    public Rsp<UserInfoResultVo> customerCapitalDetails(@RequestBody @Valid IdReq req) {
        return userInfoService.customerCapitalDetails(req.getId());
    }

    @Operation(summary = "客户详情V1")
    @PostMapping("/userDetailsV1")
    public Rsp<UserInfoDetailsVoV1> userDetailsV1(@RequestBody @Valid IdReq req) {
        return userInfoService.userDetailsV1(req.getId() );
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<UserInfoResult> getById(@RequestParam("id") Long id) {
        return userInfoClient.getById(id);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid UserInfoUpdateParamVo paramVo) {
        // 更新用户属性
        this.updateUserAttr(paramVo);

        Rsp<Void> update = userInfoClient.update(Func.copy(paramVo, UserInfoUpdateParam.class));
        return update;
    }

    /**
     * 更新用户属性
     * @param param
     */
    private void updateUserAttr(UserInfoUpdateParamVo param) {
        List<UserAttrBatchSaveItem> itemList = new ArrayList<>();
        if (Func.isNotEmpty(param.getCustomerName())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.NAME_CODE).setValue(param.getCustomerName()));
        }

        if (Func.isNotEmpty(param.getCustomerWechat())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.WECHAT_CODE).setValue(param.getCustomerWechat()));
        }

        if (Func.isNotEmpty(param.getProvince())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.PROVINCE_CODE).setValue(param.getProvince()));
        }

        if (Func.isNotEmpty(param.getCity())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.CITY_CODE).setValue(param.getCity()));
        }

        if (Func.isNotEmpty(param.getDistrict())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.DISTRICT_CODE).setValue(param.getDistrict()));
        }

        if (Func.isNotEmpty(param.getRemark())) {
            itemList.add(new UserAttrBatchSaveItem().setCode(UserAttrConstant.REMARK_CODE).setValue(param.getRemark()));
        }

        if (Func.isNotEmpty(itemList)) {
            UserAttrBatchSaveParam attrParam = new UserAttrBatchSaveParam()
                    .setUserId(param.getId())
                    .setItems(itemList);
            userAttrClient.batchSave(attrParam);
        }

    }

    @Operation(summary = "会话用户分页查询")
    @PostMapping("/chatUserPage")
    public Rsp<PageResult<ChatUserInfoResultVo>> chatUserPage(@RequestBody @Valid ChatUserPageParamVo paramVo) {
        EsUserInfoQueryParam userQueryParam = Func.copy(paramVo, EsUserInfoQueryParam.class);
        userQueryParam.setGtRobotPlatform(RobotPlatform.DIVERSION.getType());
        userQueryParam.setUserNickname(paramVo.getKeyword());
        Rsp<PageResult<UserInfoResult>> pageResultRsp = esUserInfoClient.page(userQueryParam);
        if (RspHd.failOrDataIsEmpty(pageResultRsp)) {
            return RspHd.fail(pageResultRsp);
        }

        PageResult<ChatUserInfoResultVo> pageResult = PageUtil.convert(pageResultRsp.getData(), ChatUserInfoResultVo.class);

        // 统计未读消息数
        userInfoService.userMsgUnreadStatistic(pageResult.getRecords());
        // 设置最后一条消息内容
        userInfoService.setLastMsgRecord(pageResult.getRecords());

        return RspHd.data(pageResult);
    }

    @Operation(summary = "会话用户滚动查询")
    @PostMapping("/chatUserScroll")
    public Rsp<ScrollResult<ChatUserInfoResultVo>> chatUserScroll(@RequestBody @Valid ChatUserScrollParamVo paramVo) {
        EsUserInfoScrollParam scrollParam = Func.copy(paramVo, EsUserInfoScrollParam.class);
        scrollParam.setGtRobotPlatform(RobotPlatform.DIVERSION.getType());
        scrollParam.setUserNickname(paramVo.getKeyword());
        Rsp<ScrollResult<UserInfoResult>> rsp = esUserInfoClient.scroll(scrollParam);
        if (RspHd.failOrDataIsEmpty(rsp)) {
            return RspHd.fail(rsp);
        }

        ScrollResult<ChatUserInfoResultVo> scrollVo = ScrollResult.convertBean(rsp.getData(), ChatUserInfoResultVo.class);

        // 统计未读消息数
        userInfoService.userMsgUnreadStatistic(scrollVo.getRecords());
        // 设置最后一条消息内容
        userInfoService.setLastMsgRecord(scrollVo.getRecords());

        return RspHd.data(scrollVo);
    }


    @Operation(summary = "会话用户详情")
    @PostMapping("/chatUserDetails")
    public Rsp<ChatUserInfoResultVo> chatUserDetails(@RequestBody @Valid IdReq idReq) {
        Rsp<UserInfoResult> userInfoRsp = userInfoClient.getById(idReq.getId());
        if (RspHd.failOrDataIsNull(userInfoRsp)) {
            return RspHd.fail(userInfoRsp);
        }
        ChatUserInfoResultVo vo = BeanUtil.copy(userInfoRsp.getData(), ChatUserInfoResultVo.class);

        // 统计未读消息数
        userInfoService.userMsgUnreadStatistic(Collections.singletonList(vo));
        // 设置最后一条消息内容
        userInfoService.setLastMsgRecord(Collections.singletonList(vo));

        return RspHd.data(vo);
    }

    @Operation(summary = "更新读取用户消息时间")
    @PostMapping("/updateLastReadTime")
    @Auth(way = Auth.Way.ALL_USER)
    public Rsp<Void> updateLastReadTime(@RequestBody @Valid IdReq req) {
        UserInfoUpdateParam updateParam = new UserInfoUpdateParam();
        updateParam.setId(req.getId());
        updateParam.setMsgLastReadTime(LocalDateTime.now());
        return userInfoClient.update(updateParam);
    }

    @Operation(summary = "用户信息下载")
    @PostMapping("/userInfoDownload")
    public Rsp<Void> userInfoDownload(@RequestBody @Valid EsUserInfoDownloadParamVo param) {
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(UserInfoConsumer.USER_INFO_EXPORT_TYPE);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

    @Operation(summary = "客资信息下载")
    @PostMapping("/customerCapitalDownload")
    public Rsp<Void> customerCapitalDownload(@RequestBody @Valid EsUserCustomerCapitalDownloadParamVo param) {
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(UserInfoConsumer.USER_CUSTOMER_CAPITAL_EXPORT_TYPE);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

}
