package com.swhd.ai.kefu.web.tenant.kefu.vo.result;

import com.swhd.ai.kefu.api.kefu.dto.result.KefuConfigResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/16
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "KefuConfigResultVo对象")
public class KefuConfigResultVo extends KefuConfigResult {

    @Schema(description = "关联平台")
    private List<KefuPlatformResultVo> platformList;

}
