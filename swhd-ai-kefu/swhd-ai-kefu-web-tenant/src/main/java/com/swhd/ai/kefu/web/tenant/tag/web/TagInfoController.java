package com.swhd.ai.kefu.web.tenant.tag.web;

import com.swhd.ai.kefu.api.tag.client.TagInfoClient;
import com.swhd.ai.kefu.api.tag.dto.param.info.TagInfoAddParam;
import com.swhd.ai.kefu.api.tag.dto.param.info.TagInfoPageParam;
import com.swhd.ai.kefu.api.tag.dto.param.info.TagInfoUpdateParam;
import com.swhd.ai.kefu.api.tag.dto.result.TagInfoResult;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.tag.service.TagInfoService;
import com.swhd.ai.kefu.web.tenant.tag.vo.result.TagInfoResultVo;
import com.swhd.magiccube.core.dto.result.AntdSelectResult;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

import static com.swhd.magiccube.core.constant.TenantConstant.SHARE_TENANT_ID;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/tagInfo")
public class TagInfoController {

    private final TagInfoClient tagInfoClient;

    private final TagInfoService tagInfoService;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<TagInfoResultVo>> page(@RequestBody @Valid TagInfoPageParam param) {
        Long tenantId = TenantHolder.getRequiredTenantId();
        param.setTenantIds(List.of(tenantId, SHARE_TENANT_ID));
        Rsp<PageResult<TagInfoResult>> rsp = TenantHolder.methodIgnoreTenant(() -> tagInfoClient.page(param));
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }

        PageResult<TagInfoResultVo> voResult = PageUtil.convert(rsp.getData(), TagInfoResultVo.class);
        // 设置状态
        tagInfoService.setState(voResult.getRecords());

        return RspHd.data(voResult);
    }

    @Operation(summary = "下拉列表")
    @GetMapping("/selectAll")
    public Rsp<List<AntdSelectResult>> selectAll() {
        Long tenantId = TenantHolder.getRequiredTenantId();
        TagInfoPageParam param = new TagInfoPageParam();
        param.setTenantIds(List.of(tenantId, SHARE_TENANT_ID));
        Rsp<List<TagInfoResult>> rsp = TenantHolder.methodIgnoreTenant(() -> tagInfoClient.list(param));
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        List<AntdSelectResult> list = rsp.getData().stream()
                .map(result -> new AntdSelectResult(result.getId(), result.getTitle()))
                .toList();
        return RspHd.data(list);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<TagInfoResultVo> getById(@RequestParam("id") Long id) {
        Rsp<TagInfoResult> rsp = tagInfoClient.getById(id);
        if (RspHd.isFail(rsp)) {
            return Rsp.fail(rsp);
        }
        if (rsp.getData() == null) {
            return RspHd.data(null);
        }
        TagInfoResultVo vo = Func.copy(rsp.getData(), TagInfoResultVo.class);
        tagInfoService.setState(List.of(vo));
        return RspHd.data(vo);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid TagInfoAddParam param) {
        return tagInfoClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid TagInfoUpdateParam param) {
        return tagInfoClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return tagInfoClient.removeByIds(ids);
    }

}
