package com.swhd.ai.kefu.web.tenant.user.vo.param.info;

import com.swhd.magiccube.core.dto.param.ScrollReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/18
 */
@Getter
@Setter
@Schema(description = "ChatUserScrollParamVo对象")
public class ChatUserScrollParamVo extends ScrollReq {

    @Schema(description = "搜索关键字（昵称）")
    private String keyword;

    @Schema(description = "机器人平台：1-抖音私信，2-微信公众号")
    private Integer robotPlatform;

    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "授权平台的openId/appId/userId")
    private List<String> oauthOpenIds;

    @Schema(description = "授权平台授权的昵称")
    private String oauthNickname;

    @Schema(description = "用户openId")
    private String userOpenId;

    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "来源场景")
    private String sourceScene;

    @Schema(description = "活跃状态：0-沉默，1-开口，2-互动")
    private Integer activeState;

    @Schema(description = "用户首次聊天（用户开口）时间")
    private List<LocalDate> userFirstChatTimeBetween;

    @Schema(description = "用户最后聊天时间")
    private List<LocalDate> userLastChatTimeBetween;

    @Schema(description = "最后聊天时间")
    private List<LocalDate> lastChatTimeBetween;

    @Schema(description = "广告回传时间")
    private List<LocalDate> adFeedbackTimeBetween;

    @Schema(description = "ai聊天：0-关闭，1-开启")
    private Integer aiChat;

    @Schema(description = "创建时间")
    private List<LocalDate> createTimeBetween;

    @Schema(description = "标签id")
    private Long tagId;

    @Schema(description = "标签id")
    private List<Long> tagIds;

    @Schema(description = "排除标签id")
    private List<Long> excludeTagIds;

    @Schema(description = "线索来源广告主Id")
    private Long sourceAdvId;

    @Schema(description = "线索来源广告主名称")
    private String sourceAdvName;

    @Schema(description = "线索来源广告计划Id")
    private Long sourceAdId;

    @Schema(description = "线索来源广告计划名")
    private String sourceAdName;

    @Schema(description = "线索来源创意Id")
    private Long sourceCreativeId;

    @Schema(description = "线索来源广告Id")
    private Long sourcePromotionId;

    @Schema(description = "线索来源广告素材-标题Id")
    private Long sourceMaterialTitleId;

    @Schema(description = "线索来源广告素材-图片Id")
    private Long sourceMaterialImageId;

    @Schema(description = "线索来源广告素材-视频Id")
    private Long sourceMaterialVideoId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "微信")
    private String wechat;

}
