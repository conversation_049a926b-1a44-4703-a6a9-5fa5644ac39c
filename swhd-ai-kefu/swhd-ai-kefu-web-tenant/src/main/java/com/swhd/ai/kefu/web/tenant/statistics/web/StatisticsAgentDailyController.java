package com.swhd.ai.kefu.web.tenant.statistics.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.ai.kefu.api.platform.wrapper.PlatformOauthInfoWrapper;
import com.swhd.ai.kefu.api.stastics.client.StatisticsAgentDailyClient;
import com.swhd.ai.kefu.api.stastics.dto.param.daily.StatisticsAgentDailyOverviewParam;
import com.swhd.ai.kefu.api.stastics.dto.param.daily.StatisticsAgentDailyPageParam;
import com.swhd.ai.kefu.api.stastics.dto.result.StatisticsAgentDailyOverviewResult;
import com.swhd.ai.kefu.api.stastics.dto.result.StatisticsAgentDailyResult;
import com.swhd.ai.kefu.web.tenant.common.constant.WebConstant;
import com.swhd.ai.kefu.web.tenant.statistics.mq.StatisticsAgentDailyOverviewDownloadConsumer;
import com.swhd.ai.kefu.web.tenant.statistics.vo.result.StatisticsAgentDailyResultVo;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.DateUtil;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2024/9/23
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping(WebConstant.BASE_PATH + "/statistics/agent/daily")
public class StatisticsAgentDailyController {

    private final StatisticsAgentDailyClient statisticsAgentDailyClient;
    private final DownloadExportRecordClient downloadExportRecordClient;

    @PostMapping("/page")
    @Operation(summary = "分页查询渠道汇总日统计表")
    public Rsp<PageResult<StatisticsAgentDailyResultVo>> page(@RequestBody @Valid StatisticsAgentDailyPageParam param) {
        Rsp<PageResult<StatisticsAgentDailyResult>> rsp = statisticsAgentDailyClient.page(param);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        PageResult<StatisticsAgentDailyResultVo> voResult = PageUtil.convert(rsp.getData(), StatisticsAgentDailyResultVo.class);

        // 设置授权平台授权的昵称
        PlatformOauthInfoWrapper.getInstance().setOauthList(voResult.getRecords(),
                StatisticsAgentDailyResultVo::getOauthOpenId,
                StatisticsAgentDailyResultVo::getRobotPlatform,
                StatisticsAgentDailyResultVo::setOauthInfo);

        return Rsp.data(voResult);
    }

    @PostMapping("/overview")
    @Operation(summary = "渠道日统计表概览")
    public Rsp<List<StatisticsAgentDailyOverviewResult>> overview(@RequestBody @Valid StatisticsAgentDailyOverviewParam param) {
        if (DateUtil.between(param.getStatisticsDateBegin(), param.getStatisticsDateEnd()).getDays() > 366) {
            return RspHd.fail("日期不能超过一年范围");
        }
        return statisticsAgentDailyClient.overview(param);
    }

    @Auth(way = Auth.Way.ANONYMOUS)
    @PostMapping("/overview/summary")
    @Operation(summary = "渠道日统计表概览-汇总")
    public Rsp<StatisticsAgentDailyOverviewResult> overviewSummary(@RequestBody @Valid StatisticsAgentDailyOverviewParam param) {
        if (DateUtil.between(param.getStatisticsDateBegin(), param.getStatisticsDateEnd()).getDays() > 366) {
            return RspHd.fail("日期不能超过一年范围");
        }
        return statisticsAgentDailyClient.overviewSummary(param);
    }

    @PostMapping("/overview/download")
    @Operation(summary = "渠道日统计导出")
    public Rsp<Void> overviewDownload(@RequestBody @Valid StatisticsAgentDailyOverviewParam param) {
        if (DateUtil.between(param.getStatisticsDateBegin(), param.getStatisticsDateEnd()).getDays() > 366) {
            return RspHd.fail("日期范围不能超过一年");
        }

        // 使用DownloadExportRecordClient提交下载任务
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(StatisticsAgentDailyOverviewDownloadConsumer.AGENT_DAILY_OVERVIEW_EXPORT_TYPE);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

}
