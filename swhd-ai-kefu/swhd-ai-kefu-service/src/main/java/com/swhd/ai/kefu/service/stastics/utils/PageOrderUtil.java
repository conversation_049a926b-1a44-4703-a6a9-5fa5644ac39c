package com.swhd.ai.kefu.service.stastics.utils;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.SortOrder;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
@UtilityClass
public class PageOrderUtil {

    private static final ConcurrentHashMap<Class<?>, List<String>> FIELD_NAME_LIST_CACHE = new ConcurrentHashMap<>();

    public void orderBy(Page<?> page, List<SortOrder> sortOrderList, Class<?> clazz) {
        if (Func.isEmpty(sortOrderList)) {
            return;
        }
        List<String> fieldNameList = FIELD_NAME_LIST_CACHE.computeIfAbsent(clazz, key ->
                Arrays.stream(clazz.getDeclaredFields()).map(Field::getName).toList());
        List<OrderItem> orders = sortOrderList.stream()
                .filter(item -> fieldNameList.contains(item.getProperty()))
                .map(item -> {
                    return new OrderItem().setColumn(item.getProperty()).setAsc(item.getAscending());
                })
                .toList();
        if (Func.isNotEmpty(orders)) {
            page.setOrders(orders);
        }
    }

}
