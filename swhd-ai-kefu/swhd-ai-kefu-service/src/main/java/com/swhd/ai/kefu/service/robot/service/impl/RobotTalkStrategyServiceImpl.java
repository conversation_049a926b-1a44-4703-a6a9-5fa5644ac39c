package com.swhd.ai.kefu.service.robot.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.robot.dto.param.talk.strategy.RobotTalkStrategyPageParam;
import com.swhd.ai.kefu.api.robot.dto.param.talk.strategy.RobotTalkStrategySaveParam;
import com.swhd.ai.kefu.service.robot.entity.RobotTalkStrategy;
import com.swhd.ai.kefu.service.robot.mapper.RobotTalkStrategyMapper;
import com.swhd.ai.kefu.service.robot.service.RobotTalkStrategyService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 机器人对话策略表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Service
@AllArgsConstructor
public class RobotTalkStrategyServiceImpl extends BaseHdServiceImpl<RobotTalkStrategyMapper, RobotTalkStrategy>
        implements RobotTalkStrategyService {

    @Override
    public IPage<RobotTalkStrategy> page(RobotTalkStrategyPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getRobotId()), RobotTalkStrategy::getRobotId, param.getRobotId())
                .eq(Func.isNotEmpty(param.getNotAnswerStrategy()), RobotTalkStrategy::getNotAnswerStrategy, param.getNotAnswerStrategy())
                .eq(Func.isNotEmpty(param.getAskStrategy()), RobotTalkStrategy::getAskStrategy, param.getAskStrategy())
                .orderByDesc(RobotTalkStrategy::getCreateTime)
                .orderByDesc(RobotTalkStrategy::getId)
                .page(convertToPage(param));
    }

    @Override
    public RobotTalkStrategy getByRobotId(Long robotId) {
        if (Func.isEmpty(robotId)) {
            return null;
        }
        return lambdaQuery().eq(RobotTalkStrategy::getRobotId, robotId).limitOne();
    }

    @Override
    public void copy(Long templateRobotId, Long toRobotId) {
        RobotTalkStrategy templateTalkStrategy = TenantHolder.methodIgnoreTenant(() -> getByRobotId(templateRobotId));
        if (templateTalkStrategy == null) {
            return;
        }
        RobotTalkStrategy add = new RobotTalkStrategy();
        add.setRobotId(toRobotId);
        add.setMobileInvalidContents(templateTalkStrategy.getMobileInvalidContents());
        add.setMobileInvalidTimes(templateTalkStrategy.getMobileInvalidTimes());
        add.setNotAnswerStrategy(templateTalkStrategy.getNotAnswerStrategy());
        add.setNotAnswerStrategyContents(templateTalkStrategy.getNotAnswerStrategyContents());
        add.setAskStrategy(templateTalkStrategy.getAskStrategy());
        save(add);
    }

    @Override
    @Lockable(prefixKey = "ai:kefu:robot:talk:strategy:save", key = "#param.robotId", tenant = false, waitTime = 6000)
    public Rsp<Void> save(RobotTalkStrategySaveParam param) {
        RobotTalkStrategy talkStrategy = getByRobotId(param.getRobotId());
        boolean result;
        if (talkStrategy == null) {
            RobotTalkStrategy add = Func.copy(param, RobotTalkStrategy.class);
            result = save(add);
        } else {
            RobotTalkStrategy update = Func.copy(param, RobotTalkStrategy.class);
            update.setId(talkStrategy.getId());
            result = updateById(update);
        }
        return RspHd.status(result);
    }

}
