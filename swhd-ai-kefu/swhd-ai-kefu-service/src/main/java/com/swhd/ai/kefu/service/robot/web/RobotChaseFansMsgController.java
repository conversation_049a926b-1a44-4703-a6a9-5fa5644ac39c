package com.swhd.ai.kefu.service.robot.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.robot.client.RobotChaseFansMsgClient;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgAddParam;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgPageParam;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.msg.RobotChaseFansMsgUpdateParam;
import com.swhd.ai.kefu.api.robot.dto.result.RobotChaseFansMsgResult;
import com.swhd.ai.kefu.service.robot.entity.RobotChaseFansMsg;
import com.swhd.ai.kefu.service.robot.service.RobotInfoService;
import com.swhd.ai.kefu.service.robot.service.RobotChaseFansMsgService;
import com.swhd.magiccube.core.constant.SwitchConstant;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@RestController
@AllArgsConstructor
@RequestMapping(RobotChaseFansMsgClient.BASE_PATH)
public class RobotChaseFansMsgController implements RobotChaseFansMsgClient {

    private final RobotChaseFansMsgService robotChaseFansMsgService;

    private final RobotInfoService robotInfoService;

    @Override
    public Rsp<PageResult<RobotChaseFansMsgResult>> page(RobotChaseFansMsgPageParam param) {
        IPage<RobotChaseFansMsg> iPage = robotChaseFansMsgService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, RobotChaseFansMsgResult.class));
    }

    @Override
    public Rsp<RobotChaseFansMsgResult> getById(Long id) {
        RobotChaseFansMsg entity = robotChaseFansMsgService.getById(id);
        return RspHd.data(Func.copy(entity, RobotChaseFansMsgResult.class));
    }

    @Override
    public Rsp<List<RobotChaseFansMsgResult>> listByIds(Collection<Long> ids) {
        List<RobotChaseFansMsg> list = robotChaseFansMsgService.listByIds(ids);
        return RspHd.data(Func.copy(list, RobotChaseFansMsgResult.class));
    }

    @Override
    public Rsp<Void> add(RobotChaseFansMsgAddParam param) {
        if (!robotInfoService.existsById(param.getRobotId())) {
            return RspHd.fail("机器人信息不存在");
        }
        RobotChaseFansMsg chaseFansMsg = Func.copy(param, RobotChaseFansMsg.class);
        chaseFansMsg.setState(Optional.ofNullable(chaseFansMsg.getState()).orElse(SwitchConstant.YES));
        boolean result = robotChaseFansMsgService.save(chaseFansMsg);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(RobotChaseFansMsgUpdateParam param) {
        boolean result = robotChaseFansMsgService.updateById(Func.copy(param, RobotChaseFansMsg.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = robotChaseFansMsgService.removeByIds(ids);
        return RspHd.status(result);
    }

}
