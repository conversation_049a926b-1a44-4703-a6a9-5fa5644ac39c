package com.swhd.ai.kefu.service.push.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordAddParam;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordPageParam;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordRePushParam;
import com.swhd.ai.kefu.service.push.entity.PushRecord;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.Rsp;

import java.time.Duration;

/**
 * 推送记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface PushRecordService extends IBaseHdService<PushRecord> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<PushRecord> page(PushRecordPageParam param);

    /**
     * 根据用户id和推送脚本id获取
     *
     * @param userId   用户id
     * @param scriptId 推送脚本id
     * @return boolean
     */
    PushRecord getBy(Long userId, Long scriptId);

    /**
     * 判断是否存在
     *
     * @param userId   用户id
     * @param scriptId 推送脚本id
     * @return boolean
     */
    boolean existsBy(Long userId, Long scriptId);

    /**
     * 新增
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> add(PushRecordAddParam param);

    /**
     * 重新推送
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> rePush(PushRecordRePushParam param);

    /**
     * 处理正在推送
     *
     * @param id 主键id
     */
    void ingPush(Long id);

    /**
     * 处理正在推送
     *
     * @param id            主键id
     * @param delayDuration 延迟时长
     */
    void ingPush(Long id, Duration delayDuration);

    /**
     * 更新状态成功
     *
     * @param id       主键id
     * @param pushData 推送数据
     */
    void success(Long id, String pushData);

    /**
     * 更新状态失败
     *
     * @param pushRecord 推送记录
     * @param failReason 失败原因
     */
    void fail(PushRecord pushRecord, String failReason);

}
