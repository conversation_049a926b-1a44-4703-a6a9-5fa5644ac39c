package com.swhd.ai.kefu.service.user.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.user.client.UserImportTaobaoClueClient;
import com.swhd.ai.kefu.api.user.dto.param.importtaobao.clue.UserImportTaobaoCluePageParam;
import com.swhd.ai.kefu.api.user.dto.result.UserImportTaobaoClueResult;
import com.swhd.ai.kefu.service.user.entity.UserImportTaobaoClue;
import com.swhd.ai.kefu.service.user.service.UserImportTaobaoClueService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-31
 */
@RestController
@AllArgsConstructor
@RequestMapping(UserImportTaobaoClueClient.BASE_PATH)
public class UserImportTaobaoClueController implements UserImportTaobaoClueClient {

    private final UserImportTaobaoClueService userImportTaobaoClueService;

    @Override
    public Rsp<PageResult<UserImportTaobaoClueResult>> page(UserImportTaobaoCluePageParam param) {
        IPage<UserImportTaobaoClue> iPage = userImportTaobaoClueService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, UserImportTaobaoClueResult.class));
    }

    @Override
    public Rsp<UserImportTaobaoClueResult> getById(Long id) {
        UserImportTaobaoClue entity = userImportTaobaoClueService.getById(id);
        return RspHd.data(Func.copy(entity, UserImportTaobaoClueResult.class));
    }

    @Override
    public Rsp<List<UserImportTaobaoClueResult>> listByIds(Collection<Long> ids) {
        List<UserImportTaobaoClue> list = userImportTaobaoClueService.listByIds(ids);
        return RspHd.data(Func.copy(list, UserImportTaobaoClueResult.class));
    }

}
