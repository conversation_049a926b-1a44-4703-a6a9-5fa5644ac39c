<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.ai.kefu.service.user.mapper.UserInfoMapper">

    <update id="updateUserInfo">
        update taikf_user_info
        set modify_time=#{now}
        <if test="!(currentSubjectId == null || currentSubjectId == '')">
            ,modifier_id=#{currentSubjectId}
        </if>
        <if test="!(param.userNickname == null || param.userNickname == '')">
            ,user_nickname=#{param.userNickname}
        </if>
        <if test="!(param.userAvatar == null || param.userAvatar == '')">
            ,user_avatar=#{param.userAvatar}
        </if>
        <if test="param.userChatTime != null">
            ,active_state=(case when active_state=0 then 1 when active_state=1 then 2 else 2 end)
            ,user_first_chat_time=(case when user_first_chat_time is null then #{param.userChatTime} else user_first_chat_time end)
            ,user_last_chat_time=#{param.userChatTime}
        </if>
        <if test="param.lastChatTime != null">
            ,last_chat_time=#{param.lastChatTime}
        </if>
        <if test="param.adFeedbackTime != null">
            ,ad_feedback_time=#{param.adFeedbackTime}
        </if>
        <if test="param.hasHumanReply != null">
            ,has_human_reply=#{param.hasHumanReply}
        </if>
        <if test="param.aiChat != null">
            ,ai_chat=#{param.aiChat}
        </if>
        <if test="param.msgLastReadTime != null">
            ,msg_last_read_time=#{param.msgLastReadTime}
        </if>
        where id=#{param.id}
    </update>

</mapper>
