package com.swhd.ai.kefu.service.web.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.ai.kefu.api.msg.dto.result.MsgFormConfig;
import com.swhd.ai.kefu.api.web.dto.result.WebPlatformTopButtonConfig;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import com.swhd.magiccube.mybatis.typehandler.OssTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 网页平台信息表实体类
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "taikf_web_platform_info", autoResultMap = true)
public class WebPlatformInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "应用id（全局唯一）")
    private String appId;

    @Schema(description = "后台标题")
    private String backendTitle;

    @Schema(description = "客服名称")
    private String kefuName;

    @Schema(description = "客服副名称")
    private String kefuSubName;

    @Schema(description = "客服头像")
    private String kefuAvatar;

    @Schema(description = "主题颜色")
    private String themeColor;

    @Schema(description = "开启客资表单：0-关闭，1-开启")
    private Boolean openCustomerCapitalForm;

    @Schema(description = "客资表单配置")
    @TableField(typeHandler = JsonTypeHandler.class)
    private MsgFormConfig customerCapitalFormConfig;

    @Schema(description = "开启顶部按钮：0-关闭，1-开启")
    private Boolean openTopButton;

    @Schema(description = "顶部按钮配置")
    @TableField(typeHandler = JsonTypeHandler.class)
    private WebPlatformTopButtonConfig topButtonConfig;

}
