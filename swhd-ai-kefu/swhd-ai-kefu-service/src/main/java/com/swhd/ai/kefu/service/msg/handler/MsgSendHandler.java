package com.swhd.ai.kefu.service.msg.handler;

import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import com.swhd.ai.kefu.api.robot.dto.result.node.NodeData;
import com.swhd.ai.kefu.api.robot.dto.result.node.NodeDataSelect;
import com.swhd.ai.kefu.service.msg.entity.MsgRecord;
import com.swhd.ai.kefu.service.user.entity.UserInfo;

import java.util.List;

/**
 * 消息发送处理者
 *
 * <AUTHOR>
 * @since 2024/5/23
 */
public interface MsgSendHandler {

    /**
     * 是否处理
     *
     * @param userInfo 用户信息
     * @return boolean
     */
    boolean isHandler(UserInfo userInfo);

    /**
     * 欢迎语消息
     *
     * @param userInfo       用户信息
     * @param eventMsgRecord 触发欢迎语事件消息
     * @param nodeData       回复节点配置数据
     */
    void welcomeMsg(UserInfo userInfo, MsgRecord eventMsgRecord, NodeData nodeData);

    /**
     * 回复消息
     *
     * @param userInfo 用户信息
     * @param contents 回复内容
     */
    void replayMsg(UserInfo userInfo, List<ResponseContent> contents);

    /**
     * 回复消息
     *
     * @param userInfo      用户信息
     * @param contents      回复内容
     * @param userMsgRecord 用户消息
     */
    void replayMsg(UserInfo userInfo, MsgRecord userMsgRecord, List<ResponseContent> contents);

}
