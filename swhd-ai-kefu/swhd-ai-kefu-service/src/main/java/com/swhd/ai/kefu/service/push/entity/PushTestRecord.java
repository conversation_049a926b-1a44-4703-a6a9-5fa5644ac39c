package com.swhd.ai.kefu.service.push.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 推送测试记录表实体类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("taikf_push_test_record")
public class PushTestRecord extends BaseHdEntity {

    @Schema(description = "请求方法")
    private String method;

    @Schema(description = "查询参数")
    private String queryParam;

    @Schema(description = "请求头")
    private String headers;

    @Schema(description = "请求体")
    private String body;

}
