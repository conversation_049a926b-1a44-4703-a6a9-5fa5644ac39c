package com.swhd.ai.kefu.service.stastics.service;

import com.swhd.ai.kefu.service.stastics.entity.StatisticsUserDaily;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

/**
 * 用户当天统计表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface StatisticsUserDailyService extends IBaseHdService<StatisticsUserDaily> {

    /**
     * 获取统计用户
     *
     * @param userInfo       用户信息
     * @param statisticsDate 统计日期
     * @return StatisticsUserDaily
     */
    @NotNull
    StatisticsUserDaily getNullSave(UserInfo userInfo, LocalDate statisticsDate);

}
