package com.swhd.ai.kefu.service.interceptor;

import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.openfeign.interceptor.MagiccubeFeignRequestInterceptor;
import com.swj.magiccube.openfeign.thread.holder.MagiccubeFeignHeaderHolder;
import feign.RequestTemplate;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 自定义magiccube feign请求头拦截器
 *
 * 请求特定magiccube服务时，将慧引流用户上下文写入feign请求头（租户ID、用户ID都加上SWHD前缀）
 *
 * <AUTHOR> <EMAIL>
 * @since 2025/3/20
 */
@Component
@ConfigurationProperties(prefix = "feign")
public class CustomMagiccubeFeignRequestHeadersInterceptor implements MagiccubeFeignRequestInterceptor {

    private final static String CTX_PREFIX = "SWHD";
    
    private List<String> addHeaderServices;

    public void setAddHeaderServices(List<String> addHeaderServices) {
        this.addHeaderServices = addHeaderServices;
    }

    @Override
    public void apply(RequestTemplate template) {
        if (addHeaderServices == null || addHeaderServices.isEmpty()) {
            return;
        }
        if (addHeaderServices.contains(template.feignTarget().name())) {
            Long userId = CurrentUserHolder.currentUserId(false);
            Long tenantId = TenantHolder.getTenantId();
            if (Objects.nonNull(userId)) {
                MagiccubeFeignHeaderHolder.set(Constant.App.REQ_USER_ID, CTX_PREFIX + userId);
            }
            if (Objects.nonNull(tenantId)) {
                MagiccubeFeignHeaderHolder.set(Constant.App.TENANT_ID, CTX_PREFIX + tenantId);
            }
        }
    }

    /**
     * 优先级：在MagiccubeFeignRequestHeadersInterceptor之后
     * @return
     */
    public int getOrder() {
        return 10;
    }
}
