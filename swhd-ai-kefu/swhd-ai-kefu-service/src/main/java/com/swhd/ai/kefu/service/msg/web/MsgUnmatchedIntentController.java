package com.swhd.ai.kefu.service.msg.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.msg.client.MsgUnmatchedIntentClient;
import com.swhd.ai.kefu.api.msg.dto.param.intent.MsgUnmatchedIntentAddParam;
import com.swhd.ai.kefu.api.msg.dto.param.intent.MsgUnmatchedIntentDetailByNameParam;
import com.swhd.ai.kefu.api.msg.dto.param.intent.MsgUnmatchedIntentPageParam;
import com.swhd.ai.kefu.api.msg.dto.param.intent.MsgUnmatchedIntentUpdateParam;
import com.swhd.ai.kefu.api.msg.dto.result.MsgUnmatchedIntentResult;
import com.swhd.ai.kefu.service.msg.entity.MsgUnmatchedIntent;
import com.swhd.ai.kefu.service.msg.entity.MsgUnmatchedIntentRecord;
import com.swhd.ai.kefu.service.msg.service.MsgUnmatchedIntentRecordService;
import com.swhd.ai.kefu.service.msg.service.MsgUnmatchedIntentService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@AllArgsConstructor
@RequestMapping(MsgUnmatchedIntentClient.BASE_PATH)
public class MsgUnmatchedIntentController implements MsgUnmatchedIntentClient {

    private final MsgUnmatchedIntentService msgUnmatchedIntentService;
    private final MsgUnmatchedIntentRecordService msgUnmatchedIntentRecordService;

    @Override
    public Rsp<PageResult<MsgUnmatchedIntentResult>> page(MsgUnmatchedIntentPageParam param) {
        // 校验上一个/下一个参数
        this.validateParam(param);

        if (Objects.nonNull(param.getModifyTimeEnd())) {
            param.setModifyTimeEnd(param.getModifyTimeEnd().plusDays(1));
        }
        IPage<MsgUnmatchedIntentResult> page = msgUnmatchedIntentService.page(param);
        // 计算留资率，保留两位小数
        page.getRecords().forEach(result -> {
            if (result.getTriggerUserCount() == null || result.getTriggerUserCount() == 0
                    || result.getCustomerCapitalCount() == null) {
                result.setCustomerCapitalRate(0.00);
            } else {
                Double rate = Math
                        .round(result.getCustomerCapitalCount() * 100.0 / result.getTriggerUserCount() * 100.0) / 100.0;
                result.setCustomerCapitalRate(rate);
            }
        });
        return RspHd.data(PageUtil.convertFromMyBatis(page, MsgUnmatchedIntentResult.class));
    }

    /**
     * 校验参数
     *
     * @param param
     */
    private void validateParam(MsgUnmatchedIntentPageParam param) {
        // 上一个/下一个参数校验
        MsgUnmatchedIntentPageParam.PreOrNextParam preOrNextParam = param.getPreOrNextParam();
        if (Func.isNotEmpty(param.getPreOrNextParam())) {
            if (!"pre".equals(preOrNextParam.getDirection()) && !"next".equals(preOrNextParam.getDirection())) {
                throw new ServiceException("上一个/下一个参数错误");
            }

            if (Boolean.TRUE.equals(param.getGroupByIntentName())) {
                if (Func.isEmpty(preOrNextParam.getCurrentIntentName())) {
                    throw new ServiceException("当前意图名称不能为空");
                }
            } else if (Objects.isNull(preOrNextParam.getCurrentId())) {
                throw new ServiceException("当前意图ID不能为空");
            }

            if (Objects.isNull(preOrNextParam.getCurrentTriggerCount())) {
                throw new ServiceException("当前触发次数不能为空");
            }
        }
    }

    @Override
    public Rsp<MsgUnmatchedIntentResult> getById(Long id) {
        MsgUnmatchedIntent entity = msgUnmatchedIntentService.getById(id);
        return RspHd.data(Func.copy(entity, MsgUnmatchedIntentResult.class));
    }

    @Override
    public Rsp<List<MsgUnmatchedIntentResult>> listByIds(Collection<Long> ids) {
        List<MsgUnmatchedIntent> list = msgUnmatchedIntentService.listByIds(ids);
        return RspHd.data(Func.copy(list, MsgUnmatchedIntentResult.class));
    }

    @Override
    public Rsp<Void> add(MsgUnmatchedIntentAddParam param) {
        boolean result = msgUnmatchedIntentService.save(Func.copy(param, MsgUnmatchedIntent.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> update(MsgUnmatchedIntentUpdateParam param) {
        boolean result = msgUnmatchedIntentService.updateById(Func.copy(param, MsgUnmatchedIntent.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = msgUnmatchedIntentService.removeByIds(ids);
        return RspHd.status(result);
    }

    @Override
    public Rsp<MsgUnmatchedIntentResult> details(Long id) {
        MsgUnmatchedIntent intent = msgUnmatchedIntentService.getById(id);
        MsgUnmatchedIntentResult intentResult = BeanUtil.copy(intent, MsgUnmatchedIntentResult.class);
        if (intentResult == null) {
            return RspHd.data(intentResult);
        }

        // 触发次数
        long triggerCount = msgUnmatchedIntentRecordService
                .lambdaQuery().eq(MsgUnmatchedIntentRecord::getIntentId, id)
                .count();
        intentResult.setTriggerCount(triggerCount);

        // 触发用户数
        long triggerUserCount = msgUnmatchedIntentRecordService
                .lambdaQuery().eq(MsgUnmatchedIntentRecord::getIntentId, id)
                .select(MsgUnmatchedIntentRecord::getUserId)
                .list().stream().map(MsgUnmatchedIntentRecord::getUserId).distinct().count();
        intentResult.setTriggerUserCount(triggerUserCount);

        // 留资客户数
        long customerCapitalCount = msgUnmatchedIntentRecordService.countCustomerCapital(id);
        intentResult.setCustomerCapitalCount(customerCapitalCount);

        // 留资率
        Double rate = Math.round(customerCapitalCount * 100.0 / triggerUserCount * 100.0) / 100.0;
        intentResult.setCustomerCapitalRate(rate);

        return RspHd.data(intentResult);
    }

    @Override
    public Rsp<MsgUnmatchedIntentResult> detailByName(MsgUnmatchedIntentDetailByNameParam param) {
        // 复用分页接口，保持逻辑一致，数据只取第一条
        MsgUnmatchedIntentPageParam pageParam = BeanUtil.copy(param, MsgUnmatchedIntentPageParam.class);
        pageParam.setGroupByIntentName(true);
        Rsp<PageResult<MsgUnmatchedIntentResult>> pageRsp = page(pageParam);
        Rsp.assertSuccessAndNotEmpty(pageRsp);

        MsgUnmatchedIntentResult intentResult = pageRsp.getData().getRecords().get(0);
        return Rsp.data(intentResult);
    }

}
