package com.swhd.ai.kefu.service.msg.web;

import com.swhd.ai.kefu.api.common.constant.ResponseContentType;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import com.swhd.ai.kefu.api.msg.client.MsgApiClient;
import com.swhd.ai.kefu.api.msg.client.MsgRecordClient;
import com.swhd.ai.kefu.api.msg.constant.MsgContentType;
import com.swhd.ai.kefu.api.msg.dto.param.api.MsgApiReceiveUserMsgParam;
import com.swhd.ai.kefu.api.msg.dto.param.api.MsgApiSendMsgParam;
import com.swhd.ai.kefu.api.msg.dto.result.MsgRecordContent;
import com.swhd.ai.kefu.service.msg.entity.MsgRecord;
import com.swhd.ai.kefu.service.msg.handler.impl.MsgSendHandlerBase;
import com.swhd.ai.kefu.service.msg.service.MsgApiService;
import com.swhd.ai.kefu.service.msg.service.MsgRecordService;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.ai.kefu.service.user.service.UserInfoService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(MsgRecordClient.BASE_PATH)
public class MsgApiController implements MsgApiClient {

    private final UserInfoService userInfoService;

    private final MsgRecordService msgRecordService;

    private final MsgApiService msgApiService;

    @Override
    public Rsp<Void> receiveUserMsg(MsgApiReceiveUserMsgParam param) {
        // 获取用户信息
        UserInfo userInfo = userInfoService.getNullSave(param.getPlatform(),
                param.getOauthOpenId(), param.getOauthNickname(), param.getOauthAvatar(),
                param.getUserOpenId(), param.getUserNickname(), param.getUserAvatar());
        // 保存消息
        MsgRecord msgRecord = convertMsgRecord(userInfo.getId(), param);
        msgRecordService.saveMsgAndExtractAttr(userInfo, msgRecord);
        if (!Objects.equals(userInfo.getAiChat(), Constant.IntNum.ONE)) {
            String platformName = RobotPlatform.ofOptional(userInfo.getRobotPlatform()).map(RobotPlatform::getName).orElse("");
            log.info("{}用户[{}]未开启AI自动回复", platformName, userInfo.getId());
            return RspHd.fail("用户未开启AI自动回复");
        }
        // 接收处理用户消息
        return msgApiService.receiveUserMsg(userInfo, msgRecord);
    }

    private MsgRecord convertMsgRecord(Long userId, MsgApiReceiveUserMsgParam param) {
        MsgRecord msgRecord = new MsgRecord();
        msgRecord.setUserId(userId);
        MsgRecordContent msgContent = new MsgRecordContent();
        msgContent.setShowType(MsgContentType.TEXT);
        msgContent.setShowContent(param.getMsgContent());
        msgRecord.setMsgContent(msgContent);
        msgRecord.setSendTime(LocalDateTime.now());
        msgRecord.setSendState(Constant.IntNum.ONE);
        return msgRecord;
    }

    @Override
    public Rsp<Void> sendMsg(MsgApiSendMsgParam param) {
        UserInfo userInfo = userInfoService.getById(param.getUserId());
        if (userInfo == null) {
            return RspHd.fail("用户不存在");
        }
        ResponseContent responseContent = new ResponseContent();
        if (param.getType() == MsgContentType.TEXT) {
            responseContent.setType(ResponseContentType.TEXT);
            responseContent.setContent(param.getContent());
        } else if (param.getType() == MsgContentType.IMAGE) {
            responseContent.setType(ResponseContentType.IMAGE);
            responseContent.setMaterialUrl(param.getContent());
        } else if (param.getType() == MsgContentType.CARD) {
            responseContent.setType(ResponseContentType.CARD);
            responseContent.setCardId(Long.valueOf(param.getContent()));
        } else {
            return RspHd.fail("不支持的消息类型");
        }
        try {
            MsgSendHandlerBase.setMsgRecordFunction(msgRecord -> {
                if (param.getOrigin() != null) {
                    msgRecord.setOrigin(param.getOrigin());
                }
                MsgRecordContent.Platform platform = new MsgRecordContent.Platform()
                        .setFrontMsgId(param.getFrontMsgId());
                msgRecord.getMsgContent().setPlatform(platform);
                return msgRecord;
            });
            msgApiService.sendMsg(userInfo, responseContent);
        } finally {
            MsgSendHandlerBase.removeMsgRecordFunction();
        }
        return RspHd.success();
    }

}
