package com.swhd.ai.kefu.service.user.service;

import com.swhd.ai.kefu.api.user.dto.param.info.EsUserCustomerCapitalQueryParam;
import com.swhd.ai.kefu.api.user.dto.param.info.EsUserInfoQueryParam;
import com.swhd.ai.kefu.api.user.dto.param.info.EsUserInfoScrollParam;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoResult;
import com.swhd.ai.kefu.service.user.entity.EsUserInfo;
import com.swhd.magiccube.core.dto.result.ScrollResult;
import com.swhd.magiccube.es.base.BaseEsService;
import com.swj.magiccube.api.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
public interface EsUserInfoService extends BaseEsService<EsUserInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return PageResult
     */
    PageResult<UserInfoResult> page(EsUserInfoQueryParam param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return List
     */
    List<UserInfoResult> list(EsUserInfoQueryParam param);

    /**
     * 滚动查询
     *
     * @param param 查询参数
     * @return PageResult
     */
    ScrollResult<UserInfoResult> scroll(EsUserInfoScrollParam param);

    /**
     * 客资分页查询
     *
     * @param param 查询参数
     * @return PageResult
     */
    PageResult<UserInfoResult> customerCapitalPage(EsUserCustomerCapitalQueryParam param);

    /**
     * 客资列表查询
     *
     * @param param 查询参数
     * @return List
     */
    List<UserInfoResult> customerCapitalList(EsUserCustomerCapitalQueryParam param);

    /**
     * esUserInfo列表转UserInfoResult列表
     *
     * @param esUserInfoList esUserInfo列表
     * @return List
     */
    List<UserInfoResult> toResultList(List<EsUserInfo> esUserInfoList);

    /**
     * esUserInfo转UserInfoResult
     *
     * @param esUserInfo esUserInfo
     * @return UserInfoResult
     */
    UserInfoResult toResult(EsUserInfo esUserInfo);

    /**
     * 同步用户数据
     *
     * @param userId 用户id
     */
    void syncData(Long userId);

}
