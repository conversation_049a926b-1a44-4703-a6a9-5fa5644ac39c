package com.swhd.ai.kefu.service.msg.handler.impl;

import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import com.swhd.ai.kefu.api.robot.dto.result.node.NodeData;
import com.swhd.ai.kefu.api.robot.dto.result.node.NodeDataSelect;
import com.swhd.ai.kefu.service.msg.entity.MsgRecord;
import com.swhd.ai.kefu.service.msg.handler.MsgSendHandler;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Slf4j
@Component
@AllArgsConstructor
public class MsgSendHandlerWebImpl extends MsgSendHandlerBase implements MsgSendHandler {

    @Override
    public boolean isHandler(UserInfo userInfo) {
        return Objects.equals(userInfo.getRobotPlatform(), RobotPlatform.WEB.getType());
    }

    @Override
    public void welcomeMsg(UserInfo userInfo, MsgRecord eventMsgRecord, NodeData nodeData) {
        String sendKey = String.format("%s:ai:kefu:msg:web:welcomeMsg:%s", Func.getApplicationName(), userInfo.getId());
        if (RedisUtil.isExists(sendKey)) {
            log.info("网页用户[{}]在24小时内已发送过欢迎语消息，不重复发送", userInfo.getId());
            return;
        }

        boolean send = false;
        if (Func.isNotEmpty(nodeData.getContents()) && isHandlerMsgContent(nodeData.getContents().getFirst())) {
            send = true;
            saveMsgRecord(userInfo.getId(), nodeData.getContents().getFirst(), RspHd.success());
        }

        NodeDataSelect select = nodeData.getSelect();
        if (select != null && select.isOpen() && Func.isNotEmpty(select.getOptions())) {
            send = true;
            ResponseContent selectContent = ResponseContent.select(select.completeOptions());
            saveMsgRecord(userInfo.getId(), selectContent, RspHd.success());
        }

        if (select != null && select.isOpen() && isHandlerMsgContent(select.getEndContent())) {
            send = true;
            saveMsgRecord(userInfo.getId(), select.getEndContent(), RspHd.success());
        }

        if (send) {
            RedisUtil.set(sendKey, System.currentTimeMillis(), Duration.ofDays(1));
        }
    }

    @Override
    public void replayMsg(UserInfo userInfo, List<ResponseContent> contents) {
        contents = contents.stream()
                .filter(content -> {
                    boolean handlerMsgContent = isHandlerMsgContent(content);
                    if (!handlerMsgContent && log.isDebugEnabled()) {
                        log.debug("不处理网页用户[{}]的回复消息：{}", userInfo.getId(), JsonLogUtil.toJsonString(content));
                    }
                    return handlerMsgContent;
                })
                .toList();
        if (Func.isEmpty(contents)) {
            return;
        }
        contents.forEach(content -> saveMsgRecord(userInfo.getId(), content, RspHd.success()));
    }

    @Override
    public void replayMsg(UserInfo userInfo, MsgRecord userMsgRecord, List<ResponseContent> contents) {
        replayMsg(userInfo, contents);
    }

}
