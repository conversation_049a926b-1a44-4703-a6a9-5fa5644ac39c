package com.swhd.ai.kefu.service.msg.mq.consumer;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.msg.constant.MsgContentType;
import com.swhd.ai.kefu.api.msg.constant.MsgOrigin;
import com.swhd.ai.kefu.api.msg.dto.result.MsgRecordContent;
import com.swhd.ai.kefu.service.msg.entity.MsgRecord;
import com.swhd.ai.kefu.service.msg.properties.MsgProperties;
import com.swhd.ai.kefu.service.msg.service.MsgApiService;
import com.swhd.ai.kefu.service.msg.service.MsgRecordService;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.ai.kefu.service.user.properties.UserProperties;
import com.swhd.ai.kefu.service.user.service.UserInfoService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swhd.oauth.api.douyin.constant.WechatMpMessageType;
import com.swhd.oauth.api.wechat.dto.message.WechatMsgEventMessage;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Slf4j
@Component
@AllArgsConstructor
public class WechatEventConsumer {

    public static final String SUBSCRIBE_MSG_TEXT = "关注公众号";

    private final UserInfoService userInfoService;

    private final MsgRecordService msgRecordService;

    private final MsgApiService msgApiService;

    private final MsgProperties msgProperties;

    private final UserProperties userProperties;

    @Bean
    public Consumer<WechatMsgEventMessage> aiKefuWechatMpEnterEvent() {
        return message -> TenantHolder.methodTenantVoid(message.getTenantId(), () -> {
            log.info("消费微信公众号进入（关注、扫码）事件：{}", JsonLogUtil.toJsonString(message));

            // 获取用户信息
            UserInfo userInfo = getUserInfo(message);

            // 保存消息
            MsgRecord msgRecord = convertMsgRecord(userInfo.getId(), message);
            msgRecordService.saveMsgAndNotifyMQ(msgRecord);

            try {
                // 接收处理欢迎语
                Rsp<Void> rsp = msgApiService.receiveWelcomeMsg(userInfo, msgRecord);
                if (RspHd.isFail(rsp)) {
                    log.warn("接收处理微信公众号欢迎语失败：{}", JsonLogUtil.toJsonString(rsp));
                }
            } catch (Exception e) {
                log.error("接收处理微信公众号欢迎语异常", e);
            }
        });
    }

    @Bean
    public Consumer<WechatMsgEventMessage> aiKefuWechatMpMsgEvent() {
        return message -> TenantHolder.methodTenantVoid(message.getTenantId(), () -> {
            log.info("消费微信公众号消息事件：{}", JsonLogUtil.toJsonString(message));

            // 获取用户信息
            UserInfo userInfo = getUserInfo(message);

            if (msgProperties.getMsgConsumerCacheDuration().toMillis() > 0 && message.getMsgId() != null) {
                String messageIdKey = String.format("%s:ai:kefu:wechat:mp:msg:%s:%s",
                        ApiConstant.APP_NAME, userInfo.getId(), message.getMsgId());
                boolean trySet = RedisUtil.trySet(messageIdKey, String.valueOf(System.currentTimeMillis()),
                        msgProperties.getMsgConsumerCacheDuration());
                if (!trySet) {
                    log.info("微信公众号消息已消费");
                    return;
                }
            }

            // 保存消息
            MsgRecord msgRecord = convertMsgRecord(userInfo.getId(), message);
            msgRecordService.saveMsgAndExtractAttr(userInfo, msgRecord);

            if (!Objects.equals(msgRecord.getOrigin(), MsgOrigin.USER.getType())) {
                log.info("微信公众号消息不是用户发送的，不进行回复");
                return;
            }

            if (!userInfoService.openAiChat(userInfo)) {
                log.info("微信公众号用户[{}]未开启AI自动回复", userInfo.getId());
                return;
            }

            String replyNumKey = String.format("%s:ai:kefu:msg:wechat:mp:replyNum:%s:%s",
                    ApiConstant.APP_NAME, LocalDate.now(), userInfo.getId());
            long replyNum = RedisUtil.incr(replyNumKey, Duration.ofDays(1));
            if (replyNum > msgProperties.getDayMaxReplyNum()) {
                log.warn("微信公众号用户[{}]回复次数已达{}上限", userInfo.getId(), msgProperties.getDayMaxReplyNum());
                return;
            }

            try {
                if (msgRecordService.waitHumanReply(userInfo.getId(), msgRecord.getId())) {
                    // 存在人工回复，等待延迟消息处理
                    return;
                }

                // 接收处理用户消息
                Rsp<Void> rsp = msgApiService.receiveUserMsg(userInfo, msgRecord);
                if (RspHd.isFail(rsp)) {
                    log.warn("接收处理微信公众号用户消息失败：{}", JsonLogUtil.toJsonString(rsp));
                }
            } catch (Exception e) {
                log.error("接收处理微信公众号用户消息异常", e);
            }
        });
    }

    /**
     * 转成消息记录对象
     */
    private MsgRecord convertMsgRecord(Long userId, WechatMsgEventMessage message) {
        MsgRecord msgRecord = new MsgRecord();
        msgRecord.setUserId(userId);
        MsgRecordContent msgContent = new MsgRecordContent();
        msgContent.setWechatMp(Func.copy(message, MsgRecordContent.WechatMp.class));
        msgRecord.setMsgContent(msgContent);
        if (message.getCreateTime() != null) {
            msgRecord.setSendTime(LocalDateTimeUtil.of(message.getCreateTime() * 1000));
        } else {
            msgRecord.setSendTime(LocalDateTime.now());
        }
        msgRecord.setSendState(Constant.IntNum.ONE);

        String msgType;
        if (Objects.equals(message.getMsgType(), "event")) {
            msgRecord.setOrigin(MsgOrigin.EVENT.getType());
            msgType = Optional.ofNullable(message.getEvent()).map(String::toLowerCase).orElse(null);
        } else {
            msgRecord.setOrigin(MsgOrigin.USER.getType());
            msgType = message.getMsgType();
        }

        if (Objects.equals(WechatMpMessageType.subscribe.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(SUBSCRIBE_MSG_TEXT);
        } else if (Objects.equals(WechatMpMessageType.unsubscribe.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent("取消关注公众号");
        } else if (Objects.equals(WechatMpMessageType.scan.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent("扫码进入公众号");
        } else if (Objects.equals(WechatMpMessageType.location.name(), msgType)) {
            // 地理位置消息设置为事件（普通消息也有地理位置消息事件）
            msgRecord.setOrigin(MsgOrigin.EVENT.getType());
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(String.format("地理位置：%s,%s", message.getLocationX(), message.getLocationY()));
        } else if (Objects.equals(WechatMpMessageType.click.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(String.format("点击菜单：%s", message.getEventKey()));
        } else if (Objects.equals(WechatMpMessageType.view.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(String.format("点击菜单跳转链接：%s", message.getEventKey()));
        } else if (Objects.equals(WechatMpMessageType.text.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(message.getContent());
        } else if (Objects.equals(WechatMpMessageType.image.name(), msgType)) {
            msgContent.setShowType(MsgContentType.IMAGE);
            msgContent.setShowContent(message.getPicUrl());
        } else if (Objects.equals(WechatMpMessageType.voice.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent("[语音消息]");
        } else if (Objects.equals(WechatMpMessageType.video.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent("[视频消息]");
        } else if (Objects.equals(WechatMpMessageType.shortvideo.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent("[短视频消息]");
        } else if (Objects.equals(WechatMpMessageType.link.name(), msgType)) {
            msgContent.setShowType(MsgContentType.TEXT);
            StringBuilder sb = new StringBuilder();
            if (Func.isNotBlank(message.getTitle())) {
                sb.append("标题：").append(message.getTitle()).append("\n");
            }
            if (Func.isNotBlank(message.getDescription())) {
                sb.append("描述：").append(message.getDescription()).append("\n");
            }
            sb.append("链接：").append(message.getUrl());
            msgContent.setShowContent(sb.toString());
        } else {
            msgContent.setShowType(MsgContentType.TEXT);
            msgContent.setShowContent(Func.emptyOrDefault(message.getContent(), "[未知消息]"));
        }

        return msgRecord;
    }

    /**
     * 获取用户信息
     */
    private UserInfo getUserInfo(WechatMsgEventMessage message) {
        // 微信公众号不能使用接口获取用户昵称和头像
        UserInfo userInfo = userInfoService.getNullSave(RobotPlatform.WECHAT_MP,
                message.getAppId(), message.getAppName(), message.getAppLogo(),
                message.getFromUser(), null, userProperties.getCustomerDefaultAvatar());
        if (userInfo.getTenantId() == null) {
            userInfo.setTenantId(message.getTenantId());
        }
        return userInfo;
    }

}
