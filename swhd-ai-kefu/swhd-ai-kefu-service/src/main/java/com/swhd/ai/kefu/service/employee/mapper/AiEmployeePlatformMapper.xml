<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swhd.ai.kefu.service.employee.mapper.AiEmployeePlatformMapper">

    <!-- 联表查询已绑定的平台ID列表 -->
    <select id="listBoundPlatformIds" resultType="java.lang.Long">
        SELECT DISTINCT p.platform_id
        FROM taikf_ai_employee_platform p
        LEFT JOIN taikf_ai_employee e ON p.employee_id = e.id
        WHERE p.is_delete = 0 and e.is_delete = 0
        <if test="employeeType != null">
            AND e.type = #{employeeType}
        </if>
    </select>

</mapper> 