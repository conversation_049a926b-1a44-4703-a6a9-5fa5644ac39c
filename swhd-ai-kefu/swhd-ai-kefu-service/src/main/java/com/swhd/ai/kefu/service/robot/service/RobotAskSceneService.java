package com.swhd.ai.kefu.service.robot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.robot.dto.param.askscene.RobotAskScenePageParam;
import com.swhd.ai.kefu.service.robot.entity.RobotAskScene;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

import java.util.List;

/**
 * 机器人询问场景表 服务类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface RobotAskSceneService extends IBaseHdService<RobotAskScene> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<RobotAskScene> page(RobotAskScenePageParam param);

	/**
	 * 根据行业id获取（包括industryId=0的数据）
	 *
	 * @param industryId 行业id
	 * @return List
	 */
	List<RobotAskScene> listByIndustryId(Long industryId);

}
