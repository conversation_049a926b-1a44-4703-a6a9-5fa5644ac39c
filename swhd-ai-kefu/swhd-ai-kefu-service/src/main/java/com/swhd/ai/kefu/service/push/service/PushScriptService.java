package com.swhd.ai.kefu.service.push.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptAddConfigParam;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptCopyParam;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.ai.kefu.api.push.dto.param.script.PushScriptPageParam;
import com.swhd.ai.kefu.service.push.entity.PushScript;

/**
 * 推送脚本表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface PushScriptService extends IBaseHdService<PushScript> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<PushScript> page(PushScriptPageParam param);

	Long copy(PushScriptCopyParam param);


}
