package com.swhd.ai.kefu.service.push.handler.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.push.constant.PushType;
import com.swhd.ai.kefu.api.push.dto.param.eval.PushEvalParam;
import com.swhd.ai.kefu.api.push.dto.result.PushEvalResult;
import com.swhd.ai.kefu.api.push.dto.result.config.PushScriptConfigData;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoSourceAdInfo;
import com.swhd.ai.kefu.service.msg.service.MsgRecordService;
import com.swhd.ai.kefu.service.push.entity.PushScript;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.DigestUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.ip.IpUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.swhd.ai.kefu.api.push.constant.PushDataNameConstant.*;
import static com.swj.magiccube.api.Constant.Str.EMPTY;

/**
 * <AUTHOR>
 * @since 2024/12/4
 */
@Component
public class PushEvalHandlerConfigImpl extends PushEvalHandlerBase {

    @Autowired
    private MsgRecordService msgRecordService;

    @Override
    public PushType pushType() {
        return PushType.CONFIG;
    }

    @Override
    public Rsp<Void> checkConfigData(JsonNode data) {
        if (data == null) {
            return RspHd.success();
        }
        return checkConfigData(JsonUtil.convertValue(data, PushScriptConfigData.class));
    }

    public Rsp<Void> checkConfigData(PushScriptConfigData configData) {
        if (Func.isEmpty(configData.getUrl())) {
            return RspHd.fail("推送链接url为空");
        }
        if (pushProperties.getPushUrlWhiteList().contains(configData.getUrl())) {
            return RspHd.success();
        }
        try {
            URI uri = URI.create(configData.getUrl());
            InetAddress addresses = InetAddress.getByName(uri.getHost());
            if (addresses == null) {
                return RspHd.fail("推送链接域名无效");
            }
            String hostAddress = addresses.getHostAddress();
            boolean intranetIp = IpUtil.isIntranetIp(hostAddress);
            if (intranetIp) {
                return RspHd.fail("推送链接域名映射内网IP地址，禁止访问");
            }
        } catch (IllegalArgumentException e) {
            return RspHd.fail("无效的推送链接url");
        } catch (UnknownHostException e) {
            return RspHd.fail("推送链接域名无效");
        } catch (Exception e) {
            return RspHd.fail("推送链接域名检测异常");
        }
        return RspHd.success();
    }

    @Override
    public Rsp<PushEvalResult> eval(PushEvalParam param, PushScript script) {
        PushScriptConfigData configData = JsonUtil.convertValue(script.getConfigData(), PushScriptConfigData.class);
        if (configData == null) {
            return RspHd.fail("配置数据为空");
        }
        Rsp<Void> checkedRsp = checkConfigData(configData);
        if (RspHd.isFail(checkedRsp)) {
            return RspHd.fail(checkedRsp);
        }

        Map<String, Object> data = getData(param, configData);
        String dataStr = JsonUtil.toJsonString(data);

        HttpPost httpPost = new HttpPost(configData.getUrl());
        // data
        if (configData.getFormContentType() != null && configData.getFormContentType()) {
            List<BasicNameValuePair> parameters = data.entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue().toString()))
                    .toList();
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(parameters, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());
        } else {
            StringEntity entity = new StringEntity(dataStr, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
        }
        // headers
        if (Func.isNotEmpty(configData.getHeaders())) {
            configData.getHeaders().forEach(httpPost::setHeader);
        }
        // 签名
        String nonce = Func.random(32);
        long currentTimeMillis = System.currentTimeMillis();
        String signature = DigestUtil.sha256Hex(configData.getSignToken() + nonce + currentTimeMillis);
        httpPost.setHeader("Sign-Nonce", nonce);
        httpPost.setHeader("Sign-Time", String.valueOf(currentTimeMillis));
        httpPost.setHeader("Sign-Signature", signature);

        try (CloseableHttpResponse response = apiApacheHttpClient.getHttpClient().execute(httpPost)) {
            HttpEntity httpEntity = response.getEntity();
            String responseBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            EntityUtils.consume(httpEntity);
            boolean success;
            if (configData.getResponseJson() == null || !configData.getResponseJson()) {
                success = Objects.equals(responseBody, configData.getResponseValue());
            } else {
                try {
                    JsonNode jsonNode = JsonUtil.parse(responseBody);
                    String value = jsonNode.get(configData.getResponseJsonField()).asText();
                    success = Objects.equals(value, configData.getResponseValue());
                } catch (Exception e) {
                    // 无效的json
                    success = false;
                }
            }
            PushEvalResult result = new PushEvalResult(dataStr, responseBody);
            if (success) {
                return RspHd.data(result);
            }
            return RspHd.data(HttpStatus.INTERNAL_SERVER_ERROR.value(), "推送失败，返回：" + responseBody, result);
        } catch (IOException e) {
            log.warn("推送数据网络异常，url：{}", configData.getUrl(), e);
            return RspHd.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "推送数据网络异常");
        }
    }

    private Map<String, Object> getData(PushEvalParam param, PushScriptConfigData configData) {
        Map<String, Object> data = new HashMap<>();
        if (Func.isNotEmpty(configData.getDataExtra())) {
            data.putAll(configData.getDataExtra());
        }
        // 用户属性
        param.getUserAttrs().forEach((key, value) -> data.put(convertDataName(configData, key), value));
        // 用户信息
        if (param.getUserInfo() != null) {
            data.put(ROBOT_PLATFORM, param.getUserInfo().getRobotPlatform());
            data.put(ROBOT_PLATFORM_NAME, RobotPlatform.of(param.getUserInfo().getRobotPlatform()).getShortName());
            data.put(OAUTH_OPEN_ID, param.getUserInfo().getOauthOpenId());
            data.put(OAUTH_NICKNAME, param.getUserInfo().getOauthNickname());
            data.put(convertDataName(configData, USER_ID), param.getUserInfo().getId());
            data.put(convertDataName(configData, USER_NICKNAME), param.getUserInfo().getUserNickname());
            data.put(convertDataName(configData, USER_AVATAR), param.getUserInfo().getUserAvatar());
            data.put(convertDataName(configData, CUSTOMER_CAPITAL_TIME), getCustomerCapitalTime(param.getUserInfo().getId()));
            // 广告信息
            UserInfoSourceAdInfo adInfo = param.getUserInfo().getSourceAdInfo();
            if (adInfo != null) {
                data.put(convertDataName(configData, AD_ADV_ID), adInfo.getAdvId());
                data.put(convertDataName(configData, AD_ADV_NAME), adInfo.getAdvName());
                data.put(convertDataName(configData, AD_AD_ID), adInfo.getAdId());
                data.put(convertDataName(configData, AD_AD_NAME), adInfo.getAdName());
                data.put(convertDataName(configData, AD_CREATIVE_ID), adInfo.getCreativeId());
                data.put(convertDataName(configData, AD_PROMOTION_ID), adInfo.getPromotionId());
                data.put(convertDataName(configData, AD_MATERIAL_TITLE_ID), adInfo.getMaterialTitleId());
                data.put(convertDataName(configData, AD_MATERIAL_IMAGE_ID), adInfo.getMaterialImageId());
                data.put(convertDataName(configData, AD_MATERIAL_VIDEO_ID), adInfo.getMaterialVideoId());
            }
        }

        if (Func.isNotEmpty(configData.getDataConvert())) {
            // 数据转换
            Map<String, Object> convertData = new HashMap<>();
            configData.getDataConvert().stream()
                    .filter(convert -> Objects.equals(data.get(convert.getFieldName()), convert.getFieldValue()))
                    .forEach(convert -> convertData.put(convert.getNewFieldName(), convert.getNewFieldValue()));
            data.putAll(convertData);
        }

        if (Func.isNotEmpty(configData.getRequiredFields())) {
            // 必须的字段：如果字段信息不存在，填充空字符串
            configData.getRequiredFields().forEach(requiredField -> {
                String fieldName = convertDataName(configData, requiredField);
                data.putIfAbsent(fieldName, EMPTY);
            });
        }

        if (param.getUserInfo() != null && configData.getChatShareUrl() != null && configData.getChatShareUrl()) {
            // 推送聊天记录链接
            String chatShareUrlFieldName = Func.blankOrDefault(configData.getChatShareUrlFieldName(), "chatShareUrl");
            Duration duration = null;
            if (configData.getChatShareDays() != null && configData.getChatShareDays() > 0) {
                duration = Duration.ofDays(configData.getChatShareDays());
            }
            String chatShareUrl = msgRecordService.genChatShareUrl(param.getUserInfo().getId(), duration);
            data.put(chatShareUrlFieldName, chatShareUrl);
        }

        return data;
    }

    private String convertDataName(PushScriptConfigData configData, String dataName) {
        if (configData.getDataFieldMap() == null) {
            return dataName;
        }
        return Func.blankOrDefault(configData.getDataFieldMap().get(dataName), dataName);
    }

}
