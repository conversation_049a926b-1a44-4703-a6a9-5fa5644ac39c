package com.swhd.ai.kefu.service.msg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.ai.kefu.api.msg.dto.result.MsgRecordContent;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 消息记录表实体类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "taikf_msg_record", autoResultMap = true)
public class MsgRecord extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "消息来源：1-用户消息，2-事件消息，3-机器人回复消息，4-人工回复消息")
    private Integer origin;

    @Schema(description = "消息内容")
    @TableField(typeHandler = JsonTypeHandler.class)
    private MsgRecordContent msgContent;

    @Schema(description = "消息发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "发送状态：0-失败，1-成功")
    private Integer sendState;

    @Schema(description = "发送失败原因")
    private String failReason;

}
