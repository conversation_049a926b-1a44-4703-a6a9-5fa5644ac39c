package com.swhd.ai.kefu.service.user.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 用户导入淘宝（居然设计家）计划表实体类
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("taikf_user_import_taobao_plan")
public class UserImportTaobaoPlan extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "子租户id")
    private Long subTenantId;

    @Schema(description = "计划id")
    private String planId;

    @Schema(description = "计划名称")
    private String planName;

}
