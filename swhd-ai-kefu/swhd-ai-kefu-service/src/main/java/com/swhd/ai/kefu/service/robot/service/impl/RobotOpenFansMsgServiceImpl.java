package com.swhd.ai.kefu.service.robot.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.common.constant.AiEmployeeType;
import com.swhd.ai.kefu.api.common.constant.ApiConstant;
import com.swhd.ai.kefu.api.msg.dto.message.MsgOpenFansMessage;
import com.swhd.ai.kefu.api.robot.dto.param.openfans.msg.RobotOpenFansMsgPageParam;
import com.swhd.ai.kefu.api.user.constant.UserActiveState;
import com.swhd.ai.kefu.service.employee.entity.AiEmployee;
import com.swhd.ai.kefu.service.employee.service.AiEmployeePlatformService;
import com.swhd.ai.kefu.service.msg.mq.producer.MsgProducer;
import com.swhd.ai.kefu.service.platform.entity.PlatformInfo;
import com.swhd.ai.kefu.service.platform.service.PlatformInfoService;
import com.swhd.ai.kefu.service.robot.entity.RobotOpenFansMsg;
import com.swhd.ai.kefu.service.robot.mapper.RobotOpenFansMsgMapper;
import com.swhd.ai.kefu.service.robot.service.RobotOpenFansMsgService;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 机器人粉丝开口消息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Service
@AllArgsConstructor
public class RobotOpenFansMsgServiceImpl extends BaseHdServiceImpl<RobotOpenFansMsgMapper, RobotOpenFansMsg>
        implements RobotOpenFansMsgService {

    private final PlatformInfoService platformInfoService;
    private final AiEmployeePlatformService aiEmployeePlatformService;

    @Override
    public IPage<RobotOpenFansMsg> page(RobotOpenFansMsgPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getRobotId()), RobotOpenFansMsg::getRobotId, param.getRobotId())
                .orderByDesc(RobotOpenFansMsg::getRobotId)
                .orderByAsc(RobotOpenFansMsg::getDelayTime)
                .orderByAsc(RobotOpenFansMsg::getId)
                .page(convertToPage(param));
    }

    @Override
    public List<RobotOpenFansMsg> listByRobotId(Long robotId) {
        return lambdaQuery()
                .eq(RobotOpenFansMsg::getRobotId, robotId)
                .orderByAsc(RobotOpenFansMsg::getDelayTime)
                .orderByAsc(RobotOpenFansMsg::getId)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long templateRobotId, Long toRobotId) {
        List<RobotOpenFansMsg> templateList = TenantHolder.methodIgnoreTenant(() -> listByRobotId(templateRobotId));
        if (Func.isEmpty(templateList)) {
            return;
        }
        List<RobotOpenFansMsg> addList = templateList.stream()
                .map(templateChaseFansMsg -> {
                    RobotOpenFansMsg add = new RobotOpenFansMsg();
                    add.setRobotId(toRobotId);
                    add.setContent(templateChaseFansMsg.getContent());
                    add.setDelayTime(templateChaseFansMsg.getDelayTime());
                    return add;
                })
                .toList();
        saveBatch(addList);
    }

    @Override
    public void openFans(UserInfo userInfo, Long eventMsgId) {
        if (!(userInfo.getActiveState() == null
                || Objects.equals(userInfo.getActiveState(), UserActiveState.SILENT.getState()))) {
            return;
        }
        PlatformInfo platformInfo = platformInfoService.getByRobotPlatformAndOauthOpenId(
                userInfo.getRobotPlatform(), userInfo.getOauthOpenId());
        if (platformInfo == null) {
            return;
        }

        // 获取客服机器人
        AiEmployee aiEmployee = aiEmployeePlatformService.getWorkingByPlatformId(platformInfo.getId(), AiEmployeeType.LEAD_GENERATION);
        if (Objects.isNull(aiEmployee)) {
            return;
        }
        List<RobotOpenFansMsg> openFansMsgs = listByRobotId(aiEmployee.getRobotId());
        if (Func.isEmpty(openFansMsgs)) {
            return;
        }

        String cacheKey = openFansCacheKey(userInfo);
        long currentTimeMillis = System.currentTimeMillis();
        OpenFansCache cache = RedisUtil.get(cacheKey, OpenFansCache.class);
        if (cache != null) {
            // 1. 一天最多发送三次
            // 2. 一小时内只能发送一次
            if (cache.getNum() >= 3 || currentTimeMillis - cache.getTimeMillis() < 3600000) {
                return;
            }
        }

        int num = Optional.ofNullable(cache).map(OpenFansCache::getNum).orElse(0) + 1;
        long tenantId = TenantHolder.getTenantId();
        openFansMsgs.forEach(openFansMsg -> {
            MsgOpenFansMessage message = new MsgOpenFansMessage();
            message.setTenantId(tenantId);
            message.setUserId(userInfo.getId());
            message.setEventMsgId(eventMsgId);
            message.setContent(openFansMsg.getContent());
            MsgProducer.msgOpenFans(message, Duration.ofSeconds(openFansMsg.getDelayTime()));
        });
        RedisUtil.set(cacheKey, new OpenFansCache(currentTimeMillis, num), Duration.ofDays(1));
    }

    private String openFansCacheKey(UserInfo userInfo) {
        return String.format("%s:ai:kefu:robot:open:fans:msg:openFans:%s:%s",
                ApiConstant.APP_NAME, LocalDate.now(), userInfo.getId());
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OpenFansCache {

        /**
         * 发送时间戳
         */
        private long timeMillis;

        /**
         * 发送次数
         */
        private int num;

    }

}
