package com.swhd.ai.kefu.service.push.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.push.constant.PushType;
import com.swhd.ai.kefu.api.push.dto.param.eval.PushEvalParam;
import com.swhd.ai.kefu.api.push.dto.result.PushEvalResult;
import com.swhd.ai.kefu.api.push.dto.result.config.PushScriptConfigEcScrm;
import com.swhd.ai.kefu.api.user.constant.UserAttrConstant;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoSourceAdInfo;
import com.swhd.ai.kefu.service.push.entity.PushScript;
import com.swhd.ai.kefu.service.push.handler.dto.EcAddCustomerResult;
import com.swhd.ai.kefu.service.push.handler.dto.EcResp;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.DigestUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.Rsp;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.swhd.ai.kefu.api.push.constant.PushDataNameConstant.*;

/**
 * <AUTHOR>
 * @since 2024/12/4
 */
@Component
public class PushEvalHandlerEcScrmImpl extends PushEvalHandlerBase {

    /**
     * 文档：https://open.workec.com/newdoc/doc/1jRy6T9uy
     */
    private static final List<String> SYS_FIELDS = List.of("name", "call", "gender", "birthday", "founddate",
            "channelId", "groupId", "title", "wechat", "qq", "mobile", "phone", "email", "fax", "region", "vocation",
            "company", "companyUrl", "companyAddress", "memo");

    private static final String PREFECTURE_NAME = "prefecture";
    private static final String PREFECTURE_TOP = "中国";
    private static final String PREFECTURE_SEPARATOR = "|";

    /**
     * 名称取手机后缀长度
     */
    private static final int MOBILE_SUFFIX_LENGTH = 4;

    private static final String URL = "https://open.workec.com/v2/customer/addCustomer";

    @Override
    public PushType pushType() {
        return PushType.EC_SCRM;
    }

    @Override
    public Rsp<Void> checkConfigData(JsonNode data) {
        if (data == null) {
            return RspHd.success();
        }
        return checkConfigData(JsonUtil.convertValue(data, PushScriptConfigEcScrm.class));
    }

    public Rsp<Void> checkConfigData(PushScriptConfigEcScrm configData) {
        if (Func.isEmpty(configData.getCorpId())) {
            return RspHd.fail("企业ID为空");
        }
        if (Func.isEmpty(configData.getAppId())) {
            return RspHd.fail("应用ID为空");
        }
        if (Func.isEmpty(configData.getAppSecret())) {
            return RspHd.fail("应用密钥为空");
        }
        return RspHd.success();
    }

    @Override
    public Rsp<PushEvalResult> eval(PushEvalParam param, PushScript script) {
        PushScriptConfigEcScrm configData = JsonUtil.convertValue(script.getConfigData(), PushScriptConfigEcScrm.class);
        if (configData == null) {
            return RspHd.fail("配置数据为空");
        }
        Rsp<Void> checkedRsp = checkConfigData(configData);
        if (RspHd.isFail(checkedRsp)) {
            return RspHd.fail(checkedRsp);
        }
        return eval(userData(param, configData), configData);
    }

    public Rsp<PushEvalResult> eval(Map<String, Object> userData, PushScriptConfigEcScrm configData) {
        Map<String, Object> data = new HashMap<>();
        data.put("optUserId", configData.getOptUserId());
        data.put("repeat", false);
        data.put("supply_detail", false);
        data.put("list", List.of(userData));
        String dataStr = JSONObject.toJSONString(data);

        HttpPost httpPost = new HttpPost(URL);
        StringEntity entity = new StringEntity(dataStr, ContentType.APPLICATION_JSON);
        httpPost.setEntity(entity);
        // 签名
        sign(httpPost, configData);
        try (CloseableHttpResponse response = apiApacheHttpClient.getHttpClient().execute(httpPost)) {
            HttpEntity httpEntity = response.getEntity();
            String responseBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            EntityUtils.consume(httpEntity);
            log.info("推送EC返回：{}", responseBody);
            EcResp<EcAddCustomerResult> resp = JsonUtil.parseObject(responseBody, new TypeReference<>() {
            });
            PushEvalResult result = new PushEvalResult(dataStr, responseBody);
            if (resp != null
                    && resp.isSuccess()
                    && resp.getData() != null
                    && Func.isNotEmpty(resp.getData().getSuccessIdList())) {
                return RspHd.data(result);
            }
            if (isPrefectureFail(resp) && userData.containsKey(PREFECTURE_NAME)) {
                // 切除省市区进行重试
                String prefecture = (String) userData.get(PREFECTURE_NAME);
                if (prefecture.contains(PREFECTURE_SEPARATOR) && !Objects.equals(prefecture, PREFECTURE_TOP)) {
                    prefecture = prefecture.substring(0, prefecture.lastIndexOf(PREFECTURE_SEPARATOR));
                    userData.put(PREFECTURE_NAME, prefecture);
                } else {
                    userData.remove(PREFECTURE_NAME);
                }
                return eval(userData, configData);
            }
            return RspHd.data(HttpStatus.INTERNAL_SERVER_ERROR.value(), "推送失败，返回：" + responseBody, result);
        } catch (IOException e) {
            log.warn("推送ec scrm数据异常", e);
            return RspHd.fail("推送数据网络异常");
        }
    }

    private boolean isPrefectureFail(EcResp<EcAddCustomerResult> resp) {
        try {
            if (resp.getData() == null || Func.isEmpty(resp.getData().getFailureList())) {
                return false;
            }
            JsonNode failureCause = resp.getData().getFailureList().getFirst().get("failureCause");
            if (failureCause == null || !failureCause.isTextual()) {
                return false;
            }
            return Objects.equals(failureCause.asText(), "prefecture请求参数值无效");
        } catch (Exception e) {
            return false;
        }
    }

    private Map<String, Object> userData(PushEvalParam param, PushScriptConfigEcScrm configData) {
        Map<String, String> attrs = getData(param);

        Map<String, Object> userData = new HashMap<>();
        // 授权平台信息
        userData.put(ROBOT_PLATFORM, attrs.get(ROBOT_PLATFORM));
        userData.put(ROBOT_PLATFORM_NAME, attrs.get(ROBOT_PLATFORM_NAME));
        userData.put(OAUTH_OPEN_ID, attrs.get(OAUTH_OPEN_ID));
        userData.put(OAUTH_NICKNAME, attrs.get(OAUTH_NICKNAME));
        userData.put("followUserId", configData.getFollowUserId());
        // 手机号
        String mobile = attrs.get(UserAttrConstant.MOBILE_CODE);
        userData.put("mobile", mobile);
        // 客户名称
        String name = Func.blankOrDefault(attrs.get(UserAttrConstant.NAME_CODE), attrs.get(USER_NICKNAME));
        if (Func.isBlank(name)) {
            if (Func.isNotEmpty(mobile) && mobile.length() >= MOBILE_SUFFIX_LENGTH) {
                name = "客户" + mobile.substring(mobile.length() - MOBILE_SUFFIX_LENGTH);
            }
        }
        if (Func.isNotBlank(configData.getNameAppendPre())) {
            name = configData.getNameAppendPre() + name;
        }
        userData.put("name", name);
        // 性别
        String sex = attrs.get(UserAttrConstant.SEX_CODE);
        if (Objects.equals(sex, "男")) {
            userData.put("gender", 1);
        } else if (Objects.equals(sex, "女")) {
            userData.put("gender", 2);
        }
        // 备注
        String remark = attrs.get(UserAttrConstant.REMARK_CODE);
        if (Func.isNotEmpty(remark)) {
            userData.put("memo", remark);
        }
        // 地区
        String province = attrs.get(UserAttrConstant.PROVINCE_CODE);
        String city = attrs.get(UserAttrConstant.CITY_CODE);
        String district = attrs.get(UserAttrConstant.DISTRICT_CODE);
        if (Func.isNotBlank(province) || Func.isNotBlank(city) || Func.isNotBlank(district)) {
            StringBuilder sb = new StringBuilder(PREFECTURE_TOP);
            if (Func.isNotBlank(province)) {
                sb.append(PREFECTURE_SEPARATOR).append(province);
            }
            if (Func.isNotBlank(city)) {
                sb.append(PREFECTURE_SEPARATOR).append(city);
            }
            if (Func.isNotBlank(district)) {
                sb.append(PREFECTURE_SEPARATOR).append(district);
            }
            userData.put(PREFECTURE_NAME, sb.toString());
        }
        // 微信
        String wechat = attrs.get(UserAttrConstant.WECHAT_CODE);
        if (Func.isNotEmpty(wechat)) {
            userData.put("wechat", wechat);
        }
        if (Func.isEmpty(configData.getDataFieldMap())) {
            return userData;
        }
        // 自定义字段
        Map<String, String> fields = new HashMap<>();
        configData.getDataFieldMap().forEach((swjAttrCode, ecFieldName) -> {
            String value = attrs.get(swjAttrCode);
            if (Func.isBlank(value)) {
                return;
            }
            if (SYS_FIELDS.contains(ecFieldName)) {
                // 系统字段
                userData.put(ecFieldName, value);
                return;
            }
            if (Func.isNumeric(ecFieldName)) {
                // 自定义字段（数字格式）
                fields.put(ecFieldName, value);
            }
        });
        if (Func.isNotEmpty(fields)) {
            userData.put("fields", fields);
        }
        return userData;
    }

    private Map<String, String> getData(PushEvalParam param) {
        Map<String, String> data = new HashMap<>(param.getUserAttrs());
        // 用户信息
        if (param.getUserInfo() != null) {
            data.put(ROBOT_PLATFORM, param.getUserInfo().getRobotPlatform().toString());
            data.put(ROBOT_PLATFORM_NAME, RobotPlatform.of(param.getUserInfo().getRobotPlatform()).getShortName());
            data.put(OAUTH_OPEN_ID, param.getUserInfo().getOauthOpenId());
            data.put(OAUTH_NICKNAME, param.getUserInfo().getOauthNickname());
            data.put(USER_ID, param.getUserInfo().getId().toString());
            data.put(USER_NICKNAME, param.getUserInfo().getUserNickname());
            data.put(USER_AVATAR, param.getUserInfo().getUserAvatar());
            data.put(CUSTOMER_CAPITAL_TIME, getCustomerCapitalTime(param.getUserInfo().getId()));
            // 广告信息
            UserInfoSourceAdInfo adInfo = param.getUserInfo().getSourceAdInfo();
            if (adInfo != null) {
                data.put(AD_ADV_ID, adInfo.getAdvId());
                data.put(AD_ADV_NAME, adInfo.getAdvName());
                data.put(AD_AD_ID, adInfo.getAdId());
                data.put(AD_AD_NAME, adInfo.getAdName());
                data.put(AD_CREATIVE_ID, adInfo.getCreativeId());
                data.put(AD_PROMOTION_ID, adInfo.getPromotionId());
                data.put(AD_MATERIAL_TITLE_ID, adInfo.getMaterialTitleId());
                data.put(AD_MATERIAL_IMAGE_ID, adInfo.getMaterialImageId());
                data.put(AD_MATERIAL_VIDEO_ID, adInfo.getMaterialVideoId());
            }
        }
        return data;
    }

    private void sign(HttpPost httpPost, PushScriptConfigEcScrm configData) {
        long timeStamp = System.currentTimeMillis() - 20000;
        String sign = String.format("appId=%s&appSecret=%s&timeStamp=%s",
                configData.getAppId(), configData.getAppSecret(), timeStamp);
        httpPost.setHeader("X-Ec-Cid", configData.getCorpId());
        httpPost.setHeader("X-Ec-TimeStamp", Long.toString(timeStamp));
        httpPost.setHeader("X-Ec-Sign", DigestUtil.md5Hex(sign).toUpperCase());
    }

}
