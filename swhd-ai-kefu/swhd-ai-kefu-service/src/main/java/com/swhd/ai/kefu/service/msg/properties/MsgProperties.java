package com.swhd.ai.kefu.service.msg.properties;

import com.swhd.oauth.api.douyin.constant.DouyinChatMessageType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Getter
@Setter
@ConfigurationProperties(MsgProperties.PREFIX)
@Component
public class MsgProperties {

    public static final String PREFIX = "ai.kefu.msg";

    /**
     * 每天最大回复次数
     */
    private long dayMaxReplyNum = 100;

    /**
     * 抖音机器人回复时长（用于区分端上发送的消息，是人工回复的还是抖音系统发送的）
     */
    private Duration douyinRobotReplyDuration = Duration.ofSeconds(2);

    /**
     * 抖音人工回复消息类型列表
     */
    private List<String> douyinHumanReplyMessageTypeList = List.of(DouyinChatMessageType.text.name(),
            DouyinChatMessageType.image.name(), DouyinChatMessageType.user_local_image.name(),
            DouyinChatMessageType.emoji.name(), DouyinChatMessageType.video.name(),
            DouyinChatMessageType.user_local_video.name(), DouyinChatMessageType.video.name());

    /**
     * 抖音系统回复消息列表
     */
    private List<String> douyinSystemMsgList = List.of("你收到一条新消息，请打开抖音app查看",
            "如无需回复，邀请您对本次服务进行评价！", "邀请您对本次服务进行评价");

    /**
     * 消息消费缓存时长
     */
    private Duration msgConsumerCacheDuration = Duration.ofMinutes(10);

    /**
     * 人工回复检查时长
     */
    private Duration humanReplyCheckDuration = Duration.ofMinutes(5);

    /**
     * 接收用户消息延迟时长
     */
    private Duration receiveUserMsgDelayDuration = Duration.ofSeconds(30);

    /**
     * 会话分享url
     */
    private String chatShareUrl;

    /**
     * 会话分享默认时长
     */
    private Duration chatShareDuration = Duration.ofDays(30);

}
