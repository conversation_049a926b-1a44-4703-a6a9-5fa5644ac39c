package com.swhd.ai.kefu.service.robot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.robot.dto.param.talk.flow.RobotTalkFlowSaveParam;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.ai.kefu.api.robot.dto.param.talk.flow.RobotTalkFlowPageParam;
import com.swhd.ai.kefu.service.robot.entity.RobotTalkFlow;
import com.swj.magiccube.api.Rsp;

/**
 * 机器人对话流表 服务类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface RobotTalkFlowService extends IBaseHdService<RobotTalkFlow> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<RobotTalkFlow> page(RobotTalkFlowPageParam param);

    /**
     * 根据robotId获取
     *
     * @param robotId 机器人id
     * @return RobotTalkFlow
     */
    RobotTalkFlow getByRobotId(Long robotId);

    /**
     * 复制机器人配置
     *
     * @param templateRobotId 模板机器人id
     * @param toRobotId       复制到的机器人id
     */
    void copy(Long templateRobotId, Long toRobotId);

    /**
     * 保存
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> save(RobotTalkFlowSaveParam param);

}
