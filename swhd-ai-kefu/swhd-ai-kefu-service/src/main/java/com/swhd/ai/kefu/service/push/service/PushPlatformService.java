package com.swhd.ai.kefu.service.push.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.ai.kefu.api.push.dto.param.platform.PushPlatformPageParam;
import com.swhd.ai.kefu.service.push.entity.PushPlatform;

/**
 * 推送平台脚本关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface PushPlatformService extends IBaseHdService<PushPlatform> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<PushPlatform> page(PushPlatformPageParam param);

}
