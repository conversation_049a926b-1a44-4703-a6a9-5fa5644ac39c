package com.swhd.ai.kefu.service.robot.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 机器人追粉时间表实体类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "taikf_robot_chase_fans_time", autoResultMap = true)
public class RobotChaseFansTime extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "机器人id")
    private Long robotId;

    @Schema(description = "延迟时间配置：[3600,7200,21600,43200,86400]")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<Integer> config;

}
