package com.swhd.ai.kefu.service.web.web;

import com.swhd.ai.kefu.api.web.client.WebCustomerSeqClient;
import com.swhd.ai.kefu.service.web.service.WebCustomerSeqService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-10-26
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebCustomerSeqClient.BASE_PATH)
public class WebCustomerSeqController implements WebCustomerSeqClient {

    private final WebCustomerSeqService webCustomerSeqService;

    @Override
    public Rsp<Long> getSeq() {
        return RspHd.data(webCustomerSeqService.getSeq());
    }

}
