package com.swhd.ai.kefu.service.stastics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swhd.ai.kefu.api.stastics.dto.param.daily.StatisticsAgentDailyOverviewParam;
import com.swhd.ai.kefu.api.stastics.dto.param.daily.StatisticsAgentDailyPageParam;
import com.swhd.ai.kefu.api.stastics.dto.result.StatisticsAgentDailyOverviewResult;
import com.swhd.ai.kefu.api.stastics.dto.result.StatisticsAgentDailyResult;
import com.swhd.ai.kefu.service.stastics.entity.StatisticsAgentDaily;
import com.swhd.ai.kefu.service.stastics.mapper.StatisticsAgentDailyMapper;
import com.swhd.ai.kefu.service.stastics.service.StatisticsAgentDailyService;
import com.swhd.ai.kefu.service.stastics.utils.PageOrderUtil;
import com.swhd.ai.kefu.service.tag.properties.TagProperties;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.SortOrder;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 渠道汇总日统计表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class StatisticsAgentDailyServiceImpl extends BaseHdServiceImpl<StatisticsAgentDailyMapper, StatisticsAgentDaily>
        implements StatisticsAgentDailyService {

    private final TagProperties tagProperties;

    @Override
    public IPage<StatisticsAgentDailyResult> page(StatisticsAgentDailyPageParam param) {
        List<SortOrder> orderList = param.getSort();
        param.setSort(null);
        Page<StatisticsAgentDaily> page = convertToPage(param);
        PageOrderUtil.orderBy(page, orderList, StatisticsAgentDailyResult.class);
        return baseMapper.page(param, page);
    }

    @Override
    public List<StatisticsAgentDailyOverviewResult> overview(StatisticsAgentDailyOverviewParam param) {
        Map<LocalDate, StatisticsAgentDailyOverviewResult> map = baseMapper.overview(param).stream()
                .collect(Collectors.toMap(StatisticsAgentDailyOverviewResult::getStatisticsDate, r -> r));
        List<StatisticsAgentDailyOverviewResult> list = new ArrayList<>();
        for (LocalDate date = param.getStatisticsDateBegin(); !date.isAfter(param.getStatisticsDateEnd()); date = date.plusDays(1)) {
            LocalDate statisticsDate = date;
            StatisticsAgentDailyOverviewResult result = map.get(statisticsDate);
            list.add(Objects.requireNonNullElseGet(result, () -> {
                StatisticsAgentDailyOverviewResult newResult = new StatisticsAgentDailyOverviewResult();
                newResult.setStatisticsDate(statisticsDate);
                return newResult;
            }));
        }

        return list;
    }

    @Override
    public void executeStatisticsJob(LocalDate statisticsDate) {
        Long customerCapitalTagId = tagProperties.getCustomerCapitalTagId();
        List<StatisticsAgentDaily> list = baseMapper.statisticsAgentInfo(statisticsDate, customerCapitalTagId);
        if (Func.isEmpty(list)) {
            return;
        }

        list.forEach(save -> {
            save.setStatisticsDate(statisticsDate);

            // 总数据
            save.setSumDirectMessageCount(save.getNaturalDirectMessageCount() + save.getAdDirectMessageCount());
            save.setSumDirectMessageTodayCount(save.getNaturalDirectMessageTodayCount() + save.getAdDirectMessageTodayCount());
            save.setSumSpeakUpCount(save.getNaturalSpeakUpCount() + save.getAdSpeakUpCount());
            save.setSumSpeakUpTodayCount(save.getNaturalSpeakUpTodayCount() + save.getAdSpeakUpTodayCount());
            save.setSumSpeakUpAddCount(save.getNaturalSpeakUpAddCount() + save.getAdSpeakUpAddCount());
            save.setSumCustomerCapitalCount(save.getNaturalCustomerCapitalCount() + save.getAdCustomerCapitalCount());
            save.setSumCustomerCapitalTodayCount(save.getNaturalCustomerCapitalTodayCount() + save.getAdCustomerCapitalTodayCount());

            StatisticsAgentDaily statisticsAdDaily = lambdaQuery()
                    .eq(StatisticsAgentDaily::getStatisticsDate, statisticsDate)
                    .eq(StatisticsAgentDaily::getOauthOpenId, save.getOauthOpenId())
                    .limitOne();
            if (statisticsAdDaily == null) {
                // 不存在则新增
                save(save);
            } else {
                // 存在则更新
                save.setId(statisticsAdDaily.getId());
                updateById(save);
            }
        });
        log.info("统计更新渠道汇总日数据，租户{}，统计日期：{}", TenantHolder.getTenantId(), statisticsDate);
    }

    @Override
    public StatisticsAgentDailyOverviewResult overviewSummary(StatisticsAgentDailyOverviewParam param) {
        return baseMapper.overviewSummary(param);
    }

}
