package com.swhd.ai.kefu.service.msg.service;

import com.swhd.ai.api.dto.result.AiKefuExtractAddressResult;
import com.swhd.ai.kefu.api.common.dto.ResponseContent;
import com.swhd.ai.kefu.api.msg.dto.result.AnswerRelevantCheckResult;
import com.swhd.ai.kefu.api.msg.dto.result.ExtractAndCheckMobileStatusResult;
import com.swhd.ai.kefu.api.user.dto.result.UserTalkFlowCurrentAskContent;
import com.swhd.ai.kefu.service.knowledge.entity.KnowledgeQa;
import com.swhd.ai.kefu.service.msg.entity.MsgRecord;
import com.swhd.ai.kefu.service.platform.entity.PlatformInfo;
import com.swhd.ai.kefu.service.tag.entity.TagInfo;
import com.swhd.ai.kefu.service.user.entity.UserAttrConfig;
import com.swhd.ai.kefu.service.user.entity.UserTalkFlow;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/5/28
 */
public interface MsgAiService {

    /**
     * 校验用户的回答是否和问题相关
     *
     * @param userId     用户id
     * @param askContent 问腿内容
     * @param userAnswer 用户回答
     * @return AnswerRelevantCheckResult
     */
    AnswerRelevantCheckResult answerRelevantCheck(Long userId,
                                                  UserTalkFlowCurrentAskContent askContent,
                                                  String userAnswer);

    /**
     * 校验用户的回答地址问题相关
     *
     * @param userId     用户id
     * @param nodeTypeId 节点类型ID
     * @param userAnswer 用户回答
     * @return AnswerRelevantCheckResult
     */
    AnswerRelevantCheckResult answerAddressCheck(Long userId,
                                                 Long nodeTypeId,
                                                 String userAnswer);

    /**
     * 校验用户的回答是否和问题相关
     *
     * @param userId          用户id
     * @param robotId         机器人id
     * @param askContent      问腿内容
     * @param msgRecord       用户回答
     * @param id
     * @return boolean
     */
    Rsp<KnowledgeQa> chatIntentDetection(Long userId,
                                         Long robotId,
                                         UserTalkFlowCurrentAskContent askContent,
                                         MsgRecord msgRecord, Long id);


    /**
     * 根据聊天记录提取对应的留资标签
     *
     * @param userId        用户id
     * @param tagList       标签列表
     * @param msgRecordList 消息记录
     * @return Rsp
     */
    Rsp<Map<String, Boolean>> extractTag(Long userId, List<TagInfo> tagList, List<MsgRecord> msgRecordList);

    /**
     * 根据聊天记录提取对应的留资信息
     *
     * @param userId         用户id
     * @param attrConfigList 用户属性配置列表
     * @param msgRecordList  消息记录
     * @return Rsp
     */
    Rsp<Map<String, String>> extractInfo(Long userId, List<UserAttrConfig> attrConfigList, List<MsgRecord> msgRecordList);

    /**
     * 提取地址省市区
     *
     * @param userId  用户id
     * @param address 地址
     * @return Rsp
     */
    Rsp<AiKefuExtractAddressResult> extractAddress(Long userId, String address);

    /**
     * 话术优化
     *
     * @param userId     用户id
     * @param nodeTypeId 询问的节点类型ID
     * @param language   话术
     */
    default void languageOptimize(Long userId, Long nodeTypeId, ResponseContent language) {
        if (language == null || !language.isText() || Func.isBlank(language.getContent())) {
            return;
        }
        language.setContent(languageOptimize(userId, nodeTypeId, language.getContent()));
    }

    /**
     * 话术优化
     *
     * @param userId     用户id
     * @param nodeTypeId 询问的节点类型ID
     * @param language   话术
     * @return String
     */
    String languageOptimize(Long userId, Long nodeTypeId, String language);

    /**
     * 从用户回答的内容提取出手机号码，并校验
     *
     * @param userTalkFlow 用户对话流
     * @param userAnswer   用户回答
     * @return ExtractAndCheckMobileStatusResult
     */
    ExtractAndCheckMobileStatusResult extractAndCheckMobileStatus(UserTalkFlow userTalkFlow, String userAnswer);

    /**
     * 机器人聊天
     *
     * @param userTalkFlow 用户对话流
     * @param platformInfo 平台信息
     * @param robotChatId  聊天机器人id
     * @param userAnswer   用户回答
     * @return Rsp
     */
    Rsp<List<ResponseContent>> chat(UserTalkFlow userTalkFlow,
                                    PlatformInfo platformInfo,
                                    Long robotChatId,
                                    String userAnswer);

}
