package com.swhd.ai.kefu.service.web.web;

import com.swhd.ai.kefu.api.web.client.WebUserMsgClient;
import com.swhd.ai.kefu.api.web.dto.message.WebUserEnterEventMessage;
import com.swhd.ai.kefu.api.web.dto.message.WebUserMsgEventMessage;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserEnterParam;
import com.swhd.ai.kefu.api.web.dto.param.user.WebUserSendMsgParam;
import com.swhd.ai.kefu.api.web.dto.result.WebPlatformInfoResult;
import com.swhd.ai.kefu.service.web.entity.WebPlatformInfo;
import com.swhd.ai.kefu.service.web.mq.producer.WebUserProducer;
import com.swhd.ai.kefu.service.web.service.WebPlatformInfoService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebUserMsgClient.BASE_PATH)
public class WebUserMsgController implements WebUserMsgClient {

    private final WebPlatformInfoService webPlatformInfoService;

    @Override
    public Rsp<WebPlatformInfoResult> enterEvent(WebUserEnterParam param) {
        WebPlatformInfo webPlatformInfo = TenantHolder.methodIgnoreTenant(() ->
                webPlatformInfoService.getByAppId(param.getAppId()));
        if (webPlatformInfo == null) {
            return RspHd.fail("渠道不存在");
        }
        WebUserEnterEventMessage message = Func.copy(param, WebUserEnterEventMessage.class);
        message.setTenantId(webPlatformInfo.getTenantId());
        message.setKefuName(webPlatformInfo.getBackendTitle());
        message.setKefuAvatar(webPlatformInfo.getKefuAvatar());
        message.setMsgId(Func.randomUUID());
        message.setMsgTime(LocalDateTime.now());
        WebUserProducer.enterEvent(message);
        return RspHd.success();
    }

    @Override
    public Rsp<Long> sendMsg(WebUserSendMsgParam param) {
        String lockKey = String.format("%s:ai:kefu:web:user:msg:sendMsg:%s",
                Func.getApplicationName(), param.getUserOpenId());
        boolean result = RedisUtil.trySet(lockKey, Func.randomUUID(), Duration.ofMillis(500));
        if (!result) {
            return RspHd.fail("操作频繁，请稍后重试");
        }

        WebPlatformInfo webPlatformInfo = TenantHolder.methodIgnoreTenant(() ->
                webPlatformInfoService.getByAppId(param.getAppId()));
        if (webPlatformInfo == null) {
            return RspHd.fail("渠道不存在");
        }
        WebUserMsgEventMessage message = Func.copy(param, WebUserMsgEventMessage.class);
        message.setTenantId(webPlatformInfo.getTenantId());
        message.setKefuName(webPlatformInfo.getBackendTitle());
        message.setKefuAvatar(webPlatformInfo.getKefuAvatar());
        message.setMsgId(Func.randomUUID());
        message.setMsgTime(LocalDateTime.now());
        WebUserProducer.msgEvent(message);
        return RspHd.success();
    }

}
