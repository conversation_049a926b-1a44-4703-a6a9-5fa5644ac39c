package com.swhd.ai.kefu.service.kefu.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.kefu.dto.param.consume.KefuRightsConsumeListParam;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swhd.ai.kefu.api.kefu.dto.param.consume.KefuRightsConsumePageParam;
import com.swhd.ai.kefu.service.kefu.entity.KefuRightsConsume;

import java.util.List;

/**
 * AI客服权益消费 服务类
 *
 * <AUTHOR>
 * @since 2024-11-20
 */
public interface KefuRightsConsumeService extends IBaseHdService<KefuRightsConsume> {

	/**
	 * 分页查询
	 *
	 * @param param 查询参数
	 * @return IPage
	 */
	IPage<KefuRightsConsume> page(KefuRightsConsumePageParam param);

	List<KefuRightsConsume> list(KefuRightsConsumeListParam param);
}
