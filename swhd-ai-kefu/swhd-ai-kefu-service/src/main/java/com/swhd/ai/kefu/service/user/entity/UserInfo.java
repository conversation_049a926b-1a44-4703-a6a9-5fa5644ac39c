package com.swhd.ai.kefu.service.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.ai.kefu.api.user.dto.result.UserBizExtendResult;
import com.swhd.ai.kefu.api.user.dto.result.UserInfoSourceAdInfo;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.JsonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息表实体类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "taikf_user_info", autoResultMap = true)
public class UserInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "机器人平台：1-抖音私信，2-微信公众号")
    private Integer robotPlatform;

    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "授权平台授权的昵称")
    private String oauthNickname;

    @Schema(description = "授权平台的权的头像")
    private String oauthAvatar;

    @Schema(description = "用户openId")
    private String userOpenId;

    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "用户头像")
    private String userAvatar;

    @Schema(description = "来源场景")
    private String sourceScene;

    @Schema(description = "线索来源广告json数据")
    @TableField(typeHandler = JsonTypeHandler.class)
    private UserInfoSourceAdInfo sourceAdInfo;

    @Schema(description = "活跃状态：0-沉默，1-开口，2-互动")
    private Integer activeState;

    @Schema(description = "用户首次聊天（用户开口）时间")
    private LocalDateTime userFirstChatTime;

    @Schema(description = "用户最后聊天时间")
    private LocalDateTime userLastChatTime;

    @Schema(description = "最后聊天时间")
    private LocalDateTime lastChatTime;

    @Schema(description = "广告回传时间")
    private LocalDateTime adFeedbackTime;

    @Schema(description = "ai聊天：0-关闭，1-开启")
    private Integer aiChat;

    @Schema(description = "是否有人工回复：0-否，1-是")
    private Integer hasHumanReply;

    @Schema(description = "消息最后读取时间")
    private LocalDateTime msgLastReadTime;

    @Schema(description = "业务扩展")
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<UserBizExtendResult> bizExtend;

}
