package com.swhd.ai.kefu.service.push.mq.consumer;

import com.swhd.ai.kefu.api.common.constant.AiEmployeeType;
import com.swhd.ai.kefu.api.push.dto.param.record.PushRecordAddParam;
import com.swhd.ai.kefu.api.push.dto.result.PushCondition;
import com.swhd.ai.kefu.api.user.dto.message.UserInfoSaveMessage;
import com.swhd.ai.kefu.api.user.dto.message.UserTalkFlowEndMessage;
import com.swhd.ai.kefu.service.employee.entity.AiEmployee;
import com.swhd.ai.kefu.service.employee.service.AiEmployeePlatformService;
import com.swhd.ai.kefu.service.platform.entity.PlatformInfo;
import com.swhd.ai.kefu.service.platform.service.PlatformInfoService;
import com.swhd.ai.kefu.service.push.entity.PushRecord;
import com.swhd.ai.kefu.service.push.entity.PushScript;
import com.swhd.ai.kefu.service.push.service.PushPlatformService;
import com.swhd.ai.kefu.service.push.service.PushRecordService;
import com.swhd.ai.kefu.service.push.service.PushScriptService;
import com.swhd.ai.kefu.service.user.entity.UserAttr;
import com.swhd.ai.kefu.service.user.entity.UserInfo;
import com.swhd.ai.kefu.service.user.entity.UserTag;
import com.swhd.ai.kefu.service.user.service.UserAttrService;
import com.swhd.ai.kefu.service.user.service.UserInfoService;
import com.swhd.ai.kefu.service.user.service.UserTagService;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Slf4j
@Component
@AllArgsConstructor
public class PushUserConsumer {

    private final UserInfoService userInfoService;

    private final UserAttrService userAttrService;

    private final UserTagService userTagService;

    private final PushScriptService pushScriptService;

    private final PushRecordService pushRecordService;
    private final AiEmployeePlatformService aiEmployeePlatformService;
    private final PlatformInfoService platformInfoService;

    /**
     * 消费用户信息保存消息
     */
    @Bean
    public Consumer<UserInfoSaveMessage> aiKefuPushUserInfoSave() {
        return message -> {
            log.info("消费用户信息变更推送消息：{}", JsonLogUtil.toJsonString(message));
            UserInfo userInfo = TenantHolder.methodIgnoreTenant(() -> userInfoService.getById(message.getUserId()));
            if (userInfo == null) {
                log.error("用户id[{}]信息不存在", message.getUserId());
                return;
            }
            TenantHolder.methodTenantVoid(userInfo.getTenantId(), () -> {
                // 获取用户属性
                List<UserAttr> userAttrList = userAttrService.listByUserId(userInfo.getId());
                Set<String> userAttrCodeList = userAttrList.stream().filter(userAttr ->
                        Func.isNotBlank(userAttr.getValue())).map(UserAttr::getCode).collect(Collectors.toSet());


                // 获取线索标签
                List<UserTag> userTagList = userTagService.listByUserId(userInfo.getId());
                Set<Long> userTagCodeList = userTagList.stream().map(UserTag::getTagId).filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                // 获取推送脚本
                List<PushScript> pushScriptList = this.getPushScript(userInfo);
                if (Func.isEmpty(pushScriptList)) {
                    return;
                }

                for (PushScript pushScript : pushScriptList) {
                    if (!meetMustPushCondition(pushScript.getMustPushCondition(), userAttrCodeList, userTagCodeList)) {
                        // 不满足必须的推送条件
                        continue;
                    }

                    PushRecord pushRecord = pushRecordService.getBy(userInfo.getId(), pushScript.getId());
                    LocalDateTime now = LocalDateTime.now();

                    // 是否满足额外的推送条件
                    boolean meetExtraPushCondition = meetExtraPushCondition(pushScript.getExtraPushCondition(), userAttrCodeList, userTagCodeList);
                    if (pushRecord == null) {
                        // 新增推送
                        pushRecordService.add(getPushRecordAddParam(pushScript, userInfo, meetExtraPushCondition));
                        continue;
                    }
                    // 修改推送等待时间
                    Integer delayedMinutes = meetExtraPushCondition ? pushScript.getDelayedMinutes() : pushScript.getUnsatisfiedDelayedMinutes();
                    LocalDateTime pushWaitTime = now.plusMinutes(Math.max(0, delayedMinutes));
                    if (pushRecord.getPushWaitTime().isBefore(pushWaitTime)) {
                        continue;
                    }
                    PushRecord update = new PushRecord();
                    update.setId(pushRecord.getId());
                    update.setPushWaitTime(pushWaitTime);
                    pushRecordService.updateById(update);
                }

            });
        };
    }

    private PushRecordAddParam getPushRecordAddParam(PushScript pushScript, UserInfo userInfo, boolean meetExtraPushCondition) {
        PushRecordAddParam addParam = new PushRecordAddParam();
        addParam.setUserId(userInfo.getId());
        addParam.setRobotPlatform(userInfo.getRobotPlatform());
        addParam.setOauthOpenId(userInfo.getOauthOpenId());
        addParam.setUserOpenId(userInfo.getUserOpenId());
        addParam.setUserNickname(userInfo.getUserNickname());
        addParam.setScriptId(pushScript.getId());
        Integer delayedMinutes = meetExtraPushCondition ? pushScript.getDelayedMinutes() : pushScript.getUnsatisfiedDelayedMinutes();
        addParam.setPushWaitTime(LocalDateTime.now().plusMinutes(Math.max(0, delayedMinutes)));
        return addParam;
    }

    /**
     * 消费用户对话流结束消息
     */
    @Bean
    public Consumer<UserTalkFlowEndMessage> aiKefuUserTalkFlowEndPush() {
        return message -> {
            log.info("消费用户对话流结束推送消息：{}", JsonLogUtil.toJsonString(message));
        };
    }

    /**
     * 获取推送脚本
     */
    private List<PushScript> getPushScript(UserInfo userInfo) {
        PlatformInfo platformInfo = platformInfoService.getByRobotPlatformAndOauthOpenId(userInfo.getRobotPlatform(), userInfo.getOauthOpenId());
        if (Objects.isNull(platformInfo)) {
            return null;
        }

        List<AiEmployee> aiEmployeeList = aiEmployeePlatformService.listWorkingByPlatformId(platformInfo.getId(), AiEmployeeType.LEAD_DISTRIBUTION);
        Set<Long> scriptIdList = aiEmployeeList.stream()
                .map(AiEmployee::getPushScriptId)
                .filter(pushScriptId -> !Objects.equals(pushScriptId, 0))
                .collect(Collectors.toSet());
        if (Func.isEmpty(scriptIdList)) {
            return null;
        }

        return pushScriptService.listByIds(scriptIdList);
    }

    /**
     * 是否满足必须的推送条件
     */
    private boolean meetMustPushCondition(List<PushCondition> mustPushCondition, Set<String> userAttrCodeList, Set<Long> userTagCodeList) {
        List<PushCondition> conditionList = Optional.ofNullable(mustPushCondition).orElse(Collections.emptyList()).stream().filter(Objects::nonNull).toList();
        if (Func.isEmpty(conditionList)) {
            return false;
        }
        // 判断条件是否满足
        return judgeCondition(userAttrCodeList, userTagCodeList, conditionList);
    }

    /**
     * 是否满足额外的推送条件
     */
    private boolean meetExtraPushCondition(List<PushCondition> extraPushCondition, Set<String> userAttrCodeList, Set<Long> userTagCodeList) {
        List<PushCondition> conditionList = Optional.ofNullable(extraPushCondition).orElse(Collections.emptyList())
                .stream().filter(condition -> condition != null && Func.isNotEmpty(condition.getUserAttrCodes())).toList();
        if (Func.isEmpty(conditionList)) {
            return true;
        }
        // 判断条件是否满足
        return judgeCondition(userAttrCodeList, userTagCodeList, conditionList);
    }

    /**
     * 判断条件是否满足
     * 列表进行and操作，列表元素进行or操作
     *
     * @return
     */
    private static boolean judgeCondition(Set<String> userAttrCodeList, Set<Long> userTagCodeList, List<PushCondition> conditionList) {
        return conditionList.stream().allMatch(condition -> {
            if (Func.isNotEmpty(condition.getUserAttrCodes())) {
                return condition.getUserAttrCodes().stream().anyMatch(userAttrCodeList::contains);
            }
            if (Func.isNotEmpty(condition.getUserAttrCodesExclude())) {
                return !userAttrCodeList.containsAll(condition.getUserAttrCodesExclude());
            }
            if (Func.isNotEmpty(condition.getUserTagCode())) {
                return condition.getUserTagCode().stream().anyMatch(userTagCodeList::contains);
            }
            if (Func.isNotEmpty(condition.getUserTagCodeExclude())) {
                return !userTagCodeList.containsAll(condition.getUserTagCodeExclude());
            }
            return false;
        });
    }
}
