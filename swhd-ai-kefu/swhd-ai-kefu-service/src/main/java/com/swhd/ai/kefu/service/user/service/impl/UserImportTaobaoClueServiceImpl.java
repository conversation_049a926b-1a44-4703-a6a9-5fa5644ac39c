package com.swhd.ai.kefu.service.user.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.common.constant.RobotPlatform;
import com.swhd.ai.kefu.api.user.dto.param.attr.UserAttrBatchSaveItem;
import com.swhd.ai.kefu.api.user.dto.param.importtaobao.clue.UserImportTaobaoCluePageParam;
import com.swhd.ai.kefu.api.user.dto.param.info.UserInfoImportParam;
import com.swhd.ai.kefu.service.user.entity.UserImportTaobaoClue;
import com.swhd.ai.kefu.service.user.entity.UserImportTaobaoPlan;
import com.swhd.ai.kefu.service.user.mapper.UserImportTaobaoClueMapper;
import com.swhd.ai.kefu.service.user.service.UserImportTaobaoClueService;
import com.swhd.ai.kefu.service.user.service.UserImportTaobaoPlanService;
import com.swhd.ai.kefu.service.user.service.UserInfoService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Constant;
import com.taobao.api.response.AlibabaMpmwCenterClueGetResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.swhd.ai.kefu.api.user.constant.UserAttrConstant.*;

/**
 * 用户导入淘宝（居然设计家）线索表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
@AllArgsConstructor
public class UserImportTaobaoClueServiceImpl extends BaseHdServiceImpl<UserImportTaobaoClueMapper, UserImportTaobaoClue>
        implements UserImportTaobaoClueService {

    private final UserImportTaobaoPlanService userImportTaobaoPlanService;

    private final UserInfoService userInfoService;

    @Override
    public IPage<UserImportTaobaoClue> page(UserImportTaobaoCluePageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getBindTenantId()), UserImportTaobaoClue::getBindTenantId, param.getBindTenantId())
                .eq(Func.isNotEmpty(param.getBindSubTenantId()), UserImportTaobaoClue::getBindSubTenantId, param.getBindSubTenantId())
                .eq(Func.isNotEmpty(param.getCustomerId()), UserImportTaobaoClue::getCustomerId, param.getCustomerId())
                .eq(Func.isNotEmpty(param.getCustomerSeqId()), UserImportTaobaoClue::getCustomerSeqId, param.getCustomerSeqId())
                .like(Func.isNotEmpty(param.getCustomerName()), UserImportTaobaoClue::getCustomerName, param.getCustomerName())
                .eq(Func.isNotEmpty(param.getMobile()), UserImportTaobaoClue::getMobile, param.getMobile())
                .betweenDateTimeList(param.getDistributeTimeBetween(), UserImportTaobaoClue::getDistributeTime)
                .like(Func.isNotEmpty(param.getProvinceName()), UserImportTaobaoClue::getProvinceName, param.getProvinceName())
                .like(Func.isNotEmpty(param.getCityName()), UserImportTaobaoClue::getCityName, param.getCityName())
                .like(Func.isNotEmpty(param.getDistrictName()), UserImportTaobaoClue::getDistrictName, param.getDistrictName())
                .eq(Func.isNotEmpty(param.getClueTag()), UserImportTaobaoClue::getClueTag, param.getClueTag())
                .like(Func.isNotEmpty(param.getLocation()), UserImportTaobaoClue::getLocation, param.getLocation())
                .like(Func.isNotEmpty(param.getBuildingName()), UserImportTaobaoClue::getBuildingName, param.getBuildingName())
                .eq(Func.isNotEmpty(param.getDemandType()), UserImportTaobaoClue::getDemandType, param.getDemandType())
                .eq(Func.isNotEmpty(param.getPropertyType()), UserImportTaobaoClue::getPropertyType, param.getPropertyType())
                .eq(Func.isNotEmpty(param.getDecoType()), UserImportTaobaoClue::getDecoType, param.getDecoType())
                .eq(Func.isNotEmpty(param.getHouseDecoStatus()), UserImportTaobaoClue::getHouseDecoStatus, param.getHouseDecoStatus())
                .eq(Func.isNotEmpty(param.getHouseDeliveryStatus()), UserImportTaobaoClue::getHouseDeliveryStatus, param.getHouseDeliveryStatus())
                .eq(Func.isNotEmpty(param.getLayoutInfo()), UserImportTaobaoClue::getLayoutInfo, param.getLayoutInfo())
                .eq(Func.isNotEmpty(param.getArea()), UserImportTaobaoClue::getArea, param.getArea())
                .like(Func.isNotEmpty(param.getStartTime()), UserImportTaobaoClue::getStartTime, param.getStartTime())
                .eq(Func.isNotEmpty(param.getMeasureFree()), UserImportTaobaoClue::getMeasureFree, param.getMeasureFree())
                .like(Func.isNotEmpty(param.getRemark()), UserImportTaobaoClue::getRemark, param.getRemark())
                .eq(Func.isNotEmpty(param.getPlanId()), UserImportTaobaoClue::getPlanId, param.getPlanId())
                .like(Func.isNotEmpty(param.getPlanName()), UserImportTaobaoClue::getPlanName, param.getPlanName())
                .orderByDesc(UserImportTaobaoClue::getCreateTime)
                .orderByDesc(UserImportTaobaoClue::getId)
                .page(convertToPage(param));
    }

    @Override
    public boolean existsByCustomerSeqId(Long customerSeqId) {
        return lambdaQuery().eq(UserImportTaobaoClue::getCustomerSeqId, customerSeqId).exists();
    }

    @Override
    @Lockable(prefixKey = "ai:kefu:user:import:taobao:clue:bindTenant", key = "#importTaobaoClue.id", tenant = false, waitTime = 6000)
    public void bindTenant(UserImportTaobaoClue importTaobaoClue, Long bindTenantId, Long bindSubTenantId) {
        UserImportTaobaoClue update = new UserImportTaobaoClue();
        update.setBindTenantId(bindTenantId);
        update.setBindSubTenantId(bindSubTenantId);
        boolean result = lambdaUpdate()
                .eq(UserImportTaobaoClue::getBindTenantId, Constant.LongNum.ZERO)
                .eq(UserImportTaobaoClue::getBindSubTenantId, Constant.LongNum.ZERO)
                .eq(UserImportTaobaoClue::getId, importTaobaoClue.getId())
                .update(update);
        if (result) {
            importTaobaoClue.setBindTenantId(bindTenantId);
            importTaobaoClue.setBindSubTenantId(bindSubTenantId);
            importUser(importTaobaoClue);
        }
    }

    @Override
    @Lockable(prefixKey = "ai:kefu:user:import:taobao:clue:sync", key = "#clueInfo.customerSeqId", tenant = false, waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public void sync(AlibabaMpmwCenterClueGetResponse.ClueInfo clueInfo, String district) {
        if (existsByCustomerSeqId(clueInfo.getCustomerSeqId())) {
            return;
        }
        UserImportTaobaoClue importTaobaoClue = toEntity(clueInfo, district);
        save(importTaobaoClue);
        importUser(importTaobaoClue);
    }

    private void importUser(UserImportTaobaoClue importTaobaoClue) {
        if (importTaobaoClue.getBindSubTenantId() == null) {
            return;
        }
        UserInfoImportParam.UserItem userItem = new UserInfoImportParam.UserItem();
        userItem.setUserOpenId("shejijia-" + importTaobaoClue.getCustomerSeqId());
        userItem.setUserNickname(importTaobaoClue.getCustomerName());
        LocalDateTime distributeTime = importTaobaoClue.getDistributeTime();
        userItem.setCreateTime(distributeTime);
        List<UserAttrBatchSaveItem> attrs = new ArrayList<>();
        userItem.setAttrs(attrs);
        attrs.add(new UserAttrBatchSaveItem(MOBILE_CODE, importTaobaoClue.getMobile(), distributeTime));
        if (Func.isNotBlank(importTaobaoClue.getCustomerName())) {
            attrs.add(new UserAttrBatchSaveItem(NAME_CODE, importTaobaoClue.getCustomerName(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getBuildingName())) {
            attrs.add(new UserAttrBatchSaveItem(COMMUNITY_CODE, importTaobaoClue.getBuildingName(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getLocation())) {
            attrs.add(new UserAttrBatchSaveItem(ADDRESS_CODE, importTaobaoClue.getLocation(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getProvinceName())) {
            attrs.add(new UserAttrBatchSaveItem(PROVINCE_CODE, importTaobaoClue.getProvinceName(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getCityName())) {
            attrs.add(new UserAttrBatchSaveItem(CITY_CODE, importTaobaoClue.getCityName(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getDistrictName())) {
            attrs.add(new UserAttrBatchSaveItem(DISTRICT_CODE, importTaobaoClue.getDistrictName(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getHouseDecoStatus())) {
            attrs.add(new UserAttrBatchSaveItem(HOUSE_STATE_CODE, importTaobaoClue.getHouseDecoStatus(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getHouseDeliveryStatus())) {
            attrs.add(new UserAttrBatchSaveItem(HOUSE_DELIVERY_STATE_CODE, importTaobaoClue.getHouseDeliveryStatus(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getArea())) {
            attrs.add(new UserAttrBatchSaveItem(SIZE_CODE, importTaobaoClue.getArea(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getLayoutInfo())) {
            attrs.add(new UserAttrBatchSaveItem(HOUSE_SPACE_TYPE_CODE, importTaobaoClue.getLayoutInfo(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getPropertyType())) {
            attrs.add(new UserAttrBatchSaveItem(HOUSE_TYPE_CODE, importTaobaoClue.getPropertyType(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getMeasureFree())) {
            attrs.add(new UserAttrBatchSaveItem(FREE_MEASURE_CODE, importTaobaoClue.getMeasureFree(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getStartTime())) {
            attrs.add(new UserAttrBatchSaveItem(DECORATION_TIME_CODE, importTaobaoClue.getStartTime(), distributeTime));
        }
        if (Func.isNotBlank(importTaobaoClue.getRemark())) {
            attrs.add(new UserAttrBatchSaveItem(REMARK_CODE, importTaobaoClue.getRemark(), distributeTime));
        }

        RobotPlatform robotPlatform = RobotPlatform.BUYING_CUSTOMER_CAPITAL;
        TenantHolder.methodTenant(importTaobaoClue.getBindSubTenantId(), () -> userInfoService.importUser(
                robotPlatform.getType(), robotPlatform.getDefaultOauthOpenId(), robotPlatform.getName(), userItem))
        ;
    }

    private UserImportTaobaoClue toEntity(AlibabaMpmwCenterClueGetResponse.ClueInfo clueInfo, String district) {
        UserImportTaobaoClue importTaobaoClue = new UserImportTaobaoClue();

        UserImportTaobaoPlan taobaoPlan = TenantHolder.methodIgnoreTenant(() ->
                userImportTaobaoPlanService.getByPlanId(clueInfo.getPlanId()));
        if (taobaoPlan != null) {
            importTaobaoClue.setBindTenantId(taobaoPlan.getTenantId());
            importTaobaoClue.setBindSubTenantId(taobaoPlan.getSubTenantId());
        }

        importTaobaoClue.setCustomerId(clueInfo.getCustomerId());
        importTaobaoClue.setCustomerSeqId(clueInfo.getCustomerSeqId());
        importTaobaoClue.setCustomerName(clueInfo.getCustomerName());
        importTaobaoClue.setMobile(clueInfo.getMobile());
        if (clueInfo.getDistributeTime() != null) {
            importTaobaoClue.setDistributeTime(LocalDateTimeUtil.of(clueInfo.getDistributeTime()));
        } else {
            importTaobaoClue.setDistributeTime(LocalDateTime.now());
        }
        importTaobaoClue.setProvinceName(clueInfo.getProvinceName());
        importTaobaoClue.setCityName(clueInfo.getCityName());
        importTaobaoClue.setDistrictName(district);
        importTaobaoClue.setClueTag(clueInfo.getClueTag());
        importTaobaoClue.setLocation(clueInfo.getLocation());
        importTaobaoClue.setBuildingName(clueInfo.getBuildingName());
        importTaobaoClue.setDemandType(clueInfo.getDemandType());
        importTaobaoClue.setPropertyType(clueInfo.getPropertyType());
        importTaobaoClue.setDecoType(clueInfo.getDecoType());
        importTaobaoClue.setHouseDecoStatus(clueInfo.getHouseDecoStatus());
        importTaobaoClue.setHouseDeliveryStatus(clueInfo.getHouseDeliveryStatus());
        importTaobaoClue.setLayoutInfo(clueInfo.getLayoutInfo());
        importTaobaoClue.setArea(clueInfo.getArea());
        importTaobaoClue.setStartTime(clueInfo.getStartTime());
        importTaobaoClue.setMeasureFree(clueInfo.getMeasureFree() != null && clueInfo.getMeasureFree() ? "是" : "否");
        importTaobaoClue.setRemark(clueInfo.getRemark());
        importTaobaoClue.setPlanId(clueInfo.getPlanId());
        importTaobaoClue.setPlanName(clueInfo.getPlanName());
        return importTaobaoClue;
    }

}
