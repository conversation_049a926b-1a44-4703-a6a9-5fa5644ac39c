package com.swhd.script.service.utils;

import cn.hutool.crypto.GlobalBouncyCastleProvider;
import cn.hutool.crypto.symmetric.AES;
import com.swhd.magiccube.tool.*;
import com.swj.magiccube.tool.crypto.rsa.RsaUtil;
import com.swj.magiccube.tool.uuid.UUIDUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Value;
import org.graalvm.polyglot.proxy.ProxyExecutable;
import org.springframework.aot.AotDetector;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.swhd.script.api.constant.ScriptLanguage.JS_LANGUAGE;

/**
 * <AUTHOR>
 * @since 2024/7/3
 */
@Slf4j
@UtilityClass
public class JavaMethodProxyUtil {

    private static final String AES_ALGORITHM = "AES";

    private final CloseableHttpClient httpClient;

    static {
        // 先设置http连接的一些配置
        RequestConfig requestConfig = RequestConfig.custom()
                // 从连接池获取连接的超时时间
                .setConnectionRequestTimeout(3000)
                // 建立连接的超时时间
                .setConnectTimeout(3000)
                // 请求的超时时间
                .setSocketTimeout(15000)
                .build();

        // 有的地方会配置一堆的https ssl的策略，点进这个构造函数他默认已经配置了，所以不用再配；
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        // 配置最大的连接数
        manager.setMaxTotal(200);
        // 每个路由最大连接数，路由是根据host来管理的，所以这里的数量不太容易把握；
        manager.setDefaultMaxPerRoute(50);
        httpClient = HttpClients.custom()
                .setConnectionManager(manager)
                .setConnectionManagerShared(true)
                .setDefaultRequestConfig(requestConfig)
                .build();

        if (AotDetector.useGeneratedArtifacts()) {
            // aot运行报错：Trying to verify a provider that was not registered at build time: BC version 1.77. All providers must be registered and verified in the Native Image builder.
            GlobalBouncyCastleProvider.setUseBouncyCastle(false);
        }
    }

    public void javaMethodProxyExecutable(Context context) {
        // 基础方法
        context.getBindings(JS_LANGUAGE).putMember("uuid", (ProxyExecutable) arguments -> UUIDUtil.uuid32Bit());
        context.getBindings(JS_LANGUAGE).putMember("randomString", (ProxyExecutable) arguments ->
                StringUtil.random(arguments[0].asInt()));
        // 日志
        Value logValue = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("log", logValue);
        logValue.putMember("info", nullProxyExecutable(arguments -> log.info(arguments[0].asString(), logArguments(arguments))));
        logValue.putMember("warn", nullProxyExecutable(arguments -> log.warn(arguments[0].asString(), logArguments(arguments))));
        logValue.putMember("error", nullProxyExecutable(arguments -> log.error(arguments[0].asString(), logArguments(arguments))));
        // Base64Util
        Value base64Util = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("Base64Util", base64Util);
        base64Util.putMember("encode", (ProxyExecutable) arguments -> Base64Util.encode(arguments[0].asString()));
        base64Util.putMember("encodeUrlSafe", (ProxyExecutable) arguments -> Base64Util.encodeUrlSafe(arguments[0].asString()));
        base64Util.putMember("decode", (ProxyExecutable) arguments -> Base64Util.decode(arguments[0].asString()));
        base64Util.putMember("decodeUrlSafe", (ProxyExecutable) arguments -> Base64Util.decodeUrlSafe(arguments[0].asString()));
        // DigestUtil
        Value digestUtil = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("DigestUtil", digestUtil);
        digestUtil.putMember("encode", (ProxyExecutable) arguments -> DigestUtil.md5Hex(arguments[0].asString()));
        digestUtil.putMember("sha1Hex", (ProxyExecutable) arguments -> DigestUtil.sha1Hex(arguments[0].asString()));
        digestUtil.putMember("sha224Hex", (ProxyExecutable) arguments -> DigestUtil.sha224Hex(arguments[0].asString()));
        digestUtil.putMember("sha256Hex", (ProxyExecutable) arguments -> DigestUtil.sha256Hex(arguments[0].asString()));
        digestUtil.putMember("sha384Hex", (ProxyExecutable) arguments -> DigestUtil.sha384Hex(arguments[0].asString()));
        digestUtil.putMember("sha512Hex", (ProxyExecutable) arguments -> DigestUtil.sha512Hex(arguments[0].asString()));
        digestUtil.putMember("hmacMd5Hex", (ProxyExecutable) arguments ->
                DigestUtil.hmacMd5Hex(arguments[0].asString(), arguments[1].asString()));
        digestUtil.putMember("hmacSha1Hex", (ProxyExecutable) arguments ->
                DigestUtil.hmacSha1Hex(arguments[0].asString(), arguments[1].asString()));
        digestUtil.putMember("hmacSha256Hex", (ProxyExecutable) arguments ->
                DigestUtil.hmacSha256Hex(arguments[0].asString(), arguments[1].asString()));
        digestUtil.putMember("hmacSha512Hex", (ProxyExecutable) arguments ->
                DigestUtil.hmacSha512Hex(arguments[0].asString(), arguments[1].asString()));
        // MaskUtil
        Value maskUtil = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("MaskUtil", maskUtil);
        maskUtil.putMember("passwordMask", (ProxyExecutable) arguments -> MaskUtil.passwordMask());
        maskUtil.putMember("mobileMask", (ProxyExecutable) arguments -> MaskUtil.mobileMask(arguments[0].asString()));
        maskUtil.putMember("idCardMask", (ProxyExecutable) arguments -> MaskUtil.idCardMask(arguments[0].asString()));
        maskUtil.putMember("bankCardMask", (ProxyExecutable) arguments -> MaskUtil.bankCardMask(arguments[0].asString()));
        maskUtil.putMember("nameMask", (ProxyExecutable) arguments -> MaskUtil.nameMask(arguments[0].asString()));
        maskUtil.putMember("emailMask", (ProxyExecutable) arguments -> MaskUtil.emailMask(arguments[0].asString()));
        maskUtil.putMember("centerMask", (ProxyExecutable) arguments -> MaskUtil.centerMask(arguments[0].asString()));
        maskUtil.putMember("leftMask", (ProxyExecutable) arguments -> MaskUtil.leftMask(arguments[0].asString()));
        maskUtil.putMember("rightMask", (ProxyExecutable) arguments -> MaskUtil.rightMask(arguments[0].asString()));
        maskUtil.putMember("mask", (ProxyExecutable) arguments ->
                MaskUtil.mask(arguments[0].asString(), arguments[1].asInt(), arguments[2].asInt()));
        // aes
        Value aesUtil = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("AesUtil", aesUtil);
        aesUtil.putMember("encrypt", (ProxyExecutable) arguments -> {
            String ivKey = arguments.length > 4 ? arguments[4].asString() : null;
            AES aes = aes(arguments[1].asString(), arguments[2].asString(), arguments[3].asString(), ivKey);
            return aes.encryptBase64(arguments[0].asString(), StandardCharsets.UTF_8);
        });
        aesUtil.putMember("decrypt", (ProxyExecutable) arguments -> {
            String ivKey = arguments.length > 4 ? arguments[4].asString() : null;
            AES aes = aes(arguments[1].asString(), arguments[2].asString(), arguments[3].asString(), ivKey);
            return new String(aes.decrypt(arguments[0].asString()), StandardCharsets.UTF_8);
        });
        // rsa
        Value rsaUtil = createJSObject(context);
        context.getBindings(JS_LANGUAGE).putMember("RsaUtil", rsaUtil);
        rsaUtil.putMember("encrypt2Base64", (ProxyExecutable) arguments ->
                RsaUtil.encrypt2Base64(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("encrypt2Hex", (ProxyExecutable) arguments ->
                RsaUtil.encrypt2Hex(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("decryptBase64", (ProxyExecutable) arguments ->
                RsaUtil.decryptBase64(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("decryptHex", (ProxyExecutable) arguments ->
                RsaUtil.decryptHex(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("sign2Base64", (ProxyExecutable) arguments ->
                RsaUtil.sign2Base64(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("sign2Hex", (ProxyExecutable) arguments ->
                RsaUtil.sign2Hex(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("verifyBase64Sign", (ProxyExecutable) arguments ->
                RsaUtil.verifyBase64Sign(arguments[0].asString(), arguments[1].asString()));
        rsaUtil.putMember("verifyHexSign", (ProxyExecutable) arguments ->
                RsaUtil.verifyHexSign(arguments[0].asString(), arguments[1].asString()));
        // http
        context.getBindings(JS_LANGUAGE).putMember("fetch", httpFetch(context));
    }

    private static Value createJSObject(Context context) {
        return context.eval(JS_LANGUAGE, "new Object()");
    }

    private ProxyExecutable nullProxyExecutable(Consumer<Value[]> consumer) {
        return arguments -> {
            consumer.accept(arguments);
            return null;
        };
    }

    private Object[] logArguments(Value... arguments) {
        if (arguments.length < 2) {
            return new Object[]{};
        }
        Object[] logArguments = new Object[arguments.length - 1];
        for (int i = 1; i < arguments.length; i++) {
            Value value = arguments[i];
            logArguments[i - 1] = value.as(Object.class);
        }
        return logArguments;
    }

    private AES aes(String secretKey, String mode, String padding, String ivKey) {
        return new AES(mode, padding,
                new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), AES_ALGORITHM),
                Func.isEmpty(ivKey) ? null : new IvParameterSpec(ivKey.getBytes(StandardCharsets.UTF_8)));
    }

    private ProxyExecutable httpFetch(Context context) {
        return arguments -> {
            try {
                String url = arguments[0].asString();
                Map<String, Object> options;
                if (arguments.length > 1 && !arguments[1].isNull()) {
                    options = arguments[1].as(Map.class);
                } else {
                    options = Collections.emptyMap();
                }
                URIBuilder uriBuilder = new URIBuilder(url, StandardCharsets.UTF_8);
                if (options.containsKey("params")) {
                    ((Map<String, String>) options.get("params")).forEach(uriBuilder::addParameter);
                }
                URI uri = uriBuilder.build();
                HttpRequestBase request;
                if ("post".equalsIgnoreCase((String) options.get("method"))) {
                    HttpPost httpPost = new HttpPost(uri);
                    request = httpPost;
                    if (options.containsKey("data")) {
                        StringEntity entity = new StringEntity((String) options.get("data"), ContentType.APPLICATION_JSON);
                        httpPost.setEntity(entity);
                    } else if (options.containsKey("formData")) {
                        List<BasicNameValuePair> parameters = ((Map<String, String>) options.get("formData")).entrySet().stream()
                                .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue()))
                                .toList();
                        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(parameters, StandardCharsets.UTF_8);
                        httpPost.setEntity(entity);
                        httpPost.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());
                    }
                } else {
                    request = new HttpGet(uri);
                }
                if (options.containsKey("headers")) {
                    ((Map<String, String>) options.get("headers")).forEach(request::setHeader);
                }
                CloseableHttpResponse response = httpClient.execute(request);
                HttpEntity httpEntity = response.getEntity();
                String responseBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
                EntityUtils.consume(httpEntity);
                int statusCode = response.getStatusLine().getStatusCode();
                Map<String, String> headers = Arrays.stream(response.getAllHeaders())
                        .collect(Collectors.groupingBy(Header::getName))
                        .entrySet().stream()
                        .collect(Collectors.toMap(e -> e.getKey().toLowerCase(), e ->
                                e.getValue().stream().map(Header::getValue).collect(Collectors.joining(","))));
                String source = String.format("""
                        new Object({
                          _data: '%s',
                          ok: %s,
                          status: %s,
                          headers: %s,
                          text: function() {
                            return this._data
                          },
                          json: function() {
                            return JSON.parse(this._data)
                          },
                        })
                        """, responseBody.replace("'", "\\'"), statusCode == 200, statusCode,
                        JsonUtil.toJsonString(headers));
                return context.eval(JS_LANGUAGE, source);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
    }

}
