package com.swhd.ai.service.common.api;

import com.swhd.ai.service.common.interceptor.OpenAiInterceptor;
import com.swhd.ai.service.common.properties.ApiModel;
import com.swhd.ai.service.service.ApiCallRecordService;
import com.swhd.magiccube.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Service
public class ChatModelApi {

    private final RestClient.Builder restClientBuilder;

    private final WebClient.Builder webClientBuilder;

    private final ResponseErrorHandler responseErrorHandler;

    private final Map<String, ChatModel> chatModelMap = new ConcurrentHashMap<>();

    public ChatModelApi(ApiCallRecordService apiCallRecordService,
                        RestClient.Builder restClientBuilder,
                        WebClient.Builder webClientBuilder,
                        ResponseErrorHandler responseErrorHandler) {
        this.restClientBuilder = restClientBuilder.requestInterceptor(new OpenAiInterceptor(apiCallRecordService));
        this.webClientBuilder = webClientBuilder;
        this.responseErrorHandler = responseErrorHandler;
    }

    public ChatModel getChatModel(ApiModel apiModel) {
        ChatModel chatModel = chatModelMap.get(apiModel.getId());
        if (chatModel != null) {
            return chatModel;
        }
        synchronized (this) {
            chatModel = chatModelMap.get(apiModel.getId());
            if (chatModel != null) {
                return chatModel;
            }
            chatModel = switch (apiModel.getModelProvider()) {
                case null -> throw new ServiceException(String.format("模型配置ID[%s]模型提供商为空", apiModel.getId()));
                case OPEN_AI -> new OpenAiChatModel(
                        new SwhdOpenAiApi(restClientBuilder, webClientBuilder, responseErrorHandler)
                );
                case COMPATIBLE_OPEN_AI -> new OpenAiChatModel(
                        new SwhdCompatibleOpenAiApi(restClientBuilder, webClientBuilder, responseErrorHandler)
                );
                case AZURE_OPEN_AI -> new OpenAiChatModel(
                        new SwhdAzureOpenAiApi(restClientBuilder, webClientBuilder, responseErrorHandler)
                );
            };
            chatModelMap.put(apiModel.getId(), chatModel);
            return chatModel;
        }
    }

}
