package com.swhd.ai.service.common.utils;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2025/1/7
 */
@UtilityClass
public class ClassUtil {

    @SneakyThrows
    public <T, OBJ> T fieldGet(Class<OBJ> clazz, String fieldName, OBJ obj) {
        return (T) findField(clazz, fieldName).get(obj);
    }

    @SneakyThrows
    public <T> T fieldGet(String fieldName, Object obj) {
        return (T) findField(obj.getClass(), fieldName).get(obj);
    }

    @SneakyThrows
    public <OBJ> void fieldSet(Class<OBJ> clazz, String fieldName, Object fieldValue, OBJ obj) {
        findField(clazz, fieldName).set(obj, fieldValue);
    }

    @SneakyThrows
    public void fieldSet(String fieldName, Object fieldValue, Object obj) {
        findField(obj.getClass(), fieldName).set(obj, fieldValue);
    }

    @SneakyThrows
    public Field findField(Class<?> clazz, String fieldName) {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field;
    }

}
