package com.swhd.ai.service.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
@Getter
@Setter
@ConfigurationProperties(BizChatProperties.PREFIX)
@Component
public class BizChatProperties {

    public static final String PREFIX = "ai.biz-chat";

    /**
     * 超时时间
     */
    private Duration responseTimeout = Duration.ofSeconds(120);

    /**
     * 流式输出超时时间
     */
    private Duration streamTimeoutDuration = Duration.ofMinutes(5);

}
