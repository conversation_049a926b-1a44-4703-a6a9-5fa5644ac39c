package com.swhd.ai.service.common.api;

import com.swhd.ai.service.common.interceptor.OpenAiInterceptor;
import com.swhd.ai.service.common.utils.AiCallHolder;
import com.swhd.ai.service.service.ApiCallRecordService;
import com.swhd.magiccube.tool.Func;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiStreamFunctionCallingHelper;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Slf4j
public class SwhdOpenAiApi extends OpenAiApi {

    private static final Predicate<String> SSE_DONE_PREDICATE = "[DONE]"::equals;

    private final RestClient restClient;

    private final WebClient webClient;

    private final OpenAiStreamFunctionCallingHelper chunkMerger;

    @SneakyThrows
    public SwhdOpenAiApi(RestClient.Builder restClientBuilder,
                         WebClient.Builder webClientBuilder,
                         ResponseErrorHandler responseErrorHandler) {
        super(null, "", restClientBuilder, webClientBuilder, responseErrorHandler);

        Field restClientField = OpenAiApi.class.getDeclaredField("restClient");
        restClientField.setAccessible(true);
        this.restClient = (RestClient) restClientField.get(this);
        Field webClientField = OpenAiApi.class.getDeclaredField("webClient");
        webClientField.setAccessible(true);
        this.webClient = (WebClient) webClientField.get(this);
        Field chunkMergerField = OpenAiApi.class.getDeclaredField("chunkMerger");
        chunkMergerField.setAccessible(true);
        this.chunkMerger = (OpenAiStreamFunctionCallingHelper) chunkMergerField.get(this);
    }

    @Override
    public ResponseEntity<ChatCompletion> chatCompletionEntity(ChatCompletionRequest chatRequest,
                                                               MultiValueMap<String, String> additionalHttpHeader) {
        Assert.notNull(chatRequest, "The request body can not be null.");
        Assert.isTrue(!chatRequest.stream(), "Request must set the stream property to false.");
        Assert.notNull(additionalHttpHeader, "The additional HTTP headers can not be null.");
        return this.restClient.post()
                .uri(getUri(chatCompletionUri(chatRequest)))
                .headers(headers -> {
                    headers.addAll(additionalHttpHeader);
                    headersConsumer(headers);
                })
                .body(chatRequest)
                .retrieve()
                .toEntity(ChatCompletion.class);
    }

    @Override
    public Flux<ChatCompletionChunk> chatCompletionStream(ChatCompletionRequest chatRequest,
                                                          MultiValueMap<String, String> additionalHttpHeader) {
        Assert.notNull(chatRequest, "The request body can not be null.");
        Assert.isTrue(chatRequest.stream(), "Request must set the stream property to true.");

        AtomicBoolean isInsideTool = new AtomicBoolean(false);

        return this.webClient.post()
                .uri(getUri(chatCompletionUri(chatRequest)))
                .headers(headers -> {
                    headers.addAll(additionalHttpHeader);
                    headersConsumer(headers);
                })
                .body(Mono.just(chatRequest), ChatCompletionRequest.class)
                .retrieve()
                .bodyToFlux(String.class)
                // cancels the flux stream after the "[DONE]" is received.
                .takeUntil(SSE_DONE_PREDICATE)
                // filters out the "[DONE]" message.
                .filter(SSE_DONE_PREDICATE.negate())
                .map(content -> ModelOptionsUtils.jsonToObject(content, ChatCompletionChunk.class))
                // Detect is the chunk is part of a streaming function call.
                .map(chunk -> {
                    if (this.chunkMerger.isStreamingToolFunctionCall(chunk)) {
                        isInsideTool.set(true);
                    }
                    return chunk;
                })
                // Group all chunks belonging to the same function call.
                // Flux<ChatCompletionChunk> -> Flux<Flux<ChatCompletionChunk>>
                .windowUntil(chunk -> {
                    if (isInsideTool.get() && this.chunkMerger.isStreamingToolFunctionCallFinish(chunk)) {
                        isInsideTool.set(false);
                        return true;
                    }
                    return !isInsideTool.get();
                })
                // Merging the window chunks into a single chunk.
                // Reduce the inner Flux<ChatCompletionChunk> window into a single
                // Mono<ChatCompletionChunk>,
                // Flux<Flux<ChatCompletionChunk>> -> Flux<Mono<ChatCompletionChunk>>
                .concatMapIterable(window -> {
                    Mono<ChatCompletionChunk> monoChunk = window.reduce(
                            new ChatCompletionChunk(null, null, null, null, null, null, null, null),
                            (previous, current) -> this.chunkMerger.merge(previous, current));
                    return List.of(monoChunk);
                })
                // Flux<Mono<ChatCompletionChunk>> -> Flux<ChatCompletionChunk>
                .flatMap(mono -> mono);
    }

    protected String chatCompletionUri(ChatCompletionRequest chatRequest) {
        return "/chat/completions";
    }

    public <T> ResponseEntity<EmbeddingList<Embedding>> embeddings(EmbeddingRequest<T> embeddingRequest) {

        Assert.notNull(embeddingRequest, "The request body can not be null.");

        // Input text to embed, encoded as a string or array of tokens. To embed multiple
        // inputs in a single
        // request, pass an array of strings or array of token arrays.
        Assert.notNull(embeddingRequest.input(), "The input can not be null.");
        Assert.isTrue(embeddingRequest.input() instanceof String || embeddingRequest.input() instanceof List,
                "The input must be either a String, or a List of Strings or List of List of integers.");

        // The input must not exceed the max input tokens for the model (8192 tokens for
        // text-embedding-ada-002), cannot
        // be an empty string, and any array must be 2048 dimensions or less.
        if (embeddingRequest.input() instanceof List list) {
            Assert.isTrue(!CollectionUtils.isEmpty(list), "The input list can not be empty.");
            Assert.isTrue(list.size() <= 2048, "The list must be 2048 dimensions or less");
            Assert.isTrue(
                    list.get(0) instanceof String || list.get(0) instanceof Integer || list.get(0) instanceof List,
                    "The input must be either a String, or a List of Strings or list of list of integers.");
        }

        return this.restClient.post()
                .uri(getUri(embeddingsUri(embeddingRequest)))
                .headers(this::headersConsumer)
                .body(embeddingRequest)
                .retrieve()
                .toEntity(new ParameterizedTypeReference<>() {
                });
    }

    protected String embeddingsUri(EmbeddingRequest<?> embeddingRequest) {
        return "/embeddings";
    }

    protected void headersConsumer(HttpHeaders headers) {
        if (!headers.containsKey(AUTHORIZATION)) {
            headers.setBearerAuth(AiCallHolder.getCache().getApiKey());
        }
    }

    private String getUri(String path) {
        String baseUrl = AiCallHolder.getCache().getApiModel().getBaseUrl();
        if (Func.isEmpty(baseUrl)) {
            return baseUrl;
        }
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl + path;
    }

}
