package com.swhd.ai.service.service.impl;

import com.swhd.ai.api.constant.ApiConstant;
import com.swhd.ai.api.dto.param.bizchat.AiBizChatBatchCreateChatIdParam;
import com.swhd.ai.api.dto.param.bizchat.AiBizChatCreateChatIdParam;
import com.swhd.ai.api.dto.result.AiBizChatCreateChatIdResult;
import com.swhd.ai.service.common.utils.AiCallHolder;
import com.swhd.ai.service.properties.BizChatProperties;
import com.swhd.ai.service.service.AiBizChatService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.exception.ServiceException;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.ParameterUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.uuid.UUIDUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/1/14
 */
@Slf4j
@Service
@AllArgsConstructor
public class AiBizChatServiceImpl implements AiBizChatService {

    private final BizChatProperties bizChatProperties;

    @Override
    public Rsp<AiBizChatCreateChatIdResult> createStreamChatId(AiBizChatCreateChatIdParam param) {
        String id = createCache(param);
        return RspHd.data(new AiBizChatCreateChatIdResult(id));
    }

    @Override
    public Rsp<List<AiBizChatCreateChatIdResult>> batchCreateStreamChatId(AiBizChatBatchCreateChatIdParam param) {
        List<AiBizChatCreateChatIdResult> list = new ArrayList<>();
        for (int i = 0; i < param.getBatchSize(); i++) {
            list.add(new AiBizChatCreateChatIdResult(createCache(param)));
        }
        return RspHd.data(list);
    }

    @Override
    public SseEmitter streamChat(String id) {
        AiBizChatCreateChatIdParam chatParam = RedisUtil.get(getRedisKey(id), AiBizChatCreateChatIdParam.class);
        if (chatParam == null) {
            throw new ServiceException("无效的chat id");
        }
        SseEmitter emitter = new SseEmitter(bizChatProperties.getStreamTimeoutDuration().toMillis());
        AiCallHolder.callStream(chatParam.getBusinessType(), config -> {
            List<Message> messages = new ArrayList<>();
            String systemMessage = formatParameter(config.getPromptTemplate(), chatParam.getPromptParams());
            if (Func.isNotEmpty(systemMessage)) {
                messages.add(new SystemMessage(systemMessage));
            }
            String userMessage = formatParameter(config.getUserTemplate(), chatParam.getUserParams());
            if (Func.isNotEmpty(userMessage)) {
                messages.add(new UserMessage(userMessage));
            }
            return messages;
        }, emitter);
        return emitter;
    }

    private String formatParameter(String text, Map<String, String> params) {
        if (Func.isEmpty(text) || Func.isEmpty(params)) {
            return text;
        }
        return ParameterUtil.format(text, params);
    }

    private String createCache(AiBizChatCreateChatIdParam param) {
        String id = "hyl_" + UUIDUtil.uuid();
        RedisUtil.set(getRedisKey(id), param, Duration.ofMinutes(5));
        return id;
    }

    private String getRedisKey(String id) {
        return String.format("%s:ai:biz:chat:%s", ApiConstant.APP_NAME, id);
    }

}
