package com.swhd.ai.service.service;

import com.swhd.ai.api.dto.param.riskcontrol.AiRiskControlAddSegmentsParam;
import com.swhd.ai.api.dto.param.riskcontrol.AiRiskControlDeleteSegmentsParam;
import com.swhd.ai.api.dto.param.riskcontrol.AiRiskControlGetLevelCategoryParam;
import com.swhd.ai.api.dto.param.riskcontrol.AiRiskControlUpdateSegmentsParam;
import com.swhd.ai.api.dto.result.AiRiskControlGetLevelCategoryResult;
import com.swhd.ai.api.dto.result.AiRiskControlSearchSegmentsResult;

import java.util.List;

public interface AiRiskControlService {

    Boolean addSegments(AiRiskControlAddSegmentsParam param);

    Boolean updateSegments(AiRiskControlUpdateSegmentsParam param);

    Boolean deleteSegments(AiRiskControlDeleteSegmentsParam param);

    List<AiRiskControlSearchSegmentsResult> searchSegments();

    AiRiskControlGetLevelCategoryResult getLevelAndCatergory(AiRiskControlGetLevelCategoryParam param);
}
