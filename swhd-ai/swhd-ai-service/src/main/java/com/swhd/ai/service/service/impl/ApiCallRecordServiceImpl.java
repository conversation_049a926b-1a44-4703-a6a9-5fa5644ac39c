package com.swhd.ai.service.service.impl;

import com.swhd.ai.service.entity.ApiCallRecord;
import com.swhd.ai.service.mapper.ApiCallRecordMapper;
import com.swhd.ai.service.service.ApiCallRecordService;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * api调用记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-22
 */
@Service
@AllArgsConstructor
public class ApiCallRecordServiceImpl extends BaseHdServiceImpl<ApiCallRecordMapper, ApiCallRecord> implements ApiCallRecordService {

    @Override
    @Async
    public void asyncSave(ApiCallRecord record) {
        this.save(record);
    }

}
