package com.swhd.ai.service.common.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/21
 */
@Getter
@Setter
@ConfigurationProperties(ApiProperties.PREFIX)
@Component
public class ApiProperties {

    public static final String PREFIX = "ai.api";

    /**
     * 模型列表
     */
    private List<ApiModel> modelList = new ArrayList<>();

}
