package com.swhd.ai.service.common.configure;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.swj.magiccube.aot.annotation.AotHints;
import com.swj.magiccube.tool.json.jackson.serializer.CollectionSerializer;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@AotHints(
        reflectionClass = {
                OpenAiApi.class,
        }
)
@Configuration(proxyBeanMethods = false)
public class SwhdCommonAiConfiguration implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SimpleModule collectionSerializeModule = new SimpleModule()
                .addSerializer(Collection.class, new CollectionSerializer());
        ModelOptionsUtils.OBJECT_MAPPER
                .registerModule(collectionSerializeModule);
    }

}
