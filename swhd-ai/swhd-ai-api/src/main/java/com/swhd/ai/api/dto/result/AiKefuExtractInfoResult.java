package com.swhd.ai.api.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
@Setter
@Schema(description = "聊天记录提取对应的留资信息结果")
@Accessors(chain = true)
public class AiKefuExtractInfoResult {

    @Schema(description = "通过json格式返回提取的信息",
            example = "{\"userName\": \"X先生\", \"phone\": \"13600000000\", \"location\": \"广州\", \"buildingName\": \"保利罗兰\", \"areaSize\": \"90平米\", \"budget\": \"10万\", \"customization\": null, \"style\": null}")
    private Map<String, String> infos;

}
