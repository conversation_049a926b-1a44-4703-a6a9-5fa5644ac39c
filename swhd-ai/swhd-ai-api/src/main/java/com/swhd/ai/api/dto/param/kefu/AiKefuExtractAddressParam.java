package com.swhd.ai.api.dto.param.kefu;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
@Setter
@Schema(description = "提取地址省市区参数")
@Accessors(chain = true)
public class AiKefuExtractAddressParam {

    @Schema(description = "已收集到的省")
    private String province;

    @Schema(description = "已收集到的市")
    private String city;

    @Schema(description = "已收集到的区")
    private String district;

    @NotEmpty(message = "地址不能为空")
    @Schema(description = "地址")
    private String address;

}
