package com.swhd.ai.api.dto.param.kefu;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
@Setter
@Schema(description = "聊天记录提取留资标签参数")
@Accessors(chain = true)
public class AiKefuExtractTagParam {

    @NotEmpty(message = "聊天记录不能为空")
    @Schema(description = "聊天记录。格式：客服：question\n 用户：answer\n 客服：question1...")
    private String chatHistory;

    @NotEmpty(message = "需要提取的标签不能为空")
    @Schema(description = "需要提取的标签。中间以逗号隔开。例如：人工,售后,已订,回传无效,已给未分,小面积,自建房,待跟进,精装")
    private String tags;

}
