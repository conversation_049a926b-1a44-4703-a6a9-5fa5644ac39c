package com.swhd.ai.api.dto.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/20
 */
@Getter
@Setter
@Schema(description = "聊天记录提取留资标签结果")
@Accessors(chain = true)
public class AiKefuExtractTagResult {

    @Schema(description = "提取的标签，key是请求体中传入的tag，value是true/false。",
            example = "{\"客资\":false,\"不接入机器人\":false,\"不接入客服\":false,\"已分配\":false,\"已留资\":false,\"无需求\":false,\"同行\":false,\"自己人\":false,\"外地\":false,\"测试用户\":true,\"重复咨询\":false,\"已上报\":false,\"无需求意图\":false,\"深度回传\":false,\"人工\":false,\"售后\":false,\"已订\":false,\"回传无效\":false,\"已给未分\":false,\"小面积\":false,\"自建房\":false,\"待跟进\":false,\"精装\":false}")
    private Map<String, Boolean> tags;

}
