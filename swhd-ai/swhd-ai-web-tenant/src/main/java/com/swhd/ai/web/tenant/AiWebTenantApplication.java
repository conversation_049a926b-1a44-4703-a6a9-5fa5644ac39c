package com.swhd.ai.web.tenant;

import com.swj.magiccube.MagiccubeApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@SpringBootApplication
@EnableFeignClients("com.swhd")
public class AiWebTenantApplication {

    public static void main(String[] args) {
        MagiccubeApplication.run(AiWebTenantApplication.class, args);
    }

}
