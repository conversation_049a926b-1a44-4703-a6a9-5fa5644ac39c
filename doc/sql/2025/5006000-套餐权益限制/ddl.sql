ALTER TABLE swhd.tservicemarket_user_rights
    ADD user_id BIGINT DEFAULT 0 NOT NULL COMMENT '用户ID' AFTER shop_id;

ALTER TABLE swhd.tservicemarket_user_rights
    ADD subject_type varchar(20) DEFAULT 'TENANT' NOT NULL COMMENT '权益主体' AFTER order_type;


ALTER TABLE swhd.tservicemarket_user_rights_consume_record
    ADD team_id BIGINT DEFAULT 0 NOT NULL COMMENT '团队ID' AFTER user_id;

-- ALTER TABLE swhd.tservicemarket_user_rights_consume_record
--     ADD subject_type varchar(20) DEFAULT 'TENANT' NOT NULL COMMENT '权益主体' AFTER record_transaction_id;



ALTER TABLE swhd.tuser_tenant_team_info
    ADD end_date DATE DEFAULT '1970-01-01' NOT NULL COMMENT '到期时间' AFTER share_team_ids;

ALTER TABLE swhd.tservicemarket_product_sku_distribution_strategy
    ADD COLUMN account_allocation_limit INT NOT NULL DEFAULT 0 COMMENT '每次可分配账号数量限制' AFTER description;


ALTER TABLE swhd.tservicemarket_user_rights_distribution_strategy
    ADD COLUMN account_allocation_limit INT NOT NULL DEFAULT 0 COMMENT '每次可分配账号数量限制' AFTER description;

ALTER TABLE swhd.tservicemarket_user_rights_distribution_strategy
    ADD COLUMN allocated_account_count INT NOT NULL DEFAULT 0 COMMENT '当前周期已分配账号数' AFTER description;



