CREATE TABLE `taikf_msg_card` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
    `name` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_createtime` (`tenant_id`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='IM消息组件类型';

CREATE TABLE `taikf_msg_card_component` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',
    `card_id` bigint NOT NULL DEFAULT '0' COMMENT '卡片ID',
    `robot_platform` tinyint NOT NULL DEFAULT '1' COMMENT '机器人平台：1-抖音私信，2-微信公众号',
    `oauth_open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权平台的openId/appId',
    `component_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组件类型',
    `component_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组件ID',
    `component_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组件名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_cardid` (`card_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='IM组件表';

CREATE TABLE `tmessage_behavior_image_original` (
    `id` bigint NOT NULL COMMENT '主键id',
    `image_url` varchar(500) NOT NULL DEFAULT '' COMMENT '图片url',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='原始图片表';

CREATE TABLE `taikf_statistics_user_daily` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `statistics_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '统计日期',
    `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户ID',
    `robot_platform` tinyint NOT NULL DEFAULT 1 COMMENT '机器人平台：1-抖音私信，2-微信公众号，3-网页',
    `oauth_open_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权平台的openId/appId',
    `reg_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '用户注册日期',
    `is_today_speak_up` tinyint(1) NOT NULL DEFAULT 0 COMMENT '当天是否开口：0-未开口，1-已开口',
    `ad_id` varchar(32) NOT NULL DEFAULT '' COMMENT '广告计划id',
    `ad_name` varchar(100) NOT NULL DEFAULT '' COMMENT '广告计划名',
    `advertiser_id` varchar(32) NOT NULL DEFAULT '' COMMENT '广告主id',
    `advertiser_name` varchar(100) NOT NULL DEFAULT '' COMMENT '广告主名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_statisticsdate_userid` (`tenant_id`, `statistics_date`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户当天统计表';

ALTER TABLE `swhd`.`taikf_statistics_agent_daily`
    ADD COLUMN `statistics_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '统计日期' AFTER `tenant_id`,
    ADD COLUMN `natural_direct_message_today_count` int NOT NULL DEFAULT 0 COMMENT '当天私信人数（自然流）' AFTER `natural_direct_message_count`,
    ADD COLUMN `natural_speak_up_add_count` int NOT NULL DEFAULT 0 COMMENT '新增开口人数（自然流）' AFTER `natural_speak_up_count`,
    ADD COLUMN `natural_speak_up_today_count` int NOT NULL DEFAULT 0 COMMENT '当天开口人数（自然流）' AFTER `natural_speak_up_add_count`,
    ADD COLUMN `natural_customer_capital_today_count` int NOT NULL COMMENT '当天留资人数（自然流）' AFTER `natural_customer_capital_count`,
    ADD COLUMN `ad_direct_message_today_count` int NOT NULL DEFAULT 0 COMMENT '当天私信人数（信息流）' AFTER `ad_direct_message_count`,
    ADD COLUMN `ad_speak_up_add_count` int NOT NULL DEFAULT 0 COMMENT '新增开口人数（信息流）' AFTER `ad_speak_up_count`,
    ADD COLUMN `ad_speak_up_today_count` int NOT NULL DEFAULT 0 COMMENT '当天开口人数（信息流）' AFTER `ad_speak_up_add_count`,
    ADD COLUMN `ad_customer_capital_today_count` int NOT NULL COMMENT '当天留资人数（信息流）' AFTER `ad_customer_capital_count`,
    ADD COLUMN `sum_direct_message_today_count` int NOT NULL DEFAULT 0 COMMENT '当天私信人数（总数据）' AFTER `sum_direct_message_count`,
    ADD COLUMN `sum_speak_up_add_count` int NOT NULL DEFAULT 0 COMMENT '新增开口人数（总数据）' AFTER `sum_speak_up_count`,
    ADD COLUMN `sum_speak_up_today_count` int NOT NULL DEFAULT 0 COMMENT '当天开口人数（总数据）' AFTER `sum_speak_up_add_count`,
    ADD COLUMN `sum_customer_capital_today_count` int NOT NULL COMMENT '当天留资人数（总数据）' AFTER `sum_customer_capital_count`;

ALTER TABLE `swhd`.`taikf_statistics_agent_daily`
    ADD INDEX `idx_tenantid_statisticsdate_robotplatform_oauthopenid`(`tenant_id`, `statistics_date`, `robot_platform`, `oauth_open_id`);

ALTER TABLE `swhd`.`taikf_statistics_ad_daily`
    ADD COLUMN `statistics_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '统计日期' AFTER `tenant_id`,
    ADD COLUMN `ad_direct_message_today_count` int NOT NULL DEFAULT 0 COMMENT '当天私信人数（信息流）' AFTER `ad_direct_message_count`,
    ADD COLUMN `ad_speak_up_add_count` int NOT NULL DEFAULT 0 COMMENT '新增开口人数（信息流）' AFTER `ad_speak_up_count`,
    ADD COLUMN `ad_speak_up_today_count` int NOT NULL DEFAULT 0 COMMENT '当天开口人数（信息流）' AFTER `ad_speak_up_add_count`,
    ADD COLUMN `ad_customer_capital_today_count` int NOT NULL COMMENT '当天留资人数（信息流）' AFTER `ad_customer_capital_count`;

ALTER TABLE `swhd`.`taikf_statistics_ad_daily`
    ADD INDEX `idx_tenantid_statisticsdate_robotplatform_oauthopenid_adid`(`tenant_id`, `statistics_date`, `robot_platform`, `oauth_open_id`, `ad_id`);

ALTER TABLE `swhd`.`taikf_statistics_ad_daily`
    MODIFY COLUMN `ad_id` varchar(32) NOT NULL DEFAULT '' COMMENT '广告计划id' AFTER `statistics_date`,
    MODIFY COLUMN `advertiser_id` varchar(32) NOT NULL DEFAULT '' COMMENT '广告主id' AFTER `ad_name`;

CREATE TABLE `taikf_user_import_taobao_clue` (
    `id` bigint NOT NULL COMMENT '主键id',
    `bind_tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '绑定的租户id',
    `bind_sub_tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '绑定的子租户id',
    `customer_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户id-加密',
    `customer_seq_id` bigint NOT NULL DEFAULT 0 COMMENT '客户seqId（数字）',
    `customer_name` varchar(100) NOT NULL DEFAULT '' COMMENT '客户姓名',
    `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系手机号',
    `distribute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下派时间',
    `province_name` varchar(100) NOT NULL DEFAULT '' COMMENT '省份',
    `city_name` varchar(100) NOT NULL DEFAULT '' COMMENT '城市',
    `clue_tag` varchar(50) NOT NULL DEFAULT '' COMMENT '跟进标签（商家）1： 未跟进，2：跟进中，3：有意向，4：无法转化',
    `location` varchar(500) NOT NULL DEFAULT '' COMMENT '楼盘所在地',
    `building_name` varchar(200) NOT NULL DEFAULT '' COMMENT '楼盘名称',
    `demand_type` varchar(100) NOT NULL DEFAULT '' COMMENT '需求类型',
    `property_type` varchar(100) NOT NULL DEFAULT '' COMMENT '房屋类型',
    `deco_type` varchar(100) NOT NULL DEFAULT '' COMMENT '装修类型',
    `house_deco_status` varchar(100) NOT NULL DEFAULT '' COMMENT '房屋装修状态',
    `house_delivery_status` varchar(100) NOT NULL DEFAULT '' COMMENT '房屋交付状态',
    `layout_info` varchar(100) NOT NULL DEFAULT '' COMMENT '户型',
    `area` varchar(100) NOT NULL DEFAULT '' COMMENT '面积',
    `start_time` varchar(100) NOT NULL DEFAULT '' COMMENT '计划动工时间',
    `measure_free` varchar(100) NOT NULL DEFAULT '' COMMENT '是否要免费量房',
    `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注信息',
    `plan_id` varchar(100) NOT NULL DEFAULT '' COMMENT '计划id',
    `plan_name` varchar(200) NOT NULL DEFAULT '' COMMENT '计划名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_customerid` (`customer_id`),
    KEY `idx_customerseqid` (`customer_seq_id`),
    KEY `idx_createtime` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户导入淘宝（居然设计家）线索表';

CREATE TABLE `taikf_user_import_taobao_plan` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `sub_tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '子租户id',
    `plan_id` varchar(100) NOT NULL DEFAULT '' COMMENT '计划id',
    `plan_name` varchar(200) NOT NULL DEFAULT '' COMMENT '计划名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_planid` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户导入淘宝（居然设计家）计划表';

CREATE TABLE `tmessage_short_link` (
    `id` bigint NOT NULL COMMENT '主键id',
    `sid` varchar(20) NOT NULL DEFAULT '' COMMENT '短链ID',
    `url` varchar(500) NOT NULL DEFAULT '' COMMENT '链接',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_sid` (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='短链表';

ALTER TABLE `swhd`.`taikf_user_import_taobao_clue`
    ADD COLUMN `district_name` varchar(100) NOT NULL DEFAULT '' COMMENT '区' AFTER `city_name`;

CREATE TABLE `toauth_wecom_oauth_app` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `corp_id` varchar(50) NOT NULL DEFAULT '' COMMENT '企业ID',
    `corp_name` varchar(100) NOT NULL DEFAULT '' COMMENT '企业名称',
    `corp_logo` varchar(255) NOT NULL DEFAULT '' COMMENT '企业logo',
    `agent_id` int NOT NULL DEFAULT 0 COMMENT '应用ID',
    `secret` varchar(200) NOT NULL DEFAULT '' COMMENT '密钥',
    `state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_corpid` (`corp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企微授权应用表';

CREATE TABLE `toauth_wecom_oauth_kefu` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `corp_id` varchar(50) NOT NULL DEFAULT '' COMMENT '企业ID',
    `kefu_id` varchar(50) NOT NULL DEFAULT '' COMMENT '客服ID',
    `kefu_name` varchar(100) NOT NULL DEFAULT '' COMMENT '客服名称',
    `kefu_avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '客服头像',
    `state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_corpid_kefuid` (`corp_id`, `kefu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企微授权客服表';

CREATE TABLE `toauth_wecom_msg_kefu_cursor` (
    `id` bigint NOT NULL COMMENT '主键id',
    `corp_id` varchar(50) NOT NULL DEFAULT '' COMMENT '企业ID',
    `kefu_id` varchar(50) NOT NULL DEFAULT '' COMMENT '客服ID',
    `next_cursor` varchar(100) NOT NULL DEFAULT '' COMMENT '上一次调用时返回的next_cursor',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_corpid_kefuid` (`corp_id`, `kefu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企微客服消息指针表';

ALTER TABLE `swhd`.`tai_api_call_config`
    ADD COLUMN `options` json NULL COMMENT 'options' AFTER `user_template`;

ALTER TABLE `swhd`.`toauth_wecom_oauth_kefu`
    ADD COLUMN `kefu_link` varchar(255) NOT NULL DEFAULT '' COMMENT '客服链接' AFTER `kefu_avatar`;

ALTER TABLE `swhd`.`taikf_web_platform_info`
    ADD COLUMN `open_top_button` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启顶部按钮：0-关闭，1-开启' AFTER `customer_capital_form_config`,
    ADD COLUMN `top_button_config` json NULL COMMENT '顶部按钮配置' AFTER `open_top_button`;
