-- 账号迁移(同时有邮箱和手机号)
insert into tuser_tenant_user_base (id, email, mobile, `password`, nickname, avatar, create_time, modify_time, creator_id, modifier_id)
select ui.id, ui.email, ui.mobile, ui.`password`, ui.nickname, ui.avatar, ui.create_time, ui.modify_time, ui.creator_id, ui.modifier_id
from tuser_tenant_user_info ui
         left join tuser_tenant_user_base ub on ui.mobile = ub.mobile and ui.email = ub.email
where ub.id is null and ui.mobile <> '' and ui.email <> '' and ui.state = 1 and ui.is_delete = 0;
-- 账号迁移(同时无邮箱和手机号)
insert into tuser_tenant_user_base (id, email, mobile, `password`, nickname, avatar, create_time, modify_time, creator_id, modifier_id)
select ui.id, ui.email, ui.mobile, ui.`password`, ui.nickname, ui.avatar, ui.create_time, ui.modify_time, ui.creator_id, ui.modifier_id
from tuser_tenant_user_info ui
         left join tuser_tenant_user_base ub on ui.id = ub.id
where ub.id is null and ui.mobile = '' and ui.email = '' and ui.state = 1 and ui.is_delete = 0;
-- 账号迁移(无邮箱和有手机号)
insert into tuser_tenant_user_base (id, email, mobile, `password`, nickname, avatar, create_time, modify_time, creator_id, modifier_id)
select ui.id, ui.email, ui.mobile, ui.`password`, ui.nickname, ui.avatar, ui.create_time, ui.modify_time, ui.creator_id, ui.modifier_id
from tuser_tenant_user_info ui
         left join tuser_tenant_user_base ub on ui.mobile = ub.mobile
where ub.id is null and ui.mobile <> '' and ui.email = '' and ui.state = 1 and ui.is_delete = 0;
-- 账号迁移(有邮箱和无手机号)
insert into tuser_tenant_user_base (id, email, mobile, `password`, nickname, avatar, create_time, modify_time, creator_id, modifier_id)
select ui.id, ui.email, ui.mobile, ui.`password`, ui.nickname, ui.avatar, ui.create_time, ui.modify_time, ui.creator_id, ui.modifier_id
from tuser_tenant_user_info ui
         left join tuser_tenant_user_base ub on ui.email = ub.email
where ub.id is null and ui.mobile = '' and ui.email <> '' and ui.state = 1 and ui.is_delete = 0;
-- 账号迁移关联租户
insert into tuser_tenant_user_tenant (id, tenant_id, user_id, state, source_scene, create_time, modify_time, creator_id, modifier_id)
select ui.id, ui.tenant_id, ui.id, ui.state, ui.source_scene, ui.create_time, ui.modify_time, ui.creator_id, ui.modifier_id
from tuser_tenant_user_info ui
         left join tuser_tenant_user_tenant ut on ut.id = ui.id
where ut.id is null and ui.state = 1 and ui.is_delete = 0;
-- 检查手机是否重复
select mobile, count(*)
from tuser_tenant_user_base ub
where mobile <> '' and is_delete = 0
group by mobile
having count(*) > 1;
select *
from tuser_tenant_user_base
where mobile = 'xxx';
-- 检查邮箱是否重复
select email, count(*)
from tuser_tenant_user_base ub
where email <> '' and is_delete = 0
group by email
having count(*) > 1;
select *
from tuser_tenant_user_base
where email = 'xxx';
-- 检查账号是否迁移
select ui.*
from tuser_tenant_user_tenant ut
         join tuser_tenant_user_info ui on ui.is_delete = 0 and ut.user_id = ui.id
         left join tuser_tenant_user_base ub on ut.user_id = ub.id and ub.is_delete = 0
where ub.id is null and ut.is_delete = 0 and ut.state = 1;
-- 迁移手机号（）
update tuser_tenant_user_tenant u1, tuser_tenant_user_base u2, tuser_tenant_user_info u3
set u1.user_id = u2.id
where u1.id = u3.id and u3.mobile <> '' and u3.mobile = u2.mobile and u1.id in (
    select ui.id
    from tuser_tenant_user_info ui
    left join tuser_tenant_user_base ub on ui.id = ub.id and ub.is_delete = 0
    where ub.id is null and ui.is_delete = 0 and ui.state = 1
    );
-- 迁移邮箱
update tuser_tenant_user_tenant u1, tuser_tenant_user_base u2, tuser_tenant_user_info u3
set u1.user_id = u2.id
where u1.id = u3.id and u3.email <> '' and u3.email = u2.email and u1.id in (
    select ui.id
    from tuser_tenant_user_info ui
    left join tuser_tenant_user_base ub on ui.id = ub.id and ub.is_delete = 0
    where ub.id is null and ui.is_delete = 0 and ui.state = 1
    );
-- 检查授权账号是否迁移
select uo.*
from tuser_tenant_user_oauth uo
         left join tuser_tenant_user_base ub on uo.user_id = ub.id and ub.is_delete = 0
where ub.id is null and uo.is_delete = 0;

-- 菜单
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1916884237416529920, `parent_list` = '[{\"id\": \"1838570248651931648\", \"name\": \"系统\"}, {\"id\": \"1916881986866249728\", \"name\": \"商家管理\"}, {\"id\": \"1916882541944635392\", \"name\": \"商家信息\"}, {\"id\": \"1916884237416529920\", \"name\": \"用户\"}]', `title` = '移除', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'user#userInfoSubRemove', `permission_url` = '/swhd-user-web-tenant/userInfoSubTenant/removeByIds', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2024-06-25 14:46:00', `modify_time` = '2025-03-05 10:00:33', `creator_id` = '1838991355741732864', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1916892390963150848;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 3, `parent_id` = 1838570569507799040, `parent_list` = '[{\"id\": \"1838570248651931648\", \"name\": \"系统\"}, {\"id\": \"1838570569507799040\", \"name\": \"用户信息\"}]', `title` = '移除', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'user#userInfoRemove', `permission_url` = '/swhd-user-web-tenant/userInfo/removeByIds', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2023-11-27 16:57:50', `modify_time` = '2025-03-05 09:59:50', `creator_id` = '', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1840461729717288960;
