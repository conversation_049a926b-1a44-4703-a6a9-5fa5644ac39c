INSERT INTO swhd.tai_api_call_config
(id, business_type, business_name, model_ids, backup_model_ids, prompt_template, user_template, `options`, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(160, 'kefu_key_intent_extract', '对用户的输入进行关键意图提取', '["doubao-1.5-pro-32k"]', '["doubao-pro-32k"]', '# 角色
你是一个意图识别机器人，你能快速地从用户的消息中提取关键意图，

# 任务
我会给出客服与用户的对话记录，以及用户最后一条消息，你需要输出用户最后一条消息的意图。

# 约束
- 匹配到多个意图，只选择其中一条意图，不要返回多条意图。
- 输出的内容不要进行意图解析（不要试图解析为什么选择这条意图）。
- 同一种意图可能有不同的输入，需要进行一定程度的聚类
- 不能识别意图时，固定返回“未知意图”

# 例子1
用户：加盟费多少？
输出：问加盟费

# 例子2
用户：要多少钱加盟？
输出：问加盟费

# 例子3
用户：要多少钱加盟？
输出：问加盟费

# 用户输入：
#{chatHistory}

# 用户最后一条消息
#{userLastMsg}', NULL, NULL, '2024-10-28 11:20:51', '2024-10-28 11:20:51', '', '', 0);


INSERT INTO swhd.tai_api_call_config
(id, business_type, business_name, model_ids, backup_model_ids, prompt_template, user_template, `options`, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(170, 'reply_extract', '挑选、组织合适的回复话术', '["doubao-1.5-pro-32k"]', '["doubao-pro-32k"]', '# 角色
你是一个回复话术组织机器人，你能挑选、组织最合适的回复话术。

# 任务
我会给出客服与用户的历史对话记录、用户最后一条消息、一个回复话术列表，你需要从回复话术列表里面找到最合适的一条，并进行语言组织、优化，输出一条回复话术。

# 约束
- 输出内容一定是从回复里列表里面提取的
- 输出的内容不要进行解析（不要试图解析为什么选择这条回复）
- 输出要进行一定的话术优化，但不要偏离原来的意思
- 从回复话术列表里面找不到合适的回复时，固定返回“未知意图”

# 例子1
用户：这套家具多少钱？
回复话术列表：["这套家具多少钱啊？ 我们有多款家具在搞活动呢，您提供具体的颜色，型号信息，我们好针对性的给您回复。", "这套沙发打几折呀？ 亲，我们现在做活动，这款沙发折后只要XXX元 ，您有空可以到店来体验呢。", "怎么这么贵？ 亲您的预算大概多少，我们可以给您推荐。"]
输出：我们有多款家具在搞活动呢，您提供具体的颜色，型号信息，我们好针对性的给您回复。


# 历史对话记录
#{chatHistory}

# 回复话术列表
#{replyList}', NULL, NULL, '2024-10-28 11:20:51', '2024-10-28 11:20:52', '', '', 0);



-- 新增菜单
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016106435347349504, 3, 2016106330301005824, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}]', 'AI会话分析', 0, '', 0, 'BarChartOutlined', '/kefu/conversation', '', '', 10, NULL, '2025-03-26 09:27:12', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016106583838294016, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '列表', 1, '', 0, '', '', 'aikf#chatIntentList', '/swhd-ai-kefu-web-tenant/msgUnmatchedIntent/page', 0, NULL, '2025-03-26 09:27:47', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016106656412336128, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '详情', 1, '', 0, '', '', 'aikf#chatIntentDetails', '/swhd-ai-kefu-web-tenant/msgUnmatchedIntent/details,/swhd-ai-kefu-web-tenant/msgUnmatchedIntentRecord/page', 0, NULL, '2025-03-26 09:28:04', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016107118519779328, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '添加至知识库', 1, '', 0, '', '', 'aikf#chatIntentAddKnowledge', '/swhd-ai-kefu-web-tenant/knowledgeLibrary/add', 0, NULL, '2025-03-26 09:29:55', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016107225705218048, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '忽略', 1, '', 0, '', '', 'aikf#chatIntentIgnore', '/swhd-ai-kefu-web-tenant/msgUnmatchedIntent/update', 0, NULL, '2025-03-26 09:30:20', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016107282936496128, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '恢复', 1, '', 0, '', '', 'aikf#chatIntentRecover', '/swhd-ai-kefu-web-tenant/msgUnmatchedIntent/update', 0, NULL, '2025-03-26 09:30:34', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016112226016034816, 3, 2016106435347349504, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "2016106435347349504", "name": "AI会话分析"}]', '导出', 1, '', 0, '', '', 'aikf#chatIntentDownload', '/swhd-ai-kefu-web-tenant/msgUnmatchedIntent/download', 0, NULL, '2025-03-26 09:50:12', '2025-03-27 14:57:26', '1943395431434158080', '1856778240690094080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016104150105980928, 3, 2016103686287261696, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}, {"id": "2016103686287261696", "name": "文档"}]', '列表', 1, '', 0, '', '', 'aikf#knowledgeDocList', '/swhd-ai-kefu-web-tenant/knowledge/doc/page', 0, NULL, '2025-03-26 09:18:07', '2025-03-26 14:06:53', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016103573435318272, 3, 2002904549236539392, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', '问答', 2, '', 0, '', '', '', '', 0, NULL, '2025-03-26 09:15:49', '2025-03-26 14:04:02', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016174791135985664, 3, 2002905643215880192, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002905643215880192", "name": "模版知识库"}]', '问答', 2, '', 0, '', '', '', '', 0, NULL, '2025-03-26 13:58:49', '2025-03-26 13:58:49', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016103686287261696, 3, 2002904549236539392, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', '文档', 2, '', 0, '', '', '', '', 0, NULL, '2025-03-26 09:16:16', '2025-03-26 13:53:49', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016104355899506688, 3, 2016103686287261696, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}, {"id": "2016103686287261696", "name": "文档"}]', '导入', 1, '', 0, '', '', 'aikf#knowledgeDocImport', '/swhd-ai-kefu-web-tenant/knowledge/doc/import', 0, NULL, '2025-03-26 09:18:56', '2025-03-26 13:53:49', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016104489001549824, 3, 2016103686287261696, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}, {"id": "2016103686287261696", "name": "文档"}]', '删除', 1, '', 0, '', '', 'aikf#knowledgeDocDelete', '/swhd-ai-kefu-web-tenant/knowledge/doc/deleteById', 0, NULL, '2025-03-26 09:19:28', '2025-03-26 13:53:49', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, menu_code, click_type, icon, route_path, button_code, permission_url, ordered, expand_attrs, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016106330301005824, 3, 1891889450061922304, '[{"id": "1891889450061922304", "name": "AI客服"}]', '会话管理', 0, '', 0, '', '', '', '', 5, NULL, '2025-03-26 09:26:47', '2025-03-26 09:26:47', '1943395431434158080', '1943395431434158080', 0);


-- 更新菜单
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016174791135985664, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002905643215880192", "name": "模版知识库"}, {"id": "2016174791135985664", "name": "问答"}]', title='问答知识列表', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#platformQaPage', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/pageTemplate', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:21:17', modify_time='2025-03-26 14:05:41', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002907988226080768;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002905643215880192, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002905643215880192", "name": "模版知识库"}]', title='列表', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aifk#platformLibraryPage', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/pageTemplate,/swhd-ai-kefu-web-tenant/getById', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:17:00', modify_time='2025-03-26 14:05:12', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002906910260592640;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904549236539392, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}, {"id": "2016103573435318272", "name": "问答"}]', title='列表', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#libraryPage', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/page,/swhd-ai-kefu-web-tenant/knowledgeLibrary/getById', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:04', modify_time='2025-03-26 14:04:02', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905166189297664;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2003536901704777728, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2006339398605471744", "name": "AI员工管理"}, {"id": "2003536901704777728", "name": "AI员工"}]', title='列表', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#employeePage', permission_url='/swhd-ai-kefu-web-tenant/aiEmployee/page,/swhd-ai-kefu-web-tenant/platformInfo/selectUnboundAiEmployee,/swhd-ai-kefu-web-tenant/tagInfo/selectAll', ordered=0, expand_attrs=NULL, create_time='2025-02-19 17:01:08', modify_time='2025-03-26 14:03:31', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2003537097549414400;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904810927554560, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}]', title='模版知识库', menu_type=2, menu_code='', click_type=0, icon='', route_path='', button_code='', permission_url='', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:11:58', modify_time='2025-03-26 13:52:31', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905643215880192;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002905643215880192, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002905643215880192", "name": "模版知识库"}]', title='复制', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#platformLibraryCopy', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/copy', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:19:23', modify_time='2025-03-26 13:52:31', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002907510935257088;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904549236539392, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='创建', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#libraryAdd', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/add', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:18', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905227526799360;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904549236539392, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='修改', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#libraryUpdate', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/update', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:27', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905263052554240;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904549236539392, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='复制', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#libraryCopy', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/copy', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:35', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905295419998208;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904549236539392, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='删除', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#libraryDelete', permission_url='/swhd-ai-kefu-web-tenant/knowledgeLibrary/removeById', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:42', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905326453653504;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016103573435318272, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='问答知识列表', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#qaPage', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/page,/swhd-ai-kefu-web-tenant/knowledgeQa/getById', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:10:56', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905384326660096;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016103573435318272, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='新增问答知识', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#qaAdd', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/add', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:11:12', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905451049648128;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016103573435318272, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='修改问答知识', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#qaUpdate', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/update', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:11:26', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905509778292736;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016103573435318272, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='导入问答知识', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#qaImport', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/batchImport', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:11:36', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905552438558720;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016103573435318272, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}, {"id": "2002904549236539392", "name": "我的知识库"}]', title='删除问答知识', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#qaDelete', permission_url='/swhd-ai-kefu-web-tenant/knowledgeQa/removeById', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:11:48', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002905602321416192;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2002904810927554560, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1950155835757953024", "name": "配置管理"}, {"id": "2002904810927554560", "name": "知识库"}]', title='我的知识库', menu_type=2, menu_code='', click_type=0, icon='', route_path='', button_code='', permission_url='', ordered=0, expand_attrs=NULL, create_time='2025-02-17 23:07:37', modify_time='2025-03-26 13:52:14', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2002904549236539392;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=1947994692234772480, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}, {"id": "1947994692234772480", "name": "会话信息"}]', title='会话', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aiKefu#chat', permission_url='/swhd-ai-kefu-web-tenant/userInfo/chatUserPage,/swhd-ai-kefu-web-tenant/userInfo/chatUserScroll,/swhd-ai-kefu-web-tenant/userInfo/chatUserDetails,/swhd-ai-kefu-web-tenant/userInfo/customerCapitalDetails,/swhd-ai-kefu-web-tenant/userInfo/updateLastReadTime,/swhd-ai-kefu-web-tenant/msgRecord/scroll,/swhd-ws/webSocketConnect/cert/ai_kefu,/swhd-ai-kefu-web-tenant/msgApi/sendMsg,/swhd-ai-kefu-web-tenant/userInfo/update,/swhd-ai-kefu-web-tenant/platformInfo/selectAll', ordered=0, expand_attrs=NULL, create_time='2024-09-19 10:47:04', modify_time='2025-03-26 09:26:57', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=1947997616423829504;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2016106330301005824, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2016106330301005824", "name": "会话管理"}]', title='会话信息', menu_type=0, menu_code='', click_type=0, icon='MessageOutlined', route_path='/kefu/dialogue', button_code='', permission_url='', ordered=5, expand_attrs=NULL, create_time='2024-09-19 10:35:27', modify_time='2025-03-26 09:26:57', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=1947994692234772480;
UPDATE swhd.tuser_tenant_menu_info
SET tenant_type=3, parent_id=2003536901704777728, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "2006339398605471744", "name": "AI员工管理"}, {"id": "2003536901704777728", "name": "AI员工"}]', title='创建', menu_type=1, menu_code='', click_type=0, icon='', route_path='', button_code='aikf#addEmployee', permission_url='/swhd-ai-kefu-web-tenant/aiEmployee/add,/swhd-content-web-tenant/ossPublic/uploadSign', ordered=0, expand_attrs=NULL, create_time='2025-02-19 17:01:24', modify_time='2025-03-25 11:25:45', creator_id='1943395431434158080', modifier_id='1943395431434158080', is_delete=0
WHERE id=2003537163609702400;


-- 后台菜单
INSERT INTO swhd.tuser_platform_menu_info
(id, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016240247440015360, 2008570306960293888, '[{"id": "1962574653742383104", "name": "AI客服"}, {"id": "2008570306960293888", "name": "AI会话分析"}]', '详情', 1, 0, '', '', 'aikf#intentDetails', '/swhd-ai-kefu-web-platform/msgUnmatchedIntent/detailByName,/swhd-ai-kefu-web-platform/msgUnmatchedIntentRecord/page', 0, '2025-03-26 18:18:55', '2025-03-27 11:37:49', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_platform_menu_info
(id, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016241618130173952, 2008570306960293888, '[{"id": "1962574653742383104", "name": "AI客服"}, {"id": "2008570306960293888", "name": "AI会话分析"}]', '恢复', 1, 0, '', '', 'aikf#intentRecover', '/swhd-ai-kefu-web-platform/msgUnmatchedIntent/update', 0, '2025-03-26 18:24:22', '2025-03-27 11:29:33', '1943395431434158080', '1943395431434158080', 1);
INSERT INTO swhd.tuser_platform_menu_info
(id, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2016240100161224704, 2008570306960293888, '[{"id": "1962574653742383104", "name": "AI客服"}, {"id": "2008570306960293888", "name": "AI会话分析"}]', '列表', 1, 0, '', '', 'aikf#intentPage', '/swhd-ai-kefu-web-platform/msgUnmatchedIntent/page', 0, '2025-03-26 18:18:20', '2025-03-26 18:18:20', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_platform_menu_info
(id, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(2008570306960293888, 1962574653742383104, '[{"id": "1962574653742383104", "name": "AI客服"}]', 'AI会话分析', 0, 0, '', '/aiKefu/conversation', '', '', 35, '2025-03-05 14:21:19', '2025-03-05 14:31:39', '1856778240690094080', '1856778240690094080', 0);

