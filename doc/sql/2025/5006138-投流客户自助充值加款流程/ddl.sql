

-- 添加商务人员列表字段 (JSON 类型，存储 List<Long>)
ALTER TABLE `swhd`.`tagent_account_oceanengine_info`
    ADD COLUMN `business_personnel_list` json NULL COMMENT '商务人员列表' AFTER `using_vpn`;

-- 添加渠道返点字段 (decimal(10,2) 类型，默认值 0)
ALTER TABLE `swhd`.`tagent_account_oceanengine_info`
    ADD COLUMN `channel_rebate` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '渠道返点' AFTER `business_personnel_list`;



-- 1. 自助充值授信表
CREATE TABLE `swhd`.`tagent_account_oceanengine_customer_recharge_credit` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',

    `custom_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '客户ID',
    `allow_self_recharge` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许自助充值 0-不允许 1-允许',
    `credit_limit` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '授信额度',
    `used_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '已使用额度',

    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='自助充值授信表';

-- 2. 额度变更记录表
CREATE TABLE `swhd`.`tagent_account_oceanengine_customer_credit_change_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',

    `self_recharge_credit_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '授信ID',
    `change_type` varchar(20) NOT NULL COMMENT '变更类型 CLOSE-关闭 OPEN-开启 INCREASE-提额 CONSUME-客户消耗',
    `before_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更前额度',
    `after_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更后额度',
    `change_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更额度',
    `recharge_detail_id` bigint(20) DEFAULT 0 COMMENT '充值明细ID',

    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='自助充值额度变更记录表';

-- 3. 充值明细表
CREATE TABLE `swhd`.`tagent_account_oceanengine_customer_recharge_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',

    `advertiser_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '广告主账号ID',
    `custom_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '客户ID',
    `source_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '自助充值源ID',
    `recharge_account_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '充值账目金额',
    `recharge_cash_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '充值现金金额',
    `channel_rebate` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '渠道返点',
    `credit_limit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '授信额度',
    `remaining_cash_limit` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '剩余现金额度',
    `execute_time` datetime DEFAULT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    `capital_type` varchar(20) NOT NULL DEFAULT 'RECHARGE_IN' COMMENT '转账类型 RECHARGE_IN-充值转入 REFUND_OUT-退款转出',
    `input_method` varchar(50) NOT NULL DEFAULT 'WECHAT_BOT' COMMENT '录入方式 WECHAT_BOT-微信机器人 MANUAL-手动录入',
    `execute_status` varchar(20) NOT NULL DEFAULT 'PROCESSING' COMMENT '执行状态 PROCESSING-处理中 FAILED-处理失败 SUCCESS-处理成功',
    `failure_reason` varchar(500) DEFAULT '' COMMENT '失败原因',
    `transfer_serial` varchar(100) DEFAULT '' COMMENT '转账单号',
    `recharge_message` text DEFAULT NULL COMMENT '充值消息',
    `trace_id` varchar(100) DEFAULT '' COMMENT '链路追踪ID',

    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='自助充值明细表';

-- 4. 自助充值源表
CREATE TABLE `swhd`.`tagent_account_oceanengine_customer_recharge_source` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id',

    `title` varchar(100) NOT NULL DEFAULT '' COMMENT '充值源标题',
    `customer_ids` json NULL COMMENT '客户ID列表',
    `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 0-关闭 1-开启',

    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='自助充值源表';