-- swhd.toauth_xhs_oauth_info definition


CREATE TABLE `toauth_xhs_oauth_info`
(
    `id`                  bigint       NOT NULL COMMENT '主键id',
    `tenant_id`           bigint       NOT NULL DEFAULT '0' COMMENT '租户id',
    `app_id`              bigint       NOT NULL DEFAULT '0' COMMENT '应用ID',
    `user_id`             varchar(100) NOT NULL DEFAULT '' COMMENT '授权账号的user_id',
    `refresh_token`       varchar(100) NOT NULL DEFAULT '' COMMENT '刷新令牌',
    `refresh_expire_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '刷新令牌过期时间',
    `corporation_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '企业名称',
    `approval_role_type`  int          NOT NULL DEFAULT '0' COMMENT '授权账号类型，4：品牌，601：代理商',
    `role_type`           tinyint      NOT NULL DEFAULT '0' COMMENT '应用角色类型，1：品牌开发者，2：代理商开发者，3：服务商开发者',
    `platform_type`       tinyint      NOT NULL DEFAULT '0' COMMENT '平台类型，1：聚光，2：蒲公英',
    `scope`               json                  DEFAULT NULL COMMENT '授权接口范围，third_im：三方工具服务，three_im_leads_push：私信留资数据',
    `state`               tinyint      NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id`          varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id`         varchar(32)  NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY                   `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB COMMENT='小红书授权信息表';

-- 抖音授权信息表增加授权范围字段
ALTER TABLE swhd.toauth_douyin_oauth_info
    ADD `scope` json DEFAULT NULL COMMENT '用户授权的作用域' after account_role;
