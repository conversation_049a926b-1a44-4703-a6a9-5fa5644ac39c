INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2023443291533475840, 1, 1861491287463821312, '[{\"id\": \"1840115093522612224\", \"name\": \"媒介\"}, {\"id\": \"1861491287463821312\", \"name\": \"数据统计\"}]', '明点统计', 0, '', 0, 'PayCircleOutlined', '/agent/rebate/sum', '', '', 20, NULL, '2025-04-15 15:21:14', '2025-04-15 15:24:39', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2023444193216561152, 1, 2023443291533475840, '[{\"id\": \"1840115093522612224\", \"name\": \"媒介\"}, {\"id\": \"1861491287463821312\", \"name\": \"数据统计\"}, {\"id\": \"2023443291533475840\", \"name\": \"明点统计\"}]', '列表', 1, '', 0, '', '', 'agent#rebateOceanengineSumList', '/swhd-agent-web-tenant/rebateOceanengineAdvertiserMonth/advertiserSumPage,/swhd-agent-web-tenant/rebateOceanengineAdvertiserMonth/customerSumPage,/swhd-agent-web-tenant/rebateOceanengineBaseRebate/page', 10, NULL, '2025-04-15 15:24:49', '2025-04-15 16:21:02', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2023444275521388544, 1, 2023443291533475840, '[{\"id\": \"1840115093522612224\", \"name\": \"媒介\"}, {\"id\": \"1861491287463821312\", \"name\": \"数据统计\"}, {\"id\": \"2023443291533475840\", \"name\": \"明点统计\"}]', '下载', 1, '', 0, '', '', 'agent#rebateOceanengineSumDownload', '/swhd-agent-web-tenant/rebateOceanengineAdvertiserMonth/advertiserSumDownload,/swhd-agent-web-tenant/rebateOceanengineAdvertiserMonth/customerSumDownload', 20, NULL, '2025-04-15 15:25:09', '2025-04-15 15:27:16', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2023444337894883328, 1, 2023443291533475840, '[{\"id\": \"1840115093522612224\", \"name\": \"媒介\"}, {\"id\": \"1861491287463821312\", \"name\": \"数据统计\"}, {\"id\": \"2023443291533475840\", \"name\": \"明点统计\"}]', '配置基础返点', 1, '', 0, '', '', 'agent#rebateOceanengineConfigBaseRebate', '/swhd-agent-web-tenant/rebateOceanengineBaseRebate/getById,/swhd-agent-web-tenant/rebateOceanengineBaseRebate/add,/swhd-agent-web-tenant/rebateOceanengineBaseRebate/update,/swhd-agent-web-tenant/rebateOceanengineBaseRebate/removeByIds', 30, NULL, '2025-04-15 15:25:24', '2025-04-15 16:21:30', '1838991355741732864', '1838991355741732864', 0);

INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1, 1000, 2025, 'ZIYUNYING', '竞价一类', 0.0450, '2025-04-09 13:50:04', '2025-04-15 17:01:31', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2, 1000, 2025, 'ZIYUNYING', '竞价二类', 0.0350, '2025-04-09 13:54:13', '2025-04-15 17:01:38', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (3, 1000, 2025, 'ZIYUNYING', '竞价三类', 0.0350, '2025-04-09 13:54:22', '2025-04-15 17:01:54', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (4, 1000, 2025, 'ZIYUNYING', '竞价四类', 0.0250, '2025-04-09 13:54:34', '2025-04-15 17:01:59', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (5, 1000, 2025, 'ZIYUNYING', '竞价五类', 0.0250, '2025-04-09 13:54:42', '2025-04-15 17:02:03', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (6, 1000, 2025, 'ZIYUNYING', '竞价六类', 0.0250, '2025-04-09 13:54:54', '2025-04-15 17:02:07', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (7, 1000, 2025, 'ZIYUNYING', '竞价七类', 0.0250, '2025-04-09 13:55:01', '2025-04-15 17:02:11', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (8, 1000, 2025, 'ZIYUNYING', '竞价八类', 0.0250, '2025-04-09 13:55:09', '2025-04-15 17:02:16', '', '1838991355741732864', 0);
INSERT INTO `swhd`.`tagent_rebate_oceanengine_base_rebate` (`id`, `tenant_id`, `rebate_year`, `operator_tag`, `rebate_calc_external_industry_category`, `base_rebate_rate`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (9, 1000, 2025, 'ZIYUNYING', '竞价九类', 0.0250, '2025-04-09 13:55:16', '2025-04-15 17:02:20', '', '1838991355741732864', 0);

UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1975960199319519232, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}]', `title` = '资金管理', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '/shop/main/fund', `button_code` = '', `permission_url` = '', `ordered` = 50, `expand_attrs` = NULL, `create_time` = '2024-12-05 14:54:25', `modify_time` = '2024-12-24 10:07:21', `creator_id` = '1943395247119663104', `modifier_id` = '1856778240690094080', `is_delete` = 0 WHERE `id` = 1975963730474696704;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantList', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/page,/swhd-shop-web-tenant/qualificationSubTenant/getById', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-05 14:56:10', `modify_time` = '2025-04-19 11:16:43', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1975964169383444480;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '新增', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantAdd', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/add,swhd-ai-web-tenant/aiOcr/businessLicense', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2024-12-05 14:57:10', `modify_time` = '2025-04-19 11:17:47', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1975964421498863616;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '修改', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantUpdate', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/update,/swhd-shop-web-tenant/qualificationSubTenant/getById,/swhd-ai-web-tenant/aiOcr/businessLicense,/swhd-ai-web-tenant/aiOcr/idCard', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2024-12-05 14:58:19', `modify_time` = '2025-04-19 11:18:13', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1975964713216901120;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '删除', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantRemove', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/removeByIds', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2024-12-05 14:59:16', `modify_time` = '2025-04-19 11:19:27', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1975964950979411968;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1975960199319519232, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}]', `title` = '门店管理', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '/shop/main/stores', `button_code` = '', `permission_url` = '', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:09:51', `modify_time` = '2024-12-24 10:07:13', `creator_id` = '1943395247119663104', `modifier_id` = '1856778240690094080', `is_delete` = 0 WHERE `id` = 1976345102586478592;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantList', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/pageWithPublish,/swhd-shop-web-tenant/shopSubTenant/getDetailById', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:12:24', `modify_time` = '2025-04-19 10:11:59', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976345744524705792;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '新增', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantAdd', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/add', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:12:59', `modify_time` = '2025-04-19 10:13:25', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976345890759114752;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '更新', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantUpdate', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/updateBase,/swhd-shop-web-tenant/shopSubTenant/update', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:13:35', `modify_time` = '2025-04-19 10:14:06', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976346040202166272;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '删除', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantRemove', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/removeById', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:14:28', `modify_time` = '2025-04-19 10:14:32', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976346264068947968;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '开启关闭功能', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopFunctionSubTenantOpenClose', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/getDetailById,/swhd-shop-web-tenant/shopFunctionSubTenant/openByCode,/swhd-shop-web-tenant/shopFunctionSubTenant/closeByCode', `ordered` = 50, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:15:17', `modify_time` = '2025-04-19 10:17:41', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976346467383640064;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '发布门店', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantPublish', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/batchPublish', `ordered` = 60, `expand_attrs` = NULL, `create_time` = '2024-12-06 16:15:53', `modify_time` = '2025-04-19 10:28:40', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1976346620534456320;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1976345102586478592, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}]', `title` = '门店信息', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'ShopOutlined', `route_path` = '/shop/main/stores/info', `button_code` = '', `permission_url` = '', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2024-12-10 16:04:46', `modify_time` = '2024-12-24 10:07:31', `creator_id` = '1856778240690094080', `modifier_id` = '1856778240690094080', `is_delete` = 0 WHERE `id` = 1977793375767756800;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1976345102586478592, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}]', `title` = '资质管理', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'VerifiedOutlined', `route_path` = '/shop/main/stores/qualificate', `button_code` = '', `permission_url` = '', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2024-12-10 16:05:25', `modify_time` = '2024-12-24 10:07:38', `creator_id` = '1856778240690094080', `modifier_id` = '1856778240690094080', `is_delete` = 0 WHERE `id` = 1977793538452226048;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1975963730474696704, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}]', `title` = '商户通订单', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'ContainerOutlined', `route_path` = '/shop/main/fund/commerce', `button_code` = '', `permission_url` = '', `ordered` = 60, `expand_attrs` = NULL, `create_time` = '2024-12-10 16:13:13', `modify_time` = '2024-12-24 10:07:47', `creator_id` = '1856778240690094080', `modifier_id` = '1856778240690094080', `is_delete` = 0 WHERE `id` = 1977795500073680896;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977795500073680896, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1977795500073680896\", \"name\": \"商户通订单\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#amapOrderSubTenantList', `permission_url` = '/swhd-shop-web-tenant/amapOrderSubTenant/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-10 18:04:44', `modify_time` = '2025-04-18 15:18:42', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1977823565071908864;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1975963730474696704, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}]', `title` = '充值订单', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'AccountBookOutlined', `route_path` = '/shop/main/fund/recharge', `button_code` = '', `permission_url` = '', `ordered` = 80, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:11:49', `modify_time` = '2024-12-31 10:17:52', `creator_id` = '1943395431434158080', `modifier_id` = '1945906260570275840', `is_delete` = 0 WHERE `id` = 1983925543640367104;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983925543640367104, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1983925543640367104\", \"name\": \"充值订单\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderSubTenantList', `permission_url` = '/swhd-service-market-web-tenant/orderInfoSubTenant/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:14:48', `modify_time` = '2025-04-19 14:26:37', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983926295301586944;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983925543640367104, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1983925543640367104\", \"name\": \"充值订单\"}]', `title` = '查看', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderSubTenantDetails', `permission_url` = '/swhd-service-market-web-tenant/orderInfoSubTenant/details', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:15:06', `modify_time` = '2025-04-19 14:26:49', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983926370308325376;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983925543640367104, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1983925543640367104\", \"name\": \"充值订单\"}]', `title` = '关闭', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderSubTenantCancel', `permission_url` = '/swhd-service-market-web-tenant/orderInfoSubTenant/cancelOrder', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:23:27', `modify_time` = '2025-04-19 14:27:07', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983928470782214144;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983925543640367104, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1983925543640367104\", \"name\": \"充值订单\"}]', `title` = '修改归属', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderSubTenantChangeShop', `permission_url` = '/swhd-service-market-web-tenant/orderInfoSubTenant/changeOrderShop', `ordered` = 40, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:24:10', `modify_time` = '2025-04-19 14:27:28', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983928652970196992;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1985393116613705728, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1985393116613705728\", \"name\": \"客资管理\"}]', `title` = '客资列表', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'TeamOutlined', `route_path` = '/shop/main/customer/info', `button_code` = '', `permission_url` = '', `ordered` = 90, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:27:08', `modify_time` = '2025-01-11 17:02:02', `creator_id` = '1943395431434158080', `modifier_id` = '1943395431434158080', `is_delete` = 0 WHERE `id` = 1983929396880343040;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983929396880343040, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1985393116613705728\", \"name\": \"客资管理\"}, {\"id\": \"1983929396880343040\", \"name\": \"客资列表\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#customLeadsSubTenantList', `permission_url` = '/swhd-shop-web-tenant/shopCustomerLeadsSubTenant/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:28:05', `modify_time` = '2025-04-18 15:14:08', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983929635880173568;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1975960199319519232, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}]', `title` = '客资管理', `menu_type` = 0, `menu_code` = '', `click_type` = 0, `icon` = 'TeamOutlined', `route_path` = '/shop/main/customer', `button_code` = '', `permission_url` = '', `ordered` = 70, `expand_attrs` = NULL, `create_time` = '2024-12-31 15:23:26', `modify_time` = '2025-01-11 17:02:02', `creator_id` = '1945906260570275840', `modifier_id` = '1943395431434158080', `is_delete` = 0 WHERE `id` = 1985393116613705728;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1983925543640367104, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1975963730474696704\", \"name\": \"资金管理\"}, {\"id\": \"1983925543640367104\", \"name\": \"充值订单\"}]', `title` = '导出', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderSubTenantDownload', `permission_url` = '/swhd-service-market-web-tenant/orderInfoSubTenant/downloadServiceOrder', `ordered` = 50, `expand_attrs` = NULL, `create_time` = '2025-01-08 10:46:50', `modify_time` = '2025-04-19 14:27:46', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1988222609921277952;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 3, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '批量导入', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantBatchImport', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/batchImport,/swhd-shop-web-tenant/qualification/downloadImportResult', `ordered` = 50, `expand_attrs` = NULL, `create_time` = '2025-01-21 09:55:49', `modify_time` = '2025-04-19 11:18:52', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1992920816039362560;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793375767756800, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793375767756800\", \"name\": \"门店信息\"}]', `title` = '批量导入', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#shopSubTenantBatchImport', `permission_url` = '/swhd-shop-web-tenant/shopSubTenant/batchImport,/swhd-shop-web-tenant/shop/downloadImportResult', `ordered` = 70, `expand_attrs` = NULL, `create_time` = '2025-01-21 17:33:01', `modify_time` = '2025-04-19 10:18:45', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1993035872290734080;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 1, `parent_id` = 1977793538452226048, `parent_list` = '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1976345102586478592\", \"name\": \"门店管理\"}, {\"id\": \"1977793538452226048\", \"name\": \"资质管理\"}]', `title` = '修改组织', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#qualificationSubTenantChangeTenant', `permission_url` = '/swhd-shop-web-tenant/qualificationSubTenant/changeTenant', `ordered` = 60, `expand_attrs` = NULL, `create_time` = '2025-01-22 18:27:52', `modify_time` = '2025-04-19 11:19:17', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1993412063300419584;
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2024528553227452416, 1, 1983929396880343040, '[{\"id\": \"1975960199319519232\", \"name\": \"云店管理\"}, {\"id\": \"1985393116613705728\", \"name\": \"客资管理\"}, {\"id\": \"1983929396880343040\", \"name\": \"客资列表\"}]', '下载', 1, '', 0, '', '', 'shop#customLeadsSubTenantDownload', '/swhd-shop-web-tenant/shopCustomerLeadsSubTenant/download', 20, NULL, '2025-04-18 15:13:41', '2025-04-18 15:14:45', '1838991355741732864', '1838991355741732864', 0);
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983931024756834304, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983931024756834304\", \"name\": \"我的权益\"}]', `title` = '导出', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#rightsDownload', `permission_url` = '/swhd-service-market-web-tenant/userRights/downloadRightsRecord', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2025-01-08 10:47:59', `modify_time` = '2025-04-19 14:58:37', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1988222902809526272;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983931024756834304, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983931024756834304\", \"name\": \"我的权益\"}]', `title` = '明细', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#reghtsDetails', `permission_url` = '/swhd-service-market-web-tenant/userRights/pageRightsRecord', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2025-01-15 14:47:50', `modify_time` = '2025-04-19 14:58:32', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1990819974268321792;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983931024756834304, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983931024756834304\", \"name\": \"我的权益\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#shopRightList', `permission_url` = '/swhd-service-market-web-tenant/userRights/pageShopRightsSummary', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:34:21', `modify_time` = '2025-04-19 14:58:25', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983931211852152832;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983930131965673472, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983930131965673472\", \"name\": \"充值订单\"}]', `title` = '充值', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#createOrder', `permission_url` = '/swhd-service-market-web-tenant/orderInfo/createServiceOrder', `ordered` = 30, `expand_attrs` = NULL, `create_time` = '2025-01-03 09:53:39', `modify_time` = '2025-04-19 14:56:55', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1986397288439873536;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983930131965673472, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983930131965673472\", \"name\": \"充值订单\"}]', `title` = '查看', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderDetails	', `permission_url` = '/swhd-service-market-web-tenant/orderInfo/details', `ordered` = 20, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:30:37', `modify_time` = '2025-04-19 14:55:22', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983930272743292928;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983930131965673472, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983930131965673472\", \"name\": \"充值订单\"}]', `title` = '列表	', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderList	', `permission_url` = '/swhd-service-market-web-tenant/orderInfo/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:30:19', `modify_time` = '2025-04-19 14:55:15', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983930197073854464;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983930131965673472, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983930131965673472\", \"name\": \"充值订单\"}]', `title` = '关闭', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderCancel', `permission_url` = '/swhd-service-market-web-tenant/orderInfo/cancelOrder', `ordered` = 0, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:30:59', `modify_time` = '2025-04-19 14:55:02', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 1 WHERE `id` = 1983930366020419584;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983930131965673472, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1983930131965673472\", \"name\": \"充值订单\"}]', `title` = '修改归属', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'market#orderChangeShop', `permission_url` = '', `ordered` = 0, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:31:16', `modify_time` = '2025-04-19 14:54:58', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 1 WHERE `id` = 1983930438007259136;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1977829300765851648, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1977829073988222976\", \"name\": \"资金管理\"}, {\"id\": \"1977829300765851648\", \"name\": \"商户通订单\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#amapOrderList', `permission_url` = '/swhd-shop-web-tenant/amapOrder/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-25 10:17:39', `modify_time` = '2025-04-18 15:16:28', `creator_id` = '1943395247119663104', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983141836922814464;
UPDATE `swhd`.`tuser_tenant_menu_info` SET `tenant_type` = 2, `parent_id` = 1983931302876938240, `parent_list` = '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1985393846837837824\", \"name\": \"客资管理\"}, {\"id\": \"1983931302876938240\", \"name\": \"客资列表\"}]', `title` = '列表', `menu_type` = 1, `menu_code` = '', `click_type` = 0, `icon` = '', `route_path` = '', `button_code` = 'shop#customLeadsList', `permission_url` = '/swhd-shop-web-tenant/shopCustomerLeads/page', `ordered` = 10, `expand_attrs` = NULL, `create_time` = '2024-12-27 14:35:07', `modify_time` = '2025-04-18 15:04:46', `creator_id` = '1943395431434158080', `modifier_id` = '1838991355741732864', `is_delete` = 0 WHERE `id` = 1983931405847101440;
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `menu_code`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `expand_attrs`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (2024525502219419648, 2, 1983931302876938240, '[{\"id\": \"1975960274238177280\", \"name\": \"我的云店\"}, {\"id\": \"1985393846837837824\", \"name\": \"客资管理\"}, {\"id\": \"1983931302876938240\", \"name\": \"客资列表\"}]', '下载', 1, '', 0, '', '', 'shop#customLeadsDownload', '/swhd-shop-web-tenant/shopCustomerLeads/download', 20, NULL, '2025-04-18 15:01:34', '2025-04-18 15:04:35', '1838991355741732864', '1838991355741732864', 0);
