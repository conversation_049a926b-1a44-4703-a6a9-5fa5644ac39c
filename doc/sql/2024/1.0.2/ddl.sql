
CREATE TABLE `swhd`.`tcrm_custom_info` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `business_code` varchar(100) NOT NULL DEFAULT '' COMMENT '业务：agent-代理商客户，content_creative-内容创意客户',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '客户名称',
    `name_pinyin` varchar(500) NOT NULL DEFAULT '' COMMENT '客户名称拼音',
    `name_pinyin_initial` varchar(100) NOT NULL DEFAULT '' COMMENT '客户名称拼音每个字的首字母',
    `custom_type_id` bigint NOT NULL DEFAULT 0 COMMENT '客户类型id：tagent_type_info#id',
    `company_name` varchar(200) NOT NULL DEFAULT '' COMMENT '公司名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '客户信息表';

CREATE TABLE `swhd`.`tcrm_custom_contact` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `business_code` varchar(100) NOT NULL DEFAULT '' COMMENT '业务：agent-代理商客户，content_creative-内容创意客户',
    `custom_id` bigint NOT NULL DEFAULT 0 COMMENT '客户id：tagent_custom_info#id',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '联系人名称',
    `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系人手机号码',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_customid` (`tenant_id`, `custom_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '客户联系人表';

CREATE TABLE `swhd`.`tcrm_custom_contract` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `business_code` varchar(100) NOT NULL DEFAULT '' COMMENT '业务：agent-代理商客户，content_creative-内容创意客户',
    `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '合同类型：1-广告合同，2-授权协议',
    `custom_id` bigint NOT NULL DEFAULT 0 COMMENT '客户id：tagent_custom_info#id',
    `contract_name` varchar(255) NOT NULL DEFAULT '' COMMENT '合同名称',
    `signing_date` date NOT NULL COMMENT '签约日期',
    `contract_oss_key` varchar(255) NOT NULL DEFAULT '' COMMENT '合同文件oss key',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_customid` (`tenant_id`, `custom_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '客户合同表';

CREATE TABLE `swhd`.`tcontent_type_info` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `classify_code` varchar(100) NOT NULL DEFAULT '' COMMENT '分类code',
    `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
    `state` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '类型信息表';

CREATE TABLE `swhd`.`tcontent_type_classify` (
    `id` bigint NOT NULL COMMENT '主键id',
    `business_code` varchar(100) NOT NULL DEFAULT '' COMMENT '业务code',
    `classify_code` varchar(100) NOT NULL DEFAULT '' COMMENT '分类code',
    `classify_name` varchar(100) NOT NULL DEFAULT '' COMMENT '分类名称',
    `ordered` int(11) NOT NULL DEFAULT 0 COMMENT '序号',
    `remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '类型分类表';

CREATE TABLE `swhd`.`tcontent_creative_actor_info` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '姓名',
    `stage_name` varchar(200) NOT NULL DEFAULT '' COMMENT '艺名',
    `id_number` varchar(20) NOT NULL DEFAULT '' COMMENT '身份证号',
    `sex` tinyint(4) NOT NULL DEFAULT 0 COMMENT '性别：0-女，1-男',
    `images` json COMMENT '图片',
    `style_type_ids` json COMMENT '风格类型',
    `proficient_industry_type_ids` json COMMENT '擅长行业类型',
    `proficient_show_type_ids` json COMMENT '擅长演出类型',
    `height` int(11) NOT NULL DEFAULT 0 COMMENT '身高(cm)',
    `weight` int(11) NOT NULL DEFAULT 0 COMMENT '体重(kg)',
    `shoe_size` int(11) NOT NULL DEFAULT 0 COMMENT '鞋码',
    `visual_age` int(11) NOT NULL DEFAULT 0 COMMENT '视觉年龄',
    `bust` int(11) NOT NULL DEFAULT 0 COMMENT '胸围',
    `waistline` int(11) NOT NULL DEFAULT 0 COMMENT '腰围',
    `hipline` int(11) NOT NULL DEFAULT 0 COMMENT '臀围',
    `reference_cost_min` int(11) NOT NULL DEFAULT 0 COMMENT '最小的参考演出费用',
    `reference_cost_max` int(11) NOT NULL DEFAULT 0 COMMENT '最大的参考演出费用',
    `comment_num` int(11) NOT NULL DEFAULT 0 COMMENT '评论数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员信息表';

CREATE TABLE `swhd`.`tcontent_creative_actor_video` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `actor_id` bigint NOT NULL DEFAULT 0 COMMENT '演员id',
    `video_oss_key` varchar(500) NOT NULL DEFAULT '' COMMENT '视频oss key',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_actorid` (`actor_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员视频案例表';

CREATE TABLE `swhd`.`tcontent_creative_actor_comment` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `actor_id` bigint NOT NULL DEFAULT 0 COMMENT '演员id',
    `content` varchar(500) NOT NULL DEFAULT '' COMMENT '评论内容',
    `commentator` varchar(100) NOT NULL DEFAULT '' COMMENT '评论人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_actorid` (`actor_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员评论表';

CREATE TABLE `swhd`.`tcontent_creative_actor_order_info` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `date` date NOT NULL COMMENT '订单日期',
    `total_cost` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总费用(单位:元)',
    `pay_custom_id` bigint NOT NULL DEFAULT 0 COMMENT '付费客户',
    `pay_type_id` bigint NOT NULL DEFAULT 0 COMMENT '付费类型',
    `actor_id` bigint NOT NULL DEFAULT 0 COMMENT '演员',
    `remark` varchar(200) NOT NULL DEFAULT 0 COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_date` (`tenant_id`, `date`) USING BTREE,
    KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员订单信息表';

CREATE TABLE `swhd`.`tcontent_creative_actor_order_custom` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `order_id` bigint NOT NULL DEFAULT 0 COMMENT '订单id',
    `custom_id` bigint NOT NULL DEFAULT 0 COMMENT '客户id',
    `capture_number` int(11) NOT NULL DEFAULT 0 COMMENT '拍摄数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_orderid` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员订单客户表';

CREATE TABLE `swhd`.`tcontent_creative_actor_order_material` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `order_id` bigint NOT NULL DEFAULT 0 COMMENT '订单id',
    `actor_id` bigint NOT NULL DEFAULT 0 COMMENT '演员',
    `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '素材类型：1-图片，2-视频',
    `oss_key` varchar(255) NOT NULL DEFAULT '' COMMENT '素材oss key',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_orderid` (`order_id`) USING BTREE,
    KEY `idx_actorid` (`actor_id`) USING BTREE,
    KEY `idx_createtime` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '创意演员订单素材表';
