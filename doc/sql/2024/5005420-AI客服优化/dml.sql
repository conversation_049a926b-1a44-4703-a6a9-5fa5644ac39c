INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946285915269758976, 1, 1891889450061922304, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}]', '推送管理', 0, 0, '', '', '', '', 50, '2024-09-14 17:25:22', '2024-09-14 17:25:22', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946286144303923200, 1, 1946285915269758976, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}]', '推送配置', 0, 0, '', '/aigc/kefu/push/script', '', '', 10, '2024-09-14 17:26:17', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946286273861779456, 1, 1946285915269758976, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}]', '推送渠道', 0, 0, '', '/aigc/kefu/push/platform', '', '', 20, '2024-09-14 17:26:48', '2024-09-18 19:08:30', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946286393571409920, 1, 1946285915269758976, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}]', '推送记录', 0, 0, '', '/aigc/kefu/push/record', '', '', 30, '2024-09-14 17:27:16', '2024-09-18 19:08:36', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946286512966467584, 1, 1946286144303923200, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286144303923200\", \"name\": \"推送配置\"}]', '列表', 1, 0, '', '', 'aiKefu#pushScriptList', '/swhd-ai-kefu-web-tenant/pushScript/page', 10, '2024-09-14 17:27:45', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946293859315089408, 1, 1946286273861779456, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286273861779456\", \"name\": \"推送渠道\"}]', '列表', 1, 0, '', '', 'aiKefu#pushPlatformList', '/swhd-ai-kefu-web-tenant/pushPlatform/page,/swhd-ai-kefu-web-tenant/platformInfo/selectAll,/swhd-ai-kefu-web-tenant/pushScript/selectAll', 10, '2024-09-14 17:56:56', '2024-09-18 19:08:31', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294132301365248, 1, 1946286393571409920, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286393571409920\", \"name\": \"推送记录\"}]', '列表', 1, 0, '', '', 'aiKefu#pushRecordList', '/swhd-ai-kefu-web-tenant/pushRecord/page,/swhd-ai-kefu-web-tenant/platformInfo/selectAll,/swhd-ai-kefu-web-tenant/pushScript/selectAll', 10, '2024-09-14 17:58:02', '2024-09-19 14:10:38', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294312190869504, 1, 1946286144303923200, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286144303923200\", \"name\": \"推送配置\"}]', '新增', 1, 0, '', '', 'aiKefu#pushScriptAdd', '/swhd-ai-kefu-web-tenant/pushScript/addConfig,/swhd-ai-kefu-web-tenant/pushScript/dataFieldMap', 20, '2024-09-14 17:58:44', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294365685022720, 1, 1946286144303923200, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286144303923200\", \"name\": \"推送配置\"}]', '修改', 1, 0, '', '', 'aiKefu#pushScriptUpdate', '/swhd-ai-kefu-web-tenant/pushScript/update,/swhd-ai-kefu-web-tenant/pushScript/getById,/swhd-ai-kefu-web-tenant/pushScript/dataFieldMap', 30, '2024-09-14 17:58:57', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294423335731200, 1, 1946286144303923200, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286144303923200\", \"name\": \"推送配置\"}]', '删除', 1, 0, '', '', 'aiKefu#pushScriptRemove', '/swhd-ai-kefu-web-tenant/pushScript/removeByIds', 40, '2024-09-14 17:59:11', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294689132969984, 1, 1946286273861779456, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286273861779456\", \"name\": \"推送渠道\"}]', '新增', 1, 0, '', '', 'aiKefu#pushPlatformAdd', '/swhd-ai-kefu-web-tenant/pushPlatform/add,/swhd-ai-kefu-web-tenant/platformInfo/selectAll,/swhd-ai-kefu-web-tenant/pushScript/selectAll', 20, '2024-09-14 18:00:14', '2024-09-18 19:08:31', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294735899459584, 1, 1946286273861779456, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286273861779456\", \"name\": \"推送渠道\"}]', '修改', 1, 0, '', '', 'aiKefu#pushPlatformUpdate', '/swhd-ai-kefu-web-tenant/pushPlatform/update,/swhd-ai-kefu-web-tenant/pushPlatform/getById', 30, '2024-09-14 18:00:25', '2024-09-18 19:08:31', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294816073580544, 1, 1946286273861779456, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286273861779456\", \"name\": \"推送渠道\"}]', '删除', 1, 0, '', '', 'aiKefu#pushPlatformRemove', '/swhd-ai-kefu-web-tenant/pushPlatform/removeByIds', 40, '2024-09-14 18:00:45', '2024-09-18 19:08:31', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1946294975280971776, 1, 1946286393571409920, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286393571409920\", \"name\": \"推送记录\"}]', '重推', 1, 0, '', '', 'aiKefu#pushRecordRePush', '/swhd-ai-kefu-web-tenant/pushRecord/rePush', 20, '2024-09-14 18:01:23', '2024-09-19 14:10:12', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO `swhd`.`tuser_tenant_menu_info` (`id`, `tenant_type`, `parent_id`, `parent_list`, `title`, `menu_type`, `click_type`, `icon`, `route_path`, `button_code`, `permission_url`, `ordered`, `create_time`, `modify_time`, `creator_id`, `modifier_id`, `is_delete`) VALUES (1947713557474836480, 1, 1946286144303923200, '[{\"id\": \"1891889450061922304\", \"name\": \"AI客服\"}, {\"id\": \"1946285915269758976\", \"name\": \"推送管理\"}, {\"id\": \"1946286144303923200\", \"name\": \"推送配置\"}]', '测试推送', 1, 0, '', '', 'aiKefu#pushScriptTestPush', '', 35, '2024-09-18 15:58:19', '2024-09-18 19:08:26', '1838991355741732864', '1838991355741732864', 0);



INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1947994692234772480, 1, 1891889450061922304, '[{"id": "1891889450061922304", "name": "AI客服"}]', '会话信息', 0, 0, '', '/aigc/kefu/dialogue', '', '', 5, '2024-09-19 10:35:27', '2024-09-20 17:03:32', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1947997616423829504, 1, 1947994692234772480, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1947994692234772480", "name": "会话信息"}]', '会话', 1, 0, '', '', 'aiKefu#chat', '/swhd-ai-kefu-web-tenant/userInfo/chatUserPage,/swhd-ai-kefu-web-tenant/userInfo/chatUserDetails,/swhd-ai-kefu-web-tenant/userInfo/customerCapitalDetails,/swhd-ai-kefu-web-tenant/userInfo/updateLastReadTime,/swhd-ai-kefu-web-tenant/msgRecord/scroll', 0, '2024-09-19 10:47:04', '2024-09-20 20:17:12', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1948457262200651776, 1, 1891889450061922304, '[{"id": "1891889450061922304", "name": "AI客服"}]', '数据总览', 0, 0, '', '/aigc/Kefu/Statistics', '', '', 0, '2024-09-20 17:13:32', '2024-09-22 16:37:29', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949171428918034432, 1, 1948457262200651776, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}]', '数据总览', 2, 0, '', '', '', '', 1, '2024-09-22 16:31:23', '2024-09-22 16:37:29', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949171491744514048, 1, 1948457262200651776, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}]', '渠道展示', 2, 0, '', '', '', '', 2, '2024-09-22 16:31:37', '2024-09-22 16:37:30', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949171546601816064, 1, 1948457262200651776, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}]', '广告计划展示', 2, 0, '', '', '', '', 3, '2024-09-22 16:31:51', '2024-09-22 16:37:30', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949171881718317056, 1, 1949171546601816064, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}, {"id": "1949171546601816064", "name": "广告计划展示"}]', '列表', 1, 0, '', '', 'aiKefu#statisticsAd', '/swhd-ai-kefu-web-tenant/statistics/ad/daily/page', 0, '2024-09-22 16:33:10', '2024-09-22 16:39:36', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949448594188664832, 1, 1949171491744514048, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}, {"id": "1949171491744514048", "name": "渠道展示"}]', '列表', 1, 0, '', '', 'aiKefu#statisticsAgent', '/swhd-ai-kefu-web-tenant/statistics/agent/daily/page', 0, '2024-09-23 10:52:44', '2024-09-23 11:03:55', '1943395431434158080', '1943395431434158080', 0);
INSERT INTO swhd.tuser_tenant_menu_info
(id, tenant_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete)
VALUES(1949551158485843968, 1, 1949171428918034432, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1948457262200651776", "name": "数据总览"}, {"id": "1949171428918034432", "name": "数据总览"}]', '图表', 1, 0, '', '', 'aiKefu#charts', '/swhd-ai-kefu-web-tenant/statistics/agent/daily/overview,/swhd-ai-kefu-web-tenant/statistics/ad/daily/topN', 0, '2024-09-23 17:40:17', '2024-09-23 17:40:56', '1943395431434158080', '1943395431434158080', 0);
