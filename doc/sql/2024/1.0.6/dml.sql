INSERT INTO swhd.taikf_industry_info (id, parent_id, parent_list, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903802371931373568, 0, '[]', '家居建材', 10, '2024-05-20 11:50:56', '2024-05-20 11:50:56', '', '', 0);
INSERT INTO swhd.taikf_industry_info (id, parent_id, parent_list, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903802509450018816, 1903802371931373568, '[{"id": "1903802371931373568", "name": "家居建材"}]', '家具', 10, '2024-05-20 11:51:29', '2024-05-20 11:51:29', '', '', 0);

INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852061318053888, 1903802371931373568, '手机号缺少1位', '[{"type": "TEXT", "content": "亲亲 您的号码不对呢，少了一位数，您再检查一下发个正确能联系的号码哦~[玫瑰]"}]', 1, '2024-05-20 15:08:23', '2024-05-20 15:08:23', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852062710562816, 1903802371931373568, '手机号多1位', '[{"type": "TEXT", "content": "亲亲 您的号码不对呢，多了一位数，您再检查一下发个正确能联系的号码哦~[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852062786060288, 1903802371931373568, '手机号多2位', '[{"type": "TEXT", "content": "亲亲 您的号码不对呢，多了两位数，您再检查一下发个正确能联系的号码哦~[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852062865752064, 1903802371931373568, '用户提供座机号码', '[{"type": "TEXT", "content": "咱们是需要11位的手机号哦，座机咱们无法录入系统给您分配顾问呢"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852062941249536, 1903802371931373568, '收到不支持的消息类型', '[{"type": "TEXT", "content": "亲亲，这边是临时窗口，部分信息有限制，看不到您发的哦"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063020941312, 1903802371931373568, '表示感谢', '[{"type": "TEXT", "content": "不客气~[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063163547648, 1903802371931373568, '表示肯定', '[{"type": "TEXT", "content": "[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063243239424, 1903802371931373568, '不愿留电话', '[{"type": "TEXT", "content": "亲亲放心哦 咱们是品牌留您的联系方式不会打扰您哦 ~ 可以帮您先登记一个活动名额的"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063310348288, 1903802371931373568, '不愿留微信', '[{"type": "TEXT", "content": "现在了解不是让您现在就定下来哦，只是给您介绍下，您好提前了解也可以多对比一下，也没有坏处嘛 您联系方式多少呢？是不会打扰您的[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063381651456, 1903802371931373568, '称呼', '[{"type": "TEXT", "content": "好哒~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063457148928, 1903802371931373568, '打招呼', '[{"type": "TEXT", "content": "您好，在的呢[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063528452096, 1903802371931373568, '当前没时间或要求指定时间联系', '[{"type": "TEXT", "content": "如果您这边忙的话，您可以先留下您的联系方式，等您有空的时候，咱们再安排专业顾问联系您为您介绍哈[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063603949568, 1903802371931373568, '房屋类型', '[{"type": "TEXT", "content": "好的，可以的呀[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063683641344, 1903802371931373568, '房子还没装修好', '[{"type": "TEXT", "content": "可以提前了解呢 亲，先给您发一下咱们家的案例还有报价，您先参考，后续您需要了可以随时联系哟~[嘿哈]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063759138816, 1903802371931373568, '可能有需求但需求时间不确定', '[{"type": "TEXT", "content": "恩恩，都是要提前了解的，我们可以安排专业顾问为您详细介绍，让您提前参考对比~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063830441984, 1903802371931373568, '倾向前往门店', '[{"type": "TEXT", "content": "亲亲，如果您直接门店了解和线上预约了解活动是不一样的哦，咱们这边是总部客服，您可以先了解下咱们现在的活动和款式，觉得合适再预约进店，这样也省得您白跑一趟呢[爱心]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063905939456, 1903802371931373568, '求职合作', '[{"type": "TEXT", "content": "这里接待窗口是不承接这类业务，抱歉哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852063977242624, 1903802371931373568, '区域', '[{"type": "TEXT", "content": "好的~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064048545792, 1903802371931373568, '手机与微信同号', '[{"type": "TEXT", "content": "好的[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064119848960, 1903802371931373568, '售后问题', '[{"type": "TEXT", "content": ""}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064191152128, 1903802371931373568, '未交房', '[{"type": "TEXT", "content": "没关系的 现在先了解不是让您现在就订[玫瑰]\\n您留个联系方式是为您安排个顾问给您发案例参考下，同时给您报个大概价格，这样心里也有个预算，做个多家对比，之后如果有需求了也心里有数的呢~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064262455296, 1903802371931373568, '问产品质量', '[{"type": "TEXT", "content": "亲亲可放心哦，我们是大品牌，质量都有保障，而且都有完善的售后服务，为您保驾护航，购买无忧哈[爱心]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064388284416, 1903802371931373568, '问发货到货时间', '[{"type": "TEXT", "content": "亲，以各门店情况为准哈，这边安排对应城市门店顾问给您介绍下"}]', 0, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064459587584, 1903802371931373568, '问公司名称', '[{"type": "TEXT", "content": ""}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064530890752, 1903802371931373568, '问家具材质', '[{"type": "TEXT", "content": "亲，咱们家不同材质都是有的哦~可以对接专业定制顾问根据您的需求介绍下不同价位、材质的定制产品供您参考了解哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064610582528, 1903802371931373568, '问门店地址', '[{"type": "TEXT", "content": "亲 这边是临时接待 全国各个城市都有门店哈，要给您录入系统自动分配门店顾问给您详细介绍并发送门店位置还有活动详情呢"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064786743296, 1903802371931373568, '问门店电话', '[{"type": "TEXT", "content": "亲 这边是临时窗口 咱们家有很多门店哈，这边没有各门店的联系方式，我给您录入系统自动分配门店顾问给您发送门店位置还有活动详情呢~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852064971292672, 1903802371931373568, '问门店营业时间', '[{"type": "TEXT", "content": "每个城市的营业时间都是不同的，稍后这边会安排当地顾问联系您哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065071955968, 1903802371931373568, '问是否可以订做家具', '[{"type": "TEXT", "content": "可以的哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065147453440, 1903802371931373568, '问是否可以送货上门', '[{"type": "TEXT", "content": "恩恩，稍后安排专业顾问给您详细介绍下咱们的服务哈~[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065210368000, 1903802371931373568, '问是否免费', '[{"type": "TEXT", "content": "咱们有免费设计哈"}]', 0, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065281671168, 1903802371931373568, '问送货区域', '[{"type": "TEXT", "content": "亲亲 咱们门店比较多呢，具体安排您就近的门店顾问详细为您介绍的哦[玫瑰]"}]', 0, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065361362944, 1903802371931373568, '问优惠活动', '[{"type": "TEXT", "content": " 亲，后续会有对应门店顾问给您介绍发送的哦"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065428471808, 1903802371931373568, '问直播相关', '[{"type": "TEXT", "content": ""}]', 0, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065499774976, 1903802371931373568, '无法通过手机号添加微信', '[{"type": "TEXT", "content": "亲，是这样的   咱们这边是需要您的电话才可以入系统给您匹配安排顾问的，微信咱们这边没办法给您私自安排，您如果不方便可以留个您的电话和微信，先给您安排顾问 ，这边帮您备注下哦[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065575272448, 1903802371931373568, '无房', '[{"type": "TEXT", "content": ""}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065650769920, 1903802371931373568, '无意向', '[{"type": "TEXT", "content": ""}]', 0, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065722073088, 1903802371931373568, '误输入', '[{"type": "TEXT", "content": "好的  您这边可以重新发下哦[愉快]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065839513600, 1903802371931373568, '需求时间', '[{"type": "TEXT", "content": "现在了解不是让您现在就定下来哦，只是给您介绍下，您好提前了解也可以多对比一下，您先看看我们的款式和报价，货比三家，然后再决定哈~[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065906622464, 1903802371931373568, '要顾问电话或微信', '[{"type": "TEXT", "content": "亲亲我们是系统分配顾问的~ 稍后让顾问联系您详细介绍哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852065973731328, 1903802371931373568, '要款式价格或咨询产品', '[{"type": "TEXT", "content": "我们有多种定制优惠套餐哦，看亲需求选择呢 我们也有多种风格的业主案例参考~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066049228800, 1903802371931373568, '要求别打电话', '[{"type": "TEXT", "content": "好的，亲亲，放心不会打扰您哈"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066128920576, 1903802371931373568, '要求电话联系', '[{"type": "TEXT", "content": "好的"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066258944000, 1903802371931373568, '要求先发案例价格或要求先看案例价格', '[{"type": "TEXT", "content": "亲亲，这里是临时对话窗口，发送不了太多案例和套餐价格内容呢，我们有多种风格的业主案例参考~"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066342830080, 1903802371931373568, '用户担心被骗', '[{"type": "TEXT", "content": "亲，咱们是品牌，都是做口碑的。是为了给您提供更加专业的服务，我这边只是前期接待，所以需要您的信息给您分配专业客服，为您介绍价格、风格呢，您提前看看也不耽误工夫，比较放心[爱心]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066409938944, 1903802371931373568, '用户担心被骚扰或要求不要骚扰', '[{"type": "TEXT", "content": "亲亲放心不会影响您的呢~您有需求的时候随时有一个专业的人为您解答，这样会更方便您咨询呢[玫瑰]"}]', 1, '2024-05-20 15:08:24', '2024-05-20 15:08:24', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066472853504, 1903802371931373568, '用户担心太贵或预算不够', '[{"type": "TEXT", "content": "价格是根据不同材质/尺寸/款式风格/都会有所不同的，我给您安排个顾问详细介绍一下不同价位~不合适也没关系的哈~您可以先看看，心里有数[愉快]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066539962368, 1903802371931373568, '用户希望现在联系', '[{"type": "TEXT", "content": "好的，这边帮您备注下，稍后让顾问联系您[愉快]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066607071232, 1903802371931373568, '用户嫌麻烦', '[{"type": "TEXT", "content": "亲亲放心哦 ，不是说要您现在订下来哦 ~ 只是先给您发一下报价和家具搭配案例，觉得合适您可以再详谈哈，也是给您省时省事不会麻烦的哦[玫瑰]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066674180096, 1903802371931373568, '有明确意向', '[{"type": "TEXT", "content": "好嘞[愉快]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066745483264, 1903802371931373568, '暂时不需要', '[{"type": "TEXT", "content": "亲亲放心哦，不是说就要您现在订下来哦 ~ 只是先给您介绍一下，报价和发一下最新案例，觉得合适您可以在详谈哈[愉快]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066808397824, 1903802371931373568, '这里就是微信', '[{"type": "TEXT", "content": "这里只是临时对话窗口看不到您号码哦，麻烦亲亲动动小手发一下您的号码哈"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066879700992, 1903802371931373568, '质疑不能发案例', '[{"type": "TEXT", "content": "因为临时窗口这边有资料限制，给您发不了，稍后让专业顾问给您发送哈，您先参考一下觉得合适在定~咱们是品牌平台，放心是不会打扰您的[调皮]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852066938421248, 1903802371931373568, '重复咨询', '[{"type": "TEXT", "content": ""}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067005530112, 1903802371931373568, '要求加微信或要求用微信联系', '[{"type": "TEXT", "content": "好的"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067076833280, 1903802371931373568, '不文明用语', '[{"type": "TEXT", "content": ""}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067152330752, 1903802371931373568, '手机号缺少2位', '[{"type": "TEXT", "content": "亲亲 您发的号码，好像少了两位，您再检查一下发个正确能联系的号码哦~[玫瑰]"}]', 1, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067236216832, 1903802371931373568, '地址不够详细', '[{"type": "TEXT", "content": "请问你具体在那里呢"}]', 0, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067311714304, 1903802371931373568, '地址在服务区域', '[{"type": "TEXT", "content": "在我们的服务区域内的"}]', 0, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067387211776, 1903802371931373568, '地址不在服务区域', '[{"type": "TEXT", "content": "很抱歉，你所在地不在我们的服务区域内，我们会尽快扩大服务范围，敬请期待。"}]', 0, '2024-05-20 15:08:25', '2024-05-20 15:08:25', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067387211777, 1903802371931373568, '要求发送发案例', '[{"type": "TEXT", "content": "因为临时窗口这边有资料限制，稍后让专业顾问给您发送哈，您先参考一下觉得合适在定~咱们是品牌平台，放心是不会打扰您的[调皮]"}]', 1, '2024-06-07 16:12:22', '2024-06-07 16:12:22', '', '', 0);
INSERT INTO swhd.taikf_knowledge_info (id, industry_id, name, intention_response_contents, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903852067387211778, 1903802371931373568, '要求发送发效果图', '[{"type": "TEXT", "content": "因为临时窗口这边有资料限制，稍后让专业顾问给您发送哈，您先参考一下觉得合适在定~咱们是品牌平台，放心是不会打扰您的[调皮]"}]', 1, '2024-06-07 16:19:37', '2024-06-07 16:19:37', '', '', 0);

INSERT INTO swhd.taikf_robot_ask_scene (id, industry_id, title, remark, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 0, '未获取手机', '手机号不存在', '2024-06-07 14:44:57', '2024-06-07 14:44:57', '', '', 0);
INSERT INTO swhd.taikf_robot_ask_scene (id, industry_id, title, remark, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(200, 0, '未获取区域', '地址不存在', '2024-06-07 14:44:57', '2024-06-07 14:44:57', '', '', 0);

INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 0, '手机号', '', 100, '2024-05-30 15:31:39', '2024-05-30 15:31:39', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(110, 0, '姓名', '', 110, '2024-05-30 15:32:47', '2024-05-30 15:32:47', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(120, 0, '地址', '', 120, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(130, 0, '性别', '', 130, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(140, 1903802371931373568, '房屋类型', '', 140, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(150, 1903802371931373568, '装修类型', '', 150, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(160, 1903802371931373568, '面积', '', 160, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(170, 1903802371931373568, '风格', '', 170, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(180, 1903802371931373568, '预算金额', '', 180, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(190, 1903802371931373568, '需求时间', '', 190, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);
INSERT INTO swhd.taikf_robot_node_type (id, industry_id, title, remark, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(200, 1903802371931373568, '装修时间', '', 200, '2024-05-30 15:32:48', '2024-05-30 15:32:48', '', '', 0);

INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903868309997355008, 0, '#0097e2', '客资', 110, '2024-05-20 16:12:57', '2024-05-20 16:12:57', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903869191015104512, 0, '#65df77', '无需求', 120, '2024-05-20 16:16:27', '2024-05-20 16:16:27', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903869256928591872, 0, '#13dcf2', '同行', 130, '2024-05-20 16:16:43', '2024-05-20 16:16:43', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903869320451325952, 0, '#ee1399', '自己人', 140, '2024-05-20 16:16:58', '2024-05-20 16:16:58', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903869390949187584, 0, '#e52250', '外地', 150, '2024-05-20 16:17:15', '2024-05-20 16:17:15', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903869440504889344, 0, '#fd5733', '测试用户', 160, '2024-05-20 16:17:27', '2024-05-20 16:17:27', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903870542545682432, 0, '#05c337', '不文明用语', 180, '2024-05-20 16:21:50', '2024-05-20 16:21:50', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903871313181933568, 1000, '#e806bb', '小面积', 400, '2024-05-20 16:24:53', '2024-05-20 16:24:53', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903871396912824320, 1000, '#8d1313', '自建房', 500, '2024-05-20 16:25:13', '2024-05-20 16:25:13', '', '', 0);
INSERT INTO swhd.taikf_tag_info (id, tenant_id, color, title, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1903871474931073024, 1000, '#0a2b48', '精装', 600, '2024-05-20 16:25:32', '2024-05-20 16:25:32', '', '', 0);

INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 0, 1, 'name', '姓名', NULL, 100, '2024-05-30 14:45:42', '2024-05-30 14:45:42', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(110, 0, 2, 'sex', '性别', NULL, 110, '2024-05-30 14:51:58', '2024-05-30 14:51:58', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(120, 0, 1, 'mobile', '手机号', '[1903868309997355008]', 120, '2024-05-30 14:51:58', '2024-05-30 14:51:58', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(130, 0, 1, 'address', '地址', NULL, 130, '2024-05-30 14:51:58', '2024-05-30 14:51:58', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(140, 1903802371931373568, 1, 'style', '风格', NULL, 140, '2024-05-30 14:56:36', '2024-05-30 14:56:36', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(150, 1903802371931373568, 1, 'house_type', '房屋类型', NULL, 150, '2024-05-30 14:56:36', '2024-05-30 14:56:36', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(160, 1903802371931373568, 1, 'size', '面积', NULL, 160, '2024-05-30 14:56:37', '2024-05-30 14:56:37', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(170, 1903802371931373568, 1, 'budget_amount', '预算金额', NULL, 170, '2024-05-30 14:56:37', '2024-05-30 14:56:37', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(180, 1903802371931373568, 1, 'decoration_time', '装修时间', NULL, 180, '2024-05-30 14:56:37', '2024-05-30 14:56:37', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(190, 1903802371931373568, 1, 'home_visit_time', '上门时间', NULL, 190, '2024-05-30 14:56:37', '2024-05-30 14:56:37', '', '', 0);
INSERT INTO swhd.taikf_user_attr_config (id, industry_id, `type`, code, title, link_tag_ids, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(200, 1903802371931373568, 1, 'remark', '备注', NULL, 200, '2024-05-30 14:56:37', '2024-05-30 14:56:37', '', '', 0);

INSERT INTO swhd.taikf_robot_info (id, tenant_id, industry_id, `type`, title, icon, remark, state, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 1000, 1903802509450018816, 2, '家装机器人', 'swhd/public/robot.jpeg', '', 1, '2024-05-30 16:12:09', '2024-06-06 11:31:53', '', '1856765079576510464', 0);
INSERT INTO swhd.taikf_robot_talk_flow (id, tenant_id, robot_id, nodes, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 1000, 100, '[{"id": "start-0", "data": {"contents": [{"type": "TALK_FLOW_NODE", "talkFlowNode": {"nodeId": "data-1"}}], "nodeName": "开始节点"}, "type": "start"}, {"id": "data-1", "data": {"skip": {"allow": true, "times": 1}, "select": {"open": true, "options": ["点击【全屋定制】", "点击【衣柜定制】", "点击【入户柜定制】", "点击【橱柜定制】", "点击【家具定制】"], "autoNumbers": false}, "contents": [{"type": "TEXT", "content": "hello，我是您的专属福利咨询官~请问您目前想要了解哪种定制呢（点击即可）"}], "nodeName": "问类型", "nodeTypeId": "150", "endCheckCustomerCapital": false}, "type": "node"}, {"id": "data-2", "data": {"skip": {"allow": true, "times": 2}, "askList": [{"content": {"type": "IMAGE", "materialUrl": "https://3vj-fe.3vjia.com/swhd/public/emoji/等您回复哦.jpg"}, "timeInterval": 30}, {"content": {"type": "TEXT", "content": "这边让顾问给您发下各风格的家具定制案例和套餐优惠，合适再安排资深设计师免费量房设计，出适合你家的方案和定制套餐，货比三家，您看可以吗？"}, "timeInterval": 48}, {"content": {"type": "TALK_FLOW_NODE", "talkFlowNode": {"nodeId": "data-3"}}, "timeInterval": 120}], "contents": [{"type": "TEXT", "content": "您房子是在哪个【省-市-区】呢？这边帮您查询离您最近的门店位置以及活动发您参考呢~[愉快]"}, {"type": "TEXT", "content": "我们全国有四千多家门店，您在【哪个城市哪个区】吗？这边给您发送一下对应城市具体的门店位置哦~"}, {"type": "TEXT", "content": "您好，请问房子是在哪个城市哪个区呢？（例：广州市  白云区）这边给您发一下当地流行的装修案例"}, {"type": "TEXT", "content": "您是在哪个城市哪个区呢？这边给您安排当地门店为您介绍优惠和案例哦[调皮]"}], "nodeName": "问区域", "nodeTypeId": "120", "endCheckCustomerCapital": false}, "type": "node"}, {"id": "data-3", "data": {"skip": {"allow": false}, "askList": [{"content": {"type": "TEXT", "content": "还在吗？三维家有个免费量房设计服务，帮您提高使用面积，增大收纳性，您看过方案觉得合适了才定制家具，您可以留一下【联系电话】，帮您预约一个免费设计名额，不合适就体验一下当参考~"}, "timeInterval": 90}, {"content": {"type": "IMAGE", "materialUrl": "https://3vj-fe.3vjia.com/swhd/public/emoji/发电话.jpg"}, "timeInterval": 120}, {"content": {"type": "TEXT", "content": "您放心，我们是连锁大品牌，很注重口碑呢~不会乱打扰您的，之前要您的联系号码是为了预留个线上限时特惠和免费设计名额，您先看看，后期1v1沟通 再进一步考虑哈[拥抱]"}, "timeInterval": 200}], "contents": [{"type": "TEXT", "content": "您可以留一下【联系电话】，帮您保留一个免费设计名额，同时也发一些相应房型的热门案例及详细价目表给您参考一下~[机智]"}, {"type": "TEXT", "content": "亲，方便留个手机号吗？[让我看看]线上可以免费预约设计师，免费出效果图您再进一步对比，这边把离您最近的门店和优惠力度发到您手机上可以吗？"}, {"type": "TEXT", "content": "我们提供免费设计服务，可以免费设计出方案、报价，您合适再考虑定制，不合适就当参考，活动公司免费体验的，您的【联系号码】是多少呢？这边简单登记一下~"}, {"type": "TEXT", "content": "亲~要您联系方式是想帮你录入我们VIP系统，只要是网络咨询去当地看产品，都会享受最大折扣价呢亲，之后也会匹配就近门店地址发到您手机上呢[调皮]"}, {"type": "TEXT", "content": "马上安排顾问为您介绍 手机号可以发一下嘛~ 这边帮您登记一下活动优惠名额 平时是不会随便打扰你的哟~[玫瑰]"}], "nodeName": "问手机号", "nodeTypeId": "100", "endCheckCustomerCapital": true}, "type": "node"}, {"id": "data-4", "data": {"skip": {"allow": true, "times": 1}, "askList": [{"content": {"type": "TEXT", "content": "亲亲，你怎么称呼呢？[调皮]"}, "timeInterval": 60}], "contents": [{"type": "TEXT", "content": "请问您这边怎么称呼呢？这边备注下~（例如：李先生/女士）"}], "nodeName": "问姓名", "nodeTypeId": "110", "endCheckCustomerCapital": false}, "type": "node"}, {"id": "data-5", "data": {"skip": {"allow": true, "times": 1}, "select": {"open": true, "options": ["【1】一周内", "【2】一个月内", "【3】三个月内", "【4】半年内"], "autoNumbers": false}, "contents": [{"type": "TEXT", "content": "亲，您什么时候方便到店了解下呢？【回复数字即可】\\n我们现在是有套餐优惠活动的哦~[礼物]"}], "nodeName": "问需求时间", "nodeTypeId": "190", "endCheckCustomerCapital": false}, "type": "node"}, {"id": "end-0", "data": {"contents": [{"type": "TEXT", "content": "感谢支持，三维家家居顾问会在【今明天内】联系您，只需1~2分钟沟通，保留线上优惠名额，也会整理最新款式、优惠套餐参考，请保持手机畅通，注意陌生来电~[玫瑰]"}], "nodeName": "结束节点"}, "type": "end"}]', '2024-05-30 16:31:49', '2024-06-11 14:13:31', '', '1856765079576510464', 0);
INSERT INTO swhd.taikf_robot_talk_strategy (id, tenant_id, robot_id, not_answer_strategy, not_answer_strategy_contents, ask_strategy, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(100, 1000, 100, 2, '[{"type": "TEXT", "content": "为了更快帮您安排顾问介绍 留个您的【姓名电话+房子所在地】帮您优先安排哈 [转圈]\\n👉已留联系方式的亲亲   小编会尽快帮您安排哟[爱心]"}]', 1, '2024-05-30 16:28:38', '2024-05-30 16:28:38', '', '', 0);

INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1909688590523170816, 2, 1891896557377159168, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', '详情', 1, 0, '', '', 'aiKefu#robotInfoDetail', '/swhd-ai-kefu-web-tenant/robotInfo/getById,/swhd-ai-kefu-web-tenant/robotAskScene/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotNodeType/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotTalkFlow/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkStrategy/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansTime/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/page,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/getById', 20, '2024-06-05 17:40:40', '2024-06-12 17:49:43', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1909707870279041024, 2, 1891896896000098304, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}]', '修改', 1, 0, '', '', 'aiKefu#platformInfoUpdate', '/swhd-ai-kefu-web-tenant/platformInfo/update,/swhd-ai-kefu-web-tenant/platformInfo/bingIndustry,/swhd-ai-kefu-web-tenant/platformInfo/getById,/swhd-ai-kefu-web-tenant/platformKnowledge/update,/swhd-ai-kefu-web-tenant/platformKnowledge/getById,/swhd-ai-kefu-web-tenant/robotInfo/selectAll', 30, '2024-06-05 18:57:17', '2024-06-12 17:50:00', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910060420173922304, 2, 1891889450061922304, '[{"id": "1891889450061922304", "name": "AI客服"}]', '标签管理', 0, 0, 'icon-标签', '/aigc/kefu/tag', '', '', 25, '2024-06-06 18:18:11', '2024-06-12 17:50:09', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910060752618651648, 2, 1910060420173922304, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1910060420173922304", "name": "标签管理"}]', '列表', 1, 0, '', '', 'aiKefu#tagInfoList', '/swhd-ai-kefu-web-tenant/tagInfo/page', 10, '2024-06-06 18:19:31', '2024-06-12 17:50:09', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910060848026484736, 2, 1910060420173922304, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1910060420173922304", "name": "标签管理"}]', '新增', 1, 0, '', '', 'aiKefu#tagInfoAdd', '/swhd-ai-kefu-web-tenant/tagInfo/add', 20, '2024-06-06 18:19:53', '2024-06-12 17:50:09', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910060893471768576, 2, 1910060420173922304, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1910060420173922304", "name": "标签管理"}]', '修改', 1, 0, '', '', 'aiKefu#tagInfoUpdate', '/swhd-ai-kefu-web-tenant/tagInfo/update,/swhd-ai-kefu-web-tenant/tagInfo/getById,/swhd-ai-kefu-web-tenant/tagState/save', 30, '2024-06-06 18:20:04', '2024-06-12 17:50:09', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910061096912289792, 2, 1910060420173922304, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1910060420173922304", "name": "标签管理"}]', '删除', 1, 0, '', '', 'aiKefu#tagInfoRemove', '/swhd-ai-kefu-web-tenant/tagInfo/removeByIds', 40, '2024-06-06 18:20:53', '2024-06-12 17:50:09', '1838991355741732864', '1838991355741732864', 0);
INSERT INTO swhd.tuser_menu_info (id, sys_type, parent_id, parent_list, title, menu_type, click_type, icon, route_path, button_code, permission_url, ordered, create_time, modify_time, creator_id, modifier_id, is_delete) VALUES(1910411676872605696, 2, 1891891005037215744, '[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891891005037215744", "name": "用户信息"}]', '会话', 1, 0, '', '', 'aiKefu#userInfoMsg', '/swhd-ai-kefu-web-tenant/msgRecord/scroll,/swhd-ai-kefu-web-tenant/msgApi/sendMsg,/swhd-ai-kefu-web-tenant/userInfo/getById,/swhd-ai-kefu-web-tenant/userInfo/update', 20, '2024-06-07 17:33:57', '2024-06-12 17:46:21', '1838991355741732864', '1838991355741732864', 0);

UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891889450061922304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}]', title='客资列表', menu_type=0, click_type=0, icon='icon-客户资料', route_path='/aigc/kefu/user/customerCapital', button_code='', permission_url='', ordered=40, create_time='2024-04-17 14:58:36', modify_time='2024-06-12 17:51:39', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891890796479643648;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891889450061922304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}]', title='用户信息', menu_type=0, click_type=0, icon='UserOutlined', route_path='/aigc/kefu/user/info', button_code='', permission_url='', ordered=30, create_time='2024-04-17 14:59:25', modify_time='2024-06-12 17:46:21', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891891005037215744;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891890796479643648, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891890796479643648", "name": "客资列表"}]', title='列表', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#userCustomerCapitalList', permission_url='/swhd-ai-kefu-web-tenant/userInfo/customerCapitalPage,/swhd-ai-kefu-web-tenant/tagState/selectActivated', ordered=10, create_time='2024-04-17 15:01:38', modify_time='2024-06-12 17:51:39', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891891560136572928;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891890796479643648, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891890796479643648", "name": "客资列表"}]', title='会话', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#userCustomerCapitalMsg', permission_url='/swhd-ai-kefu-web-tenant/msgRecord/scroll,/swhd-ai-kefu-web-tenant/msgApi/sendMsg,/swhd-ai-kefu-web-tenant/userInfo/getById,/swhd-ai-kefu-web-tenant/userInfo/update', ordered=20, create_time='2024-04-17 15:06:48', modify_time='2024-06-12 17:51:39', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891892860626665472;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891891005037215744, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891891005037215744", "name": "用户信息"}]', title='列表', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#userInfoList', permission_url='/swhd-ai-kefu-web-tenant/userInfo/page,/swhd-ai-kefu-web-tenant/tagState/selectActivated', ordered=10, create_time='2024-04-17 15:12:29', modify_time='2024-06-12 17:46:21', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891894293967142912;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891889450061922304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}]', title='机器人配置', menu_type=0, click_type=0, icon='icon-机器人', route_path='/aigc/kefu/robot', button_code='', permission_url='', ordered=10, create_time='2024-04-17 15:21:29', modify_time='2024-06-12 17:49:43', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891896557377159168;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891889450061922304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}]', title='渠道', menu_type=0, click_type=0, icon='icon-渠道', route_path='/aigc/kefu/platform', button_code='', permission_url='', ordered=20, create_time='2024-04-17 15:22:50', modify_time='2024-06-12 17:50:00', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891896896000098304;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896896000098304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}]', title='公众号', menu_type=2, click_type=0, icon='', route_path='/aigc/oauth/wechat/mp', button_code='', permission_url='', ordered=50, create_time='2024-04-17 15:23:07', modify_time='2024-06-05 18:09:02', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=1 WHERE id=1891896968955822080;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='列表', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#robotInfoList', permission_url='/swhd-ai-kefu-web-tenant/robotInfo/page,/swhd-ai-kefu-web-tenant/robotInfo/templatePage,/swhd-ai-kefu-web-tenant/industryInfo/selectTree', ordered=10, create_time='2024-04-17 15:26:22', modify_time='2024-06-12 17:49:43', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891897787210006528;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896968955822080, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}, {"id": "1891896968955822080", "name": "公众号"}]', title='列表', menu_type=1, click_type=0, icon='', route_path='', button_code='oauth#aigc#wechatMpOauthInfoList', permission_url='/swhd-oauth-web-tenant/wechatOauthInfo/page/MP', ordered=10, create_time='2024-04-17 15:43:06', modify_time='2024-06-05 18:09:02', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=1 WHERE id=1891901996860964864;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896968955822080, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}, {"id": "1891896968955822080", "name": "公众号"}]', title='授权', menu_type=1, click_type=0, icon='', route_path='', button_code='oauth#aigc#wechatMpOauthInfoOauth', permission_url='/swhd-oauth-web-tenant/wechatOpenProviderOauthPre/createUrl/MP', ordered=20, create_time='2024-04-17 15:43:58', modify_time='2024-06-05 18:09:02', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=1 WHERE id=1891902214599868416;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896896000098304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}]', title='列表', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#platformInfoList', permission_url='/swhd-ai-kefu-web-tenant/platformInfo/page,/swhd-oauth-web-tenant/douyinOauthInfo/selectAll,/swhd-oauth-web-tenant/wechatOauthInfo/selectAll/MP,/swhd-ai-kefu-web-tenant/platformKnowledge/page,/swhd-ai-kefu-web-tenant/industryInfo/selectTree,/swhd-ai-kefu-web-tenant/platformInfo/getById', ordered=10, create_time='2024-04-17 15:57:25', modify_time='2024-06-12 17:50:00', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891905599256395776;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896896000098304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}]', title='授权', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#platformInfoOauth', permission_url='/swhd-oauth-web-tenant/douyinOauthPre/createPreAuthUrl,/swhd-oauth-web-tenant/wechatOpenProviderOauthPre/createUrl/MP', ordered=20, create_time='2024-04-17 15:58:02', modify_time='2024-06-12 17:50:00', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1891905756618293248;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='复制', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#robotInfoCopy', permission_url='/swhd-ai-kefu-web-tenant/robotInfo/copy', ordered=30, create_time='2024-04-18 16:49:59', modify_time='2024-06-12 17:49:43', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1892281217391263744;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='修改', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#robotInfoUpdate', permission_url='/swhd-ai-kefu-web-tenant/robotInfo/update,/swhd-ai-kefu-web-tenant/robotInfo/getById,/swhd-ai-kefu-web-tenant/robotAskScene/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotNodeType/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotTalkFlow/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkFlow/save,/swhd-ai-kefu-web-tenant/robotTalkStrategy/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkStrategy/save,/swhd-ai-kefu-web-tenant/robotChaseFansTime/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansTime/save,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/page,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/getById,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/add,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/update,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/removeByIds,/swhd-ai-kefu-web-tenant/tagState/selectActivated', ordered=40, create_time='2024-04-18 16:50:30', modify_time='2024-06-12 17:49:43', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1892281346575826944;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='删除', menu_type=1, click_type=0, icon='', route_path='', button_code='aigc#kefuAppInfoRemove', permission_url='/swhd-aigc-web-tenant/kefuAppInfo/removeByIds', ordered=40, create_time='2024-04-18 16:50:47', modify_time='2024-06-05 17:35:08', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=1 WHERE id=1892281418625581056;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891889450061922304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}]', title='效果图', menu_type=0, click_type=0, icon='FileImageOutlined', route_path='/aigc/image', button_code='', permission_url='', ordered=60, create_time='2024-05-06 09:49:49', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1898698459234435072;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898698459234435072, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}]', title='文生图', menu_type=0, click_type=0, icon='', route_path='/aigc/image/text', button_code='', permission_url='', ordered=10, create_time='2024-05-06 09:50:33', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1898698645235040256;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898698459234435072, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}]', title='图生图', menu_type=0, click_type=0, icon='', route_path='/aigc/image/image', button_code='', permission_url='', ordered=20, create_time='2024-05-06 09:51:42', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1898698935589928960;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898698459234435072, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}]', title='变化', menu_type=0, click_type=0, icon='', route_path='/aigc/image/change', button_code='', permission_url='', ordered=30, create_time='2024-05-06 09:52:20', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1898699092943437824;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898698459234435072, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}]', title='效果图搜索', menu_type=0, click_type=0, icon='', route_path='/aigc/image/search', button_code='', permission_url='', ordered=40, create_time='2024-05-06 09:52:42', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1898699184152772608;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898698935589928960, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}, {"id": "1898698935589928960", "name": "图生图"}]', title='上传', menu_type=1, click_type=0, icon='', route_path='', button_code='', permission_url='/swhd-content-web-tenant/ossPublic/uploadSign', ordered=10, create_time='2024-05-07 09:44:36', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1899059535067217920;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1898699092943437824, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1898698459234435072", "name": "效果图"}, {"id": "1898699092943437824", "name": "变化"}]', title='上传', menu_type=1, click_type=0, icon='', route_path='', button_code='', permission_url='/swhd-content-web-tenant/ossPublic/uploadSign', ordered=10, create_time='2024-05-07 09:44:50', modify_time='2024-06-12 17:52:05', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1899059592512405504;

UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='修改', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#robotInfoUpdate', permission_url='/swhd-ai-kefu-web-tenant/robotInfo/update,/swhd-ai-kefu-web-tenant/robotInfo/getById,/swhd-ai-kefu-web-tenant/robotAskScene/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotNodeType/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotTalkFlow/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkFlow/save,/swhd-ai-kefu-web-tenant/robotTalkStrategy/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkStrategy/save,/swhd-ai-kefu-web-tenant/robotChaseFansTime/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansTime/save,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/page,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/getById,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/add,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/update,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/removeByIds,/swhd-ai-kefu-web-tenant/tagState/selectActivated,/swhd-ai-kefu-web-tenant/robotChat/selectAll', ordered=40, create_time='2024-04-18 16:50:30', modify_time='2024-06-17 19:29:22', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1892281346575826944;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896557377159168, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896557377159168", "name": "机器人配置"}]', title='详情', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#robotInfoDetail', permission_url='/swhd-ai-kefu-web-tenant/robotInfo/getById,/swhd-ai-kefu-web-tenant/robotAskScene/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotNodeType/selectByIndustryId,/swhd-ai-kefu-web-tenant/robotTalkFlow/getByRobotId,/swhd-ai-kefu-web-tenant/robotTalkStrategy/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansTime/getByRobotId,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/page,/swhd-ai-kefu-web-tenant/robotChaseFansMsg/getById,/swhd-ai-kefu-web-tenant/robotChat/selectAll', ordered=20, create_time='2024-06-05 17:40:40', modify_time='2024-06-17 19:29:31', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1909688590523170816;
UPDATE swhd.tuser_menu_info SET sys_type=2, parent_id=1891896896000098304, parent_list='[{"id": "1891889450061922304", "name": "AI客服"}, {"id": "1891896896000098304", "name": "渠道"}]', title='修改', menu_type=1, click_type=0, icon='', route_path='', button_code='aiKefu#platformInfoUpdate', permission_url='/swhd-ai-kefu-web-tenant/platformInfo/update,/swhd-ai-kefu-web-tenant/platformInfo/bingIndustry,/swhd-ai-kefu-web-tenant/platformInfo/getById,/swhd-ai-kefu-web-tenant/platformKnowledge/update,/swhd-ai-kefu-web-tenant/platformKnowledge/getById,/swhd-ai-kefu-web-tenant/robotInfo/selectAll,/swhd-ai-kefu-web-tenant/materialSpace/selectAll', ordered=30, create_time='2024-06-05 18:57:17', modify_time='2024-06-17 19:28:37', creator_id='1838991355741732864', modifier_id='1838991355741732864', is_delete=0 WHERE id=1909707870279041024;
