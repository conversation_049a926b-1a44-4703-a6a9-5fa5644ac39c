
CREATE TABLE `swhd`.`tagent_statistics_self_operate_config` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `enabled_sync_data` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启同步数据：0-否，1-是',
    `notify_robot` varchar(500) NOT NULL DEFAULT '' COMMENT '通知机器人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid` (`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '自运营统计配置表';

CREATE TABLE `swhd`.`tagent_statistics_self_operate_oceanengine_total_day` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `date` date NOT NULL COMMENT '统计日期',
    `self_operate_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '自运营消耗(单位元)',
    `self_operate_consume_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营消耗占比',
    `self_operate_account_num` int(11) NOT NULL DEFAULT 0 COMMENT '自运营账户数',
    `self_operate_account_num_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营账户数占比',
    `total_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总消耗(单位元)',
    `total_account_num` int(11) NOT NULL DEFAULT 0 COMMENT '总账户数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_date` (`tenant_id`, `date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '自运营统计汇总日表';

CREATE TABLE `swhd`.`tagent_statistics_self_operate_oceanengine_total_month` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `date` date NOT NULL COMMENT '统计日期',
    `self_operate_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '自运营消耗(单位元)',
    `self_operate_consume_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营消耗占比',
    `total_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总消耗(单位元)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_date` (`tenant_id`, `date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '自运营统计汇总月表';

CREATE TABLE `swhd`.`tagent_statistics_self_operate_oceanengine_company_day` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `date` date NOT NULL COMMENT '统计日期',
    `company_name` varchar(200) NOT NULL DEFAULT 0 COMMENT '公司名称',
    `self_operate_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '自运营消耗(单位元)',
    `self_operate_consume_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营消耗占比',
    `self_operate_account_num` int(11) NOT NULL DEFAULT 0 COMMENT '自运营账户数',
    `self_operate_account_num_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营账户数占比',
    `total_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总消耗(单位元)',
    `total_account_num` int(11) NOT NULL DEFAULT 0 COMMENT '总账户数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_date_companyname` (`tenant_id`, `date`, `company_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '自运营统计公司日表';

CREATE TABLE `swhd`.`tagent_statistics_self_operate_oceanengine_company_month` (
    `id` bigint NOT NULL COMMENT '主键id',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户id',
    `date` date NOT NULL COMMENT '统计日期',
    `company_name` varchar(200) NOT NULL DEFAULT 0 COMMENT '公司名称',
    `self_operate_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '自运营消耗(单位元)',
    `self_operate_consume_ratio` decimal(10,4) NOT NULL DEFAULT 0 COMMENT '自运营消耗占比',
    `total_consume` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '总消耗(单位元)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_tenantid_date_companyname` (`tenant_id`, `date`, `company_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '自运营统计公司月表';

ALTER TABLE swhd.tagent_account_oceanengine_info
    ADD optimization_engineer bigint DEFAULT 0 NOT NULL COMMENT '优化师' AFTER custom_channel_id;
