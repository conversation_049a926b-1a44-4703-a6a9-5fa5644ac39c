(function () {
  var _open = window.XMLHttpRequest.prototype.open;
  window.XMLHttpRequest.prototype.open = function(method, url) {
    if (url && url.startsWith('/api/')) {
      if (!(url.startsWith('/api/muse/account/')
        || url.startsWith('/api/muse/hybridapp/'))) {
        url = 'https://muse.console.volcengine.com' + url
      }
    }
    this._url = url; // 保存URL以便在响应处理时使用
    return _open.apply(this, [method, url]);
  };
  var _send = window.XMLHttpRequest.prototype.send;
  window.XMLHttpRequest.prototype.send = function() {
    var xhr = this;
    var _onreadystatechange = xhr.onreadystatechange;
    xhr.onreadystatechange = function() {
      if (xhr.readyState === 4 && xhr.status >= 200 && xhr.status < 300) {
        try {
          var contentType = xhr.getResponseHeader('Content-Type');
          if (contentType && contentType.includes('application/json')) {
            var jsonResponse = JSON.parse(xhr.responseText);
            window.parent.postMessage({
              type: 'swhd_muse_send_json_response',
              url: xhr._url,
              jsonResponse: jsonResponse,
            }, '*')
          }
        } catch (e) {
          console.error('解析JSON响应失败:', e);
        }
      }
      if (_onreadystatechange) {
        return _onreadystatechange.apply(this, arguments);
      }
    };
    return _send.apply(this, arguments);
  };

  function loadCss(href) {
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = href;
    document.head.appendChild(link);
  }

  var isSub = window.self !== window.top
  if (isSub) {
    loadCss('https://3vj-fe.3vjia.com/swhd/muse/css/muse-common-v1.0.0.css?_=24110501')
    loadCss('https://3vj-fe.3vjia.com/swhd/muse/css/muse-sub-v1.0.0.css?_=24110501')
  }

  var historyHref = ''
  var historyPathname = ''
  var moreToolsPath = '/muse/home/<USER>'
  var toolPathList = ['/muse/text2video', '/muse/editor/create', '/muse/recommend',
    '/muse/intelligent-dub', '/muse/graphic-editor', '/muse/data-video-choose-chart', '/muse/data-video',
    '/muse/video-segment-new/home', '/muse/videosegment-create', '/muse/smart-crop', '/muse/brands/attach']

  function inToolPathList(pathname) {
    for (var i = 0; i < toolPathList.length; i++) {
      if (pathname.startsWith(toolPathList[i])) {
        return true
      }
    }
    return false
  }

  var ingPush = false
  window.addEventListener('message', function (event) {
    var data = event.data
    if (data.type === 'swhd_history_push') {
      if (window._history) {
        historyHref = window.location.origin + data.url
        historyPathname = data.url.split('?')[0]
        if (historyPathname === window.pathname) {
          return
        }
        ingPush = true
        window._history.push(data.url)
        historyHref = window.location.href
        historyPathname = window.location.pathname
        ingPush = false
      } else {
        window.location.href = data.url
      }
    } else if (data.type === 'swhd_history_replace') {
      if (window._history) {
        historyHref = window.location.origin + data.url
        historyPathname = data.url.split('?')[0]
        if (historyPathname === window.pathname) {
          return
        }
        ingPush = true
        window._history.replace(data.url)
        historyHref = window.location.href
        historyPathname = window.location.pathname
        ingPush = false
      } else {
        window.location.href = data.url
      }
    } else if (data.type === 'swhd_page_hidden') {
      var videos = document.querySelectorAll('video');
      for (var i = 0; i < videos.length; i++) {
        videos[i].pause();
      }
    }
  });

  if (window.parent !== window) {
    setInterval(function () {
      if (ingPush || window.location.href === historyHref) {
        return
      }
      if (isSub && historyPathname === '/muse/mixedCut' && window.location.pathname === '/muse/home/<USER>') {
        window._history.go(-3)
        return
      }
      if (isSub && inToolPathList(historyPathname) && window.location.pathname === '/muse/home/<USER>') {
        window._history.go(-2)
        return
      }
      var moreToolsToToolPage = historyPathname === moreToolsPath && inToolPathList(window.location.pathname)
      historyHref = window.location.href
      historyPathname = window.location.pathname
      var search = window.location.search
      if (search && search.indexOf('access_token=') > 0) {
        var url = new URL(window.location.href);
        url.searchParams.delete('access_token')
        search = url.search
      }
      window.parent.postMessage({
        type: 'swhd_muse_path_change',
        pathname: window.location.pathname,
        search: search,
        hash: window.location.hash,
        moreToolsToToolPage: moreToolsToToolPage
      }, '*')
    }, 300)
  }
})()