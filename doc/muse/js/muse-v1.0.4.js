(function () {
  var _open = window.XMLHttpRequest.prototype.open;
  window.XMLHttpRequest.prototype.open = function(method, url) {
    if (url && url.startsWith('/api/')) {
      if (!(url.startsWith('/api/muse/account/')
        || url.startsWith('/api/muse/hybridapp/'))) {
        url = 'https://muse.console.volcengine.com' + url
      }
    }
    return _open.apply(this, [method, url]);
  };
  window.addEventListener('message', (event) => {
    const data = event.data
    if (data.type === 'swhd_history_push') {
      if (window._history) {
        window._history.push(data.url)
      } else {
        window.location.href = data.url
      }
    }
  });
})()